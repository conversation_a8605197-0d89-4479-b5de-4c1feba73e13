import { computed, defineComponent, reactive, ref, watch, watchEffect } from "vue";
import {
    GET_IRON_PRODUCTION_PLAN_LINE_DETAIL,
    GET_IRON_PRODUCTION_PLAN_MATERIAL_PAGE_LIST
} from "@/api/application/TowerScan";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import {
    TableSearchbar,
    type TableSearchbarConfig,
    type TableSearchbarData,
    type TableSearchbarOptions
} from "@/components/TableSearchbar";
import { TableActions } from "@/components/TableActions";
import TowerScanEngineeringProcessesPartsExecution from "../Engineering/Processes/TowerScanEngineeringProcessesPartsExecution";

export default defineComponent({
    name: "TowerScanProductionMaterialDetail",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        // 获取详情
        interface DetailDataProps {
            [key: string]: any;
        }

        const detailData = ref<DetailDataProps>({});

        const getDetail = () => {
            GET_IRON_PRODUCTION_PLAN_LINE_DETAIL({ id: props.configData.id }).then((res) => {
                if (res.data.code === 0) {
                    detailData.value = res.data.data;
                    // 设置默认选中的机器
                    if (detailData.value?.equipmentList?.length > 0) {
                        equipmentActive.value = String(detailData.value.equipmentList[0].equipmentId);
                    }
                }
            });
        };

        const onClose = () => {
            changeModalShow(false);
        };

        // 机器tab
        const equipmentOptions = ref<any[]>([]);
        const equipmentActive = ref<string>("");

        // 监听变化并重新查询
        const handleTabChange = () => {
            if (show.value) {
                tablePagination.page = 1;
                getTableData();
            }
        };

        watch(() => equipmentActive.value, handleTabChange);

        // 搜索项
        const searchConfig = ref<TableSearchbarConfig>([
            { label: "零件编号", prop: "materialCode", type: "input" },
            { label: "规格", prop: "materialSpec", type: "input" },
            { label: "尺寸", prop: "materialSize", type: "input" },
            { label: "分类", prop: "materialClassify", type: "select" }
        ]);

        const searchOptions = ref<TableSearchbarOptions>({
            materialClassify: [
                { label: "角钢", value: 1 },
                { label: "钢板", value: 2 },
                { label: "圆钢", value: 3 },
                { label: "圆管", value: 4 },
                { label: "槽钢", value: 5 }
            ]
        });

        const searchForm = ref<TableSearchbarData>({
            materialCode: null,
            materialSpec: null,
            materialSize: null,
            materialClassify: null
        });

        const onSearch = () => {
            tablePagination.page = 1;
            tablePagination.pageSize = 10;
            getTableData();
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading } = useCommonTable<RowProps>("id");

        // 执行情况弹窗
        const executionShow = ref(false);
        const executionConfigData = ref<any>({});

        const showExecutionModal = (row: RowProps) => {
            executionConfigData.value = {
                // 2025年6月26日14:51:38后台让materialId改成id
                planMaterialId: row.id,
                materialCode: row.materialCode,
                typeName: row.typeName,
                projectName: row.projectName
            };
            executionShow.value = true;
        };

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { title: "工程名称", key: "projectName", align: "center" },
            { title: "塔型", key: "typeName", align: "center" },
            { title: "合同号", key: "contractNumber", align: "center" },
            { title: "零件编号", key: "materialCode", align: "center" },
            {
                title: "零件类别",
                key: "materialClassify",
                align: "center",
                render: (row) => {
                    if (String(row.materialClassify) === "1") {
                        return <n-text type="info">角钢</n-text>;
                    } else if (String(row.materialClassify) === "2") {
                        return <n-text type="info">钢板</n-text>;
                    } else if (String(row.materialClassify) === "3") {
                        return <n-text type="info">圆钢</n-text>;
                    } else if (String(row.materialClassify) === "4") {
                        return <n-text type="info">圆管</n-text>;
                    } else if (String(row.materialClassify) === "5") {
                        return <n-text type="info">槽钢</n-text>;
                    } else {
                        return <n-text type="info">/</n-text>;
                    }
                }
            },
            { title: "材质", key: "materialQuality", align: "center" },
            { title: "规格", key: "materialSpec", align: "center" },
            { title: "尺寸", key: "materialSize", align: "center" },
            {
                title: "需求单基数量",
                key: "singleBaseQuantity",
                align: "center",
                render: (row) => <n-text type="info">{row.singleBaseQuantity ?? "/"}</n-text>
            },
            {
                title: "需求多基数量",
                key: "multiBaseQuantity",
                align: "center",
                render: (row) => <n-text type="info">{row.multiBaseQuantity ?? "/"}</n-text>
            },
            {
                title: "需求单重",
                key: "singleWeight",
                align: "center",
                render: (row) => <n-text type="info">{row.singleWeight ?? "/"}</n-text>
            },
            {
                title: "需求多基总重",
                key: "multiWeight",
                align: "center",
                render: (row) => <n-text type="info">{row.multiWeight ?? "/"}</n-text>
            },
            {
                title: "计划数",
                key: "planQuantity",
                align: "center",
                render: (row) => <n-text type="info">{row.planQuantity ?? "/"}</n-text>
            },
            {
                title: "计划下料完成数",
                key: "deliverFinishQuantity",
                align: "center",
                render: (row) => <n-text type="info">{row.finishQuantity ?? "/"}</n-text>
            },
            {
                title: "计划下料完成重量",
                key: "deliverFinishWeight",
                align: "center",
                render: (row) => <n-text type="info">{row.finishWeight ?? "/"}</n-text>
            },
            {
                title: "需求孔数",
                key: "holeQuantity",
                align: "center",
                render: (row) => <n-text type="info">{row.holeQuantity ?? "/"}</n-text>
            },
            {
                title: "完成孔数",
                key: "finishHoleQuantity",
                align: "center",
                render: (row) => <n-text type="info">{row.finishHoleQuantity ?? "/"}</n-text>
            },
            {
                title: "零件状态",
                key: "finishStatus",
                align: "center",
                render: (row) => {
                    if (row.finishStatus === 1) {
                        return <n-text type="error">待完成</n-text>;
                    } else if (row.finishStatus === 2) {
                        return <n-text type="success">已完成</n-text>;
                    } else if (row.finishStatus === 3) {
                        return <n-text type="info">进行中</n-text>;
                    } else {
                        return <n-text>/</n-text>;
                    }
                }
            },
            {
                title: "查看执行情况",
                key: "action",
                align: "center",
                width: 110,
                fixed: "right",
                render: (row) => {
                    return (
                        <TableActions
                            type="button"
                            buttonActions={[
                                {
                                    label: "点击查看",
                                    tertiary: true,
                                    type: "primary",
                                    onClick: () => showExecutionModal(row)
                                }
                            ]}
                        />
                    );
                }
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;

            const params: any = {
                current: tablePagination.page,
                size: tablePagination.pageSize,
                planLineId: props.configData.id,
                equipmentId: equipmentActive.value,
                ...searchForm.value
            };

            GET_IRON_PRODUCTION_PLAN_MATERIAL_PAGE_LIST(params).then((res) => {
                tableLoading.value = false;
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                } else {
                    tableData.value = [];
                    tablePagination.itemCount = 0;
                }
            });
        };

        watchEffect(async () => {
            if (show.value) {
                if (props.configData.id) {
                    getDetail();
                }
            }
        });

        return () => (
            <div>
                <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                    <n-card
                        title={`查看【${props.configData.lineName}${props.configData.director}】下料明细 - ${detailData.value?.productionPlan?.planName}`}
                        class="w-1200px"
                        closable
                        onClose={onClose}
                    >
                        <n-form label-placement="left" label-width="auto">
                            <n-grid cols={12} x-gap={16}>
                                <n-form-item-gi required span={4} label="计划名称">
                                    {detailData.value?.productionPlan?.planName ?? "/"}
                                </n-form-item-gi>{" "}
                                <n-form-item-gi required span={4} label="操作人">
                                    {detailData.value?.productionPlan?.operByName ?? "/"}
                                </n-form-item-gi>{" "}
                                <n-form-item-gi required span={4} label="操作时间">
                                    {detailData.value?.productionPlan?.operTime ?? "/"}
                                </n-form-item-gi>
                                <n-form-item-gi required span={4} label="生产计划状态">
                                    {detailData.value?.productionPlan?.planStatus === 1 ? (
                                        <n-text type="error">未开始</n-text>
                                    ) : detailData.value?.productionPlan?.planStatus === 2 ? (
                                        <n-text type="info">进行中</n-text>
                                    ) : detailData.value?.productionPlan?.planStatus === 3 ? (
                                        <n-text type="success">已完成</n-text>
                                    ) : detailData.value?.productionPlan?.planStatus === 4 ? (
                                        <n-text type="warning">已结束</n-text>
                                    ) : (
                                        <n-text>/</n-text>
                                    )}
                                </n-form-item-gi>
                                <n-form-item-gi required span={8} label="原始计划文件">
                                    <n-text type="info">
                                        {detailData.value?.productionPlan?.originalFile?.fileName ?? "/"}
                                    </n-text>
                                </n-form-item-gi>
                                <n-form-item-gi required span={4} label="下料产线名称">
                                    <n-text type="info">{props.configData.lineName ?? "/"}</n-text>
                                </n-form-item-gi>
                                <n-form-item-gi required span={8} label="负责人">
                                    <n-text type="info">{detailData.value?.director ?? "/"}</n-text>
                                </n-form-item-gi>
                                <n-form-item-gi required span={4} label="任务计划总重">
                                    <n-text type="info">
                                        {detailData.value?.productionPlan?.totalPlannedWeight ?? "0"}KG
                                    </n-text>
                                </n-form-item-gi>
                                <n-form-item-gi required span={4} label="已完成总重">
                                    <n-text type="info">
                                        {detailData.value?.productionPlan?.completedWeight ?? "0"}KG
                                    </n-text>
                                </n-form-item-gi>
                            </n-grid>
                        </n-form>
                        {detailData.value?.equipmentList?.length > 0 && (
                            <n-tabs
                                v-model:value={equipmentActive.value}
                                animated
                                class="flex-fixed-200"
                                type="bar"
                                onUpdate:value={(val: string) => (equipmentActive.value = val)}
                            >
                                {detailData.value.equipmentList.map((item: any) => (
                                    <n-tab-pane
                                        key={item.equipmentId}
                                        name={String(item.equipmentId)}
                                        tab={item.equipmentName}
                                    />
                                ))}
                            </n-tabs>
                        )}
                        <n-card class="mt-2">
                            <TableSearchbar
                                form={searchForm.value}
                                config={searchConfig.value}
                                options={searchOptions.value}
                                onSearch={onSearch}
                                buttonAlign="left"
                            />
                        </n-card>
                        <n-card class="mt">
                            <n-data-table
                                columns={tableColumns.value}
                                data={tableData.value}
                                loading={tableLoading.value}
                                pagination={tablePagination}
                                row-key={tableRowKey}
                                single-line={false}
                                bordered
                                remote
                                striped
                                scroll-x={2400}
                            />
                        </n-card>
                    </n-card>
                </n-modal>

                {/* 执行情况弹窗 */}
                <TowerScanEngineeringProcessesPartsExecution
                    v-model:show={executionShow.value}
                    configData={executionConfigData.value}
                    permissions={{
                        edit: false, // 生产模块禁用修改按钮
                        delete: false, // 生产模块禁用删除按钮
                        history: true // 保留查看修改记录功能
                    }}
                    onRefresh={() => {
                        getTableData();
                    }}
                />
            </div>
        );
    }
});
