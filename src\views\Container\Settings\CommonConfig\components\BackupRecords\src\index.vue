<template>
    <div class="w-100%">
        <n-space class="mb">
            <n-button type="primary" @click="openEditModal()">新增任务</n-button>
        </n-space>
        <n-card hoverable>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <BackupSettings v-model:show="editModal.show" :config-data="editModal.configData" />
    </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from "vue";
import type { DataTableColumns } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import { BackupSettings } from "../../../components";

interface RowProps {
    [key: string]: any;
}

onMounted(async () => {
    getSearchOptions();
    getTableData();
});

// 搜索项
const searchConfig = ref<TableSearchbarConfig>([]);

const searchOptions = ref<TableSearchbarOptions>({});

const searchForm = ref<TableSearchbarData>({});

const getSearchOptions = () => {};

// 数据列表
const { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("id");

const tableColumns = ref<DataTableColumns<RowProps>>([
    { title: "任务名称", key: "name", align: "center" },
    { title: "状态", key: "status", align: "center" },
    { title: "时间", key: "time", align: "center" }
]);

const tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
    }
});

const getTableData = () => {
    // GET_PRODUCTION_LINE_PAGE({}).then((res) => {
    //     tableData.value = res.data.data.records;
    //     tablePagination.itemCount = res.data.data.total;
    //     tableLoading.value = false;
    // });
    tableData.value = [
        { name: "数据库定时备份", status: "正常", time: "2024年12月13日 03:00:00" },
        { name: "数据库定时备份", status: "正常", time: "2024年12月12日 03:00:00" },
        { name: "数据库定时备份", status: "正常", time: "2024年12月11日 03:00:00" },
        { name: "数据库定时备份", status: "正常", time: "2024年12月10日 03:00:00" },
        { name: "数据库定时备份", status: "正常", time: "2024年12月09日 03:00:00" },
        { name: "数据库定时备份", status: "正常", time: "2024年12月08日 03:00:00" },
        { name: "数据库定时备份", status: "正常", time: "2024年12月07日 03:00:00" },
        { name: "数据库定时备份", status: "正常", time: "2024年12月06日 03:00:00" },
        { name: "数据库定时备份", status: "正常", time: "2024年12月05日 03:00:00" },
        { name: "数据库定时备份", status: "正常", time: "2024年12月04日 03:00:00" }
    ];
    tablePagination.itemCount = 126;
    tableLoading.value = false;
};

// 新增编辑
const editModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

const openEditModal = (row?: UnKnownObject) => {
    editModal.value.show = true;
    editModal.value.configData = row ?? {};
};
</script>
