<template>
    <div>
        <n-space class="mb">
            <n-button secondary type="primary" @click="openConfirmPaymentModal()">确认回款</n-button>
        </n-space>
        <n-table :single-line="false" class="text-center" v-if="(formData.paymentReturnApplyList || []).length">
            <thead>
                <tr>
                    <th>回款金额（元）</th>
                    <th>回款到账时间</th>
                    <th>回款确认人</th>
                    <th>回款类型</th>
                </tr>
            </thead>
            <tbody>
                <tr v-for="(item, index) in formData.paymentReturnApplyList || []" :key="index">
                    <td>{{ item.returnAmount }}</td>
                    <td>{{ item.returnTime }}</td>
                    <td>
                        <n-text type="info">{{ item.confirmUserName || "未知" }}</n-text>
                    </td>
                    <td>{{ getTypeLabel(item.returnType) }}</td>
                </tr>
            </tbody>
        </n-table>
        <n-result v-else class="py-50px" size="large" status="404" title="暂无回款记录" />
        <!--确认回款-->
        <ProjectConfirmPayment
            v-model:show="confirmPaymentModal.show"
            :config-data="confirmPaymentModal.configData"
            @refresh="onRefresh"
        />
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { ProjectConfirmPayment } from "@/views/Container/Application/Sale/Project/components";

interface FormDataProps {
    [key: string]: any;
}

let props = withDefaults(defineProps<{ formData: FormDataProps }>(), {});

let emits = defineEmits(["refresh"]);

let onRefresh = () => emits("refresh");

let getTypeLabel = (type: number) => {
    let dicts = [
        { label: "预付款", value: 1 },
        { label: "首付款", value: 2 },
        { label: "进度款", value: 3 },
        { label: "尾款", value: 4 },
        { label: "履约保证金", value: 5 },
        { label: "投标保证金", value: 6 },
        { label: "质量保证金", value: 7 }
    ];
    let dict = dicts.find((item) => item.value === type);
    return dict ? dict.label : "未知";
};

// 确认回款
let confirmPaymentModal = ref<{ show: boolean; configData: FormDataProps }>({ show: false, configData: {} });

let openConfirmPaymentModal = () => {
    confirmPaymentModal.value.show = true;
    confirmPaymentModal.value.configData = {
        ...props.formData,
        nextFormKey: "ConfirmPayment"
    };
};
</script>
