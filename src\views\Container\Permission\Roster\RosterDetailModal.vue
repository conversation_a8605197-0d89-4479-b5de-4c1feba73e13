<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card
                :title="`${formData.trueName}的员工档案`"
                class="w-860px"
                closable
                content-style="padding: 0"
                @close="closeModal"
            >
                <template #header-extra>
                    <n-space class="mr-20px">
                        <n-button
                            v-if="!editModal.show"
                            text
                            type="primary"
                            @click="openEditModal(formData.archivesId || '')"
                        >
                            <template #icon>
                                <dynamic-icon icon="EditOutlined" />
                            </template>
                            编辑档案
                        </n-button>
                        <n-button v-else text type="error" @click="editModal.show = false">
                            <template #icon>
                                <dynamic-icon icon="EditOutlined" />
                            </template>
                            取消编辑
                        </n-button>
                        <n-button
                            :type="String(formData.lockFlag) === '1' ? 'success' : 'error'"
                            text
                            @click="toggleLockFlag"
                        >
                            <template #icon>
                                <dynamic-icon
                                    :icon="
                                        String(formData.lockFlag) === '1'
                                            ? 'CheckCircleOutlined'
                                            : 'CloseCircleOutlined'
                                    "
                                />
                            </template>
                            {{ String(formData.lockFlag) === "1" ? "启用账号" : "停用账号" }}
                        </n-button>
                    </n-space>
                </template>
                <div class="h-80vh">
                    <n-scrollbar trigger="hover">
                        <div class="p-20px">
                            <div class="flex-center">
                                <div class="text-center">
                                    <n-avatar :size="100" round v-if="!formData.avatar">
                                        <span class="text-25px">
                                            {{
                                                formData.trueName?.substring(
                                                    formData.trueName?.length - 2,
                                                    formData.trueName?.length
                                                )
                                            }}
                                        </span>
                                    </n-avatar>
                                    <n-avatar :size="100" round v-else :src="spliceImageUrl(formData.avatar)" />
                                    <n-space class="flex-y-center mt-10px mb-15px" justify="center">
                                        <n-text class="text-20px text-bold">{{ formData.trueName }}</n-text>
                                        <n-tag v-if="formData.typeOfEmployee" type="success">
                                            {{ dictValueToLabel(formData.typeOfEmployee, "employee_type") }}
                                        </n-tag>
                                        <n-tag v-if="formData.post" type="primary">{{ formData.postName }}</n-tag>
                                    </n-space>
                                    <n-text class="color-[#999999]">入职时间：{{ formData.timeOfEntry || "暂无" }}</n-text>
                                </div>
                            </div>
                            <div v-if="!editModal.show" class="w-100% mt-50px">
                                <n-collapse :default-expanded-names="['基本信息']">
                                    <n-collapse-item name="基本信息" title="基本信息">
                                        <n-grid :cols="24" :x-gap="16" :y-gap="16">
                                            <n-grid-item :span="12" class="text-14px">
                                                姓名：{{ formData.trueName || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                昵称：{{ formData.nickname || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                职级：{{ formData.postName || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                公司：{{ formData.company || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                部门：{{ formData.deptName || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                入职时间：{{ formData.timeOfEntry || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                手机号：{{ formData.phone || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                备注：{{ formData.remark || "暂无" }}
                                            </n-grid-item>
                                        </n-grid>
                                    </n-collapse-item>
                                    <n-collapse-item name="个人信息" title="个人信息">
                                        <n-grid :cols="24" :x-gap="16" :y-gap="16">
                                            <n-grid-item :span="12" class="text-14px">
                                                性别：{{ formData.sex || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                出生年月：{{ formData.dateOfBirth || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                年龄：{{ formData.age || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                身份证号：{{ formData.idNumber || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                邮箱：{{ formData.email || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                个人简介：{{ formData.individualResume || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                籍贯：{{ formData.nativePlace || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                政治面貌：{{ formData.politicsStatus || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                婚姻状态：{{ formData.maritalStatus || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                户口类型：{{ formData.typeOfRegisteredPermanentResidence || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                健康状况：{{ formData.physicalCondition || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                体检时间：{{ formData.timeForPhysicalExamination || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                交通工具：{{ formData.vehicle || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                车牌号码：{{ formData.licenseNumber || "暂无" }}
                                            </n-grid-item>
                                        </n-grid>
                                    </n-collapse-item>
                                    <n-collapse-item name="工作信息" title="工作信息">
                                        <n-grid :cols="24" :x-gap="16" :y-gap="16">
                                            <n-grid-item :span="12" class="text-14px">
                                                职称：{{ formData.theTitleOfATechnicalPost || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                职业资格：{{ formData.professionalQualification || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                工龄：{{ formData.workingYears || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                原单位及岗位：{{ formData.previousEmployerAndPosition || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                参保状况：{{
                                                    String(formData.healthInsuranceStatus) === "1" ? "已参保" : "未参保"
                                                }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                参保单位：{{ formData.insuredEntity || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                有无公积金：{{
                                                    String(formData.whetherThereIsProvidentFund) === "1" ? "有" : "无"
                                                }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                公积金参保单位：{{ formData.providentFundParticipatingUnits || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                应聘方式：{{ formData.howToApply || "暂无" }}
                                            </n-grid-item>
                                        </n-grid>
                                    </n-collapse-item>
                                    <n-collapse-item name="学历信息" title="学历信息">
                                        <n-grid :cols="24" :x-gap="16" :y-gap="16">
                                            <n-grid-item :span="12" class="text-14px">
                                                学历：{{ formData.educationBackground || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                学位：{{ formData.degree || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                毕业院校：{{ formData.graduateInstitutions || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                所学专业：{{ formData.majorOfStudy || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                外语水平：{{ formData.foreignLanguageLevel || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                计算机水平：{{ formData.computerSkill || "暂无" }}
                                            </n-grid-item>
                                        </n-grid>
                                    </n-collapse-item>
                                    <n-collapse-item name="家庭信息" title="家庭信息">
                                        <n-grid :cols="24" :x-gap="16" :y-gap="16">
                                            <n-grid-item :span="12" class="text-14px">
                                                家庭地址：{{ formData.contactAddress || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                家庭情况：{{ formData.familyBackground || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                紧急联系人：{{ formData.emergencyContact || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                联系人关系：{{ formData.contactRelation || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                联系人电话：{{ formData.phoneNumberOfTheContact || "暂无" }}
                                            </n-grid-item>
                                        </n-grid>
                                    </n-collapse-item>
                                    <n-collapse-item name="合同信息" title="合同信息">
                                        <n-grid :cols="24" :x-gap="16" :y-gap="16">
                                            <n-grid-item :span="12" class="text-14px">
                                                工号：{{ formData.username || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                档案编码：{{ formData.codeOfFile || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                员工状态：
                                                {{
                                                    formData.statusOfEmployees
                                                        ? dictValueToLabel(
                                                              formData.statusOfEmployees,
                                                              "employee_status"
                                                          )
                                                        : "暂无"
                                                }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                员工类型：
                                                {{
                                                    formData.typeOfEmployee
                                                        ? dictValueToLabel(formData.typeOfEmployee, "employee_type")
                                                        : "暂无"
                                                }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                合同编号：{{ formData.contractNo || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                当前薪资标准：{{ formData.currentSalaryScale || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                职类：{{ formData.jobCategoryPostName || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                岗位类型：{{ formData.typeOfJobPostName || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                岗位名称：{{ formData.nameOfThePost || "暂无" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                薪资级别：{{ formData.rankPostName || "暂无" }}
                                            </n-grid-item>
                                        </n-grid>
                                    </n-collapse-item>
                                </n-collapse>
                            </div>
                            <RosterEditModal
                                v-else
                                :id="editModal.id"
                                v-model:show="editModal.show"
                                class="mt-50px"
                                @refresh="getDetail"
                            />
                        </div>
                    </n-scrollbar>
                </div>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import { useStoreUser } from "@/store";
import { DynamicIcon } from "@/components/DynamicIcon";
import RosterEditModal from "./RosterEditModal.vue";
import { GET_ARCHIVES_BY_ID, UPDATE_ARCHIVES_LOCK_FLAG } from "@/api/permission";
import type { RosterProps } from "types/business/roster";
import { useDicts, usePublic } from "@/hooks";

let storeUser = useStoreUser();

let props = defineProps({
    show: { type: Boolean, default: false },
    id: { type: [String, Number] as PropType<any> }
});

let emits = defineEmits(["update:show", "refresh"]);

// 字典操作
let { dictLibs, getDictLibs, dictValueToLabel } = useDicts();

let setDictLibs = async () => {
    let dictName = ["employee_status", "employee_type"];
    await getDictLibs(dictName);
};

// 获取详情
watch(
    () => ({ id: props.id, show: props.show }),
    async (newVal) => {
        await setDictLibs();
        if (newVal.show && newVal.id) getDetail();
    },
    { deep: true }
);

type FormDataProps = RosterProps;

let formData = ref<FormDataProps>({});

let getDetail = () => {
    GET_ARCHIVES_BY_ID({ id: props.id }).then((res) => {
        formData.value = res.data.data;
    });
};

// 关闭弹窗
let closeModal = () => {
    emits("update:show", false);
    editModal.value = { show: false, id: "" };
};

// 停用启用账号
let toggleLockFlag = () => {
    if (!formData.value.userId) {
        window.$message.warning("用户ID缺失，无法操作！");
        return false;
    }
    let lockFlag: string | number | null;
    lockFlag = String(formData.value.lockFlag) === "0" ? "1" : "0";
    UPDATE_ARCHIVES_LOCK_FLAG({
        archivesId: formData.value.archivesId,
        userId: formData.value.userId,
        lockFlag
    }).then(() => {
        getDetail();
        emits("refresh");
    });
};

// 头像展示
let { spliceImageUrl } = usePublic();

// 新增编辑
let editModal = ref<{ show: boolean; id: string | number }>({
    show: false,
    id: ""
});

let openEditModal = (id: string | number) => {
    editModal.value.show = true;
    editModal.value.id = id;
};
</script>
