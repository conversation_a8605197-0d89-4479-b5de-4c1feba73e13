<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card :title="id ? '编辑职位' : '新增职位'" class="w-600px" closable @close="closeModal">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-form-item label="职位名称" path="postName">
                        <n-input
                            v-model:value="formData.postName"
                            class="w-100%"
                            clearable
                            placeholder="请输入职位名称"
                        />
                    </n-form-item>
                    <n-form-item label="职位标识" path="postCode">
                        <n-input
                            v-model:value="formData.postCode"
                            class="w-100%"
                            clearable
                            placeholder="请输入职位标识"
                        />
                    </n-form-item>
                    <n-form-item label="职位组" path="postGroup">
                        <n-select
                            v-model:value="formData.postGroup"
                            :options="dictLibs.post_group"
                            placeholder="请选择职位组"
                        />
                    </n-form-item>
                    <n-form-item label="职位描述" path="remark">
                        <n-input
                            v-model:value="formData.remark"
                            :autosize="{ minRows: 1 }"
                            class="w-100%"
                            clearable
                            placeholder="请输入职位描述"
                            type="textarea"
                        />
                    </n-form-item>
                    <n-form-item label="职位排序" path="postSort">
                        <n-input-number v-model:value="formData.postSort" class="w-100%" />
                    </n-form-item>
                    <n-form-item>
                        <n-space>
                            <n-button type="primary" @click="onSubmit">提交</n-button>
                            <n-button @click="closeModal">取消</n-button>
                        </n-space>
                    </n-form-item>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { ADD_POST, GET_POST_BY_ID, UPDATE_POST } from "@/api/permission";
import { useDicts } from "@/hooks";

let props = defineProps({
    show: { type: Boolean, default: false },
    id: { type: [String, Number] as PropType<any> }
});

let emits = defineEmits(["update:show", "refresh"]);

onMounted(async () => {
    await setDictLibs();
});

// 字典操作
let { dictLibs, getDictLibs, dictValueToLabel } = useDicts();

let setDictLibs = async () => {
    let dictName = ["post_group"];
    await getDictLibs(dictName);
};

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {
    postName: {
        required: true,
        message: "请输入职位名称",
        trigger: ["input", "blur"]
    },
    postCode: {
        required: false,
        message: "请输入职位标识",
        trigger: ["input", "blur"]
    },
    postGroup: {
        required: true,
        message: "请选择职位组",
        trigger: ["blur", "change"]
    },
    remark: {
        required: false,
        message: "请输入职位描述",
        trigger: ["input", "blur"]
    },
    postSort: {
        required: true,
        type: "number",
        message: "请输入职位排序",
        trigger: ["input", "blur"]
    }
};

// 表单数据
interface FormDataProps<T = string | null> {
    postName: T;
    postCode: T;
    remark: T;
    postSort: number;
    postGroup: T | number;
}

let initFormData: FormDataProps = { postName: null, postCode: null, postGroup: null, remark: null, postSort: 0 };

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 编辑时获取详情
watch(
    () => ({ id: props.id, show: props.show }),
    (newVal) => {
        if (newVal.show && newVal.id) getDetail();
    },
    { deep: true }
);

// 获取详情
let getDetail = () => {
    GET_POST_BY_ID({ id: props.id }).then((res) => {
        let rowItem: FormDataProps = res.data.data;
        formData.value = {
            postName: rowItem.postName,
            postCode: rowItem.postCode,
            postGroup: rowItem.postGroup,
            remark: rowItem.remark,
            postSort: rowItem.postSort
        };
    });
};

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    emits("update:show", false);
};

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    if (!props.id) {
        ADD_POST({
            ...formData.value
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("新增成功");
                closeModal();
                emits("refresh");
            }
        });
    } else {
        UPDATE_POST({
            postId: props.id,
            ...formData.value
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("编辑成功");
                closeModal();
                emits("refresh");
            }
        });
    }
};
</script>
