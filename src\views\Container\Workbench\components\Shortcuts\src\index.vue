<template>
    <div class="flex items-center flex-wrap p-10px">
        <n-grid x-gap="10" y-gap="10">
            <n-grid-item n-element v-for="item in shortcutList" :span="8"  @click="routerTo(item.path)">
              <n-el>
                <n-card class="text-center cursor-pointer quick-operation-block" >
                    <dynamic-icon :icon="item.icon" size="30" />
                    <span class="block mt-2">{{ item.title }}</span>
                </n-card>
              </n-el>
            </n-grid-item>
        </n-grid>
    </div>
</template>

<script lang="ts" setup>
import { NCard, NGrid, NGridItem } from "naive-ui";
import { DynamicIcon } from "@/components/DynamicIcon";
import { usePublic } from "@/hooks";

let { $router } = usePublic();

let shortcutList = [
    { title: "员工通讯录", icon: "UserOutlined", path: "/staff/list" },
    { title: "OA流程", icon: "DeploymentUnitOutlined", path: "/process/lib" },
    { title: "待添加", icon: "SyncOutlined", path: "" },
    { title: "待添加", icon: "SyncOutlined", path: "" },
    { title: "待添加", icon: "SyncOutlined", path: "" },
    { title: "待添加", icon: "SyncOutlined", path: "" }
];

let routerTo = (path: string) => {
    if (!path) return false;
    $router.push(path);
};
</script>
<style lang="scss" scoped>
.quick-operation-block:hover{
  background: var(--primary-color);
  color:#fff;
}
</style>