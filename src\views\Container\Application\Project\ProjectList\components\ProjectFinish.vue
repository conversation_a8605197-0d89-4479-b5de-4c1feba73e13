<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-600px" closable title="结束项目" @close="closeModal">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-form-item label="通知人员" path="sendUsers">
                        <UserSelector
                            v-model:value="formData.sendUsers"
                            class="w-100%"
                            clearable
                            :multiple="false"
                            key-name="username"
                            placeholder="请选择通知人员"
                        />
                    </n-form-item>
                    <n-form-item>
                        <n-space>
                            <n-button type="primary" @click="onSubmit">提交</n-button>
                            <n-button @click="closeModal">取消</n-button>
                        </n-space>
                    </n-form-item>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { UserSelector } from "@/components/UserSelector";
import { SET_BIND_STATUS, SUBMIT_PROJECT_FINISH_FORM } from "@/api/application/project";

let props = defineProps({
    show: { type: Boolean, default: false },
    configData: { type: Object as PropType<any> }
});

let emits = defineEmits(["update:show", "refresh"]);

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {
    sendUsers: [{ required: true, message: "请选择通知人员" }]
};

// 表单数据
interface FormDataProps<T = string | null> {
    sendUsers: T;
}

let initFormData: FormDataProps = {
    sendUsers: null
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    emits("update:show", false);
};

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    SUBMIT_PROJECT_FINISH_FORM({
        ...formData.value,
        projectId: props.configData.projectId,
        nodeKey: props.configData.nextNodeKey
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("操作成功");
            closeModal();
            emits("refresh");
        }
    });
};
</script>
