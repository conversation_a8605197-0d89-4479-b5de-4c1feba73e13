<template>
    <div class="flex">
        <n-card class="flex-fixed-200">
            <div class="flex-y-center mb">
                <n-input v-model:value="treePattern" clearable placeholder="搜索" />
            </div>
            <n-tree
                v-model:selected-keys="treeSelectKeys"
                :cancelable="false"
                :data="treeData"
                :pattern="treePattern"
                :show-irrelevant-nodes="false"
                block-line
                children-field="childrenList"
                default-expand-all
                key-field="id"
                label-field="categoryName"
                selectable
                @update:selected-keys="selectTreeNode"
            />
        </n-card>
        <div class="flex-1 ml">
            <n-card>
                <table-searchbar
                    auto-search
                    v-model:form="searchForm"
                    :config="searchConfig"
                    :options="searchOptions"
                    @search="onSearch"
                    @componentClick="onComponentClick"
                >
                    <template #buttons>
                        <n-button v-if="wageType === 2" type="success" @click="onCopyNonQuotaCost">
                            复制非定额生产费用
                        </n-button>
                    </template>
                </table-searchbar>
            </n-card>
            <n-card class="mt">
                <div class="flex-y-center mb">
                    <n-space>
                        <n-button secondary type="success" @click="saveTableData">保存填写</n-button>
                        <n-button v-if="wageType === 1" secondary type="warning" @click="onGenerateCost">
                            计算实际产量合计工资
                        </n-button>
                    </n-space>
                    <div v-if="wageType === 1" class="h-100% flex-y-center ml-a">
                        <div>
                            实际产量定额工资全部合计：
                            <n-text type="primary">{{ wagesTotal.reference }}元</n-text>
                        </div>
                        <div class="ml">
                            实际产量实际工资全部合计：
                            <n-text type="primary">{{ wagesTotal.actual }}元</n-text>
                        </div>
                        <div class="ml">
                            差值：
                            <n-text :type="wagesTotal.difference > 0 ? 'success' : 'error'">
                                {{ wagesTotal.difference }}元
                            </n-text>
                        </div>
                    </div>
                </div>
                <n-data-table
                    v-if="wageType === 1"
                    :columns="tableColumnsQuota"
                    :data="tableData"
                    :loading="tableLoading"
                    :row-key="tableRowKey"
                    :single-line="false"
                    bordered
                    remote
                    striped
                    @update:checked-row-keys="changeTableSelection"
                />
                <n-data-table
                    v-else-if="wageType === 2"
                    :columns="tableColumnsNonQuota"
                    :data="tableData"
                    :loading="tableLoading"
                    :row-key="tableRowKey"
                    :single-line="false"
                    bordered
                    remote
                    striped
                    @update:checked-row-keys="changeTableSelection"
                />
            </n-card>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, ref } from "vue";
import {
    COPY_DAILY_PLAN,
    COPY_NON_QUOTA_PRODUCT_COST,
    GENERATE_PRODUCT_COST,
    GET_CONFIG_WORK_GROUP_LIST,
    GET_PLAN_AMOUNT_LIST,
    GET_PRODUCT_NONQUOTA_COST_LIST,
    GET_PRODUCT_QUOTA_COST_LIST,
    GET_WAGE_CATEGORY_TREE_LIST,
    SAVE_PRODUCT_NONQUOTA_COST_LIST,
    SAVE_PRODUCT_QUOTA_COST_LIST
} from "@/api/application/reporting";
import type { DataTableColumns } from "naive-ui";
import { NButton, NDatePicker, NInput, NTooltip } from "naive-ui";
import { useCommonTable, useReportingTree } from "@/hooks";
import {
    TableSearchbar,
    TableSearchbarConfig,
    TableSearchbarData,
    TableSearchbarOptions
} from "@/components/TableSearchbar";
import dayjs from "dayjs";
import { useStoreReportingSearch } from "@/store";
import { useThrottleFn } from "@vueuse/core";

let storeReportingSearch = useStoreReportingSearch();

interface RowProps {
    [key: string]: any;
}

onMounted(async () => {
    await getTreeData();
    await getWorkGroupIdOptions();
    getTableData();
});

// 工资类型（1：定额，2：非定额）
let wageType = ref<Nullable<number>>(null);

// 树相关操作
let { treeData, treePattern, treeSelectKeys, treeAddDisabled, findLastLevel } = useReportingTree();

let getTreeData = async () => {
    await GET_WAGE_CATEGORY_TREE_LIST({}).then((res) => {
        if (res.data.code === 0) {
            treeData.value = treeAddDisabled(res.data.data ?? []);
            treeSelectKeys.value = [findLastLevel(treeData.value)[0].id];
            wageType.value = findLastLevel(treeData.value)[0].wageType;
        }
    });
};

let selectTreeNode = (keys?: (string | number)[], option?: any[]) => {
    treeSelectKeys.value = keys ?? [];
    wageType.value = option?.[0].wageType;
    onSearch();
};

// 搜索相关操作
let getWorkGroupIdOptions = async () => {
    await GET_CONFIG_WORK_GROUP_LIST({}).then((res) => {
        searchOptions.value.workGroupId = (res.data.data ?? []).map((item: any) => ({
            label: item.companyName + "-" + item.workshopName + "-" + item.groupName,
            value: item.id
        }));
    });
    searchForm.value.workGroupId = searchOptions.value.workGroupId[0].value;
    searchForm.value.planDate = dayjs().format("YYYY-MM-DD");
    if (storeReportingSearch.getSearchForm.workGroupId) {
        searchForm.value.workGroupId = storeReportingSearch.getSearchForm.workGroupId;
    }
    if (storeReportingSearch.getSearchForm.planDate) {
        searchForm.value.planDate = storeReportingSearch.getSearchForm.planDate;
    }
};

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { label: "班组", type: "select", prop: "workGroupId", span: 2 },
    { label: "日期筛选", type: "date", dateFormat: "yyyy-MM-dd", prop: "planDate" }
]);

let searchOptions = ref<TableSearchbarOptions>({ workGroupId: [] });

let searchForm = ref<TableSearchbarData>({ workGroupId: null, planDate: null });

let onSearch = () => {
    if (!searchForm.value.workGroupId) return window.$message.error("请先选择班组！");
    if (!searchForm.value.planDate) return window.$message.error("请先选择日期！");
    storeReportingSearch.setSearchForm({
        workGroupId: searchForm.value.workGroupId ?? null,
        planDate: searchForm.value.planDate ?? null
    });
    getTableData();
};

// 搜索栏自动保存逻辑
let autoSave = useThrottleFn(async () => {
    if (wageType.value === 1) {
        SAVE_PRODUCT_QUOTA_COST_LIST({
            ...searchForm.value,
            dailyQuotaCostList: tableData.value
        }).then((res) => {
            if (res.data.code === 0) window.$message.success("自动保存成功");
        });
    } else if (wageType.value === 2) {
        SAVE_PRODUCT_NONQUOTA_COST_LIST({
            ...searchForm.value,
            dailyNonQuotaCostList: tableData.value
        }).then((res) => {
            if (res.data.code === 0) window.$message.success("自动保存成功");
        });
    }
}, 1000);

let onComponentClick = async (val: TableSearchbarData) => {
    // await autoSave();
};

// 数据列表
let { tableRowKey, tableData, tableLoading, changeTableSelection } = useCommonTable<RowProps>("id");

let tableColumnsQuota = ref<DataTableColumns<RowProps>>([
    { type: "selection" },
    { title: "名称", align: "center", key: "poleName" },
    { title: "规格型号", align: "center", key: "productModel" },
    { title: "定额单价（元）", align: "center", key: "quotaWage" },
    { title: "实际单价（元）", align: "center", key: "planCost" },
    { title: "实际完成量", align: "center", key: "productAmount" },
    { title: "实际产量定额合计工资（元）", align: "center", key: "quotaCost" },
    {
        title: "实际产量实际合计工资（元）",
        align: "center",
        key: "productCost",
        render: (row) => {
            return h(
                NTooltip,
                {},
                {
                    trigger: () => {
                        return h(NInput, { value: row.productCost, onUpdateValue: (v) => (row.productCost = v) });
                    },
                    default: () => {
                        return row.productCost;
                    }
                }
            );
        }
    }
]);

let tableColumnsNonQuota = ref<DataTableColumns<RowProps>>([
    { type: "selection" },
    { title: "每日定额（元）", align: "center", key: "quotaWage" },
    { title: "计划工资（元）", align: "center", key: "planCost" },
    {
        title: "实际工资（元）",
        align: "center",
        key: "productCost",
        render: (row) => {
            return h(
                NTooltip,
                {},
                {
                    trigger: () => {
                        return h(NInput, { value: row.productCost, onUpdateValue: (v) => (row.productCost = v) });
                    },
                    default: () => {
                        return row.productCost;
                    }
                }
            );
        }
    }
]);

// 参考工资合计、实际工资合计
let wagesTotal = ref<Record<"reference" | "actual" | "difference", number>>({
    reference: 0,
    actual: 0,
    difference: 0
});

let getTableData = () => {
    tableLoading.value = true;
    if (wageType.value === 1) {
        GET_PRODUCT_QUOTA_COST_LIST({
            ...searchForm.value,
            wageCategoryId: treeSelectKeys.value[0]
        }).then((res) => {
            wagesTotal.value = { reference: 0, actual: 0, difference: 0 };
            tableData.value = res.data.data.map((item: any) => {
                wagesTotal.value.reference += Number(item.quotaCost) ?? 0;
                wagesTotal.value.actual += Number(item.productCost) ?? 0;
                return item;
            });
            // 2023年9月14日修复计算数字精度问题
            wagesTotal.value = {
                reference: Number(wagesTotal.value.reference.toFixed(2)),
                actual: Number(wagesTotal.value.actual.toFixed(2)),
                difference: Number(wagesTotal.value.reference.toFixed(2)) - Number(wagesTotal.value.actual.toFixed(2))
            };
            tableLoading.value = false;
        });
    } else {
        GET_PRODUCT_NONQUOTA_COST_LIST({
            ...searchForm.value,
            wageCategoryId: treeSelectKeys.value[0]
        }).then((res) => {
            tableData.value = res.data.data;
            tableLoading.value = false;
        });
    }
};

// 保存全部
let saveTableData = () => {
    if (wageType.value === 1) {
        SAVE_PRODUCT_QUOTA_COST_LIST({
            ...searchForm.value,
            dailyQuotaCostList: tableData.value
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("保存成功");
                onSearch();
            }
        });
    } else if (wageType.value === 2) {
        SAVE_PRODUCT_NONQUOTA_COST_LIST({
            ...searchForm.value,
            dailyNonQuotaCostList: tableData.value
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("保存成功");
                onSearch();
            }
        });
    }
};

// 生成实际变量工资
let onGenerateCost = () => {
    window.$dialog.warning({
        title: "提醒",
        content: "请注意！本次操作将会覆盖当前填报的实际产量实际工资合计，请谨慎操作！！",
        positiveText: "确定",
        negativeText: "取消",
        onPositiveClick: () => {
            GENERATE_PRODUCT_COST({
                ...searchForm.value
            }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("操作成功");
                    onSearch();
                } else {
                    window.$message.error(res.data.msg);
                }
            });
        }
    });
};

// 复制非定额生产费用
let copyDate = ref<any>(null);

let onCopyNonQuotaCost = () => {
    window.$dialog.warning({
        title: "选择日期",
        positiveText: "确认",
        negativeText: "取消",
        content: () => {
            return h("div", { class: "py-10px" }, [
                h("div", { class: "text-16px mb" }, "确认后将复制目标当天的非定额生产费用。"),
                h(NDatePicker, {
                    formattedValue: copyDate.value,
                    class: "w-100%",
                    clearable: true,
                    placeholder: "请选择日期",
                    type: "date",
                    valueFormat: "yyyy-MM-dd",
                    onUpdateFormattedValue: (v: any) => {
                        copyDate.value = v;
                    }
                })
            ]);
        },
        onPositiveClick: async () => {
            let planId = null;
            await GET_PLAN_AMOUNT_LIST({ ...searchForm.value, planDate: copyDate.value }).then((res) => {
                if (res.data.code === 0) planId = res.data.data.id ?? null;
            });
            if (!planId) return window.$message.error("目标日期没有计划！");
            COPY_NON_QUOTA_PRODUCT_COST({
                fromDailyPlanId: planId,
                toPlanDate: searchForm.value.planDate,
                wageCategoryId: treeSelectKeys.value[0]
            }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("复制成功");
                    onSearch();
                }
            });
        },
        onNegativeClick: () => {
            copyDate.value = null;
        }
    });
};
</script>
