import { defineComponent, onMounted, ref } from "vue";
import { DataTableColumns, PaginationProps } from "naive-ui";
import { useCommonTable } from "@/hooks";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import {
    GET_CRUSH_SCHEDULE_PAGE_LIST,
    POST_CRUSH_SCHEDULE_END_SCHEDULE,
    POST_CRUSH_SCHEDULE_FINISH_SCHEDULE,
    POST_CRUSH_SCHEDULE_PUSH_SCHEDULE
} from "@/api/application/plasticMes";
import { DynamicIcon } from "@/components/DynamicIcon";
import { TableActions } from "@/components/TableActions";
import PlasticMesCrushProductionScheduleEdit from "./PlasticMesCrushProductionScheduleEdit";
import PlasticMesCrushProductionScheduleDetail from "./PlasticMesCrushProductionScheduleDetail";

export default defineComponent({
    name: "PlasticMesCrushProductionSchedule",
    setup() {
        // 搜索项
        const searchConfig = ref<TableSearchbarConfig>([]);
        const searchOptions = ref<TableSearchbarOptions>({});
        const searchForm = ref<TableSearchbarData>({});

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            { title: "排产单号", key: "id", align: "center", width: 190 },
            {
                title: "破碎类型",
                key: "ptTypeName",
                align: "center",
                render: (row) => <n-text type="info">{row.ptTypeName || "/"}</n-text>
            },
            {
                title: "生产状态",
                key: "scheduleStatus",
                align: "center",
                render: (row) => {
                    if (row.scheduleStatus === 0) {
                        return <n-text type="warning">未开始</n-text>;
                    } else if (row.scheduleStatus === 1) {
                        return <n-text type="info">进行中</n-text>;
                    } else if (row.scheduleStatus === 2) {
                        return <n-text type="warning">待确认完成</n-text>;
                    } else if (row.scheduleStatus === 3) {
                        return <n-text type="success">已完成</n-text>;
                    } else if (row.scheduleStatus === 5) {
                        return <n-text type="error">已结束</n-text>;
                    } else {
                        return "/";
                    }
                }
            },
            {
                title: "备注",
                key: "remark",
                align: "center",
                render: (row) => row.remark ?? "/"
            },
            {
                title: "操作",
                key: "actions",
                align: "center",
                fixed: "right",
                width: 380,
                render: (row) => (
                    <TableActions
                        type="button"
                        buttonActions={[
                            {
                                label: row.scheduleStatus === 0 ? "编辑排产" : "查看详情",
                                type: "default",
                                tertiary: true,
                                onClick: () => {
                                    if (row.scheduleStatus === 0) {
                                        openScheduleModal(row);
                                    } else {
                                        openDetailModal(row);
                                    }
                                }
                            },
                            {
                                disabled: () => row.scheduleStatus !== 0,
                                label: "推送排产",
                                type: "success",
                                tertiary: true,
                                onClick: () => onBatchPush(row.id)
                            },
                            {
                                disabled: () => row.scheduleStatus !== 2,
                                label: "完成排产",
                                type: "warning",
                                tertiary: true,
                                onClick: () => onBatchComplete(row.id)
                            },
                            {
                                disabled: () => row.scheduleStatus === 3 || row.scheduleStatus === 5,
                                label: "结束排产",
                                type: "error",
                                tertiary: true,
                                onClick: () => onBatchEnd(row.id)
                            }
                        ]}
                    />
                )
            }
        ]);

        const tablePagination = ref<PaginationProps>({
            page: 1,
            pageSize: 10,
            itemCount: 0,
            pageSizes: [10, 50, 100],
            showSizePicker: true,
            showQuickJumper: true,
            onChange: (page: number) => {
                tablePagination.value.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.value.pageSize = pageSize;
                tablePagination.value.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_CRUSH_SCHEDULE_PAGE_LIST({
                current: tablePagination.value.page,
                size: tablePagination.value.pageSize,
                ...searchForm.value
            }).then((res) => {
                tableData.value = res.data.data.records || [];
                tablePagination.value.itemCount = res.data.data.total;
                tableLoading.value = false;
            });
        };

        const onSearch = () => {
            getTableData();
        };

        onMounted(() => {
            getTableData();
        });

        // 查看详情
        const detailModal = ref<{ show: boolean; configData: UnKnownObject }>({
            show: false,
            configData: {}
        });

        const openDetailModal = (row: RowProps) => {
            detailModal.value = { show: true, configData: row };
        };

        // 排产
        const scheduleModal = ref<{ show: boolean; configData: UnKnownObject }>({
            show: false,
            configData: {}
        });

        const openScheduleModal = (row?: RowProps) => {
            scheduleModal.value = { show: true, configData: row ?? {} };
        };

        // 批量推送排产单
        const onBatchPush = (id?: string | number) => {
            const ids = id ? [id] : tableSelection.value;
            if (ids.length <= 0) return window.$message.warning("请选择需要推送的排产单");

            window.$dialog.warning({
                title: "温馨提示",
                content: "是否推送排产单？推送之后该排产单将在计划开始时间被工人接收！",
                positiveText: "确定推送",
                negativeText: "取消",
                onPositiveClick: () => {
                    POST_CRUSH_SCHEDULE_PUSH_SCHEDULE({ ids: ids.join(",") }).then((res) => {
                        if (res.data.code === 0) {
                            window.$message.success("推送成功");
                            getTableData();
                        } else {
                            window.$message.error(res.data.msg ?? "推送失败");
                        }
                    });
                }
            });
        };

        // 批量完成排产
        const onBatchComplete = (id?: string | number) => {
            const ids = id ? [id] : tableSelection.value;
            if (ids.length <= 0) return window.$message.warning("请选择需要完成的排产单");

            window.$dialog.warning({
                title: "温馨提示",
                content: "是否确认完成该排产单",
                positiveText: "确定完成",
                negativeText: "取消",
                onPositiveClick: () => {
                    POST_CRUSH_SCHEDULE_FINISH_SCHEDULE({ ids: ids.join(",") }).then((res) => {
                        if (res.data.code === 0) {
                            window.$message.success("完成成功");
                            getTableData();
                        } else {
                            window.$message.error(res.data.msg ?? "完成失败");
                        }
                    });
                }
            });
        };

        // 批量结束排产
        const onBatchEnd = (id?: string | number) => {
            const ids = id ? [id] : tableSelection.value;
            if (ids.length <= 0) return window.$message.warning("请选择需要结束的排产单");

            window.$dialog.warning({
                title: "温馨提示",
                content: "确认结束后，生产中的排产单将强制完成并告知工人填写当前数据！",
                positiveText: "确定关闭",
                negativeText: "取消",
                onPositiveClick: () => {
                    POST_CRUSH_SCHEDULE_END_SCHEDULE({ ids: ids.join(","), endReason: "手动结束" }).then((res) => {
                        if (res.data.code === 0) {
                            window.$message.success("结束成功");
                            getTableData();
                        } else {
                            window.$message.error(res.data.msg ?? "结束失败");
                        }
                    });
                }
            });
        };

        return () => (
            <div>
                <n-card>
                    <TableSearchbar
                        form={searchForm.value}
                        config={searchConfig.value}
                        options={searchOptions.value}
                        onSearch={onSearch}
                    />
                </n-card>
                <n-card class="mt" hoverable>
                    <n-space>
                        <n-button type="primary" onClick={() => openScheduleModal()}>
                            {{
                                default: () => <span>新建排产单</span>,
                                icon: () => <DynamicIcon icon="PlusCircleOutlined" />
                            }}
                        </n-button>
                        <n-button type="success" onClick={() => onBatchPush()}>
                            批量推送排产单
                        </n-button>
                    </n-space>
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        pagination={tablePagination.value}
                        row-key={tableRowKey}
                        single-line={false}
                        bordered
                        class="mt"
                        remote
                        striped
                        onUpdate:checked-row-keys={changeTableSelection}
                    />
                    <PlasticMesCrushProductionScheduleEdit
                        v-model:show={scheduleModal.value.show}
                        config-data={scheduleModal.value.configData}
                        onRefresh={getTableData}
                    />
                    <PlasticMesCrushProductionScheduleDetail
                        v-model:show={detailModal.value.show}
                        config-data={detailModal.value.configData}
                    />
                </n-card>
            </div>
        );
    }
});
