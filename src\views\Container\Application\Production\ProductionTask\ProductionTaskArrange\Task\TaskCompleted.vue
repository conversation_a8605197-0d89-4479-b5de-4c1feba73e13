<template>
    <div>
        <n-data-table
            :columns="tableColumns"
            :data="tableData"
            :loading="tableLoading"
            :pagination="tablePagination"
            :row-key="tableRowKey"
            :single-line="false"
            bordered
            remote
            striped
            @update:checked-row-keys="changeTableSelection"
        />
        <!--需求反馈详情-->
        <Requirements v-model:show="requirementsModal.show" :config-data="requirementsModal.configData" />
        <!--订单需求详情-->
        <OrderDetail v-model:show="orderDetailModal.show" :config-data="orderDetailModal.configData" />
    </div>
</template>

<script lang="ts" setup>
import { useCommonTable } from "@/hooks";
import { h, onMounted, reactive, ref } from "vue";
import { DataTableColumns, NButton } from "naive-ui";
import { GET_PRODUCTION_ARRANGE_COMPLETED } from "@/api/application/production";
import { OrderDetail, Requirements } from "../Modal";
import { TableActions } from "@/components/TableActions";

let props = withDefaults(
    defineProps<{
        searchForm?: UnKnownObject;
    }>(),
    {}
);

interface RowProps {
    poId: string | number;
    pomNumber: string | number;
    projectName: string;
    deliveryDate: string;
    orderCheckBy: string;
    orderCheckTime: string;
}

onMounted(() => {
    getTableData();
});

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("poId");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "订单号",
        key: "pomNumber",
        align: "center",
        render: (row) => {
            return row.pomNumber || "/";
        }
    },
    {
        title: "项目名称",
        key: "projectName",
        align: "center"
    },
    {
        title: "需求详情",
        key: "poId",
        align: "center",
        render(row) {
            return h(
                NButton,
                {
                    type: "primary",
                    text: true,
                    onClick: () => openRequirementsModal(row)
                },
                () => "点击查看"
            );
        }
    },
    {
        title: "交货日期",
        key: "deliveryDate",
        align: "center"
    },
    {
        title: "确认人",
        key: "orderCheckBy",
        align: "center"
    },
    {
        title: "确认时间",
        key: "orderCheckTime",
        align: "center"
    },
    {
        title: "生产明细单",
        key: "poId",
        align: "center",
        width: 120,
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "点击查看",
                        type: "primary",
                        tertiary: true,
                        onClick: () => openOrderDetailModal(row)
                    }
                ]
            });
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_PRODUCTION_ARRANGE_COMPLETED({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        ...props.searchForm
    }).then((res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data.records;
            tablePagination.itemCount = res.data.data.total;
            tableLoading.value = false;
            console.log(111, tableData.value);
        }
    });
};

// 需求反馈详情
let requirementsModal = ref<{ show: boolean; configData: Record<string, any> }>({
    show: false,
    configData: {}
});

let openRequirementsModal = (row: RowProps) => {
    requirementsModal.value = {
        show: true,
        configData: row
    };
};

// 订单详情
let orderDetailModal = ref<{ show: boolean; configData: Record<string, any> }>({
    show: false,
    configData: {}
});

let openOrderDetailModal = (row: RowProps) => {
    orderDetailModal.value = {
        show: true,
        configData: row
    };
};
</script>
