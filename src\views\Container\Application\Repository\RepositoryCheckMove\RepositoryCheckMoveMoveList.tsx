import { defineComponent, h, onMounted, reactive, ref } from "vue";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import { GET_REPOSITORY_INVENTORY_TASK_LIST, GET_REPOSITORY_MOVE_TASK_LIST } from "@/api/application/repository";
import { TableActions } from "@/components/TableActions";
import RepositoryCheckMoveMoveEdit from "@/views/Container/Application/Repository/RepositoryCheckMove/RepositoryCheckMoveMoveEdit";

export default defineComponent({
    name: "RepositoryCheckMoveMoveList",
    setup(props, { expose }) {
        // 搜索项
        const searchConfig = ref<TableSearchbarConfig>([{ label: "移库状态", prop: "moveState", type: "select" }]);
        const searchOptions = ref<TableSearchbarOptions>({
            moveState: [
                { label: "进行中", value: 1 },
                { label: "已完成", value: 2 }
            ]
        });
        const getSearchOptions = async () => {};
        const searchForm = ref<TableSearchbarData>({
            moveState: null
        });
        const onSearch = () => {
            tablePagination.page = 1;
            tablePagination.pageSize = 10;
            getTableData();
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            { title: "移库任务单", key: "id", align: "center" },
            { title: "转出仓库", key: "fromStoreroomName", align: "center" },
            { title: "转入仓库", key: "toStoreroomName", align: "center" },
            {
                title: "移库类型",
                key: "moveType",
                align: "center",
                render: (row) => (row.moveType === 1 ? "全仓转出" : "部分转出")
            },
            { title: "执行人", key: "moveByName", align: "center" },
            { title: "计划完成时间", key: "planTime", align: "center" },
            { title: "实际完成时间", key: "finishTime", align: "center" },
            { title: "备注", key: "remark", align: "center" },
            {
                title: "操作",
                key: "actions",
                align: "center",
                width: 120,
                render(row) {
                    return h(TableActions, {
                        type: "button",
                        buttonActions: [
                            {
                                label: "编辑移库任务",
                                tertiary: true,
                                onClick: () => openEditModal(row)
                            }
                        ]
                    });
                }
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_REPOSITORY_MOVE_TASK_LIST({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                ...searchForm.value
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        // 编辑弹窗
        const editModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const openEditModal = (row: RowProps) => {
            editModal.value.show = true;
            editModal.value.configData = row;
        };

        onMounted(async () => {
            await getSearchOptions();
            getTableData();
        });

        expose({
            refresh: getTableData
        });

        return () => (
            <div class="repository-page">
                <n-card>
                    <TableSearchbar
                        form={searchForm.value}
                        config={searchConfig.value}
                        options={searchOptions.value}
                        onSearch={onSearch}
                    />
                </n-card>
                <n-card class="mt">
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        pagination={tablePagination}
                        row-key={tableRowKey}
                        single-line={false}
                        bordered
                        remote
                        striped
                        onUpdate:checked-row-keys={changeTableSelection}
                    />
                </n-card>
                <RepositoryCheckMoveMoveEdit
                    v-model:show={editModal.value.show}
                    config-data={editModal.value.configData}
                    onRefresh={getTableData}
                />
            </div>
        );
    }
});
