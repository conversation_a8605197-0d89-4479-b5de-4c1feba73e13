import { computed, defineComponent, ref, watchEffect } from "vue";
import { type FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { useDicts } from "@/hooks";
import { UserSelector } from "@/components/UserSelector";
import { useStoreUser } from "@/store";
import dayjs from "dayjs";
import {
    ADD_SUPPLY_CATEGORY_MANAGEMENT,
    EDIT_SUPPLY_CATEGORY_MANAGEMENT,
    GET_SUPPLY_CATEGORY_MANAGEMENT_DETAIL
} from "@/api/application/purchase";

export default defineComponent({
    name: "PurchaseSupplierCategoryEdit",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        const storeUser = useStoreUser();

        // 字典操作
        const { dictLibs, getDictLibs } = useDicts();

        const setDictLibs = async () => {
            let dictName = ["common_units"];
            await getDictLibs(dictName);
        };

        interface FormDataProps {
            [key: string]: any;
        }

        const formRef = ref<FormInst | null>(null);

        const getFormOptions = async () => {};

        const initFormData: FormDataProps = {
            operUser: storeUser.getUserData.sysUser?.username,
            operTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
            categoryName: null,
            remarks: null
        };

        const formRules = computed(() => ({
            operTime: [{ required: false, message: "请选择操作时间", trigger: ["blur", "change"] }],
            categoryName: [{ required: true, message: "请输入类别名称", trigger: ["input", "blur"] }],
            remarks: [{ required: true, message: "请输入备注", trigger: ["input", "blur"] }]
        }));

        const formData = ref(cloneDeep(initFormData));

        const clearForm = () => {
            formData.value = cloneDeep(initFormData);
        };

        // 获取详情
        const getDetail = () => {
            GET_SUPPLY_CATEGORY_MANAGEMENT_DETAIL({ id: props.configData.id }).then((res) => {
                if (res.data.code === 0) {
                    formData.value = {
                        operTime: res.data.data.operTime,
                        categoryName: res.data.data.categoryName,
                        remarks: res.data.data.remarks
                    };
                }
            });
        };

        watchEffect(async () => {
            if (show.value) {
                await setDictLibs();
                await getFormOptions();
                if (props.configData.id) {
                    getDetail();
                }
            }
        });

        const onClose = () => {
            clearForm();
            changeModalShow(false);
            emit("refresh");
        };

        const onSubmit = async () => {
            let validateError = await formRef.value?.validate((errors) => !!errors);
            if (validateError) return false;

            if (props.configData.id) {
                await EDIT_SUPPLY_CATEGORY_MANAGEMENT({
                    id: props.configData.id,
                    ...formData.value
                }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("编辑成功");
                        onClose();
                    }
                });
            } else {
                await ADD_SUPPLY_CATEGORY_MANAGEMENT({
                    ...formData.value
                }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("新增成功");
                        onClose();
                    }
                });
            }
        };

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card
                    title={props.configData.id ? "编辑类别标签" : "新增类别标签"}
                    class="w-800px"
                    closable
                    onClose={onClose}
                >
                    <n-form
                        ref={formRef}
                        model={formData.value}
                        rules={formRules.value}
                        label-placement="left"
                        label-width="auto"
                    >
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi span={6} label="操作人" path="createBy" required>
                                <UserSelector
                                    v-model:value={formData.value.operUser}
                                    class="w-100%"
                                    clearable
                                    disabled
                                    key-name="username"
                                    placeholder="请选择操作人"
                                />
                                {}
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="操作时间" path="operTime">
                                <n-date-picker
                                    v-model:formatted-value={formData.value.operTime}
                                    className="w-100%"
                                    clearable
                                    placeholder="请选择操作时间"
                                    type="datetime"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="类别名称" path="categoryName">
                                <n-input
                                    v-model:value={formData.value.categoryName}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入类别名称"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="备注" path="remarks">
                                <n-input
                                    v-model:value={formData.value.remarks}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入备注"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={12}>
                                <n-space>
                                    <n-button type="primary" onClick={onSubmit}>
                                        提交
                                    </n-button>
                                    <n-button onClick={onClose}>取消</n-button>
                                </n-space>
                            </n-form-item-gi>
                        </n-grid>
                    </n-form>
                </n-card>
            </n-modal>
        );
    }
});
