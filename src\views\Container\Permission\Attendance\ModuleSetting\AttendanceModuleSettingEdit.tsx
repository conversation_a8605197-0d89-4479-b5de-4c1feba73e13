import { computed, defineComponent, ref, watchEffect } from "vue";
import { type FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { useDicts } from "@/hooks";
import { UserSelector } from "@/components/UserSelector";
import { ADD_ATTENDANCE_MODULE_SETTING, GET_DEPT_TREE } from "@/api/permission";
import dayjs from "dayjs";
import { useStoreDesign, useStoreUser } from "@/store";

export default defineComponent({
    name: "AttendanceModuleSettingEdit",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "update:configData", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        const storeUser = useStoreUser();
        const storeDesign = useStoreDesign();

        // 表单数据
        interface FormDataProps {
            [key: string]: any;
        }

        // 字典操作
        const { dictLibs, getDictLibs } = useDicts();

        const setDictLibs = async () => {
            const dictName = ["common_units"];
            await getDictLibs(dictName);
        };

        // 表单实例
        const formRef = ref<FormInst | null>(null);

        // 获取表单选项
        const corpIdOptions = ref<any[]>([]);

        const getFormOptions = async () => {
            await GET_DEPT_TREE({ deptName: "" }).then((res) => {
                corpIdOptions.value = res.data.data ?? [];
            });
        };

        const initFormData: FormDataProps = {
            operator: null,
            operationTime: null,
            corpId: null,
            comRule: null,
            userList: null
        };

        const formRules = computed(() => ({
            operator: [{ required: true, message: "请选择操作人", trigger: ["blur", "change"] }],
            operationTime: [{ required: true, message: "请选择操作时间", trigger: ["blur", "change"] }],
            corpId: [{ required: true, message: "请选择考勤公司", trigger: ["blur", "change"] }],
            comRule: [{ required: true, message: "请输入考勤同步规则", trigger: ["blur", "change"] }],
            userList: [{ required: true, message: "请选择同步用户", trigger: ["blur", "change"] }]
        }));

        const formData = ref(cloneDeep(initFormData));

        const clearForm = () => {
            formData.value = cloneDeep(initFormData);
        };

        // 关闭
        const onClose = () => {
            clearForm();
            changeModalShow(false);
            emit("refresh");
        };

        // 提交
        const onSubmit = async () => {
            const validateError = await formRef.value?.validate((errors) => !!errors);
            if (validateError) return false;

            const userList = (formData.value.userList.split(",") ?? []).map((item: any) => {
                return { userId: item };
            });

            ADD_ATTENDANCE_MODULE_SETTING({
                ...formData.value,
                userList
            }).then(async (res) => {
                if (res.data.code === 0) {
                    window.$message.success("操作成功");
                    onClose();
                }
            });
        };

        watchEffect(async () => {
            if (show.value) {
                await getFormOptions();
                formData.value.operator = storeUser.getUserData.sysUser?.username;
                formData.value.operationTime = dayjs().format("YYYY-MM-DD HH:mm:ss");
            }
        });

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card title="新增考勤公司" class={["w-800px"]} closable onClose={onClose}>
                    <n-form
                        ref={formRef}
                        model={formData.value}
                        rules={formRules.value}
                        label-placement="left"
                        label-width={120}
                    >
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi span={6} label="操作人" path="operator">
                                <UserSelector
                                    v-model:value={formData.value.operator}
                                    class="w-100%"
                                    multiple={false}
                                    key-name="username"
                                    placeholder="请选择操作人"
                                    disabled
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="操作时间" path="operationTime">
                                <n-date-picker
                                    v-model:formatted-value={formData.value.operationTime}
                                    class="w-100%"
                                    clearable
                                    placeholder="请选择操作时间"
                                    type="datetime"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    disabled
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="考勤公司" path="corpId">
                                <n-tree-select
                                    class="w-100%"
                                    v-model:value={formData.value.corpId}
                                    options={corpIdOptions.value}
                                    clearable
                                    filterable
                                    key-field="id"
                                    label-field="name"
                                    placeholder="请选择考勤公司"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="考勤同步规则" path="comRule">
                                <n-input
                                    v-model:value={formData.value.comRule}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入考勤同步规则"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="考勤管理员" path="userList">
                                <UserSelector
                                    v-model:value={formData.value.userList}
                                    class="w-100%"
                                    multiple
                                    key-name="userId"
                                    placeholder="请选择考勤管理员"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={12}>
                                <n-space>
                                    <n-button onClick={() => onClose()}>取消操作</n-button>
                                    <n-button type="primary" onClick={() => onSubmit()}>
                                        提交同步
                                    </n-button>
                                </n-space>
                            </n-form-item-gi>
                        </n-grid>
                    </n-form>
                </n-card>
            </n-modal>
        );
    }
});
