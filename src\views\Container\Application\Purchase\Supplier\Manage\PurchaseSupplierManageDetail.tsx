import { computed, defineComponent, ref, watchEffect } from "vue";
import { cloneDeep } from "lodash-es";
import { GET_SUPPLIERS_MANAGEMENT_DETAIL, GET_SUPPLY_CATEGORY_MANAGEMENT_LIST } from "@/api/application/purchase";
import { FilePreviewBeta } from "@/components/FilePreview";
import { GET_FILES_BY_ID } from "@/api/public";
import { ProcessDetail } from "@/views/Container/Application/Process/components";
import { GET_OA_INSTANCE_FORM } from "@/api/application/oa";

interface FormDataProps {
    [key: string]: any;
}

export default defineComponent({
    name: "PurchaseSupplierManageDetail",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => emit("update:show", val) });

        const getFormOptions = async () => {
            const res = await GET_SUPPLY_CATEGORY_MANAGEMENT_LIST({ current: 1, size: 999, techState: 0 });
            if (res.data.code === 0) {
                supplyCategoryOptions.value = (res.data.data.records || []).map((item: any) => ({
                    label: item.categoryName,
                    value: String(item.id)
                }));
            }
        };

        const supplyCategoryOptions = ref<{ label: string; value: string }[]>([]);

        const initFormData: FormDataProps = {
            supContactsList: [],
            supDeliveryInfoList: [],
            supInvoiceInfoList: [],
            supQualificationAttachmentsList: [],
            supSuppliers: {
                operator: "",
                operationTime: "",
                supplierName: "",
                supplierType: 1,
                businessLicenseCode: "",
                remark: "",
                contactAddress: "",
                supplyCategory: [],
                supplierStatus: 0
            }
        };

        const formData = ref(cloneDeep(initFormData));

        // 获取详情
        const getDetail = () => {
            GET_SUPPLIERS_MANAGEMENT_DETAIL({ id: props.configData.id }).then((res) => {
                if (res.data.code === 0) {
                    formData.value = {
                        supContactsList: res.data.data?.supContactsList || [],
                        supDeliveryInfoList: res.data.data?.supDeliveryInfoList || [],
                        supInvoiceInfoList: res.data.data?.supInvoiceInfoList || [],
                        supQualificationAttachmentsList: res.data.data?.supQualificationAttachmentsList || [],
                        supSuppliers: {
                            operationTime: res.data.data?.supSuppliers?.operationTime,
                            operator: res.data.data?.supSuppliers?.operator,
                            supplierName: res.data.data?.supSuppliers?.supplierName,
                            supplierType: res.data.data?.supSuppliers?.supplierType,
                            businessLicenseCode: res.data.data?.supSuppliers?.businessLicenseCode,
                            remark: res.data.data?.supSuppliers?.remark,
                            contactAddress: res.data.data?.supSuppliers?.contactAddress,
                            supplyCategory: res.data.data?.supSuppliers?.supplyCategory
                                ? res.data.data.supSuppliers.supplyCategory.split(",")
                                : [],
                            supplierStatus: res.data.data?.supSuppliers?.supplierStatus
                        }
                    };
                }
            });
        };

        watchEffect(async () => {
            if (show.value) {
                await getFormOptions();
                if (props.configData.id) {
                    getDetail();
                }
            }
        });

        const onClose = () => {
            show.value = false;
        };

        // Add preview modal state
        const previewBetaModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const getFileInfo = async (attachmentId: string) => {
            try {
                const res = await GET_FILES_BY_ID({ ids: attachmentId });
                if (res.data.code === 0 && res.data.data?.length > 0) {
                    return res.data.data[0];
                }
                return null;
            } catch (error) {
                window.$message.error("获取附件信息失败");
                return null;
            }
        };

        const previewFileBeta = async (row: any) => {
            const fileInfo = await getFileInfo(row.attachmentId);
            if (fileInfo?.url) {
                const url = import.meta.env.VITE_PREVIEW_URL + fileInfo.url;
                previewBetaModal.value = { show: true, configData: { ...fileInfo, url } };
            } else {
                window.$message.error("预览失败");
            }
        };

        const onDownload = async (row: any) => {
            const fileInfo = await getFileInfo(row.attachmentId);
            if (fileInfo?.url) {
                window.open(import.meta.env.VITE_API_URL + fileInfo.url);
            } else {
                window.$message.error("下载失败");
            }
        };

        // 查看审批单
        const processDetailModal = ref<{ show: boolean; configData: Record<string, any> }>({
            show: false,
            configData: {}
        });

        const openProcessDetailModal = (id: string | number) => {
            GET_OA_INSTANCE_FORM({ processInstId: id }).then((res) => {
                if (res.data.code === 0) {
                    processDetailModal.value.show = true;
                    processDetailModal.value.configData = res.data.data;
                } else {
                    window.$message.error("该审批单不存在");
                }
            });
        };

        return () => (
            <>
                <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                    <n-card title="查看供应商详情" class="w-1200px" closable onClose={onClose}>
                        <n-grid cols={12} x-gap={16} y-gap={16}>
                            <n-grid-item span={4}>
                                <div class="flex items-center mb-4">
                                    <span>供应商名称：</span>
                                    <n-text>{formData.value.supSuppliers.supplierName || "/"}</n-text>
                                </div>
                            </n-grid-item>
                            <n-grid-item span={4}>
                                <div class="flex items-center mb-4">
                                    <span>供应商类型：</span>
                                    <n-text type="info">
                                        {formData.value.supSuppliers.supplierType === 1 ? "企业" : "个人"}
                                    </n-text>
                                </div>
                            </n-grid-item>
                            <n-grid-item span={4}>
                                <div class="flex items-center mb-4">
                                    <span>联系地址：</span>
                                    <n-text>{formData.value.supSuppliers.contactAddress || "/"}</n-text>
                                </div>
                            </n-grid-item>
                            <n-grid-item span={4}>
                                <div class="flex items-center mb-4">
                                    <span>
                                        {formData.value.supSuppliers.supplierType === 1
                                            ? "营业执照编码："
                                            : "身份证号码："}
                                    </span>
                                    <n-text>{formData.value.supSuppliers.businessLicenseCode || "/"}</n-text>
                                </div>
                            </n-grid-item>
                            <n-grid-item span={4}>
                                <div class="flex items-center mb-4">
                                    <span>供货类别：</span>
                                    <n-text type="info">
                                        {formData.value.supSuppliers.supplyCategory
                                            .map(
                                                (id: any) =>
                                                    supplyCategoryOptions.value.find((opt) => opt.value === id)?.label
                                            )
                                            .filter(Boolean)
                                            .join(", ") || "/"}
                                    </n-text>
                                </div>
                            </n-grid-item>
                            <n-grid-item span={4}>
                                <div class="flex items-center mb-4">
                                    <span>备注：</span>
                                    <n-text>{formData.value.supSuppliers.remark || "/"}</n-text>
                                </div>
                            </n-grid-item>
                            <n-grid-item span={4}>
                                <div class="flex items-center mb-4">
                                    <span>供应商状态：</span>
                                    <n-text
                                        type={
                                            formData.value.supSuppliers.supplierStatus === 0
                                                ? "warning"
                                                : formData.value.supSuppliers.supplierStatus === 1
                                                ? "success"
                                                : formData.value.supSuppliers.supplierStatus === 2
                                                ? "warning"
                                                : formData.value.supSuppliers.supplierStatus === 3
                                                ? "error"
                                                : formData.value.supSuppliers.supplierStatus === 4
                                                ? "error"
                                                : formData.value.supSuppliers.supplierStatus === 5
                                                ? "info"
                                                : "info"
                                        }
                                    >
                                        {formData.value.supSuppliers.supplierStatus === 0
                                            ? "待审批"
                                            : formData.value.supSuppliers.supplierStatus === 1
                                            ? "正常"
                                            : formData.value.supSuppliers.supplierStatus === 2
                                            ? "暂停合作"
                                            : formData.value.supSuppliers.supplierStatus === 3
                                            ? "终止合作"
                                            : formData.value.supSuppliers.supplierStatus === 4
                                            ? "已拉黑"
                                            : formData.value.supSuppliers.supplierStatus === 5
                                            ? "评审中"
                                            : "/"}
                                    </n-text>
                                </div>
                            </n-grid-item>
                            {/*<n-grid-item span={4}>*/}
                            {/*    <div class="flex items-center mb-4">*/}
                            {/*        <span>评审流程：</span>*/}
                            {/*        <n-text type="info">点击查看</n-text>*/}
                            {/*    </div>*/}
                            {/*</n-grid-item>*/}

                            <n-grid-item span={4}>
                                <div class="flex items-center mb-4">
                                    <span>操作人：</span>
                                    <n-text>{formData.value.supSuppliers.operator || "/"}</n-text>
                                </div>
                            </n-grid-item>
                            <n-grid-item span={4}>
                                <div class="flex items-center mb-4">
                                    <span>操作时间：</span>
                                    <n-text>{formData.value.supSuppliers.operationTime || "/"}</n-text>
                                </div>
                            </n-grid-item>
                        </n-grid>
                        <n-tabs animated type="bar" defaultValue={2}>
                            <n-tab-pane name={1} tab="供货订单列表"></n-tab-pane>
                            <n-tab-pane name={2} tab="供应商联系人信息">
                                <n-data-table
                                    columns={[
                                        { title: "联系人姓名", key: "contactName", align: "center" },
                                        { title: "联系电话", key: "contactPhone", align: "center" },
                                        { title: "联系人职位", key: "contactPosition", align: "center" }
                                    ]}
                                    data={formData.value.supContactsList}
                                    single-line={false}
                                    striped
                                    bordered
                                />
                            </n-tab-pane>
                            <n-tab-pane name={3} tab="供应商邮寄信息">
                                <n-data-table
                                    columns={[
                                        { title: "收件人姓名", key: "receiverName", align: "center" },
                                        { title: "收件人联系方式", key: "receiverContact", align: "center" },
                                        { title: "收件地址", key: "deliveryAddress", align: "center" },
                                        { title: "备注信息", key: "remarks", align: "center" }
                                    ]}
                                    data={formData.value.supDeliveryInfoList}
                                    single-line={false}
                                    striped
                                    bordered
                                />
                            </n-tab-pane>
                            <n-tab-pane name={4} tab="供应商资质附件">
                                <n-data-table
                                    columns={[
                                        { title: "附件ID", key: "attachmentId", align: "center" },
                                        { title: "附件名称", key: "attachmentName", align: "center" },
                                        // TODO attachmentType=>attachmentTypeName
                                        { title: "附件类型", key: "attachmentTypeName", align: "center" },
                                        {
                                            title: "操作",
                                            key: "actions",
                                            align: "center",
                                            width: 200,
                                            render: (row: any) => (
                                                <n-space justify="center">
                                                    <n-button
                                                        size="small"
                                                        type="warning"
                                                        onClick={() => previewFileBeta(row)}
                                                    >
                                                        预览附件
                                                    </n-button>
                                                    <n-button
                                                        size="small"
                                                        type="primary"
                                                        onClick={() => onDownload(row)}
                                                    >
                                                        下载附件
                                                    </n-button>
                                                </n-space>
                                            )
                                        }
                                    ]}
                                    data={formData.value.supQualificationAttachmentsList}
                                    single-line={false}
                                    striped
                                    bordered
                                />
                            </n-tab-pane>
                            <n-tab-pane name={5} tab="供应商开票信息">
                                <n-data-table
                                    columns={[
                                        { title: "发票抬头", key: "invoiceTitle", align: "center" },
                                        { title: "纳税人识别号", key: "taxpayerIdentificationNumber", align: "center" },
                                        { title: "开户银行", key: "bankOfDeposit", align: "center" },
                                        { title: "银行账号", key: "bankAccountNumber", align: "center" },
                                        { title: "联系电话", key: "contactPhone", align: "center" },
                                        { title: "注册地址", key: "registeredAddress", align: "center" },
                                        {
                                            title: "默认开票信息",
                                            key: "isDefault",
                                            align: "center",
                                            render: (row: any) => {
                                                if (row.isDefault === 1) {
                                                    return <n-text type="info">默认</n-text>;
                                                } else {
                                                    return <n-text>非默认</n-text>;
                                                }
                                            }
                                        }
                                    ]}
                                    data={formData.value.supInvoiceInfoList}
                                    single-line={false}
                                    striped
                                    bordered
                                />
                            </n-tab-pane>
                        </n-tabs>
                    </n-card>
                </n-modal>
                <FilePreviewBeta
                    v-model:show={previewBetaModal.value.show}
                    configData={previewBetaModal.value.configData}
                />
                <ProcessDetail
                    v-model:show={processDetailModal.value.show}
                    config-data={processDetailModal.value.configData}
                />
            </>
        );
    }
});
