<template>
    <div>
        <n-drawer
            v-model:show="show"
            :close-on-esc="false"
            :mask-closable="false"
            placement="right"
            width="666px"
            @update:show="changeModalShow"
        >
            <n-drawer-content :title="operationType === '1' ? '选择技术配方' : '新增技术配方'" closable>
                <n-form label-placement="left">
                    <n-form-item label="操作类型：">
                        <n-radio-group
                            v-model:value="operationType"
                            name="operationType"
                            @update:value="changeOperationType"
                        >
                            <n-space>
                                <n-radio label="选择配方" value="1" />
                                <n-radio label="新增配方" value="2" />
                            </n-space>
                        </n-radio-group>
                    </n-form-item>
                    <template v-if="operationType === '1'">
                        <n-form-item label="当前配方：">
                            <n-select
                                v-model:value="formData.formulaId as string|number"
                                :options="formulaOptions"
                                clearable
                                filterable
                                label-field="formulaName"
                                placeholder="请选择当前配方"
                                value-field="formulaId"
                                @update:value="changeFormula"
                            />
                        </n-form-item>
                        <n-form-item label="配方详情：" label-placement="top">
                            <DynamicTable
                                v-model:header="formulaDetail.header"
                                v-model:value="formulaDetail.value"
                                :addable="false"
                                :data-source="[]"
                                :deletable="false"
                                :headerConfigurable="false"
                                class="w-100%"
                                disabled
                            />
                        </n-form-item>
                    </template>
                    <template v-if="operationType === '2'">
                        <n-form-item label="配方名称：">
                            <n-input v-model:value="formData.formulaName" clearable placeholder="请输入配方名称" />
                        </n-form-item>
                        <n-form-item label="配方详情：" label-placement="top">
                            <DynamicTable
                                v-model:header="formulaDetail.header"
                                v-model:value="formulaDetail.value"
                                :data-source="dynamicTableDataSource"
                                class="w-100%"
                            />
                        </n-form-item>
                    </template>
                    <n-form-item>
                        <n-space>
                            <n-button type="primary" @click="onSubmit">提交</n-button>
                            <n-button @click="changeModalShow(false)">取消</n-button>
                        </n-space>
                    </n-form-item>
                </n-form>
            </n-drawer-content>
        </n-drawer>
    </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from "vue";
import {
    ADD_PRODUCTION_FORMULA,
    GET_PRODUCTION_FORMULA_DETAIL,
    GET_PRODUCTION_FORMULA_LIST,
    GET_PRODUCTION_TYPE_LIST,
    RELATION_PRODUCTION_FORMULA
} from "@/api/application/production";
import type { DynamicTableHeaderProps, DynamicTableRowProps } from "@/components/Dynamic";
import { DynamicTable, DynamicTableDataSourceProps } from "@/components/Dynamic";
import { isJSON } from "@/utils/tools";
import { useDicts } from "@/hooks";

let props = withDefaults(defineProps<{ show: Boolean; configData: UnKnownObject }>(), {
    show: () => false
});

let emits = defineEmits(["update:show", "refresh"]);

onMounted(async () => {});

watch(
    () => props.show,
    async (val) => {
        if (val) {
            await setDictLibs();
            await setDynamicTableDataSource();
            await getFormulaOptions();
        }
    },
    { immediate: true }
);

// 字典操作
let { dictLibs, getDictLibs } = useDicts();

let setDictLibs = async () => {
    let dictName = ["product_type"];
    await getDictLibs(dictName);
};

// 操作类型
let operationType = ref("1");

let changeOperationType = (type: string) => {
    operationType.value = type;
    // 清空选择
    formData.value = {
        formulaId: null,
        formulaName: ""
    };
    formulaDetail.value = { header: [], value: [] };
};

// 配方详情
let dynamicTableDataSource = ref<DynamicTableDataSourceProps[]>([]);

let setDynamicTableDataSource = async () => {
    // 规格型号
    let specifications = await GET_PRODUCTION_TYPE_LIST({ current: 1, size: 10000 }).then((res) => {
        if (res.data.code === 0) {
            return res.data.data.records.map((item: any) => ({ label: item.specification, value: item.id }));
        }
    });

    let tubeClassification = dictLibs.product_type;

    dynamicTableDataSource.value = [
        { label: "管材分类", value: "tubeClassification", defaultOptions: tubeClassification },
        { label: "规格型号", value: "specifications", defaultOptions: specifications }
    ];
};

let formulaDetail = ref<{
    header: DynamicTableHeaderProps[];
    value: DynamicTableRowProps[];
}>({
    header: [],
    value: []
});

// 配方列表
let formulaOptions = ref([]);

let getFormulaOptions = async () => {
    await GET_PRODUCTION_FORMULA_LIST({ current: 1, size: 1000 }).then((res) => {
        if (res.data.code === 0 && res.data.data) {
            formulaOptions.value = res.data.data.records || [];
        }
    });
};

let changeFormula = (id: string | number) => {
    GET_PRODUCTION_FORMULA_DETAIL({ formulaId: id }).then((res) => {
        formulaDetail.value = isJSON(res.data.data.chargerSheet)
            ? JSON.parse(res.data.data.chargerSheet)
            : {
                  header: [],
                  value: []
              };
    });
};

let formData = ref<{
    formulaId: Nullable<string | number>;
    formulaName: string;
}>({
    formulaId: null,
    formulaName: ""
});

// 弹窗状态更改
let changeModalShow = (show: boolean) => emits("update:show", show);

// 提交
let onRelation = () => {
    RELATION_PRODUCTION_FORMULA({
        potId: props.configData.id,
        formulaId: formData.value.formulaId
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("关联成功");
            changeModalShow(false);
            emits("refresh");
        }
    });
};

let onSubmit = () => {
    if (operationType.value === "1") {
        onRelation();
    } else if (operationType.value === "2") {
        ADD_PRODUCTION_FORMULA({
            potId: props.configData.id,
            formulaName: formData.value.formulaName,
            chargerSheet: formulaDetail.value ? JSON.stringify(formulaDetail.value) : ""
        }).then((res) => {
            if (res.data.code === 0) {
                formData.value.formulaId = res.data.data || null;
                onRelation();
            } else {
                window.$message.error("新增配方失败");
            }
        });
    }
};
</script>
