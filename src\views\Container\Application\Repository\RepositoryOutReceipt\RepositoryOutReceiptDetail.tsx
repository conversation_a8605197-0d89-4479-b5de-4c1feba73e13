import { computed, defineComponent, ref, watchEffect } from "vue";
import type { DataTableColumns } from "naive-ui";
import { GET_REPOSITORY_OUT_RECEIPT_DETAIL } from "@/api/application/repository";
import RepositoryOutReceiptPrint from "@/views/Container/Application/Repository/RepositoryOutReceipt/RepositoryOutReceiptPrint";

export default defineComponent({
    name: "RepositoryOutReceiptDetail",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        // 获取详情
        const detailData = ref<any>(null);

        const getDetail = async () => {
            await GET_REPOSITORY_OUT_RECEIPT_DETAIL({ id: props.configData.id }).then((res) => {
                if (res.data.code === 0) detailData.value = res.data.data;
            });
            // detailData.value = props.configData;
        };

        const tableColumns = ref<DataTableColumns<any>>([
            { title: "物料名称", key: "goodsName", align: "center" },
            { title: "物料规格", key: "goodsSpec", align: "center" },
            { title: "物料代码", key: "goodsCode", align: "center" },
            { title: "应出库数", key: "applyQuality", align: "center" },
            { title: "实出库数", key: "actualQuality", align: "center" },
            { title: "物料基本单位", key: "goodsUnitName", align: "center" },
            { title: "备注", key: "goodsRemark", align: "center" }
        ]);

        const onClose = () => {
            changeModalShow(false);
            emit("refresh");
        };

        // 打印
        const printModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const openPrintModal = () => {
            printModal.value.show = true;
            printModal.value.configData = detailData.value;
        };

        watchEffect(() => {
            if (props.configData.id) getDetail();
        });

        return () => (
            <div>
                <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                    <n-card title="查看出库详情" class="w-800px" closable onClose={onClose}>
                        <n-form label-placement="left" label-width="auto">
                            <n-grid cols={12} x-gap={16}>
                                <n-form-item-gi required span={6} label="申请人">
                                    {detailData.value?.applyByName ?? "/"}
                                </n-form-item-gi>
                                <n-form-item-gi required span={6} label="所属部门">
                                    {detailData.value?.applyDepName ?? "/"}
                                </n-form-item-gi>
                                <n-form-item-gi required span={6} label="出库类型">
                                    {detailData.value?.outType === 1 && "手动出库"}
                                    {detailData.value?.outType === 2 && "发货出库"}
                                    {detailData.value?.outType === 3 && "领料出库"}
                                </n-form-item-gi>
                                <n-form-item-gi required span={6} label="用途类型">
                                    {detailData.value?.purposeType === 1 && "原材料"}
                                    {detailData.value?.purposeType === 2 && "维修配件"}
                                    {detailData.value?.purposeType === 3 && "辅料"}
                                    {![1, 2, 3].includes(detailData.value?.purposeType) && "/"}
                                </n-form-item-gi>
                                <n-form-item-gi required span={6} label="出库状态">
                                    {detailData.value?.outState === 1 && <n-text type="info">待出库</n-text>}
                                    {detailData.value?.outState === 2 && <n-text type="success">已确认</n-text>}
                                    {detailData.value?.outState === 3 && <n-text type="error">已拒绝</n-text>}
                                </n-form-item-gi>
                                {detailData.value?.outState === 3 && (
                                    <n-form-item-gi required span={6} label="拒绝原因">
                                        {detailData.value?.rejectReason ?? "/"}
                                    </n-form-item-gi>
                                )}
                                {detailData.value?.outType === 2 && (
                                    <>
                                        <n-form-item-gi required span={6} label="收货人姓名">
                                            {detailData.value?.receiveUser ?? "/"}
                                        </n-form-item-gi>
                                        <n-form-item-gi required span={6} label="收货人电话">
                                            {detailData.value?.contactPhone ?? "/"}
                                        </n-form-item-gi>
                                        <n-form-item-gi required span={6} label="收货地址">
                                            {detailData.value?.receiveAddress ?? "/"}
                                        </n-form-item-gi>
                                        <n-form-item-gi required span={6} label="发货方式">
                                            {detailData.value?.deliveryWay === 1 ? "配送" : "物流"}
                                        </n-form-item-gi>
                                        {detailData.value?.deliveryWay === 1 && (
                                            <>
                                                <n-form-item-gi required span={6} label="司机姓名">
                                                    {detailData.value?.driverName ?? "/"}
                                                </n-form-item-gi>
                                                <n-form-item-gi required span={6} label="车牌号">
                                                    {detailData.value?.driverNo ?? "/"}
                                                </n-form-item-gi>
                                                <n-form-item-gi required span={6} label="司机电话">
                                                    {detailData.value?.driverPhone ?? "/"}
                                                </n-form-item-gi>
                                            </>
                                        )}
                                        {detailData.value?.deliveryWay === 2 && (
                                            <>
                                                <n-form-item-gi required span={6} label="物流公司">
                                                    {detailData.value?.logisticsCompany ?? "/"}
                                                </n-form-item-gi>
                                                <n-form-item-gi required span={6} label="物流单号">
                                                    {detailData.value?.logisticsNo ?? "/"}
                                                </n-form-item-gi>
                                            </>
                                        )}
                                    </>
                                )}
                                <n-form-item-gi required span={6} label="备注">
                                    {detailData.value?.remark ?? "/"}
                                </n-form-item-gi>
                            </n-grid>
                            <n-data-table
                                columns={tableColumns.value}
                                data={detailData.value?.recordItemList}
                                single-line={false}
                                bordered
                                striped
                            />
                            <div class="flex-center pt-4">
                                <n-button type="success" onClick={() => openPrintModal()}>
                                    打印单据
                                </n-button>
                            </div>
                        </n-form>
                    </n-card>
                </n-modal>
                <RepositoryOutReceiptPrint
                    v-model:show={printModal.value.show}
                    configData={printModal.value.configData}
                />
            </div>
        );
    }
});
