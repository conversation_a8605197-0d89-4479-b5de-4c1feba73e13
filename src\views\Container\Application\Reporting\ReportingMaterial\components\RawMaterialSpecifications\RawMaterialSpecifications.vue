<template>
    <div>
        <n-drawer
            v-model:show="show"
            :close-on-esc="false"
            :height="600"
            :mask-closable="false"
            placement="bottom"
            @update:show="changeModalShow(false)"
        >
            <n-drawer-content :title="`物料名称：${props.configData.materialName ?? '未知'}`" closable>
                <n-data-table :columns="tableColumns" :data="tableData" :single-line="false" bordered striped />
                <div class="flex-x-center mt">
                    <n-space>
                        <n-button type="primary" @click="addTableItem">新增一行</n-button>
                        <n-button type="success" @click="onSubmit">保存全部</n-button>
                    </n-space>
                </div>
            </n-drawer-content>
        </n-drawer>
    </div>
</template>

<script lang="ts" setup>
import { computed, h, onMounted, ref, watchEffect } from "vue";
import type { DataTableColumns } from "naive-ui";
import { NButton, NInput } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { TableActions } from "@/components/TableActions";
import { GET_MATERIAL_SPEC_LIST_BY_MID, SAVE_MATERIAL_SPEC_LIST } from "@/api/application/reporting";
import { UnitSelector } from "@/views/Container/Application/Reporting/components";
import { useDicts } from "@/hooks";

let props = withDefaults(defineProps<{ show: boolean; configData: UnKnownObject }>(), { show: () => false });

let emits = defineEmits(["update:show", "refresh"]);

// 弹窗展示
let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

let changeModalShow = (show: boolean) => emits("update:show", show);

// 字典操作
let { dictLibs, getDictLibs } = useDicts();

let setDictLibs = async () => {
    let dictName = ["common_units"];
    await getDictLibs(dictName);
};

onMounted(async () => {
    await setDictLibs();
});

// 表单数据
interface RowProps {
    spec: string; // 规格型号
    unitPrice: string; // 单价
    unit: string; // 单位
    consumeCoefficient: string; // 用料系数
    deviationRatio: string; // 偏差比例

    [key: string]: any;
}

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        title: "名称代号",
        key: "spec",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.spec,
                onUpdateValue: (v) => (row.spec = v)
            });
        }
    },
    {
        title: "单价（元）",
        key: "unitPrice",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.unitPrice,
                onUpdateValue: (v) => (row.unitPrice = v)
            });
        }
    },
    {
        title: "价格计量单位",
        key: "unit",
        align: "center",
        render: (row) => {
            return h(UnitSelector, {
                type: "select",
                value: row.unit,
                options: dictLibs["common_units"] ?? [],
                onSubmit: (v: any) => (row.unit = v)
            });
        }
    },
    {
        title: "用料系数",
        key: "consumeCoefficient",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.consumeCoefficient,
                onUpdateValue: (v) => (row.consumeCoefficient = v)
            });
        }
    },
    // 2023年9月8日单位变更需要对接数据-已完成
    {
        title: "用量计量单位",
        key: "amountUnit",
        align: "center",
        render: (row) => {
            return h(UnitSelector, {
                type: "select",
                value: row.amountUnit,
                options: dictLibs["common_units"] ?? [],
                onSubmit: (v: any) => (row.amountUnit = v)
            });
        }
    },
    {
        title: "偏差比例(%)",
        key: "deviationRatio",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.deviationRatio,
                onUpdateValue: (v) => (row.deviationRatio = v)
            });
        }
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: "80",
        render(row, index) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "删除",
                        tertiary: true,
                        type: "error",
                        onClick: () => deleteItem(index)
                    }
                ]
            });
        }
    }
]);

// 可编辑表单配置
let tableItem: RowProps = {
    spec: "",
    unitPrice: "",
    unit: "",
    consumeCoefficient: "1",
    deviationRatio: "0"
};

let addTableItem = () => {
    tableData.value.push(cloneDeep(tableItem));
};

let deleteItem = (index: number) => {
    tableData.value.splice(index, 1);
};

let tableData = ref<RowProps[]>([]);

let clearFrom = () => {
    tableData.value = [];
};

let getTableData = () => {
    GET_MATERIAL_SPEC_LIST_BY_MID({ materialId: props.configData.id }).then((res) => {
        tableData.value = res.data.data;
    });
};

watchEffect(() => {
    if (props.show && props.configData.id) {
        getTableData();
    }
});

// 提交
let onSubmit = () => {
    SAVE_MATERIAL_SPEC_LIST({
        materialId: props.configData.id,
        specList: tableData.value
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("保存成功");
            changeModalShow(false);
            clearFrom();
            emits("refresh");
        } else {
            window.$message.error(res.data.message);
        }
    });
};
</script>
