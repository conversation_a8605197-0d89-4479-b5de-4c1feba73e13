import { defineComponent, ref } from "vue";
import PlasticMesProductionPlanYearQuantity from "./PlasticMesProductionPlanYearQuantity";
import PlasticMesProductionPlanYearRepairCost from "./PlasticMesProductionPlanYearRepairCost";
import PlasticMesProductionPlanYearWages from "./PlasticMesProductionPlanYearWages";
import PlasticMesProductionPlanYearEnergy from "./PlasticMesProductionPlanYearEnergy";
import PlasticMesProductionPlanYearMaterial from "@/views/Container/Application/PlasticMes/Production/Plan/Year/PlasticMesProductionPlanYearMaterial";

export default defineComponent({
    name: "PlasticMesProductionPlanYear",
    setup() {
        const tabActive = ref(1);

        const tabList = [
            { label: "产量计划", value: 1 },
            { label: "修理费计划", value: 2 },
            { label: "工资计划", value: 3 },
            { label: "原辅材料计划", value: 4 },
            { label: "能耗计划", value: 5 }
        ];

        return () => (
            <div class="plastic-mes-production-plan-year">
                <n-card hoverable>
                    <n-tabs v-model:value={tabActive.value} animated type="bar">
                        {tabList.map((item) => (
                            <n-tab-pane name={item.value} tab={item.label}>
                                {tabActive.value === 1 && <PlasticMesProductionPlanYearQuantity />}
                                {tabActive.value === 2 && <PlasticMesProductionPlanYearRepairCost />}
                                {tabActive.value === 3 && <PlasticMesProductionPlanYearWages />}
                                {tabActive.value === 4 && <PlasticMesProductionPlanYearMaterial />}
                                {tabActive.value === 5 && <PlasticMesProductionPlanYearEnergy />}
                            </n-tab-pane>
                        ))}
                    </n-tabs>
                </n-card>
            </div>
        );
    }
});
