<template>
    <div class="w-100%">
        <n-card>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt">
            <n-space class="mb">
                <n-button secondary type="primary" @click="openEditModal()">新增</n-button>
            </n-space>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <UserDictEditModal v-model:id="editModal.id" v-model:show="editModal.show" @refresh="getTableData" />
        <UserDictItemList v-model:show="dictItemModal.show" :row-data="dictItemModal.rowData" @refresh="getTableData" />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from "vue";
import type { DataTableColumns } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { TableActions } from "@/components/TableActions";
import { useCommonTable, useDicts } from "@/hooks";
import { DELETE_DICT_MANAGE, GET_DICT_MANAGE_LIST } from "@/api/public";
import UserDictEditModal from "./UserDictEditModal.vue";
import UserDictItemList from "./UserDictItemList.vue";

interface RowProps {
    id: Nullable<string | number>;
    description: Nullable<string>;
    type: Nullable<string>;
    remark: Nullable<string>;
    createTime: Nullable<string>;
}

onMounted(async () => {
    await getSearchOptions();
    getTableData();
});

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    {
        label: "所属分类",
        type: "select",
        prop: "dictType"
    },
    {
        label: "功能标识",
        type: "input",
        prop: "type"
    }
]);

let searchOptions = ref<TableSearchbarOptions>({
    dictType: []
});

let searchForm = ref<TableSearchbarData>({
    type: null
});

let { dictLibs, getDictLibs, dictValueToLabel } = useDicts();

let getSearchOptions = async () => {
    let dictName = ["dict_type"];
    await getDictLibs(dictName);
    searchOptions.value.dictType = dictLibs.dict_type;
};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "功能标识",
        key: "type",
        align: "center"
    },
    {
        title: "功能名称",
        key: "description",
        align: "center"
    },
    {
        title: "备注",
        key: "remark",
        align: "center",
        render: (row: RowProps) => {
            return row.remark || "暂无";
        }
    },
    {
        title: "创建时间",
        key: "createTime",
        align: "center"
    },

    {
        title: "操作",
        key: "actions",
        align: "center",
        width: 250,
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "查看详情",
                        tertiary: true,
                        type: "success",
                        onClick: () => {
                            openDictItemModal(row);
                        }
                    },
                    {
                        label: "编辑",
                        tertiary: true,
                        onClick: () => {
                            openEditModal(row.id);
                        }
                    },
                    {
                        label: "删除",
                        tertiary: true,
                        type: "error",
                        onClick: () => {
                            onDelete(row.id);
                        }
                    }
                ]
            });
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_DICT_MANAGE_LIST({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        systemFlag: "0",
        ...searchForm.value
    }).then((res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data.records;
            tablePagination.itemCount = res.data.data.total;
            tableLoading.value = false;
        }
    });
};

// 搜索
let onSearch = () => {
    tablePagination.page = 1;
    tablePagination.pageSize = 10;
    getTableData();
};

// 新增编辑
let editModal = ref<{ show: boolean; id: string | number | null }>({
    show: false,
    id: null
});

let openEditModal = (id?: string | number | null) => {
    editModal.value.show = true;
    editModal.value.id = id || null;
};

// 字典项目
let dictItemModal = ref<{ show: boolean; rowData: RowProps }>({
    show: false,
    rowData: {
        id: null,
        description: null,
        type: null,
        remark: null,
        createTime: null
    }
});

let openDictItemModal = (row: RowProps) => {
    dictItemModal.value.show = true;
    dictItemModal.value.rowData = row;
};

// 删除
let onDelete = (id?: string | number | null) => {
    if (!id && tableSelection.value.length < 1) {
        window.$message.error("请选择要删除的数据");
        return false;
    }
    window.$dialog.warning({
        title: "警告",
        content: `确定删除${id ? "该" : "选中"}字典吗？`,
        positiveText: "删除",
        negativeText: "取消",
        onPositiveClick: () => {
            if (id) {
                DELETE_DICT_MANAGE({ id: id }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("删除成功");
                        onSearch();
                    }
                });
            }
        }
    });
};
</script>
