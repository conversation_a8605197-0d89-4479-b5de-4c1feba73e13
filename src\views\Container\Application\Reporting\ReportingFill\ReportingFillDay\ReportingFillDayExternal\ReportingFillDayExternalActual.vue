<template>
    <n-spin :show="loadingShow">
        <template #description>正在处理中，请耐心等候</template>
        <div class="flex">
            <n-card class="flex-fixed-200">
                <div class="flex-y-center mb">
                    <n-input v-model:value="treePattern" clearable placeholder="搜索" />
                </div>
                <n-tree
                    v-model:selected-keys="treeSelectKeys"
                    :cancelable="false"
                    :data="treeData"
                    :pattern="treePattern"
                    :show-irrelevant-nodes="false"
                    block-line
                    children-field="childrenList"
                    default-expand-all
                    key-field="id"
                    label-field="categoryName"
                    selectable
                    @update:selected-keys="selectTreeNode"
                />
            </n-card>
            <div class="flex-1 ml">
                <n-card>
                    <table-searchbar
                        auto-search
                        v-model:form="searchForm"
                        :config="searchConfig"
                        :options="searchOptions"
                        @search="onSearch"
                        @componentClick="onComponentClick"
                    />
                </n-card>
                <n-card class="mt">
                    <n-space class="mb">
                        <n-button secondary type="success" @click="saveTableData">保存填写</n-button>
                    </n-space>
                    <n-data-table
                        :columns="tableColumns"
                        :data="tableData"
                        :loading="tableLoading"
                        :row-key="tableRowKey"
                        :single-line="false"
                        bordered
                        remote
                        striped
                        @update:checked-row-keys="changeTableSelection"
                    />
                </n-card>
            </div>
        </div>
    </n-spin>
</template>

<script lang="ts" setup>
import { h, onMounted, ref } from "vue";
import {
    GET_CONFIG_WORK_GROUP_LIST,
    GET_DAILY_OUTSOURCE_COST_PRODUCT_COST_LIST,
    GET_MANUFACTURE_PAGE_LIST,
    GET_OTHER_CATEGORY_TREE_BY_TYPE,
    GET_SEMI_MANUFACTURE_PAGE_LIST,
    SAVE_DAILY_OUTSOURCE_COST_PRODUCT_COST_LIST
} from "@/api/application/reporting";
import type { DataTableColumns } from "naive-ui";
import { NButton, NInput, NTooltip } from "naive-ui";
import { useCommonTable, useReportingTree } from "@/hooks";
import {
    TableSearchbar,
    TableSearchbarConfig,
    TableSearchbarData,
    TableSearchbarOptions
} from "@/components/TableSearchbar";
import dayjs from "dayjs";
import { SpecSelector } from "@/views/Container/Application/Reporting/components";
import { useStoreReportingSearch } from "@/store";
import { useThrottleFn } from "@vueuse/core";

let storeReportingSearch = useStoreReportingSearch();

interface RowProps {
    [key: string]: any;
}

let loadingShow = ref(false);

onMounted(async () => {
    await getTreeData();
    await getWorkGroupIdOptions();
    getTableData();
});

// 树相关操作
let { treeData, treePattern, treeSelectKeys, treeAddDisabled, findLastLevel } = useReportingTree();

let getTreeData = async () => {
    await GET_OTHER_CATEGORY_TREE_BY_TYPE({
        wageType: 1
    }).then((res) => {
        if (res.data.code === 0) {
            treeData.value = treeAddDisabled(res.data.data ?? []);
            treeSelectKeys.value = [findLastLevel(treeData.value)[0]?.id];
        }
    });
};

let selectTreeNode = (keys?: (string | number)[], option?: any[]) => {
    treeSelectKeys.value = keys ?? [];
    onSearch();
};

// 搜索相关操作
let getWorkGroupIdOptions = async () => {
    await GET_CONFIG_WORK_GROUP_LIST({}).then((res) => {
        searchOptions.value.workGroupId = (res.data.data ?? []).map((item: any) => ({
            label: item.companyName + "-" + item.workshopName + "-" + item.groupName,
            value: item.id
        }));
    });
    // searchForm.value.workGroupId = searchOptions.value.workGroupId[0].value;
    searchForm.value.planDate = dayjs().format("YYYY-MM-DD");
    // if (storeReportingSearch.getSearchForm.workGroupId) {
    //     searchForm.value.workGroupId = storeReportingSearch.getSearchForm.workGroupId;
    // }
    if (storeReportingSearch.getSearchForm.planDate) {
        searchForm.value.planDate = storeReportingSearch.getSearchForm.planDate;
    }
};

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    // { label: "班组", type: "select", prop: "workGroupId", span: 2 },
    { label: "日期筛选", type: "date", dateFormat: "yyyy-MM-dd", prop: "planDate" }
]);

let searchOptions = ref<TableSearchbarOptions>({ workGroupId: [] });

let searchForm = ref<TableSearchbarData>({
    // workGroupId: null,
    planDate: null
});

let onSearch = () => {
    // if (!searchForm.value.workGroupId) return window.$message.error("请先选择班组！");
    if (!searchForm.value.planDate) return window.$message.error("请先选择日期！");
    storeReportingSearch.setSearchForm({
        workGroupId: searchForm.value.workGroupId ?? null,
        planDate: searchForm.value.planDate ?? null
    });
    getTableData();
};

// 搜索栏自动保存逻辑
let autoSave = useThrottleFn(async () => {
    SAVE_DAILY_OUTSOURCE_COST_PRODUCT_COST_LIST({
        ...searchForm.value,
        categoryId: treeSelectKeys.value[0],
        outsourceCostList: tableData.value
    }).then((res) => {
        if (res.data.code === 0) window.$message.success("自动保存成功");
    });
}, 1000);

let onComponentClick = async (val: TableSearchbarData) => {
    // await autoSave();
};

// 数据列表
let { tableRowKey, tableData, tableLoading, changeTableSelection } = useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    { type: "selection" },
    {
        title: "规格型号",
        align: "center",
        key: "productModel"
    },
    {
        title: "计划金额（元）",
        align: "center",
        key: "planCost"
    },
    {
        title: "实际金额（元）",
        align: "center",
        key: "productCost",
        render: (row) => {
            return h(
                NTooltip,
                {},
                {
                    trigger: () => {
                        return h(NInput, {
                            value: row.productCost,
                            onUpdateValue: (v) => (row.productCost = v),
                            onFocus: () => {
                                if (row.productCost === "0") row.productCost = "";
                            },
                            onBlur: () => {
                                if (!row.productCost) row.productCost = "0";
                            }
                        });
                    },
                    default: () => {
                        return row.productCost;
                    }
                }
            );
        }
    },

    {
        title: "备注",
        align: "center",
        key: "remark",
        render: (row) => {
            return h(
                NTooltip,
                {},
                {
                    trigger: () => {
                        return h(NInput, { value: row.remark, onUpdateValue: (v) => (row.remark = v) });
                    },
                    default: () => {
                        return row.remark;
                    }
                }
            );
        }
    }
]);

let getTableData = () => {
    tableLoading.value = true;
    GET_DAILY_OUTSOURCE_COST_PRODUCT_COST_LIST({
        ...searchForm.value,
        categoryId: treeSelectKeys.value[0]
    }).then((res) => {
        tableData.value = res.data.data;
        tableLoading.value = false;
    });
};

// 保存全部
let saveTableData = () => {
    loadingShow.value = true;
    SAVE_DAILY_OUTSOURCE_COST_PRODUCT_COST_LIST({
        ...searchForm.value,
        categoryId: treeSelectKeys.value[0],
        outsourceCostList: tableData.value
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("保存成功");
            onSearch();
        }
        loadingShow.value = false;
    });
};
</script>
