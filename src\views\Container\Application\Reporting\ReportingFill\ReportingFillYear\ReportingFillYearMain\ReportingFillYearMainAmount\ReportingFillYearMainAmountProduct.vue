<template>
    <n-spin :show="loadingShow">
        <template #description>正在处理中，请耐心等候</template>
        <n-card>
            <table-searchbar
                auto-search
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
                @componentClick="onComponentClick"
            />
        </n-card>
        <n-card class="mt">
            <div class="flex-y-center mb">
                <n-space>
                    <n-button secondary type="primary" @click="addTableItem">新增记录</n-button>
                    <n-button secondary type="success" @click="saveTableData">保存填写</n-button>
                </n-space>
                <div class="h-100% flex-y-center ml-a">计划数量合计：{{ totalNumber }}根</div>
            </div>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
    </n-spin>
</template>

<script lang="ts" setup>
import { computed, h, onMounted, ref, watchEffect } from "vue";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import { NButton, NInput, NTooltip } from "naive-ui";
import {
    GET_CONFIG_WORK_GROUP_LIST,
    GET_MANUFACTURE_PAGE_LIST,
    GET_YEAR_PLAN_AMOUNT_LIST,
    GET_SEMI_MANUFACTURE_PAGE_LIST,
    SAVE_YEAR_PLAN_AMOUNT_LIST
} from "@/api/application/reporting";
import { TableActions } from "@/components/TableActions";
import { SpecSelector } from "@/views/Container/Application/Reporting/components";
import { cloneDeep } from "lodash-es";
import dayjs from "dayjs";
import { useThrottleFn } from "@vueuse/core";

let props = withDefaults(
    defineProps<{
        planYear: string;
    }>(),
    {}
);

interface RowProps {
    [key: string]: any;
}

let loadingShow = ref(false);

onMounted(async () => {
    await getWorkGroupIdOptions();
    getTableData();
});

let getWorkGroupIdOptions = async () => {
    await GET_CONFIG_WORK_GROUP_LIST({}).then((res) => {
        searchOptions.value.workGroupId = (res.data.data ?? []).map((item: any) => {
            return { label: item.companyName + "-" + item.workshopName + "-" + item.groupName, value: item.id };
        });
    });
    searchForm.value.workGroupId = searchOptions.value.workGroupId[0].value;
};

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([{ label: "班组", type: "select", prop: "workGroupId", span: 2 }]);

let searchOptions = ref<TableSearchbarOptions>({ workGroupId: [] });

let searchForm = ref<TableSearchbarData>({ workGroupId: null });

let onSearch = () => {
    // getTableData();
};

// 搜索栏自动保存逻辑
let autoSave = useThrottleFn(async () => {
    let totalPlanAmount = 0;
    tableData.value.forEach((item: any) => (totalPlanAmount += Number(item.planAmount)));
    let params = {
        ...searchForm.value,
        planYear: props.planYear,
        totalPlanAmount: totalPlanAmount,
        yearAmountList: tableData.value
    };
    await SAVE_YEAR_PLAN_AMOUNT_LIST({ ...params }).then((res) => {
        if (res.data.code === 0) window.$message.success("自动保存成功");
    });
}, 1000);

let onComponentClick = async (val: TableSearchbarData) => {
    // await autoSave();
};

// 数据列表
let { tableRowKey, tableData, tableLoading, changeTableSelection } = useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "名称",
        align: "center",
        key: "poleName",
        render: (row) => {
            return row.poleName ?? "暂无";
        }
    },
    {
        title: "规格型号",
        align: "center",
        key: "contentId",
        render: (row) => {
            return h(SpecSelector, {
                value: row.contentId,
                onSubmit: async (v: unknown) => {
                    row.contentId = v;
                    let selectArray: any[] = [];
                    let [semiManufacture, manufacture] = await Promise.all([
                        GET_SEMI_MANUFACTURE_PAGE_LIST({ ids: v }),
                        GET_MANUFACTURE_PAGE_LIST({ ids: v })
                    ]);
                    let semiManufactureData = (semiManufacture.data.data.records ?? []).map((item: any) => {
                        return { ...item, contentType: 2 };
                    });
                    let manufactureData = (manufacture.data.data.records ?? []).map((item: any) => {
                        return { ...item, contentType: 1 };
                    });
                    selectArray = [...semiManufactureData, ...manufactureData];
                    row.contentType = selectArray[0].contentType;
                }
            });
        }
    },
    {
        title: "计划数量",
        align: "center",
        key: "planAmount",
        render: (row) => {
            return h(
                NTooltip,
                {},
                {
                    trigger: () => {
                        return h(NInput, {
                            value: row.planAmount,
                            onUpdateValue: (v) => (row.planAmount = v),
                            onFocus: () => {
                                if (row.planAmount === "0") row.planAmount = "";
                            },
                            onBlur: () => {
                                if (!row.planAmount) row.planAmount = "0";
                            }
                        });
                    },
                    default: () => {
                        return row.planAmount;
                    }
                }
            );
        }
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: "80",
        render(row, index) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "删除",
                        tertiary: true,
                        type: "error",
                        onClick: () => deleteItem(index)
                    }
                ]
            });
        }
    }
]);

let getTableData = () => {
    tableLoading.value = true;
    GET_YEAR_PLAN_AMOUNT_LIST({
        ...searchForm.value,
        planYear: props.planYear
    }).then((res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data ?? [];
        }
        tableLoading.value = false;
    });
};

watchEffect(() => {
    if (searchForm.value.workGroupId && props.planYear) {
        getTableData();
    }
});

// 可编辑表单配置
let tableItem: RowProps = {
    id: "",
    contentId: "",
    contentType: "",
    planAmount: ""
};

//新增计划
let addTableItem = () => {
    if (!searchForm.value.workGroupId) {
        window.$message.error("请先选择班组！");
        return false;
    }
    tableData.value.push(cloneDeep(tableItem));
};

let deleteItem = (index: number) => {
    tableData.value.splice(index, 1);
};

let totalNumber = computed(() => {
    let total = 0;
    tableData.value.forEach((item: any) => {
        total += Number(item.planAmount);
    });
    return total;
});

//保存填写
let saveTableData = async () => {
    loadingShow.value = true;
    let totalPlanAmount = 0;
    tableData.value.forEach((item: any) => {
        totalPlanAmount += Number(item.planAmount);
    });
    let params = {
        ...searchForm.value,
        planYear: props.planYear,
        totalPlanAmount: totalPlanAmount,
        yearAmountList: tableData.value
    };
    if (!searchForm.value.workGroupId) {
        window.$message.error("请先选择班组！");
        return false;
    }
    await SAVE_YEAR_PLAN_AMOUNT_LIST({ ...params }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("保存成功");
            onSearch();
        }
    });
    loadingShow.value = false;
};
</script>
