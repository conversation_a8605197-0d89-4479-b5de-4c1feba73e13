import { computed, defineComponent, ref, watchEffect } from "vue";
import { type FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { POST_IM_PRODUCTION_SCHEDULE_CHANGE_LINE } from "@/api/application/plasticMes";
import { GET_INJECT_PLASTIC_MACHINE_LIST } from "@/api/application/machine";

export default defineComponent({
    name: "PlasticMesIMProductionScheduleChangeLine",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        // 表单数据
        interface FormDataProps {
            [key: string]: any;
        }

        const formRef = ref<FormInst | null>(null);

        // 获取表单选项
        const machineIdOptions = ref<any[]>([]);

        const getFormOptions = async () => {
            await GET_INJECT_PLASTIC_MACHINE_LIST({}).then((res) => {
                machineIdOptions.value = (res.data.data.records ?? []).map((item: any) => {
                    return {
                        label: item.machineName,
                        value: item.id
                    };
                });
            });
        };

        watchEffect(async () => {
            if (show.value) await getFormOptions();
        });

        const initFormData: FormDataProps = {
            machineId: null,
            changeReason: null
        };

        const formRules = computed(() => ({
            machineId: [{ required: true, message: "请选择新注塑机", trigger: ["blur", "change"] }],
            changeReason: [{ required: true, message: "请输入换线原因", trigger: ["input", "blur"] }]
        }));

        const formData = ref(cloneDeep(initFormData));

        const clearForm = () => {
            formData.value = cloneDeep(initFormData);
        };

        const onClose = () => {
            clearForm();
            changeModalShow(false);
            emit("refresh");
        };

        const onSubmit = async () => {
            const validateError = await formRef.value?.validate((errors) => !!errors);
            if (validateError) return false;

            POST_IM_PRODUCTION_SCHEDULE_CHANGE_LINE({
                ids: props.configData.ids,
                ...formData.value
            }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("操作成功");
                    onClose();
                } else window.$message.error(res.data.msg);
            });
        };

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card title="换线生产" class="w-600px" closable onClose={onClose}>
                    <n-form
                        ref={formRef}
                        model={formData.value}
                        rules={formRules.value}
                        label-placement="left"
                        label-width="auto"
                    >
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi span={12} label="当前选择排产单">
                                {props.configData.ids}
                            </n-form-item-gi>
                            <n-form-item-gi span={12} label="新注塑机" path="machineId">
                                <n-select
                                    v-model:value={formData.value.machineId}
                                    options={machineIdOptions.value}
                                    clearable
                                    filterable
                                    placeholder="请选择新注塑机"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={12} label="换线原因" path="changeReason">
                                <n-input
                                    v-model:value={formData.value.changeReason}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入换线原因"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={12}>
                                <n-space>
                                    <n-button type="primary" onClick={onSubmit}>
                                        提交
                                    </n-button>
                                    <n-button onClick={onClose}>取消</n-button>
                                </n-space>
                            </n-form-item-gi>
                        </n-grid>
                    </n-form>
                </n-card>
            </n-modal>
        );
    }
});
