<template>
    <div>
        <n-tabs v-model:value="tabActive" animated type="bar" @before-leave="onTabBeforeLeave">
            <n-tab-pane :name="1" tab="产成品实际用量" />
            <n-tab-pane :name="2" tab="原材料实际用量" />
            <n-tab-pane :name="3" tab="混凝土实际用量" />
        </n-tabs>
        <div class="mt-2">
            <ReportingFillDayCommonActualAmountProduct v-if="tabActive === 1" />
            <ReportingFillDayCommonActualAmountMaterial v-if="tabActive === 2" />
            <ReportingFillDayCommonActualAmountConcrete v-if="tabActive === 3" />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import ReportingFillDayCommonActualAmountProduct from "./ReportingFillDayCommonActualAmountProduct.vue";
import ReportingFillDayCommonActualAmountMaterial from "./ReportingFillDayCommonActualAmountMaterial.vue";
import ReportingFillDayCommonActualAmountConcrete from "./ReportingFillDayCommonActualAmountConcrete.vue";
import { usePublic } from "@/hooks";

let { onTabBeforeLeave } = usePublic();

let tabActive = ref(1);
</script>
