<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-space class="mb">
                <n-button secondary type="primary" @click="openEditModal()">新增</n-button>
            </n-space>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <!--新增编辑-->
        <RepositoryInReceiptEdit
            v-model:show="editModal.show"
            :configData="editModal.configData"
            @refresh="getTableData()"
        />
        <!--同意入库-->
        <RepositoryInReceiptConfirm
            v-model:show="confirmModal.show"
            :configData="confirmModal.configData"
            @refresh="getTableData()"
        />
        <!--查看清单-->
        <RepositoryInReceiptItemList v-model:show="itemListModal.show" :configData="itemListModal.configData" />
        <!--查看详情-->
        <RepositoryInReceiptDetail v-model:show="detailModal.show" :configData="detailModal.configData" />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from "vue";
import { DataTableColumns, NButton, NInput, NText } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { TableActions } from "@/components/TableActions";
import { useCommonTable } from "@/hooks";
import { GET_REPOSITORY_IN_RECEIPT_LIST, REJECT_REPOSITORY_IN_RECEIPT } from "@/api/application/repository";
import RepositoryInReceiptEdit from "./RepositoryInReceiptEdit";
import RepositoryInReceiptConfirm from "./RepositoryInReceiptConfirm";
import RepositoryInReceiptItemList from "./RepositoryInReceiptItemList";
import RepositoryInReceiptDetail from "./RepositoryInReceiptDetail";

interface RowProps {
    [key: string]: any;
}

onMounted(async () => {
    await getSearchOptions();
    getTableData();
});

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { label: "入库类型", prop: "enterType", type: "select" },
    { label: "入库状态", prop: "enterState", type: "select" }
]);

let searchOptions = ref<TableSearchbarOptions>({
    enterType: [
        { label: "手动入库", value: 1 },
        { label: "产成品入库", value: 2 },
        { label: "退料入库", value: 3 }
    ],
    enterState: [
        { label: "待入库", value: 1 },
        { label: "已确认", value: 2 },
        { label: "已拒绝", value: 3 }
    ]
});

let searchForm = ref<TableSearchbarData>({
    enterType: null,
    enterState: null
});

let getSearchOptions = async () => {};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "入库单号",
        key: "id",
        align: "center"
    },
    {
        title: "入库类型",
        key: "enterType",
        align: "center",
        render: (row) => {
            let enterTypeArray = [
                { label: "手动入库", value: 1 },
                { label: "产成品入库", value: 2 },
                { label: "退料入库", value: 3 }
            ];
            let enterTypeObj = enterTypeArray.find((item) => item.value === row.enterType);
            return enterTypeObj ? enterTypeObj.label : "/";
        }
    },
    { title: "申请人", key: "applyByName", align: "center" },
    { title: "所属部门", key: "applyDepName", align: "center" },
    {
        title: "入库物品清单",
        key: "recordItemList",
        align: "center",
        width: 100,
        render: (row) => {
            return h(NButton, { type: "primary", onClick: () => openItemListModal(row) }, () => "点击查看");
        }
    },
    {
        title: "入库状态",
        key: "enterState",
        align: "center",
        render: (row) => {
            if (row.enterState === 1) {
                return h(NText, { type: "primary" }, () => "待入库");
            } else if (row.enterState === 2) {
                return h(NText, { type: "success" }, () => "已确认");
            } else if (row.enterState === 3) {
                return h(NText, { type: "error" }, () => "已拒绝");
            } else {
                return h(NText, { type: "default" }, () => "/");
            }
        }
    },
    { title: "备注", key: "remark", align: "center" },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: "220",
        render(row) {
            if (row.enterState === 1) {
                return h(TableActions, {
                    type: "button",
                    buttonActions: [
                        {
                            label: "同意入库",
                            type: "success",
                            disabled: () => row.enterState !== 1,
                            tertiary: true,
                            onClick: () => openConfirmModal(row)
                        },
                        {
                            label: "拒绝入库",
                            type: "error",
                            disabled: () => row.enterState !== 1,
                            tertiary: true,
                            onClick: () => onRefuseIn(row)
                        }
                    ]
                });
            } else {
                return h(TableActions, {
                    type: "button",
                    buttonActions: [
                        {
                            label: "查看详情",
                            tertiary: true,
                            onClick: () => openDetailModal(row)
                        }
                    ]
                });
            }
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_REPOSITORY_IN_RECEIPT_LIST({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        ...searchForm.value
    }).then((res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data.records;
            tablePagination.itemCount = res.data.data.total;
            tableLoading.value = false;
        }
    });
};

// 搜索
let onSearch = () => {
    tablePagination.page = 1;
    tablePagination.pageSize = 10;
    getTableData();
};

// 新增编辑
let editModal = ref<{ show: boolean; configData: UnKnownObject }>({
    show: false,
    configData: {}
});

let openEditModal = (row?: RowProps) => {
    editModal.value = {
        show: true,
        configData: row ?? {}
    };
};

// 查看清单
let itemListModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

let openItemListModal = (row: RowProps) => {
    itemListModal.value = { show: true, configData: row };
};

// 确认拒绝
let confirmModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

let openConfirmModal = (row: RowProps) => {
    confirmModal.value = { show: true, configData: row };
};

let rejectReason = ref("");

let onRefuseIn = (row: RowProps) => {
    window.$dialog.warning({
        title: "提示",
        content: () => {
            return h("div", { class: "py-10px flex-y-center" }, [
                h(
                    "div",
                    {
                        class: "flex-fixed-120"
                    },
                    "请输入拒绝原因："
                ),
                h(NInput, {
                    value: rejectReason.value,
                    onUpdateValue: (v) => (rejectReason.value = v),
                    clearable: true,
                    placeholder: "请输入拒绝原因"
                })
            ]);
        },
        positiveText: "确认拒绝",
        negativeText: "我再想想",
        onPositiveClick: () => {
            if (!rejectReason.value) return window.$message.error("请输入拒绝原因");
            REJECT_REPOSITORY_IN_RECEIPT({ id: row.id, rejectReason: rejectReason.value }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("操作成功");
                    getTableData();
                }
            });
        }
    });
};

// 详情弹窗
const detailModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

const openDetailModal = (row: RowProps) => {
    detailModal.value = { show: true, configData: row };
};
</script>
