<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-600px" closable title="签订订单合同" @close="closeModal">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-grid :cols="24" :x-gap="16">
                        <n-form-item-gi :span="24" label="订单合同" path="contractId">
                            <n-select
                                v-model:value="formData.contractId"
                                :options="orderContractIdOptions"
                                label-field="pomName"
                                placeholder="请选择订单合同"
                                value-field="pomId"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="24">
                            <n-space>
                                <n-button type="primary" @click="onSubmit">提交</n-button>
                                <n-button @click="closeModal">取消</n-button>
                            </n-space>
                        </n-form-item-gi>
                    </n-grid>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { GET_FRAME_CONTRACT_LIST, GET_ORDER_CONTRACT_LIST, POST_BIND_ORDER_CONTRACT } from "@/api/application/power";

let props = defineProps({
    show: { type: Boolean, default: false },
    configData: { type: Object as PropType<any> }
});

let emits = defineEmits(["update:show", "refresh"]);

// 表单实例
let formRef = ref<FormInst | null>(null);

// 获取选项
let contractIdOptions = ref<any[]>([]);

// 获取选项
let orderContractIdOptions = ref<any[]>([]);

let getOptions = () => {
    GET_FRAME_CONTRACT_LIST({ current: 1, size: 9999, pfcReviewState: 3 }).then((res) => {
        contractIdOptions.value = res.data.data.records;
    });
    GET_ORDER_CONTRACT_LIST({
        // pfcId: props.configData.contractId,
        current: 1,
        size: 9999,
        pomReviewState: 3
    }).then((res) => {
        orderContractIdOptions.value = res.data.data.records;
    });
};

// 表单校验
let formRules = {
    contractId: { required: true, message: "请选择合同", trigger: ["blur", "change"] }
};

// 表单数据
interface FormDataProps {
    [key: string]: any;
}

let initFormData: FormDataProps = {
    contractId: null
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 监听
watch(
    () => ({ configData: props.configData, show: props.show }),
    (newVal) => {
        if (newVal.show) getOptions();
    },
    { deep: true }
);

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    emits("update:show", false);
};

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    POST_BIND_ORDER_CONTRACT({
        projectId: props.configData.projectId,
        nodeKey: props.configData.nextNodeKey,
        contractId: formData.value.contractId,
        contractType: 2
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("提交成功");
            closeModal();
            emits("refresh");
        }
    });
};
</script>
