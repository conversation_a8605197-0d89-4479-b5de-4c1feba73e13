<template>
    <div class="p-4">
        <!--<n-tabs type="line">-->
        <!--    <n-tab-pane name="待办" />-->
        <!--    <n-tab-pane name="已完成" />-->
        <!--</n-tabs>-->
        <n-data-table
            :columns="tableColumns"
            :data="tableData"
            :loading="tableLoading"
            :row-key="tableRowKey"
            :single-line="false"
            bordered
            remote
            striped
        />
    </div>
</template>

<script lang="ts" setup>
import { useCommonTable } from "@/hooks";
import { onMounted, ref } from "vue";
import type { DataTableColumns } from "naive-ui";
import { NDataTable } from "naive-ui";
import { GET_PROCESS_TASK_RUNNING } from "@/api/application/oa";

interface RowProps<T = string | null> {
    [propName: string]: any;
}

onMounted(() => {
    getTableData();
});

// 数据列表
let { tableRowKey, tableData, tableLoading } = useCommonTable<RowProps>("processInstanceId");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        title: "流程名称",
        key: "processDefinitionName",
        align: "center"
    },
    {
        title: "发起人",
        key: "startUsername",
        align: "center"
    },
    {
        title: "时间",
        key: "createTime",
        align: "center"
    }
]);

let getTableData = () => {
    tableLoading.value = true;
    GET_PROCESS_TASK_RUNNING({
        current: 1,
        size: 10
    }).then((res) => {
        if (res.data.code === 0) {
            console.log("流程实例", res.data.data);
            tableData.value = res.data.data.records;
            tableLoading.value = false;
        }
    });
};
</script>
