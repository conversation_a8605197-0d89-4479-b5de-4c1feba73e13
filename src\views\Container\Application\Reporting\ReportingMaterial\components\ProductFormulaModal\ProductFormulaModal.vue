<template>
    <div>
        <n-drawer
            v-model:show="show"
            :close-on-esc="false"
            :height="600"
            :mask-closable="false"
            placement="bottom"
            @update:show="changeModalShow(false)"
        >
            <n-drawer-content closable>
                <template #header>
                    <div class="flex-y-center">
                        <n-h3 class="m-0 p-0">
                            {{ `产成品规格型号：${props.configData.poleName} - ${props.configData.productModel}` }}
                        </n-h3>
                        <!--<n-button class="ml" type="warning" @click="onImportFormula">导入配方</n-button>-->
                    </div>
                </template>
                <n-tabs v-model:value="tabActive" type="bar">
                    <n-tab-pane name="原材料配方清单">
                        <FormulaSpecifications :configData="props.configData" tabType="manufactureSpecList" />
                    </n-tab-pane>
                    <n-tab-pane name="半成品配方清单">
                        <FormulaSpecifications :configData="props.configData" tabType="semiManufactureList" />
                    </n-tab-pane>
                    <n-tab-pane name="独立半成品">
                        <BindSemiProduct :configData="props.configData" />
                    </n-tab-pane>
                    <n-tab-pane name="独立原材料">
                        <FormulaRawMaterial :config-data="props.configData" />
                    </n-tab-pane>
                </n-tabs>
            </n-drawer-content>
        </n-drawer>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref } from "vue";
import FormulaRawMaterial from "./FormulaRawMaterial.vue";
import FormulaSpecifications from "./FormulaSpecifications.vue";
import BindSemiProduct from "./BindSemiProduct.vue";

let props = withDefaults(defineProps<{ show: boolean; configData: UnKnownObject }>(), { show: () => false });

let emits = defineEmits(["update:show", "refresh"]);

// 弹窗展示
let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

let changeModalShow = (show: boolean) => {
    emits("update:show", show);
    if (!show) {
        emits("refresh");
    }
};

let tabActive = ref("原材料配方清单");
</script>
