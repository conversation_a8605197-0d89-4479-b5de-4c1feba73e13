## 简介

永达信息管理系统前端项目

## 特性

-   使用 Vue3、Typescript、Pinia、Vite、UnoCSS 等前端前沿技术栈
-   使用 naive-ui 框架

## Git 贡献提交规范

-   参考 [vue](https://github.com/vuejs/vue/blob/dev/.github/COMMIT_CONVENTION.md)
    规范 ([Angular](https://github.com/conventional-changelog/conventional-changelog/tree/master/packages/conventional-changelog-angular))

    -   `feat` 增加新功能
    -   `fix` 修复问题/BUG
    -   `style` 代码风格相关无影响运行结果的
    -   `perf` 优化/性能提升
    -   `refactor` 重构
    -   `revert` 撤销修改
    -   `test` 测试相关
    -   `docs` 文档/注释
    -   `chore` 依赖更新/脚手架配置修改等
    -   `workflow` 工作流改进
    -   `ci` 持续集成
    -   `types` 类型定义文件更改
    -   `wip` 开发中

## 项目备注

-   大部分情况下 0 作为有效值，1 作为无效值
