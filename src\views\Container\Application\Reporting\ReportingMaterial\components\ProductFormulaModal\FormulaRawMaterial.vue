<template>
    <div>
        <n-data-table :columns="tableColumns" :data="tableData" :single-line="false" bordered striped />
        <div class="flex-x-center mt">
            <n-space>
                <!--<n-button type="warning" @click="onImportFormula">导入配方</n-button>-->
                <n-button type="primary" @click="addTableItem">新增一行</n-button>
                <n-button type="success" @click="onSubmit">保存全部</n-button>
            </n-space>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, ref, watchEffect } from "vue";
import type { DataTableColumns } from "naive-ui";
import { NButton, NInput, NSelect, NText } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { TableActions } from "@/components/TableActions";
import {
    GET_FORMULA_PAGE_LIST,
    GET_MANUFACTURE_SPEC_LIST,
    GET_MATERIAL_SPEC_PAGE_LIST,
    MANUFACTURE_IMPORT_FORMULA,
    SAVE_MANUFACTURE_SPEC_LIST
} from "@/api/application/reporting";
import { UnitSelector } from "@/views/Container/Application/Reporting/components";
import { useDicts } from "@/hooks";

let props = withDefaults(defineProps<{ configData: UnKnownObject }>(), {});

onMounted(async () => {
    await setDictLibs();
    await getMaterialSpecIdOptions();
});

// 字典操作
let { dictLibs, getDictLibs } = useDicts();

let setDictLibs = async () => {
    let dictName = ["common_units"];
    await getDictLibs(dictName);
};

// 获取原材料规格
let materialSpecIdOptions = ref<any[]>([]);

let getMaterialSpecIdOptions = async () => {
    await GET_MATERIAL_SPEC_PAGE_LIST({ current: 1, size: 1000, companyId: props.configData.companyId }).then((res) => {
        materialSpecIdOptions.value = (res.data.data.records ?? []).map((item: any) => {
            return {
                ...item,
                label: `${item.materialName} - ${item.spec}`,
                value: item.id
            };
        });
    });
};

// 表单数据
interface RowProps {
    materialSpecId: Nullable<string>; // 原材料
    unitPrice: string; // 单价
    unit: string; // 单位
    amount: string; // 用量
    deviationRatio: string; // 偏差比例
    consumeCoefficient: string; // 用量系数

    [key: string]: any;
}

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        title: "原材料",
        key: "materialSpecId",
        align: "center",
        render: (row) => {
            return h(NSelect, {
                options: materialSpecIdOptions.value,
                clearable: true,
                filterable: true,
                placeholder: "请选择原材料",
                value: row.materialSpecId,
                onUpdateValue: (v, o: any) => {
                    row.materialSpecId = v;
                    row.unit = o.unit;
                    row.unitPrice = o.unitPrice;
                    row.consumeCoefficient = o.consumeCoefficient;
                    row.deviationRatio = o.deviationRatio;
                }
            });
        }
    },
    {
        title: "单价",
        key: "unitPrice",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.unitPrice,
                onUpdateValue: (v) => (row.unitPrice = v)
            });
        }
    },
    {
        title: "价格计量单位",
        key: "unit",
        align: "center",
        render: (row) => {
            return h("div", { class: "flex-y-center" }, [
                h(NText, { class: "mr-2", type: "error" }, () => "*"),
                h(UnitSelector, {
                    type: "select",
                    value: row.unit,
                    options: dictLibs["common_units"] ?? [],
                    onSubmit: (v: any) => (row.unit = v)
                })
            ]);
        }
    },
    {
        title: "用量",
        key: "amount",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.amount,
                onUpdateValue: (v) => (row.amount = v)
            });
        }
    },
    {
        title: "用量系数",
        key: "consumeCoefficient",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.consumeCoefficient,
                onUpdateValue: (v) => (row.consumeCoefficient = v)
            });
        }
    },
    // 2023年9月8日单位变更需要对接数据-已完成
    {
        title: "用量计量单位",
        key: "amountUnit",
        align: "center",
        render: (row) => {
            return h("div", { class: "flex-y-center" }, [
                h(NText, { class: "mr-2", type: "error" }, () => "*"),
                h(UnitSelector, {
                    type: "select",
                    value: row.amountUnit,
                    options: dictLibs["common_units"] ?? [],
                    onSubmit: (v: any) => (row.amountUnit = v)
                })
            ]);
        }
    },
    {
        title: "偏差比例",
        key: "deviationRatio",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.deviationRatio,
                onUpdateValue: (v) => (row.deviationRatio = v)
            });
        }
    },
    {
        title: "状态",
        align: "center",
        key: "specDelFlag",
        width: "100",
        render: (row) => {
            if (row.specDelFlag === 1) {
                return h(NText, { type: "error" }, () => "已删除");
            } else {
                return h(NText, { type: "primary" }, () => "正常");
            }
        }
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: "80",
        render(row, index) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "删除",
                        tertiary: true,
                        type: "error",
                        onClick: () => deleteItem(index)
                    }
                ]
            });
        }
    }
]);

// 可编辑表单配置
let tableItem: RowProps = {
    materialSpecId: null,
    unitPrice: "",
    unit: "",
    amountUnit: "",
    amount: "",
    consumeCoefficient: "",
    deviationRatio: ""
};

let addTableItem = () => {
    tableData.value.push(cloneDeep(tableItem));
};

let deleteItem = (index: number) => {
    tableData.value.splice(index, 1);
};

let tableData = ref<RowProps[]>([]);

let clearFrom = () => {
    tableData.value = [];
};

let getTableData = () => {
    GET_MANUFACTURE_SPEC_LIST({ id: props.configData.id }).then((res) => {
        tableData.value = res.data.data;
    });
};

watchEffect(() => {
    if (props.configData.id) {
        getTableData();
    }
});

// 提交
let onSubmit = () => {
    let array = tableData.value.map((item) => {
        return {
            id: item.id ?? null,
            formulaId: item.formulaId ?? null,
            materialSpecId: item.materialSpecId,
            unitPrice: item.unitPrice,
            unit: item.unit,
            amountUnit: item.amountUnit,
            amount: item.amount,
            consumeCoefficient: item.consumeCoefficient,
            deviationRatio: item.deviationRatio,
            manufactureId: props.configData.id as string
        };
    });
    let unitCheck = array.some((item) => !item.unit || !item.amountUnit);
    if (unitCheck) return window.$message.error("请检查单位信息是否填写完整");
    SAVE_MANUFACTURE_SPEC_LIST({
        manufactureId: props.configData.id,
        manufactureSpecList: array
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("保存成功");
            clearFrom();
            getTableData();
        } else {
            window.$message.error(res.data.message);
        }
    });
};
</script>
