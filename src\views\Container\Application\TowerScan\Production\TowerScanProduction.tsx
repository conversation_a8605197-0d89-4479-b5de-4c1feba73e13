import { defineComponent, h, onMounted, reactive, ref } from "vue";
import {
    TableSearchbar,
    type TableSearchbarConfig,
    type TableSearchbarData,
    type TableSearchbarOptions
} from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import { TableActions } from "@/components/TableActions";
import {
    DELETE_IRON_PRODUCTION_PLAN,
    GET_IRON_PRODUCTION_PLAN_FILE_INFO,
    GET_IRON_PRODUCTION_PLAN_PAGE_LIST,
    GET_IRON_PRODUCTION_PLAN_HAS_FINISHED_QUANTITY
} from "@/api/application/TowerScan";
import TowerScanProductionImport from "./TowerScanProductionImport";
import TowerScanProductionReImport from "./TowerScanProductionReImport";
import { FilePreviewBeta } from "@/components/FilePreview";
import TowerScanProductionDetail from "./TowerScanProductionDetail";
import TowerScanProductionPush from "./TowerScanProductionPush";

export default defineComponent({
    name: "TowerScanProduction",
    setup(props) {
        // 搜索项
        const searchConfig = ref<TableSearchbarConfig>([
            { prop: "planName", label: "计划名称", type: "input" },
            {
                labelWidth: "110",
                prop: "planBeginDate",
                label: "计划开始日期",
                type: "date",
                dateFormat: "yyyy-MM-dd"
            },
            {
                labelWidth: "100",
                prop: "planEndDate",
                label: "计划结束日期",
                type: "date",
                dateFormat: "yyyy-MM-dd"
            },
            {
                prop: "planStatus",
                label: "计划状态",
                type: "select"
            }
        ]);

        const searchOptions = ref<TableSearchbarOptions>({
            planStatus: [
                { label: "未开始", value: 1 },
                { label: "进行中", value: 2 },
                { label: "已完成", value: 3 }
            ]
        });

        const getSearchOptions = async () => {};

        const searchForm = ref<TableSearchbarData>({
            planName: null,
            planBeginDate: null,
            planEndDate: null,
            planStatus: null
        });

        const onSearch = () => {
            tablePagination.page = 1;
            getTableData();
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            { title: "生产计划名称", key: "planName", align: "center" },
            { title: "操作人", key: "operByName", align: "center" },
            { title: "操作时间", key: "operTime", align: "center" },
            { title: "计划日期", key: "planDate", align: "center" },
            {
                title: "计划状态",
                key: "planStatus",
                align: "center",
                render: (row) => {
                    switch (row.planStatus) {
                        case 1:
                            return <n-text type="error">未开始</n-text>;
                        case 2:
                            return <n-text type="info">进行中</n-text>;
                        case 3:
                            return <n-text type="success">已完成</n-text>;
                        case 4:
                            return <n-text type="warning">已结束</n-text>;
                        default:
                            return <n-text>/</n-text>;
                    }
                }
            },
            {
                title: "角钢总重",
                key: "angleSteelWeight",
                align: "center",
                render: (row) => {
                    return <n-text type="info">{row.angleSteelWeight}kg</n-text>;
                }
            },
            {
                title: "钢板总重",
                key: "steelPlateWeight",
                align: "center",
                render: (row) => {
                    return <n-text type="info">{row.steelPlateWeight}kg</n-text>;
                }
            },
            {
                title: "计划总重",
                key: "totalPlannedWeight",
                align: "center",
                render: (row) => {
                    return <n-text type="info">{row.totalPlannedWeight}kg</n-text>;
                }
            },
            {
                title: "已完成总重",
                key: "completedWeight",
                align: "center",
                render: (row) => {
                    return <n-text type="info">{row.completedWeight}kg</n-text>;
                }
            },
            {
                title: "原始计划文件",
                key: "id",
                align: "center",
                render: (row) => {
                    return (
                        <n-button type="primary" text onClick={() => onPreview(row.originalFileId)}>
                            点击预览
                        </n-button>
                    );
                }
            },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 380,
                render: (row) => {
                    return (
                        <TableActions
                            type="button"
                            buttonActions={[
                                {
                                    label: "查看详情",
                                    tertiary: true,
                                    type: "primary",
                                    onClick: () => openDetailModal(row)
                                },
                                {
                                    label: "推送计划",
                                    tertiary: true,
                                    type: "success",
                                    disabled: () => row.planStatus !== 1,
                                    onClick: () => openPushModal(row)
                                },
                                {
                                    label: "重新导入",
                                    tertiary: true,
                                    type: "warning",
                                    disabled: () => row.planStatus === 3 || row.planStatus === 4,
                                    onClick: () => openReImportModal(row)
                                },
                                {
                                    label: "删除计划",
                                    tertiary: true,
                                    type: "error",
                                    disabled: () => row.planStatus !== 1,
                                    onClick: () => {
                                        handleBatchDelete(row.id);
                                    }
                                }
                            ]}
                        />
                    );
                }
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            const params = {
                current: tablePagination.page,
                size: tablePagination.pageSize,
                ...searchForm.value
            };

            GET_IRON_PRODUCTION_PLAN_PAGE_LIST(params).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        // 批量删除
        const handleBatchDelete = (id?: string) => {
            if (!id && tableSelection.value.length <= 0) return window.$message.warning("请选择要删除的数据");

            window.$dialog.warning({
                title: "警告",
                content: "确定要删除选中的生产计划吗？",
                positiveText: "确定",
                negativeText: "取消",
                onPositiveClick: () => {
                    DELETE_IRON_PRODUCTION_PLAN({
                        planIds: id ? id : tableSelection.value.join(",")
                    }).then((res) => {
                        if (res.data.code === 0) {
                            window.$message.success("删除成功");
                            getTableData();
                            changeTableSelection([]);
                        } else {
                            window.$message.error(res.data.message || "删除失败");
                        }
                    });
                }
            });
        };

        // 详情弹窗
        const detailModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const openDetailModal = (row: RowProps) => {
            detailModal.value = { show: true, configData: row };
        };

        // 导入弹窗
        const importShow = ref(false);
        const showImportModal = () => {
            importShow.value = true;
        };

        // beta预览
        const previewBetaModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const onPreview = (id?: string) => {
            if (id) {
                GET_IRON_PRODUCTION_PLAN_FILE_INFO({ id }).then((res) => {
                    const url = import.meta.env.VITE_PREVIEW_URL + res.data.data.url;
                    previewBetaModal.value = { show: true, configData: { ...res.data.data, url } };
                });
            } else {
                window.$message.error("预览失败");
            }
        };

        // 推送计划弹窗
        const pushModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const openPushModal = (row: RowProps) => {
            pushModal.value = { show: true, configData: row };
        };

        // 重新导入弹窗
        const reImportModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const openReImportModal = async (row: RowProps) => {
            try {
                const res = await GET_IRON_PRODUCTION_PLAN_HAS_FINISHED_QUANTITY({ planId: row.id });

                const { hasFinishedQuantity } = res.data.data;

                if (hasFinishedQuantity) {
                    // 构建提示信息
                    let content = "该计划已经在执行，重新导入将结束执行中的计划，是否继续？";

                    window.$dialog.warning({
                        title: "确认重新导入",
                        content,
                        positiveText: "继续",
                        negativeText: "取消",
                        onPositiveClick: () => {
                            reImportModal.value = { show: true, configData: row };
                        }
                    });
                } else {
                    // 直接打开重新导入弹窗
                    reImportModal.value = { show: true, configData: row };
                }
            } catch (error) {
                console.log("接口调用异常:", error);
                window.$message.error("校验失败");
            }
        };

        onMounted(async () => {
            await getSearchOptions();
            getTableData();
        });

        return () => (
            <div class="tower-scan-production-plan">
                <n-card>
                    <TableSearchbar
                        form={searchForm.value}
                        config={searchConfig.value}
                        options={searchOptions.value}
                        onSearch={onSearch}
                    />
                </n-card>
                <n-card class="mt">
                    <n-space class="mb">
                        <n-button type="primary" onClick={() => showImportModal()}>
                            导入生产计划
                        </n-button>
                        <n-button type="error" onClick={() => handleBatchDelete()}>
                            批量删除
                        </n-button>
                    </n-space>
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        pagination={tablePagination}
                        row-key={tableRowKey}
                        single-line={false}
                        bordered
                        remote
                        striped
                        onUpdate:checked-row-keys={changeTableSelection}
                    />
                </n-card>

                <TowerScanProductionImport
                    v-model:show={importShow.value}
                    onRefresh={() => {
                        getTableData();
                    }}
                />

                <FilePreviewBeta
                    v-model:show={previewBetaModal.value.show}
                    configData={previewBetaModal.value.configData}
                />

                <TowerScanProductionDetail
                    v-model:show={detailModal.value.show}
                    config-data={detailModal.value.configData}
                    onRefresh={getTableData}
                />

                <TowerScanProductionPush
                    v-model:show={pushModal.value.show}
                    config-data={pushModal.value.configData}
                    onRefresh={getTableData}
                />

                <TowerScanProductionReImport
                    v-model:show={reImportModal.value.show}
                    config-data={reImportModal.value.configData}
                    onRefresh={getTableData}
                />
            </div>
        );
    }
});
