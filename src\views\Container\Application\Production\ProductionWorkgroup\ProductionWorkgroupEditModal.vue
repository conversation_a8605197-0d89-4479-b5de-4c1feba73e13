<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card
                :title="configData.id ? '编辑班组' : '新增班组'"
                class="w-1000px"
                closable
                @close="changeModalShow(false)"
            >
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-grid :cols="12" x-gap="16">
                        <n-form-item-gi :span="6" label="所属车间" path="workShopId">
                            <n-cascader
                                v-model:value="formData.workShopId"
                                :options="workShopIdOptions"
                                class="w-100%"
                                clearable
                                filterable
                                label-field="workshopName"
                                placeholder="请选择所属车间"
                                value-field="id"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="班组名称" path="groupName">
                            <n-input
                                v-model:value="formData.groupName"
                                class="w-100%"
                                clearable
                                placeholder="请输入班组名称"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="班组长" path="groupLeader">
                            <UserSelector
                                v-model:value="formData.groupLeader"
                                class="w-100%"
                                key-name="username"
                                placeholder="请选择班组长"
                            />
                        </n-form-item-gi>
                    </n-grid>
                </n-form>
                <ProductionWorkgroupUserSelector v-model:value="formData.groupUserList" @confirm="onSubmit" />
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref, watchEffect } from "vue";
import type { FormInst } from "naive-ui";
import { NInput } from "naive-ui";
import { cloneDeep } from "lodash-es";
import {
    ADD_WORK_GROUP,
    GET_WORK_GROUP_BY_ID,
    GET_WORK_SHOP_PAGE_LIST,
    UPDATE_WORK_GROUP
} from "@/api/application/production";
import { UserSelector } from "@/components/UserSelector";
import { SpecCountSelector } from "@/views/Container/Application/PlasticMes/IMProduction/Tasks/components";
import ProductionWorkgroupUserSelector from "@/views/Container/Application/Production/ProductionWorkgroup/ProductionWorkgroupUserSelector.vue";

let props = withDefaults(defineProps<{ show: boolean; configData: UnKnownObject }>(), { show: () => false });

let emits = defineEmits(["update:show", "refresh"]);

let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

let changeModalShow = (show: boolean) => {
    emits("update:show", show);
    clearFrom();
};

onMounted(async () => {
    await getWorkShopIdOptions();
});

// 获取公司选项
let workShopIdOptions = ref<any[]>([]);

let getWorkShopIdOptions = async () => {
    await GET_WORK_SHOP_PAGE_LIST({ current: 1, size: 9999 }).then((res) => {
        workShopIdOptions.value = res.data.data.records || [];
    });
};

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {
    workShopId: { required: true, message: "请选择所属车间", trigger: ["blur", "change"] },
    groupName: { required: true, message: "请输入班组名称", trigger: ["input", "blur"] },
    groupLeader: { required: true, message: "请选择班组长", trigger: ["blur", "change"] }
};

// 表单数据
interface FormDataProps {
    [key: string]: any;
}

let initFormData: FormDataProps = {
    workShopId: null,
    groupName: "",
    groupLeader: null,
    groupUserList: []
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

let getDetail = async () => {
    await GET_WORK_GROUP_BY_ID({ id: props.configData.id }).then((res) => {
        if (res.data.code === 0) {
            let resData = res.data.data;
            formData.value = formData.value = {
                id: props.configData.id,
                workShopId: resData.workShopId,
                groupName: resData.groupName,
                groupLeader: resData.groupLeader,
                groupUserList: (resData.groupUserList ?? []).map((item: any) => {
                    return {
                        ...item,
                        jobType: String(item.jobType)
                    };
                })
            };
        }
    });
};

watchEffect(() => {
    if (props.show && props.configData.id) getDetail();
});

// 提交
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;

    if (props.configData.id) {
        UPDATE_WORK_GROUP({
            id: props.configData.id,
            ...formData.value
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("修改成功");
                changeModalShow(false);
                emits("refresh");
            } else window.$message.error(res.data.message);
        });
    } else {
        ADD_WORK_GROUP({ ...formData.value }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("新增成功");
                changeModalShow(false);
                emits("refresh");
            } else window.$message.error(res.data.message);
        });
    }
};
</script>
