<template>
    <div class="project-list-launch">
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-space class="mb">
                <n-button secondary type="primary" @click="openEditModal()">新增项目</n-button>
            </n-space>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <!--新增编辑-->
        <ProjectEdit :configData="editModal.configData" v-model:show="editModal.show" @refresh="getTableData" />
        <!--详情-->
        <ProjectDetailModal
            v-model:show="detailModal.show"
            :configData="detailModal.configData"
            @refresh="getTableData"
        />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, ref } from "vue";
import type { DataTableColumns, PaginationProps } from "naive-ui";
import { NText } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable, useDicts } from "@/hooks";
import { GET_PROJECT_LIST_LAUNCH } from "@/api/application/power";
import { ProjectDetailModal, ProjectEdit } from "../components";
import { TableActions } from "@/components/TableActions";

interface RowProps {
    [key: string]: any;
}

onMounted(async () => {
    await setDictLibs();
    getSearchOptions();
    getTableData();
});

let { dictLibs, getDictLibs, dictValueToLabel, dictValueToAll } = useDicts();

let setDictLibs = async () => {
    let dictName = ["node_status", "win_bind_status", "project_type", "project_status"];
    await getDictLibs(dictName);
};

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { prop: "projectType", type: "select", label: "项目类型" },
    { prop: "projectStatus", type: "select", label: "项目状态" },
    { prop: "projectName", type: "input", label: "关键词" }
]);

let searchOptions = ref<TableSearchbarOptions>({
    projectType: [],
    projectStatus: []
});

let searchForm = ref<TableSearchbarData>({
    projectType: null,
    projectStatus: null,
    projectName: null
});

let getSearchOptions = () => {
    searchOptions.value.projectType = dictLibs.project_type;
    searchOptions.value.projectStatus = dictLibs.project_status;
};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, changeTableSelection } =
    useCommonTable<RowProps>("projectId");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "项目编号",
        key: "projectNumber",
        align: "center"
    },
    {
        title: "项目名称",
        key: "projectName",
        align: "center",
        render: (row: RowProps) => {
            return h(NText, { type: "primary" }, () => row.projectName);
        }
    },
    {
        title: "招投标项目编号",
        key: "projectCode",
        align: "center",
        render: (row: RowProps) => {
            return row.projectCode || "暂无";
        }
    },
    {
        title: "最新进度",
        key: "nodeName",
        align: "center",
        render: (row: RowProps) => {
            if (row.nodeKey === "MatchContractOrderList") {
                return h(
                    NText,
                    { type: "primary", class: "cursor-pointer", onClick: () => openDetailModal(row) },
                    () => row.nodeName
                );
            } else {
                return row.nodeName;
            }
        }
    },
    {
        title: "项目负责人",
        key: "projectLeaderName",
        align: "center",
        render: (row: RowProps) => {
            return h(NText, { type: "primary" }, () => row.projectLeaderName || "暂无");
        }
    },
    {
        title: "节点负责人",
        key: "nodeDirectorName",
        align: "center",
        render: (row: RowProps) => {
            return h(NText, { type: "primary" }, () => row.nodeDirectorName || "暂无");
        }
    },
    {
        title: "项目市场类型",
        key: "projectType",
        align: "center",
        render(row: RowProps) {
            return dictValueToLabel(row.projectType, "project_type") || "暂无";
        }
    },
    {
        title: "需要投标",
        key: "winBidFlag",
        align: "center",
        render(row: RowProps) {
            if (row.winBidFlag === 0) {
                return "不需要";
            } else if (row.winBidFlag === 1) {
                return "需要";
            } else {
                return "暂无";
            }
        }
    },
    {
        title: "无合同",
        key: "nonContractFlag",
        align: "center",
        render(row: RowProps) {
            if (row.nonContractFlag === 0) {
                return "否";
            } else if (row.nonContractFlag === 1) {
                return "是";
            } else {
                return "暂无";
            }
        }
    },
    {
        title: "项目状态",
        key: "projectStatus",
        align: "center",
        width: 100,
        render: (row: RowProps) => {
            let item = dictValueToAll(row.projectStatus, "project_status");
            return h(NText, { style: `color:${item.color}` }, () => item.label || "暂无");
        }
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: 180,
        render(row: RowProps) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "查看",
                        type: "success",
                        tertiary: true,
                        onClick: () => openDetailModal(row)
                    },
                    {
                        label: "编辑",
                        tertiary: true,
                        onClick: () => {
                            if (row.projectId) openEditModal(row.projectId);
                        }
                    }
                ]
            });
        }
    }
]);

let tablePagination = ref<PaginationProps>({
    page: 1,
    pageSize: 10,
    itemCount: 0,
    pageSizes: [10, 50, 100],
    showSizePicker: true,
    showQuickJumper: true,
    displayOrder: ["size-picker", "pages", "quick-jumper"],
    onChange: (page: number) => {
        tablePagination.value.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.value.pageSize = pageSize;
        tablePagination.value.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_PROJECT_LIST_LAUNCH({
        current: tablePagination.value.page,
        size: tablePagination.value.pageSize,
        leaderFlag: 1,
        ...searchForm.value
    }).then((res) => {
        tableData.value = res.data.data.records || [];
        tablePagination.value.itemCount = res.data.data.total;
        tableLoading.value = false;
    });
};

let onSearch = () => {
    getTableData();
};

// 新增编辑
let editModal = ref<{ show: boolean; configData: UnKnownObject }>({
    show: false,
    configData: {}
});

let openEditModal = (id?: string | number | null) => {
    editModal.value.show = true;
    editModal.value.configData = {
        id: id || null
    };
};

// 详情
let detailModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

let openDetailModal = (row: RowProps) => {
    detailModal.value = { show: true, configData: row };
};
</script>
