<template>
    <div class="user-list">
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <process-approval
            v-model:show="approvalModal.show"
            :config-data="approvalModal.configData"
            @refresh="getTableData"
        />
        <ProcessDetail v-model:show="processDetailModal.show" :config-data="processDetailModal.configData" />
        <ProcessFallback
            v-model:show="fallbackModal.show"
            :config-data="fallbackModal.configData"
            @refresh="getTableData"
        />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from "vue";
import type { DataTableColumns } from "naive-ui";
import {
    TableSearchbar,
    TableSearchbarConfig,
    TableSearchbarData,
    TableSearchbarOptions
} from "@/components/TableSearchbar";
import { useCommonTable, useDicts } from "@/hooks";
import { GET_OA_MODEL_LIST, GET_PROCESS_TASK_RUNNING } from "@/api/application/oa";
import { TableActions } from "@/components/TableActions";
import { ProcessApproval, ProcessDetail, ProcessFallback } from "../components";

interface RowProps<T = string | null> {
    id: T | number;
    processInstanceId: T | number;
    processDefinitionName: T;
    name: T;
    createTime: T;
    businessName: T;
    startUsername: T;
    businessNumber: T;
    businessKey: T;

    [key: string]: any;
}

// 字典操作
let { dictLibs, getDictLibs } = useDicts();

let setDictLibs = async () => {
    let dictName = ["process_business_status"];
    await getDictLibs(dictName);
};

onMounted(async () => {
    await setDictLibs();
    getSearchOptions();
    getTableData();
});

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { prop: "modelId", type: "select", label: "流程名称", span: 2 },
    { prop: "paramProjectName", type: "input", label: "项目名称/主题", labelWidth: 120 },
    { prop: "status", type: "select", label: "流程状态" },
    { prop: "modelLabel", type: "input", label: "关键词" },
    { prop: "businessNumber", type: "input", label: "审批单号" },
    {
        prop: "beginStartTime",
        type: "datetime",
        label: "发起开始时间",
        dateFormat: "yyyy-MM-dd HH:mm:ss",
        labelWidth: "100"
    },
    {
        prop: "endStartTime",
        type: "datetime",
        label: "发起截止时间",
        dateFormat: "yyyy-MM-dd HH:mm:ss",
        labelWidth: "100"
    }
]);

let searchOptions = ref<TableSearchbarOptions>({
    status: [],
    modelId: []
});

let searchForm = ref<TableSearchbarData>({
    status: null,
    modelLabel: null,
    paramProjectName: null,
    businessNumber: null,
    beginStartTime: null,
    endStartTime: null
});

let getSearchOptions = () => {
    searchOptions.value.status = dictLibs.process_business_status;
    /*
     * 时间：2025年6月5日16:28:53
     * 来源：上海公司研发群
     * 内容：modelState: 1 后台让删这个
     */
    GET_OA_MODEL_LIST({ current: 1, size: 1000 }).then((res) => {
        if (res.data.code === 0) {
            searchOptions.value.modelId = res.data.data.records.map((item: any) => {
                return {
                    label: item.name,
                    value: item.id
                };
            });
        }
    });
};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("processInstanceId");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "审批单号",
        key: "businessNumber",
        align: "center",
        render: (row: RowProps) => {
            if (row.businessNumber != null) {
                return row.businessNumber;
            } else {
                return row.businessKey;
            }
        }
    },
    {
        title: "流程名称",
        align: "center",
        key: "processDefinitionName",
        render: (row: RowProps) => {
            if (row.businessName) {
                return `【项目名称:${row.businessName}】${row.startUsername}提交的${row.processDefinitionName}`;
            } else {
                return `${row.startUsername}提交的${row.processDefinitionName}`;
            }
        }
    },
    {
        title: "项目名称/主题",
        align: "center",
        key: "paramProjectName",
        render: (row: RowProps) => {
            return row.paramProjectName ?? "/";
            // return (row.elements || []).find((item) => item.modelName === "projectName")?.value || "暂无";
        }
    },
    {
        title: "发起人",
        key: "startUsername",
        align: "center"
    },
    {
        title: "节点名称",
        key: "name",
        align: "center",
        render(row) {
            return row.name || "暂无";
        }
    },
    {
        title: "创建时间",
        key: "createTime",
        align: "center"
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: 320,
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "查看单据",
                        tertiary: true,
                        type: "primary",
                        onClick: () => {
                            openProcessDetailModal(row);
                        }
                    },
                    {
                        label: "立即处理",
                        tertiary: true,
                        type: "success",
                        onClick: () => {
                            openApprovalModal(row);
                        }
                    }
                    // {
                    //     label: "回退任务",
                    //     tertiary: true,
                    //     type: "error",
                    //     onClick: () => openFallbackModal(row)
                    // }
                ]
            });
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_PROCESS_TASK_RUNNING({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        ...searchForm.value
    }).then((res) => {
        if (res.data.code === 0) {
            console.log("流程实例", res.data.data);
            tableData.value = res.data.data.records;
            tablePagination.itemCount = res.data.data.total;
            tableLoading.value = false;
        }
    });
};

// 搜索
let onSearch = () => {
    tablePagination.page = 1;
    tablePagination.pageSize = 10;
    getTableData();
};

// 处理审批
let approvalModal = ref<{ show: boolean; configData: any }>({
    show: false,
    configData: {}
});

let openApprovalModal = (row: RowProps) => {
    approvalModal.value.show = true;
    approvalModal.value.configData = row;
};

// 单据详情
let processDetailModal = ref<any>({
    show: false,
    configData: {}
});

let openProcessDetailModal = (row: any) => {
    processDetailModal.value.show = true;
    processDetailModal.value.configData = row;
};

// 回退任务
let fallbackModal = ref<any>({
    show: false,
    configData: {}
});

let openFallbackModal = (row: any) => {
    fallbackModal.value.show = true;
    fallbackModal.value.configData = row;
};
</script>
