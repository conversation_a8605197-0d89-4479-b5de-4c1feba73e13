<template>
    <div>
        <n-card>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-space>
                <n-button type="primary" @click="openScheduleModal()">
                    <template #icon>
                        <DynamicIcon icon="PlusCircleOutlined" />
                    </template>
                    <span>新建排产单</span>
                </n-button>
                <n-dropdown :options="buttonOptions" class="p!" trigger="hover">
                    <n-button type="warning" @click="onBatchPause()">
                        <span class="mr-1">暂停排产</span>
                        <DynamicIcon icon="DownOutlined" />
                    </n-button>
                </n-dropdown>
                <n-button type="primary" @click="openRawMaterialModal()">在场原材料清单</n-button>
                <n-button type="success" @click="onBatchPush()">批量推送排产单</n-button>
            </n-space>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                class="mt"
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <!--排产-->
        <PlasticMesIMProductionScheduleEdit
            v-model:show="scheduleModal.show"
            :config-data="scheduleModal.configData"
            @refresh="getTableData"
        />
        <!--在场原材料清单-->
        <PlasticMesIMProductionScheduleRawMaterial
            v-model:show="rawMaterialModal.show"
            :config-data="rawMaterialModal.configData"
        />
        <!--换线生产-->
        <PlasticMesIMProductionScheduleChangeLine
            v-model:show="changeLineModal.show"
            :config-data="changeLineModal.configData"
            @refresh="getTableData"
        />
        <!--查看详情-->
        <PlasticMesIMProductionScheduleDetail
            v-model:show="detailModal.show"
            :config-data="detailModal.configData"
            @refresh="getTableData"
        />
        <!--技术反馈详情-->
        <TechCheck v-model:show="techCheckModal.show" :config-data="techCheckModal.configData" />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, ref } from "vue";
import { DataTableColumns, NButton, NSpace, NText, PaginationProps } from "naive-ui";
import { useCommonTable } from "@/hooks";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import {
    DELETE_IM_PRODUCTION_SCHEDULE,
    GET_IM_PRODUCTION_SCHEDULE_LIST,
    POST_IM_PRODUCTION_SCHEDULE_CONTINUE,
    POST_IM_PRODUCTION_SCHEDULE_END,
    POST_IM_PRODUCTION_SCHEDULE_FINISH,
    POST_IM_PRODUCTION_SCHEDULE_PUSH,
    POST_IM_PRODUCTION_SCHEDULE_SUSPEND
} from "@/api/application/plasticMes";
import { DynamicIcon } from "@/components/DynamicIcon";
import PlasticMesIMProductionScheduleRawMaterial from "./PlasticMesIMProductionScheduleRawMaterial";
import PlasticMesIMProductionScheduleEdit from "./PlasticMesIMProductionScheduleEdit";
import PlasticMesIMProductionScheduleDetail from "./PlasticMesIMProductionScheduleDetail";
import PlasticMesIMProductionScheduleChangeLine from "./PlasticMesIMProductionScheduleChangeLine";
import { TableActions } from "@/components/TableActions";
import { TechCheck } from "./components";
import { useStoreUser } from "@/store";

const storeUser = useStoreUser();

// 搜索项
const searchConfig = ref<TableSearchbarConfig>([]);

const searchOptions = ref<TableSearchbarOptions>({});

const searchForm = ref<TableSearchbarData>({});

const getSearchOptions = async () => {};

// 数据列表
interface RowProps {
    [key: string]: any;
}

const { tableRowKey, tableData, tableLoading, tableSelection, changeTableSelection } = useCommonTable<RowProps>("id");

const tableColumns = ref<DataTableColumns<RowProps>>([
    { type: "selection" },
    { title: "订单号", key: "orderCode", align: "center", render: (row) => row.orderCode || "/" },
    { title: "排产单号", key: "id", align: "center", width: 190, render: (row) => row.id || "/" },
    {
        title: "生产规格",
        key: "moldingSpec",
        align: "center",
        render: (row) => h(NText, { type: "info" }, { default: () => row.moldingSpec || "/" })
    },
    { title: "生产注塑机", key: "machineName", align: "center", render: (row) => row.machineName || "/" },
    {
        title: "生产状态",
        key: "scheduleStatus",
        align: "center",
        render: (row) => {
            if (row.scheduleStatus === 0) {
                return h(NText, { type: "warning" }, { default: () => "未开始" });
            } else if (row.scheduleStatus === 1) {
                return h(NText, { type: "info" }, { default: () => "进行中" });
            } else if (row.scheduleStatus === 2) {
                return h(NText, { type: "warning" }, { default: () => "待确认完成" });
            } else if (row.scheduleStatus === 3) {
                return h(NText, { type: "success" }, { default: () => "已完成" });
            } else if (row.scheduleStatus === 4) {
                return h(NText, { type: "warning" }, { default: () => "已暂停" });
            } else if (row.scheduleStatus === 5) {
                const currentUser = storeUser.getUserData.sysUser?.username;
                if (currentUser === "admin") {
                    return h(
                        NButton,
                        {
                            type: "error",
                            text: true,
                            onClick: () => {
                                onDelete(row);
                            }
                        },
                        { default: () => "已结束，点击删除" }
                    );
                } else {
                    return h(NText, { type: "error" }, { default: () => "已结束" });
                }
            } else {
                return "/";
            }
        }
    },
    {
        title: "技术核对反馈",
        key: "techState",
        align: "center",
        width: 100,
        render: (row) => {
            if (row.techState === 1) {
                return h(NButton, { type: "primary", onClick: () => openTechCheckModal(row) }, () => "点击查看");
            } else {
                return "暂未反馈";
            }
        }
    },
    { title: "计划开始时间", key: "planBeginTime", align: "center", render: (row) => row.planBeginTime || "/" },
    { title: "计划结束时间", key: "planEndTime", align: "center", render: (row) => row.planEndTime || "/" },
    { title: "备注", key: "remark", align: "center", render: (row) => row.remark || "/" },
    {
        title: "操作",
        key: "actions",
        align: "center",
        fixed: "right",
        width: 370,
        render: (row) => {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: row.scheduleStatus === 0 ? "编辑排产" : "查看详情",
                        type: "default",
                        tertiary: true,
                        onClick: () => {
                            if (row.scheduleStatus === 0) {
                                openScheduleModal(row);
                            } else {
                                openDetailModal(row);
                            }
                        }
                    },
                    {
                        disabled: () => row.scheduleStatus !== 0 && row.scheduleStatus !== 1,
                        label: row.scheduleStatus === 0 ? "推送排产" : "换线生产",
                        type: "success",
                        tertiary: true,
                        onClick: () => {
                            if (row.scheduleStatus === 0) {
                                onBatchPush(row.id);
                            } else if (row.scheduleStatus === 1) {
                                openChangeLineModal(row.id);
                            }
                        }
                    },
                    {
                        disabled: () =>
                            row.scheduleStatus === 0 ||
                            row.scheduleStatus === 2 ||
                            row.scheduleStatus === 3 ||
                            row.scheduleStatus === 5,
                        label: row.scheduleStatus === 4 ? "继续排产" : "暂停排产",
                        type: row.scheduleStatus === 4 ? "primary" : "warning",
                        tertiary: true,
                        onClick: () => {
                            if (row.scheduleStatus === 4) {
                                onBatchContinue(row.id);
                            } else {
                                onBatchPause(row.id);
                            }
                        }
                    },
                    {
                        disabled: () => row.scheduleStatus === 3 || row.scheduleStatus === 5,
                        label: row.scheduleStatus === 2 ? "完成排产" : "结束排产",
                        type: "error",
                        tertiary: true,
                        onClick: () => {
                            if (row.scheduleStatus === 2) {
                                onBatchComplete(row.id);
                            } else {
                                onBatchEnd(row.id);
                            }
                        }
                    }
                ]
            });
        }
    }
]);

const tablePagination = ref<PaginationProps>({
    page: 1,
    pageSize: 10,
    itemCount: 0,
    pageSizes: [10, 50, 100],
    showSizePicker: true,
    showQuickJumper: true,
    displayOrder: ["size-picker", "pages", "quick-jumper"],
    onChange: (page: number) => {
        tablePagination.value.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.value.pageSize = pageSize;
        tablePagination.value.page = 1;
        getTableData();
    }
});

const getTableData = () => {
    tableLoading.value = true;
    GET_IM_PRODUCTION_SCHEDULE_LIST({
        current: tablePagination.value.page,
        size: tablePagination.value.pageSize,
        ...searchForm.value
    }).then((res) => {
        tableData.value = res.data.data.records || [];
        tablePagination.value.itemCount = res.data.data.total;
        tableLoading.value = false;
    });
};

const onSearch = () => {
    getTableData();
};

onMounted(async () => {
    await getSearchOptions();
    getTableData();
});

// 查看详情
const detailModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

const openDetailModal = (row: RowProps) => {
    detailModal.value = { show: true, configData: row };
};

// 排产
const scheduleModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

const openScheduleModal = (row?: RowProps) => (scheduleModal.value = { show: true, configData: row ?? {} });

// 操作按钮组
const buttonOptions = ref<any[]>([
    {
        key: "buttons",
        type: "render",
        render: () => {
            return h(NSpace, { vertical: true, class: "p-2" }, () => [
                h(NButton, { type: "warning", onClick: () => onBatchPause() }, () => "暂停排产"),
                h(NButton, { type: "success", onClick: () => onBatchContinue() }, () => "继续排产"),
                h(NButton, { type: "primary", onClick: () => openChangeLineModal() }, () => "换线排产"),
                h(NButton, { type: "error", onClick: () => onBatchEnd() }, () => "结束排产")
            ]);
        }
    }
]);

// 批量推送排产单
const onBatchPush = (id?: string | number) => {
    const ids = id ? [id] : tableSelection.value;
    if (ids.length <= 0) return window.$message.warning("请选择需要推送的排产单");
    window.$dialog.warning({
        title: "温馨提示",
        content: "是否推送排产单？推送之后该排产单将在计划开始时间被工人接收！",
        positiveText: "确定推送",
        negativeText: "取消",
        onPositiveClick: () => {
            POST_IM_PRODUCTION_SCHEDULE_PUSH({ ids: ids.join(",") }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("推送成功");
                } else {
                    window.$dialog.warning({
                        title: "温馨提示",
                        content: res.data.msg,
                        positiveText: "已知晓",
                        negativeText: "取消"
                    });
                }
                getTableData();
            });
        }
    });
};

// 批量暂停排产
const onBatchPause = (id?: string | number) => {
    const ids = id ? [id] : tableSelection.value;
    if (ids.length <= 0) return window.$message.warning("请选择需要暂停的排产单");
    window.$dialog.warning({
        title: "温馨提示",
        content: "确认暂停后，未生产的排产单将不会被工人接收，生产中的订单将会告知工人立即停止生产并返回当前完成数！",
        positiveText: "确定暂停",
        negativeText: "取消",
        onPositiveClick: () => {
            POST_IM_PRODUCTION_SCHEDULE_SUSPEND({ ids: ids.join(",") }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("暂停成功");
                } else {
                    window.$message.error(res.data.msg ?? "暂停失败");
                }
                getTableData();
            });
        }
    });
};

// 批量继续排产
const onBatchContinue = (id?: string | number) => {
    const ids = id ? [id] : tableSelection.value;
    if (ids.length <= 0) return window.$message.warning("请选择需要继续的排产单");
    window.$dialog.warning({
        title: "温馨提示",
        content: "是否继续开始排产单？确认之后排产单将按正常排产进行推送给工人。",
        positiveText: "确定继续",
        negativeText: "取消",
        onPositiveClick: () => {
            POST_IM_PRODUCTION_SCHEDULE_CONTINUE({ ids: ids.join(",") }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("继续成功");
                } else {
                    window.$message.error(res.data.msg ?? "继续失败");
                }
                getTableData();
            });
        }
    });
};

// 批量结束排产
const onBatchEnd = (id?: string | number) => {
    const ids = id ? [id] : tableSelection.value;
    if (ids.length <= 0) return window.$message.warning("请选择需要结束的排产单");
    window.$dialog.warning({
        title: "温馨提示",
        content: "确认结束后，生产中的排产单将强制完成并告知工人填写当前数据！",
        positiveText: "确定关闭",
        negativeText: "取消",
        onPositiveClick: () => {
            POST_IM_PRODUCTION_SCHEDULE_END({ ids: ids.join(","), endReason: "手动结束" }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("结束成功");
                } else {
                    window.$message.error(res.data.msg ?? "结束失败");
                }
                getTableData();
            });
        }
    });
};

// 批量完成排产
const onBatchComplete = (id?: string | number) => {
    const ids = id ? [id] : tableSelection.value;
    if (ids.length <= 0) return window.$message.warning("请选择需要完成的排产单");
    window.$dialog.warning({
        title: "温馨提示",
        content: "是否确认完成该排产单",
        positiveText: "确定完成",
        negativeText: "取消",
        onPositiveClick: () => {
            POST_IM_PRODUCTION_SCHEDULE_FINISH({ ids: ids.join(",") }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("完成成功");
                } else {
                    window.$message.error(res.data.msg ?? "完成失败");
                }
                getTableData();
            });
        }
    });
};

// 在场原材料清单
const rawMaterialModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

const openRawMaterialModal = () => {
    rawMaterialModal.value = { show: true, configData: {} };
};

// 技术反馈查看
const techCheckModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

const openTechCheckModal = (row: RowProps) => {
    techCheckModal.value = { show: true, configData: row };
};

// 换线生产
const changeLineModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

const openChangeLineModal = (id?: string | number) => {
    const ids = id ? [id] : tableSelection.value;
    if (ids.length <= 0) return window.$message.warning("请选择需要换线的排产单");
    changeLineModal.value = {
        show: true,
        configData: { ids: ids.join(",") }
    };
};

// 删除
const onDelete = (row: RowProps) => {
    window.$dialog.warning({
        title: "警告",
        content: "确认删除该条数据？该操作不可逆",
        positiveText: "确认删除",
        negativeText: "我再想想",
        onPositiveClick: () => {
            DELETE_IM_PRODUCTION_SCHEDULE({ ids: row.id }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("删除成功");
                    onSearch();
                }
            });
        }
    });
};
</script>
