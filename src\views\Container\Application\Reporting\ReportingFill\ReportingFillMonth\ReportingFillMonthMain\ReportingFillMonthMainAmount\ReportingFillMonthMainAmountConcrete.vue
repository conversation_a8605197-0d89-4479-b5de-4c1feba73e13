<template>
    <n-spin :show="loadingShow">
        <template #description>正在处理中，请耐心等候</template>
        <n-card>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                auto-search
                @componentClick="onComponentClick"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt">
            <div class="flex-y-center mb">
                <n-space>
                    <n-button secondary type="success" @click="saveTableData">保存填写</n-button>
                </n-space>
            </div>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
    </n-spin>
</template>

<script lang="ts" setup>
import { h, onMounted, ref, watchEffect } from "vue";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import { NButton, NInput, NInputGroup, NInputGroupLabel, NTooltip } from "naive-ui";
import {
    GET_CONFIG_WORK_GROUP_LIST,
    GET_MONTH_PLAN_CONCRETE_AMOUNT_LIST,
    SAVE_MONTH_PLAN_CONCRETE_AMOUNT_LIST
} from "@/api/application/reporting";
import { useThrottleFn } from "@vueuse/core";

let props = withDefaults(
    defineProps<{
        planMonth: string;
    }>(),
    {}
);

interface RowProps {
    [key: string]: any;
}

let loadingShow = ref(false);

onMounted(async () => {
    await getWorkGroupIdOptions();
    getTableData();
});

let getWorkGroupIdOptions = async () => {
    await GET_CONFIG_WORK_GROUP_LIST({}).then((res) => {
        searchOptions.value.workGroupId = (res.data.data ?? []).map((item: any) => {
            return { label: item.companyName + "-" + item.workshopName + "-" + item.groupName, value: item.id };
        });
    });
    searchForm.value.workGroupId = searchOptions.value.workGroupId[0].value;
};

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([{ label: "班组", type: "select", prop: "workGroupId", span: 2 }]);

let searchOptions = ref<TableSearchbarOptions>({ workGroupId: [] });

let searchForm = ref<TableSearchbarData>({ workGroupId: null });

let onSearch = () => {
    getTableData();
};

// 搜索栏自动保存逻辑
let autoSave = useThrottleFn(async () => {
    let params = {
        ...searchForm.value,
        planMonth: props.planMonth,
        monthAmountList: tableData.value
    };
    await SAVE_MONTH_PLAN_CONCRETE_AMOUNT_LIST({ ...params }).then((res) => {
        if (res.data.code === 0) window.$message.success("自动保存成功");
    });
}, 1000);

let onComponentClick = async (val: TableSearchbarData) => {
    // await autoSave();
};

// 数据列表
let { tableRowKey, tableData, tableLoading, changeTableSelection } = useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "名称",
        align: "center",
        key: "poleName",
        render: (row) => {
            return row.poleName ?? "未知";
        }
    },
    {
        title: "规格型号",
        align: "center",
        key: "productModel",
        render: (row) => {
            return row.productModel;
        }
    },
    {
        // 计划定额用量
        title: "定额用量合计",
        align: "center",
        key: "planConcreteQuotaAmount",
        render: (row) => {
            return `${row.planConcreteQuotaAmount}m³`;
        }
    },
    {
        title: "计划用量",
        align: "center",
        key: "planConcreteAmount",
        render: (row) => {
            return h(
                NTooltip,
                {},
                {
                    trigger: () => {
                        return h(NInputGroup, {}, () => [
                            h(NInput, {
                                value: row.planConcreteAmount,
                                onUpdateValue: (v) => (row.planConcreteAmount = v),
                                onFocus: () => {
                                    if (row.planConcreteAmount === "0") row.planConcreteAmount = "";
                                },
                                onBlur: () => {
                                    if (!row.planConcreteAmount) row.planConcreteAmount = "0";
                                }
                            }),
                            h(NInputGroupLabel, {}, () => [h("span", {}, "m³")])
                        ]);
                    },
                    default: () => {
                        return row.planConcreteAmount;
                    }
                }
            );
        }
    }
]);

let getTableData = () => {
    tableLoading.value = true;
    GET_MONTH_PLAN_CONCRETE_AMOUNT_LIST({
        ...searchForm.value,
        planMonth: props.planMonth
    }).then((res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data.monthAmountList ?? [];
        }
        tableLoading.value = false;
    });
};

watchEffect(() => {
    if (props.planMonth && searchForm.value.workGroupId) {
        getTableData();
    }
});

//保存填写
let saveTableData = async () => {
    loadingShow.value = true;
    let params = {
        ...searchForm.value,
        planMonth: props.planMonth,
        monthAmountList: tableData.value
    };
    if (!searchForm.value.workGroupId) {
        window.$message.error("请先选择班组！");
        return false;
    }
    await SAVE_MONTH_PLAN_CONCRETE_AMOUNT_LIST({ ...params }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("保存成功");
            onSearch();
        }
    });
    loadingShow.value = false;
};
</script>
