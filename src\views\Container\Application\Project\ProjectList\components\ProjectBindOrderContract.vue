<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-600px" closable title="绑定订单合同" @close="closeModal">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-form-item label="订单合同" path="orderContractId">
                        <n-select
                            v-model:value="formData.orderContractId"
                            :options="orderContractIdOptions"
                            label-field="pomName"
                            placeholder="请选择订单合同"
                            value-field="pomId"
                        />
                    </n-form-item>
                    <!--<n-form-item label="是否需要审批" path="isNeedApproval">-->
                    <!--    <n-radio-group v-model:value="formData.isNeedApproval">-->
                    <!--        <n-radio value="1">是</n-radio>-->
                    <!--        <n-radio value="0">否</n-radio>-->
                    <!--    </n-radio-group>-->
                    <!--</n-form-item>-->
                    <n-form-item>
                        <n-space>
                            <n-button type="primary" @click="onSubmit">提交</n-button>
                            <n-button @click="closeModal">取消</n-button>
                        </n-space>
                    </n-form-item>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { SUBMIT_BIND_ORDER_FORM } from "@/api/application/project";
import { GET_ORDER_CONTRACT_LIST } from "@/api/application/contract";

let props = defineProps({
    show: { type: Boolean, default: false },
    configData: { type: Object as PropType<any> },
    nodeKey: { type: String }
});

let emits = defineEmits(["update:show", "refresh"]);

// 表单实例
let formRef = ref<FormInst | null>(null);

// 获取选项
let orderContractIdOptions = ref<any[]>([]);

let getOptions = () => {
    GET_ORDER_CONTRACT_LIST({
        current: 1,
        size: 1000,
        pfcId: props.configData?.contractId || null
    }).then((res) => {
        orderContractIdOptions.value = res.data.data.records;
    });
};

// 表单校验
let formRules = {
    contractId: { required: true, message: "请选择框架合同", trigger: ["blur", "change"] }
};

// 表单数据
interface FormDataProps<T = string | null> {
    orderContractId: T | number;
    isNeedApproval: T | number;
}

let initFormData: FormDataProps = {
    orderContractId: null,
    isNeedApproval: "0"
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 监听
watch(
    () => ({ configData: props.configData, show: props.show }),
    (newVal) => {
        if (newVal.show) getOptions();
    },
    { deep: true }
);

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    emits("update:show", false);
};

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    SUBMIT_BIND_ORDER_FORM({
        projectId: props.configData.projectId,
        nodeKey: props.nodeKey ? props.nodeKey : props.configData.nextNodeKey,
        ...formData.value
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("提交成功");
            closeModal();
            emits("refresh");
        } else {
            window.$message.error(res.data.msg);
        }
    });
};
</script>
