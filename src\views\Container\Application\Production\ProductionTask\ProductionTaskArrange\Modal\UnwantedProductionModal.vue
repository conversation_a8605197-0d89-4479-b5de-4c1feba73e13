<template>
    <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
        <n-card class="w-600px" closable title="无需生产" @close="closeModal">
            <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left">
                <n-grid cols="24" x-gap="16">
                    <n-form-item-gi :span="12" path="ignoreCount">
                        <n-select
                            v-model:value="formData.potId"
                            :options="potIdOptions"
                            class="w-100%"
                            label-field="specification"
                            placeholder="请选择项目"
                            value-field="id"
                        />
                    </n-form-item-gi>
                    <n-form-item-gi :span="12" path="ignoreCount">
                        <n-input-number
                            v-model:value="formData.ignoreCount"
                            class="w-100%"
                            clearable
                            placeholder="请输入无需生产的数量"
                        />
                    </n-form-item-gi>
                </n-grid>
            </n-form>
            <n-space class="mt-30px">
                <n-button type="primary" @click="onSubmit">提交</n-button>
                <n-button secondary type="primary" @click="closeModal">取消</n-button>
            </n-space>
        </n-card>
    </n-modal>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import { cloneDeep } from "lodash-es";
import { useStoreUser } from "@/store";
import type { FormInst } from "naive-ui";
import { NSelect } from "naive-ui";
import { GET_PRODUCTION_ORDER_SPECIFICATIONS, PRODUCTION_ORDER_UNWANT } from "@/api/application/production";

let storeUser = useStoreUser();

let props = withDefaults(
    defineProps<{
        show: Boolean;
        configData: UnKnownObject;
    }>(),
    {
        show: () => false
    }
);
let emits = defineEmits(["update:show", "refresh"]);

// 表单实例
let formRef = ref<Nullable<FormInst>>(null);

// 表单数据
interface FormDataProps {
    potId: Nullable<string | number>;
    ignoreCount: number;
}

let initFormData: FormDataProps = {
    potId: null,
    ignoreCount: 0
};

let formData = ref(cloneDeep(initFormData));

// 表单校验规则
let formRules = {
    ignoreCount: { required: true, type: "number", message: "请输入无需生产的数量", trigger: ["input", "blur"] }
};

// 重置表单
let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 监听
watch(
    () => ({ show: props.show }),
    async (newVal) => {
        if (newVal.show) {
            await getOptions();
            await getPotIdOptions();
        }
    },
    { deep: true }
);

// 获取详情
let potIdOptions = ref([]);

let getPotIdOptions = async () => {
    await GET_PRODUCTION_ORDER_SPECIFICATIONS({
        poId: props.configData.poId
    }).then((res) => {
        if (res.data.code === 0) {
            potIdOptions.value = res.data.data;
        }
    });
};

// 表单选项
let getOptions = async () => {};

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    emits("update:show", false);
};

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    PRODUCTION_ORDER_UNWANT({
        ...formData.value
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("操作成功");
            closeModal();
            emits("refresh");
        }
    });
};
</script>
