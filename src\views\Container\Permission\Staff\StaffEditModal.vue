<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card :title="id ? '编辑用户' : '新增用户'" class="w-600px" closable @close="closeModal">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-form-item label="工号" path="username">
                        <n-input v-model:value="formData.username" class="w-100%" clearable placeholder="请输入工号" />
                    </n-form-item>
                    <n-form-item label="密码" path="password">
                        <n-input
                            v-model:value="formData.password"
                            class="w-100%"
                            clearable
                            placeholder="请输入密码"
                            type="password"
                        />
                    </n-form-item>
                    <n-form-item label="员工姓名" path="trueName">
                        <n-input
                            v-model:value="formData.trueName"
                            class="w-100%"
                            clearable
                            placeholder="请输入员工姓名"
                        />
                    </n-form-item>
                    <n-form-item label="手机号" path="phone">
                        <n-input v-model:value="formData.phone" class="w-100%" clearable placeholder="请输入手机号" />
                    </n-form-item>
                    <n-form-item label="所属组织架构" path="deptId">
                        <n-tree-select
                            v-model:value="formData.deptId"
                            :options="deptIdOptions"
                            clearable
                            filterable
                            key-field="id"
                            label-field="name"
                            placeholder="请选择所属组织架构"
                        />
                    </n-form-item>
                    <n-form-item label="所属职位" path="post">
                        <n-select
                            v-model:value="formData.post"
                            :options="postOptions"
                            label-field="postName"
                            multiple
                            placeholder="请选择所属职位"
                            value-field="postId"
                            @update:value="test"
                        />
                    </n-form-item>
                    <n-form-item label="所属角色" path="role">
                        <n-select
                            v-model:value="formData.role"
                            :options="roleOptions"
                            label-field="roleName"
                            multiple
                            placeholder="请选择所属角色"
                            value-field="roleId"
                        />
                    </n-form-item>
                    <n-form-item label="邮箱地址" path="email">
                        <n-input v-model:value="formData.email" class="w-100%" clearable placeholder="请输入邮箱地址" />
                    </n-form-item>
                    <n-form-item label="个人简介" path="individualResume">
                        <n-input
                            v-model:value="formData.individualResume"
                            class="w-100%"
                            clearable
                            placeholder="请输入个人简介"
                        />
                    </n-form-item>
                    <n-form-item label="联系地址" path="contactAddress">
                        <n-input
                            v-model:value="formData.contactAddress"
                            class="w-100%"
                            clearable
                            placeholder="请输入联系地址"
                        />
                    </n-form-item>
                    <n-form-item>
                        <n-space>
                            <n-button type="primary" @click="onSubmit">提交</n-button>
                            <n-button @click="closeModal">取消</n-button>
                        </n-space>
                    </n-form-item>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref, watch } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { ADD_USER, GET_DEPT_TREE, GET_POST_LIST, GET_ROLE_LIST, GET_USER_BY_ID, UPDATE_USER } from "@/api/permission";

let props = defineProps({
    show: { type: Boolean, default: false },
    id: { type: [String, Number] as PropType<any> },
    deptSelected: { type: [Array] as PropType<any[]> }
});

let emits = defineEmits(["update:show", "refresh"]);

onMounted(() => {
    getOptions();
});

let test = (val: any) => {
    console.log(val);
};

// 获取选项
let deptIdOptions = ref([]);
let postOptions = ref([]);
let roleOptions = ref([]);

let getOptions = () => {
    GET_DEPT_TREE({ deptName: "" }).then((res) => {
        deptIdOptions.value = res.data.data || [];
    });
    GET_POST_LIST({}).then((res) => {
        postOptions.value = res.data.data.records || [];
    });
    GET_ROLE_LIST({}).then((res) => {
        roleOptions.value = res.data.data.records || [];
    });
};

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = computed(() => {
    return {
        username: {
            required: true,
            message: "请输入工号",
            trigger: ["input", "blur"]
        },
        password: {
            required: !props.id,
            message: "请输入密码",
            trigger: ["input", "blur"]
        },
        trueName: {
            required: true,
            message: "请输入工号",
            trigger: ["input", "blur"]
        },
        phone: {
            required: true,
            message: "请输入手机号",
            trigger: ["input", "blur"]
        },
        deptId: {
            required: true,
            message: "请选择所属组织架构",
            trigger: ["blur", "change"]
        },
        post: {
            type: "array",
            required: true,
            message: "请选择所属职位",
            trigger: ["blur", "change"]
        },
        role: {
            type: "array",
            required: true,
            message: "请选择所属角色",
            trigger: ["blur", "change"]
        }
    };
});

// 表单数据
interface FormDataProps<T = string | null> {
    username: T;
    password: T;
    trueName: T;
    phone: T;
    email: T;
    individualResume: T;
    contactAddress: T;
    deptId: T;
    post: any[];
    role: any[];
}

let initFormData: FormDataProps = {
    username: null,
    password: null,
    trueName: null,
    phone: null,
    email: null,
    individualResume: null,
    contactAddress: null,
    deptId: null,
    post: [],
    role: []
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 编辑时获取详情
watch(
    () => ({ id: props.id, show: props.show }),
    (newVal) => {
        if (!newVal.id) formData.value.deptId = props.deptSelected?.[0] || null;
        if (newVal.show && newVal.id) getDetail();
    },
    { deep: true }
);

// 获取详情
let getDetail = () => {
    GET_USER_BY_ID({ id: props.id }).then((res) => {
        let rowItem: any = res.data.data;
        formData.value = {
            username: rowItem.username,
            password: "",
            trueName: rowItem.trueName,
            phone: rowItem.phone,
            email: rowItem.email,
            individualResume: rowItem.individualResume,
            contactAddress: rowItem.contactAddress,
            deptId: rowItem.deptId,
            post: rowItem.postList?.map((item: any) => item.postId) || [],
            role: rowItem.roleList?.map((item: any) => item.roleId) || []
        };
    });
};

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    emits("update:show", false);
};

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    if (!props.id) {
        ADD_USER({
            ...formData.value
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("新增成功");
                closeModal();
                emits("refresh");
            }
        });
    } else {
        UPDATE_USER({
            userId: props.id,
            ...formData.value
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("编辑成功");
                closeModal();
                emits("refresh");
            }
        });
    }
};
</script>
