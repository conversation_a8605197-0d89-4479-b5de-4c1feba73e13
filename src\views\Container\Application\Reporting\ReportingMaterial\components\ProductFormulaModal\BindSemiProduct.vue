<template>
    <div>
        <n-data-table :columns="tableColumns" :data="tableData" :single-line="false" bordered striped />
        <div class="flex-x-center mt">
            <n-space>
                <!--<n-button type="warning" @click="onImportFormula">导入配方</n-button>-->
                <n-button type="primary" @click="addTableItem">新增一行</n-button>
                <n-button type="success" @click="onSubmit">保存全部</n-button>
            </n-space>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, ref, watchEffect } from "vue";
import type { DataTableColumns } from "naive-ui";
import { NButton, NInput, NInputGroup, NInputGroupLabel, NSelect } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { TableActions } from "@/components/TableActions";
import {
    GET_SEMI_MANUFACTURE_PAGE_LIST,
    PRODUCT_GET_SEMI_LIST,
    PRODUCT_SAVE_SEMI_LIST
} from "@/api/application/reporting";
import { UnitSelector } from "@/views/Container/Application/Reporting/components";
import { useDicts } from "@/hooks";

let props = withDefaults(defineProps<{ configData: UnKnownObject }>(), {});

// 字典操作
let { dictLibs, getDictLibs } = useDicts();

let setDictLibs = async () => {
    let dictName = ["common_units"];
    await getDictLibs(dictName);
};

onMounted(async () => {
    await setDictLibs();
    await getSemiManufactureIdOptions();
});

// 获取半成品
let semiManufactureIdOptions = ref<any[]>([]);

let getSemiManufactureIdOptions = async () => {
    await GET_SEMI_MANUFACTURE_PAGE_LIST({ current: 1, size: 9999, companyId: props.configData.companyId }).then(
        (res) => {
            semiManufactureIdOptions.value = (res.data.data.records ?? []).map((item: any) => {
                return {
                    ...item,
                    label: `${item.productModel}-${item.cementSuffix ?? ""}`,
                    value: item.id
                };
            });
        }
    );
};

// 表单数据
interface RowProps {
    semiManufactureId: Nullable<string>; // 半成品
    amount: string; // 用量

    [key: string]: any;
}

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        title: "半成品",
        key: "semiManufactureId",
        align: "center",
        render: (row) => {
            return h(NSelect, {
                options: semiManufactureIdOptions.value,
                clearable: true,
                filterable: true,
                placeholder: "请选择半成品",
                value: row.semiManufactureId,
                onUpdateValue: (v, o: any) => {
                    row.semiManufactureId = v;
                    row.semiUnit = o?.semiUnit ?? null;
                }
            });
        }
    },
    // 2023年9月8日单位变更需要对接数据-已处理
    {
        title: "用量",
        key: "amount",
        align: "center",
        render: (row) => {
            return h(NInputGroup, {}, () => [
                h(NInput, { value: row.amount, onUpdateValue: (v) => (row.amount = v) }),
                h(NInputGroupLabel, {}, () => [
                    h(UnitSelector, {
                        type: "text",
                        value: row.semiUnit,
                        options: dictLibs["common_units"] ?? []
                    })
                ])
            ]);
        }
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: "80",
        render(row, index) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "删除",
                        tertiary: true,
                        type: "error",
                        onClick: () => deleteItem(index)
                    }
                ]
            });
        }
    }
]);

// 可编辑表单配置
let tableItem: RowProps = {
    semiManufactureId: null,
    amount: "1"
};

let addTableItem = () => {
    tableData.value.push(cloneDeep(tableItem));
};

let deleteItem = (index: number) => {
    tableData.value.splice(index, 1);
};

let tableData = ref<RowProps[]>([]);

let clearFrom = () => {
    tableData.value = [];
};

let getTableData = () => {
    PRODUCT_GET_SEMI_LIST({ id: props.configData.id }).then((res) => {
        tableData.value = res.data.data.manufactureSemiItemList;
    });
};

watchEffect(() => {
    if (props.configData.id) {
        getTableData();
    }
});

// 提交
let onSubmit = () => {
    let array = tableData.value.map((item) => {
        return {
            id: item.id ?? null,
            manufactureId: props.configData.id,
            semiManufactureId: item.semiManufactureId,
            amount: item.amount
        };
    });
    PRODUCT_SAVE_SEMI_LIST({
        manufactureId: props.configData.id,
        manufactureSemiItemList: array
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("保存成功");
            clearFrom();
            getTableData();
        } else {
            window.$message.error(res.data.message);
        }
    });
};
</script>
