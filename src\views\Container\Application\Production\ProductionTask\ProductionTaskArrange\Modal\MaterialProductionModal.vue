<template>
    <div>
        <n-drawer v-model:show="show" :close-on-esc="false" :mask-closable="false" placement="right" width="666px">
            <n-drawer-content class="p-0!" closable title="领料生产">
                <n-grid :col="24" :x-gap="16" :y-gap="16" class="pb b-b-1px b-b-solid b-[#efeff5]">
                    <n-grid-item :span="12" class="text-center b-r-1px b-r-solid b-[#efeff5]">
                        <n-element class="text-20px color-[var(--primary-color)]" tag="div">
                            {{ productionData.reqDetailCount }}
                        </n-element>
                        <div class="text-14px mt-5px">已排产的生产任务</div>
                    </n-grid-item>
                    <n-grid-item :span="12" class="text-center">
                        <n-element class="text-20px color-[var(--primary-color)]" tag="div">
                            {{ productionData.materialCount }}KG
                        </n-element>
                        <div class="text-14px mt-5px">已领料数量</div>
                    </n-grid-item>
                </n-grid>
                <n-tabs v-model:value="tabActive" animated class="mt" type="line">
                    <template #suffix>
                        <n-button
                            v-if="tabActive === '排产记录'"
                            size="small"
                            type="primary"
                            @click="openProductionArrangeModal"
                        >
                            新建排产任务
                        </n-button>
                        <n-button
                            v-if="tabActive === '领料记录'"
                            size="small"
                            type="primary"
                            @click="openMaterialProcessModal"
                        >
                            发起领料申请
                        </n-button>
                    </template>
                    <n-tab-pane name="排产记录">
                        <n-table :single-line="false" class="mt text-center">
                            <thead>
                                <tr>
                                    <th>生产任务单号</th>
                                    <th>排产时间</th>
                                    <th>排产数量</th>
                                    <th>排产线路</th>
                                </tr>
                            </thead>
                            <tbody>
                                <template v-for="item in productionRecord">
                                    <tr v-for="citem in item.productReqDetailList">
                                        <td>{{ citem.id }}</td>
                                        <td>{{ citem.prodScheDate }}</td>
                                        <td>{{ citem.prodCount }}</td>
                                        <td>{{ citem.prodLineName }}</td>
                                    </tr>
                                </template>
                            </tbody>
                        </n-table>
                    </n-tab-pane>
                    <n-tab-pane name="领料记录">
                        <n-space></n-space>
                        <n-table :single-line="false" class="mt text-center">
                            <thead>
                                <tr>
                                    <th>发生时间</th>
                                    <!--<th>领料数量</th>-->
                                    <!--<th>领料来源</th>-->
                                    <th>审批单</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="item in materialRecord">
                                    <td>{{ item.createTime }}</td>
                                    <!--<td>建设中</td>-->
                                    <!--<td>建设中</td>-->
                                    <td>
                                        <n-element
                                            class="cursor-pointer color-[var(--primary-color)]"
                                            @click="openProcessDetailModal(item)"
                                        >
                                            点击查看
                                        </n-element>
                                    </td>
                                </tr>
                            </tbody>
                        </n-table>
                    </n-tab-pane>
                </n-tabs>
            </n-drawer-content>
        </n-drawer>
        <!--领料流程-->
        <MaterialProcess
            v-model:show="materialProcessModal.show"
            :config-data="materialProcessModal.configData"
            @refresh="getMaterialRecord"
        />
        <!--领料流程表单-->
        <ProcessDetail
            v-model:show="processDetailModal.show"
            :config-data="processDetailModal.configData"
            @refresh="getMaterialRecord"
        />
        <!--新增排产任务-->
        <ProductionArrange
            v-model:show="productionArrangeModal.show"
            :config-data="productionArrangeModal.configData"
            @refresh="getProductionRecord"
        />
    </div>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from "vue";
import { GET_PO_MATERIAL_RECORD, GET_PRODUCTION_ORDER_ARRANGE_RECORD_ORDER } from "@/api/application/production";
import { MaterialProcess, ProductionArrange } from "../Modal";
import { ProcessDetail } from "@/views/Container/Application/Process/components";

let props = withDefaults(defineProps<{ show: boolean; configData: UnKnownObject }>(), {
    show: () => false
});

let emits = defineEmits(["update:show", "refresh"]);

let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

watch(
    () => show.value,
    async (val) => {
        if (val) {
            await getProductionRecord();
            await getMaterialRecord();
        }
    },
    { immediate: true }
);

let tabActive = ref("排产记录");

// 获取排产记录
let productionRecord = ref<Production.ProductionRecordProps[]>([]);

let productionData = ref({
    materialCount: 0,
    reqDetailCount: 0
});

let getProductionRecord = async () => {
    await GET_PRODUCTION_ORDER_ARRANGE_RECORD_ORDER({ poId: props.configData.poId }).then((res) => {
        productionData.value = {
            materialCount: res.data.data.materialCount || 0,
            reqDetailCount: res.data.data.reqDetailCount || 0
        };
        productionRecord.value = res.data.data.productOrderModelList ?? [];
    });
};

// 获取领料记录
let materialRecord = ref<{ createTime: string }[]>([]);
let getMaterialRecord = async () => {
    await GET_PO_MATERIAL_RECORD({ poId: props.configData.poId }).then((res) => {
        materialRecord.value = res.data.data ?? [];
    });
};

// 发起领料流程弹窗
let materialProcessModal = ref<{ show: boolean; configData: Record<string, any> }>({
    show: false,
    configData: {}
});

let openMaterialProcessModal = () => {
    materialProcessModal.value.show = true;
    materialProcessModal.value.configData = props.configData;
};

// 领料流程流程弹窗
let processDetailModal = ref<{ show: boolean; configData: Record<string, any> }>({
    show: false,
    configData: {}
});

let openProcessDetailModal = (row: Record<string, any>) => {
    processDetailModal.value.show = true;
    processDetailModal.value.configData = row;
};

// 新增排产任务弹窗
let productionArrangeModal = ref<{ show: boolean; configData: Record<string, any> }>({
    show: false,
    configData: {}
});

let openProductionArrangeModal = () => {
    productionArrangeModal.value.show = true;
    productionArrangeModal.value.configData = props.configData;
};

// 弹窗状态更改
let changeModalShow = (show: boolean) => emits("update:show", show);
</script>
