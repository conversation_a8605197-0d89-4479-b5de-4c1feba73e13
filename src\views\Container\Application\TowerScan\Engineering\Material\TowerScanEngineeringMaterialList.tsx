import { computed, defineComponent, reactive, ref, watchEffect } from "vue";
import {
    TableSearchbar,
    type TableSearchbarConfig,
    type TableSearchbarData,
    type TableSearchbarOptions
} from "@/components/TableSearchbar";
import {
    GET_IRON_PROJECT_DETAIL,
    GET_IRON_PROJECT_MATERIAL_LIST,
    GET_IRON_PROJECT_TYPE_DETAIL
} from "@/api/application/TowerScan";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import TowerScanEngineeringMaterialImport from "./TowerScanEngineeringMaterialImport";

export default defineComponent({
    name: "TowerScanEngineeringMaterialList",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        // 获取详情
        interface DetailDataProps {
            [key: string]: any;
        }

        const detailData = ref<DetailDataProps>({});

        const getDetail = async () => {
            await GET_IRON_PROJECT_DETAIL({ projectId: props.configData.projectId }).then((res) => {
                if (res.data.code === 0) {
                    detailData.value = res.data.data;
                }
            });
            // 查询塔型信息
            GET_IRON_PROJECT_TYPE_DETAIL({ typeId: props.configData.id }).then((res) => {
                if (res.data.code === 0) {
                    detailData.value.type = res.data.data;
                }
            });
        };

        const onClose = () => {
            changeModalShow(false);
            emit("refresh");
        };

        // 搜索项
        const searchConfig = ref<TableSearchbarConfig>([
            { label: "零件号", prop: "materialCode", type: "input" },
            { label: "规格", prop: "materialSpec", type: "input" },
            { label: "尺寸", prop: "materialSize", type: "input" },
            { label: "状态", prop: "finishStatus", type: "select" },
            { label: "零件类别", prop: "materialClassify", type: "select" }
        ]);

        const searchOptions = ref<TableSearchbarOptions>({
            finishStatus: [
                { label: "待完成", value: 1 },
                { label: "已完成", value: 2 },
                { label: "进行中", value: 3 }
            ],
            materialClassify: [
                { label: "角钢", value: 1 },
                { label: "钢板", value: 2 },
                { label: "圆钢", value: 3 },
                { label: "圆管", value: 4 },
                { label: "槽钢", value: 5 }
            ]
        });

        const getSearchOptions = async () => {};

        const searchForm = ref<TableSearchbarData>({
            materialCode: null,
            materialSpec: null,
            materialSize: null,
            finishStatus: null,
            materialClassify: null
        });

        const onSearch = () => {
            tablePagination.page = 1;
            tablePagination.pageSize = 10;
            getTableData();
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            { title: "零件编号", key: "materialCode", align: "center" },
            {
                title: "零件类别",
                key: "materialClassify",
                align: "center",
                render: (row) => {
                    if (String(row.materialClassify) === "1") {
                        return <n-text type="info">角钢</n-text>;
                    } else if (String(row.materialClassify) === "2") {
                        return <n-text type="info">钢板</n-text>;
                    } else if (String(row.materialClassify) === "3") {
                        return <n-text type="info">圆钢</n-text>;
                    } else if (String(row.materialClassify) === "4") {
                        return <n-text type="info">圆管</n-text>;
                    } else if (String(row.materialClassify) === "5") {
                        return <n-text type="info">槽钢</n-text>;
                    } else {
                        return <n-text type="info">/</n-text>;
                    }
                }
            },
            {
                title: "状态",
                key: "finishStatus",
                align: "center",
                render: (row) => {
                    if (row.finishStatus === 1) {
                        return <n-text type="error">待完成</n-text>;
                    } else if (row.finishStatus === 2) {
                        return <n-text type="success">已完成</n-text>;
                    } else if (row.finishStatus === 3) {
                        return <n-text type="info">进行中</n-text>;
                    } else {
                        return <n-text>/</n-text>;
                    }
                }
            },
            { title: "材质", key: "materialQuality", align: "center", render: (row) => row.materialQuality ?? "/" },
            { title: "规格", key: "materialSpec", align: "center", render: (row) => row.materialSpec ?? "/" },
            { title: "尺寸", key: "materialSize", align: "center", render: (row) => row.materialSize ?? "/" },
            {
                title: "单基数量",
                key: "singleBaseQuantity",
                align: "center",
                render: (row) => row.singleBaseQuantity ?? "/"
            },
            {
                title: "多基数量",
                key: "multiBaseQuantity",
                align: "center",
                render: (row) => row.multiBaseQuantity ?? "/"
            },
            { title: "单重", key: "singleWeight", align: "center", render: (row) => row.singleWeight ?? "/" },
            { title: "多基总重", key: "multiWeight", align: "center", render: (row) => row.multiWeight ?? "/" },
            {
                title: "备注",
                key: "remark",
                align: "center",
                render: (row) => (!!row.remark && row.remark !== "" ? row.remark : "/")
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_IRON_PROJECT_MATERIAL_LIST({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                typeId: props.configData.id,
                ...searchForm.value
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        // 导入弹窗
        const importShow = ref(false);
        const showImportModal = () => {
            importShow.value = true;
        };

        watchEffect(async () => {
            if (show.value) {
                await getSearchOptions();
                if (props.configData.id) {
                    getDetail();
                    getTableData();
                }
            }
        });

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card
                    title={`材料表详情 - ${detailData.value?.type?.typeName ?? "/"} - ${
                        detailData.value?.projectName ?? "/"
                    }`}
                    class="w-1200px"
                    closable
                    onClose={onClose}
                >
                    <n-form label-placement="left" label-width="auto">
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi required span={4} label="工程名称">
                                {detailData.value?.projectName ?? "/"}
                            </n-form-item-gi>
                            <n-form-item-gi required span={4} label="工程简称">
                                {detailData.value?.projectAs ?? "/"}
                            </n-form-item-gi>
                            <n-form-item-gi required span={4} label="合同号">
                                {detailData.value?.contractNumber ?? "/"}
                            </n-form-item-gi>
                            <n-form-item-gi required span={4} label="塔型">
                                {detailData.value?.type?.typeName ?? "/"}
                            </n-form-item-gi>
                            <n-form-item-gi required span={4} label="电压等级">
                                {detailData.value?.powerLevel ?? "/"}
                            </n-form-item-gi>
                        </n-grid>
                    </n-form>
                    <n-card>
                        <TableSearchbar
                            form={searchForm.value}
                            config={searchConfig.value}
                            options={searchOptions.value}
                            onSearch={onSearch}
                            v-slots={{
                                buttons: () => (
                                    <n-button type="warning" onClick={showImportModal}>
                                        导入材料表
                                    </n-button>
                                )
                            }}
                        />
                    </n-card>
                    <n-card class="mt">
                        {/*<n-space class="mb">*/}
                        {/*    <n-button type="warning" onClick={showImportModal}>*/}
                        {/*        导入材料表*/}
                        {/*    </n-button>*/}
                        {/*</n-space>*/}
                        <n-data-table
                            columns={tableColumns.value}
                            data={tableData.value}
                            loading={tableLoading.value}
                            pagination={tablePagination}
                            row-key={tableRowKey}
                            single-line={false}
                            bordered
                            remote
                            striped
                            onUpdate:checked-row-keys={changeTableSelection}
                        />
                    </n-card>

                    <TowerScanEngineeringMaterialImport
                        v-model:show={importShow.value}
                        configData={props.configData}
                        onRefresh={() => {
                            getTableData();
                        }}
                    />
                </n-card>
            </n-modal>
        );
    }
});
