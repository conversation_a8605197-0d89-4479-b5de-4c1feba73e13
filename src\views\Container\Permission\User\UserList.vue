<template>
    <div class="user-list flex">
        <n-card class="flex-fixed-300" hoverable>
            <div class="flex-y-center mb">
                <n-input v-model:value="deptTreePattern" placeholder="搜索" />
                <n-button class="ml-2" type="default" @click="cancelTreeSelect">查看全部</n-button>
            </div>
            <n-tree
                v-model:selected-keys="deptSelectKeys"
                :cancelable="false"
                :data="deptTree"
                :pattern="deptTreePattern"
                :show-irrelevant-nodes="false"
                block-line
                key-field="id"
                label-field="name"
                selectable
                @update:selected-keys="selectDeptNode"
            />
        </n-card>
        <div class="flex-1 ml-4">
            <n-card hoverable>
                <table-searchbar
                    v-model:form="searchForm"
                    :config="searchConfig"
                    :options="searchOptions"
                    @reset="searchReset"
                    @search="onSearch"
                />
            </n-card>
            <n-card class="mt" hoverable>
                <n-space class="mb">
                    <n-button secondary type="primary" @click="openEditModal()">新增</n-button>
                    <n-button secondary type="success" @click="openImportModal()">批量导入</n-button>
                    <n-button secondary type="warning" @click="openChangeDeptModal()">批量修改部门</n-button>
                    <!--<n-button secondary type="error" @click="onDelete()">批量删除</n-button>-->
                </n-space>
                <n-data-table
                    :columns="tableColumns"
                    :data="tableData"
                    :loading="tableLoading"
                    :pagination="tablePagination"
                    :row-key="tableRowKey"
                    :single-line="false"
                    bordered
                    remote
                    striped
                    @update:checked-row-keys="changeTableSelection"
                />
            </n-card>
            <user-edit-modal
                v-model:dept-selected="deptSelectKeys"
                v-model:id="editModal.id"
                v-model:show="editModal.show"
                @refresh="getTableData"
            />
        </div>
        <!--批量导入-->
        <n-modal v-model:show="importModal.show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-500px" closable title="批量导入" @close="importModal.show = false">
                <n-upload v-model:file-list="fileList" directory-dnd @before-upload="onBeforeUpload">
                    <n-upload-dragger>
                        <div class="mb-4">
                            <dynamic-icon icon="UploadOutlined" size="40" />
                        </div>
                        <n-p class="text-16px">点击或拖动文件到该区域上传</n-p>
                    </n-upload-dragger>
                </n-upload>
                <n-button class="mt-10px" text type="primary" @click="onDownloadTemplate">
                    点击此处下载批量导入模板
                </n-button>
            </n-card>
        </n-modal>
        <!--批量修改部门-->
        <ChangeUsersDept v-model:show="changeDeptModal.show" :user-ids="tableSelection" @refresh="getTableData" />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from "vue";
import type { DataTableColumns, UploadFileInfo } from "naive-ui";
import { NSpace, NTag } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { TableActions } from "@/components/TableActions";
import { DynamicIcon } from "@/components/DynamicIcon";
import { DELETE_USER, DELETE_USERS, GET_DEPT_TREE, GET_USER_LIST, IMPORT_USERS } from "@/api/permission";
import { useCommonTable } from "@/hooks";
import UserEditModal from "./UserEditModal.vue";
import { ChangeUsersDept } from "@/components/Business";

type RowProps = {
    userId: string | number;
    username: string;
    postList: any[];
    roleList: any[];
};

onMounted(() => {
    getDeptTree();
    getSearchOptions();
    getTableData();
});

// 获取组织树
let deptTree = ref([]);

let deptTreePattern = ref("");

let getDeptTree = () => {
    GET_DEPT_TREE({ deptName: "" }).then((res) => {
        deptTree.value = res.data.data || [];
    });
};

// 组织树选中
let deptSelectKeys = ref<(string | number)[]>([]);

let selectDeptNode = (keys: (string | number)[]) => {
    deptSelectKeys.value = keys;
    console.log(111, keys);
    onSearch();
};

let cancelTreeSelect = () => {
    deptSelectKeys.value = [];
    onSearch();
};

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    {
        prop: "username",
        type: "input",
        label: "工号"
    },
    {
        prop: "trueName",
        type: "input",
        label: "员工姓名"
    }
]);

let searchOptions = ref<TableSearchbarOptions>({});

let searchForm = ref<TableSearchbarData>({
    username: null,
    trueName: null
});

let getSearchOptions = () => {};

let onSearch = () => {
    getTableData();
};

let searchReset = () => {
    deptSelectKeys.value = [];
    onSearch();
};
// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("userId");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "工号",
        key: "username",
        align: "center"
    },
    {
        title: "员工姓名",
        key: "trueName",
        align: "center"
    },
    // {
    //     title: "手机号",
    //     key: "phone",
    //     align: "center"
    // },
    {
        title: "所属部门",
        key: "deptName",
        align: "center"
    },
    // {
    //     title: "所属角色",
    //     key: "roleList",
    //     align: "center",
    //     render: (row: RowProps) => {
    //         return row.roleList.map((item: any) => {
    //             return h(NTag, { type: "primary", class: "ml-1 mr-1" }, { default: () => item.roleName });
    //         });
    //     }
    // },
    {
        title: "所属职位",
        key: "postList",
        align: "center",
        render: (row: RowProps) => {
            return row.postList.map((item: any) => {
                return h(NTag, { type: "primary", class: "ml-1 mr-1" }, { default: () => item.postName });
            });
        }
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: 150,
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "编辑",
                        tertiary: true,
                        onClick: () => {
                            openEditModal(row.userId);
                        }
                    },
                    {
                        label: "删除",
                        tertiary: true,
                        type: "error",
                        onClick: () => {
                            onDelete(row.userId);
                        }
                    }
                ]
            });
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_USER_LIST({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        ...searchForm.value,
        deptId: deptSelectKeys.value.length > 0 ? deptSelectKeys.value[0] : null
    }).then((res) => {
        if (res.data.code === 0) {
            console.log("用户列表", res.data.data.records);
            tableData.value = res.data.data.records;
            tablePagination.itemCount = res.data.data.total;
            tableLoading.value = false;
        }
    });
};

// 新增编辑
let editModal = ref<{ show: boolean; id: string | number | null }>({
    show: false,
    id: null
});

let openEditModal = (id?: string | number | null) => {
    editModal.value.show = true;
    editModal.value.id = id || null;
};

// 删除
let onDelete = (id?: string | number) => {
    if (!id && tableSelection.value.length < 1) {
        window.$message.error("请选择要删除的数据");
        return false;
    }
    window.$dialog.warning({
        title: "警告",
        content: `确定删除${id ? "该" : "选中"}用户吗？`,
        positiveText: "删除",
        negativeText: "取消",
        onPositiveClick: () => {
            if (id) {
                DELETE_USER({ id: id }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("删除成功");
                        onSearch();
                    }
                });
            } else {
                DELETE_USERS({ ids: tableSelection.value.join(",") }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("删除成功");
                        onSearch();
                    }
                });
            }
        }
    });
};

// 批量导入
let importModal = ref<{ show: boolean }>({
    show: false
});

let openImportModal = () => {
    importModal.value.show = true;
};

let fileList = ref<UploadFileInfo[]>([]);

let onBeforeUpload = async (fileData: { file: UploadFileInfo; fileList: UploadFileInfo[] }) => {
    let formData = new FormData();
    formData.append("file", fileData.file.file as any);
    IMPORT_USERS(formData).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("导入成功");
            importModal.value.show = false;
            onSearch();
        } else {
            window.$message.error("导入失败，请检查后重试！");
        }
    });
};

let onDownloadTemplate = () => {
    window.location.href = import.meta.env.VITE_API_URL + "/admin/sys-file/local/user.xlsx";
};

// 批量修改部门
let changeDeptModal = ref<{ show: boolean }>({
    show: false
});

let openChangeDeptModal = () => {
    if (tableSelection.value.length < 1) {
        window.$message.error("请选择要变更部门的人员");
        return false;
    }
    changeDeptModal.value.show = true;
};
</script>
