import { computed, defineComponent, reactive, ref, watchEffect } from "vue";
import { GET_MATERIAL_FILL_HISTORY_PAGE_LIST } from "@/api/application/TowerScan";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";

interface RowProps {
    [key: string]: any;
}

export default defineComponent({
    name: "TowerScanEngineeringProcessesPartsExecutionHistory",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show"],
    setup(props, { emit }) {
        const show = computed({
            get: () => props.show,
            set: (val) => emit("update:show", val)
        });

        // 数据列表
        const { tableRowKey, tableData, tablePaginationPreset, tableLoading } = useCommonTable<RowProps>("id");

        // 渲染新旧数据对比
        const renderCompareField = (oldValue: any, newValue: any, isFlag = false) => {
            const oldDisplay = isFlag ? (oldValue == 1 ? "入库" : oldValue == 0 ? "不入库" : "/") : oldValue ?? "/";
            const newDisplay = isFlag ? (newValue == 1 ? "入库" : newValue == 0 ? "不入库" : "/") : newValue ?? "/";

            // 如果新旧值相同，只显示一个值
            if (oldDisplay === newDisplay) {
                return <n-text depth={1}>{newDisplay}</n-text>;
            }

            // 对于标志位字段，判断是否为真正的空值
            if (isFlag) {
                // 如果旧值是真正的空值（null、undefined、""），显示新增
                if (oldValue === null || oldValue === undefined || oldValue === "") {
                    return (
                        <div class="flex items-center justify-center gap-4px">
                            <n-text type="success" strong>
                                {newDisplay}
                            </n-text>
                            <n-tag type="success" size="small">
                                新增
                            </n-tag>
                        </div>
                    );
                }

                // 如果新值是真正的空值（null、undefined、""），显示删除
                if (newValue === null || newValue === undefined || newValue === "") {
                    return (
                        <div class="flex items-center justify-center gap-4px">
                            <n-text type="error" delete>
                                {oldDisplay}
                            </n-text>
                            <n-tag type="error" size="small">
                                删除
                            </n-tag>
                        </div>
                    );
                }
            } else {
                // 对于非标志位字段，如果只有旧值为空，显示新增
                if (!oldValue || oldValue === "") {
                    return (
                        <div class="flex items-center justify-center gap-4px">
                            <n-text type="success" strong>
                                {newDisplay}
                            </n-text>
                            <n-tag type="success" size="small">
                                新增
                            </n-tag>
                        </div>
                    );
                }

                // 如果只有新值为空，显示删除
                if (!newValue || newValue === "") {
                    return (
                        <div class="flex items-center justify-center gap-4px">
                            <n-text type="error" delete>
                                {oldDisplay}
                            </n-text>
                            <n-tag type="error" size="small">
                                删除
                            </n-tag>
                        </div>
                    );
                }
            }

            // 显示变化对比
            return (
                <div class="flex items-center justify-center gap-8px">
                    <n-text type="error" depth={2} delete>
                        {oldDisplay}
                    </n-text>
                    <n-icon size="14" color="#18a058">
                        <svg viewBox="0 0 24 24">
                            <path
                                fill="currentColor"
                                d="M4,11V13H16L10.5,18.5L11.92,19.92L19.84,12L11.92,4.08L10.5,5.5L16,11H4Z"
                            />
                        </svg>
                    </n-icon>
                    <n-text type="success" strong>
                        {newDisplay}
                    </n-text>
                </div>
            );
        };

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { title: "所属项目", key: "projectName", align: "center" },
            { title: "所属塔型", key: "typeName", align: "center" },
            { title: "零件编号", key: "materialCode", align: "center" },
            {
                title: "质检数量变化",
                key: "fillQuantity",
                align: "center",
                width: 160,
                render: (row) => renderCompareField(row.fillQuantityOld, row.fillQuantityNew)
            },
            {
                title: "工艺变化",
                key: "fillTechnique",
                align: "center",
                width: 200,
                render: (row) => renderCompareField(row.fillTechniqueNameOld, row.fillTechniqueNameNew)
            },
            // {
            //     title: "入库状态变化",
            //     key: "inFlag",
            //     align: "center",
            //     width: 140,
            //     render: (row) => renderCompareField(row.inFlagOld, row.inFlagNew, true)
            // },
            {
                title: "操作工变化",
                key: "belongUserNames",
                align: "center",
                width: 180,
                render: (row) => renderCompareField(row.belongUserNamesOld, row.belongUserNamesNew)
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            if (!props.configData.id) {
                return;
            }

            tableLoading.value = true;

            const params = {
                current: tablePagination.page,
                size: tablePagination.pageSize,
                taskMaterialFillId: props.configData.id
            };

            GET_MATERIAL_FILL_HISTORY_PAGE_LIST(params)
                .then((res) => {
                    tableLoading.value = false;
                    if (res.data.code === 0) {
                        tableData.value = res.data.data.records || res.data.data || [];
                        tablePagination.itemCount = res.data.data.total || 0;
                    } else {
                        tableData.value = [];
                        tablePagination.itemCount = 0;
                        window.$message?.error(res.data.msg || "获取历史记录失败");
                    }
                })
                .catch(() => {
                    tableLoading.value = false;
                    tableData.value = [];
                    tablePagination.itemCount = 0;
                    window.$message?.error("获取历史记录失败");
                });
        };

        const onClose = () => {
            show.value = false;
            tableData.value = [];
            tablePagination.page = 1;
            tablePagination.pageSize = 10;
            tablePagination.itemCount = 0;
        };

        // 监听弹窗显示状态和配置数据变化
        watchEffect(() => {
            if (show.value && props.configData.id) {
                tablePagination.page = 1;
                tablePagination.pageSize = 10;
                getTableData();
            }
        });

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card
                    title={`${props.configData?.materialCode ?? "/"}零件修改记录 - 填报时间: ${
                        props.configData?.fillDate ?? "/"
                    }`}
                    class="w-1200px"
                    closable
                    onClose={onClose}
                >
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        pagination={tablePagination}
                        row-key={tableRowKey}
                        single-line={false}
                        bordered
                        remote
                        striped
                    />
                </n-card>
            </n-modal>
        );
    }
});
