<template>
    <div class="flex">
        <n-card class="flex-fixed-200" content-style="padding:0" hoverable>
            <n-menu
                v-model:value="tabActive"
                :indent="20"
                :options="tabOptions"
                class="flex-fixed-150 border-r-1px border-[#E5E5E5]"
                mode="vertical"
                @update:value="changeTabActive"
            />
        </n-card>
        <n-card class="flex-1 ml" hoverable>
            <ReportingFillDayCommonPlanAmount v-if="tabActive === 'amount'" />
            <ReportingFillDayCommonPlanCost v-if="tabActive === 'cost'" />
        </n-card>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import ReportingFillDayCommonPlanAmount from "./ReportingFillDayCommonPlanAmount/ReportingFillDayCommonPlanAmount.vue";
import ReportingFillDayCommonPlanCost from "./ReportingFillDayCommonPlanCost.vue";

let tabActive = ref("amount");

let changeTabActive = (key: string) => {};

let tabOptions = ref<any[]>([
    { label: "计划用量", key: "amount" },
    { label: "计划人力资金", key: "cost" }
]);
</script>

<style lang="scss" scoped>
::v-deep(.n-menu) {
    .n-menu-item {
        &:first-child {
            margin-top: 0;
        }

        .n-menu-item-content {
            &:before {
                left: 0;
                right: 0;
            }
        }

        .n-menu-item-content--selected {
            &:before {
                border-right: 2px solid var(--n-item-text-color-active);
            }
        }
    }
}
</style>
