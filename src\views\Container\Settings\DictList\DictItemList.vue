<template>
    <div>
        <n-drawer v-model:show="show" :close-on-esc="false" :mask-closable="false" placement="right" width="666px">
            <n-card :bordered="false" closable title="字典项配置" @close="closeModal">
                <div class="flex-y-center mb-15px">
                    <n-p class="m-0">字典名称：{{ rowData.type }}</n-p>
                    <n-space class="ml-auto">
                        <n-button secondary type="primary" @click="openEditModal()">新增</n-button>
                        <n-button secondary type="error" @click="onDelete()">批量删除</n-button>
                    </n-space>
                </div>
                <n-data-table
                    :columns="tableColumns"
                    :data="tableData"
                    :loading="tableLoading"
                    :row-key="tableRowKey"
                    :single-line="false"
                    bordered
                    remote
                    striped
                    @update:checked-row-keys="changeTableSelection"
                />
            </n-card>
        </n-drawer>
        <dict-item-edit-modal
            :id="editModal.id"
            v-model:show="editModal.show"
            :dict-id="rowData.id"
            :dict-type="rowData.type"
            @refresh="getTableData"
        />
    </div>
</template>

<script lang="ts" setup>
import { h, ref, watch } from "vue";
import { useStoreUser } from "@/store";
import { DELETE_DICT_ITEM_BATCH, GET_DICT_ITEM_LIST_BY_TYPE } from "@/api/public";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import { NButton } from "naive-ui";
import { TableActions } from "@/components/TableActions";
import DictItemEditModal from "./DictItemEditModal.vue";

let storeUser = useStoreUser();

let props = defineProps({
    show: { type: Boolean, default: false },
    rowData: { type: Object as PropType<any> }
});

let emits = defineEmits(["update:show", "refresh"]);

// 监听
watch(
    () => ({ show: props.show }),
    (newVal) => {
        if (newVal.show) getTableData();
    },
    { deep: true }
);

// 数据列表
interface RowProps<T = string | number> {
    id: T | number;
    label: string;
    value: T | number;
    color?: string;
}

let { tableRowKey, tableData, tableLoading, tableSelection, changeTableSelection } = useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "字典项名称",
        key: "label",
        align: "center"
    },
    {
        title: "字典项值",
        key: "value",
        align: "center"
    },
    {
        title: "字典配色",
        key: "color",
        align: "center",
        width: 100,
        render: (row) => {
            if (row.color) {
                return h("div", { class: "flex-center" }, [h(NButton, { class: "w-30px h-30px", color: row.color })]);
            } else return "无";
        }
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: 150,
        render: (row: RowProps) => {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "编辑",
                        tertiary: true,
                        type: "primary",
                        onClick: () => {
                            openEditModal(row.id);
                        }
                    },
                    {
                        label: "删除",
                        tertiary: true,
                        type: "error",
                        onClick: () => {
                            onDelete(row.id);
                        }
                    }
                ]
            });
        }
    }
]);

let getTableData = () => {
    tableLoading.value = true;
    GET_DICT_ITEM_LIST_BY_TYPE({
        type: props.rowData.type || null
    }).then((res) => {
        if (res.data.code === 0) {
            console.log(res.data.data);
            tableData.value = res.data.data;
            tableLoading.value = false;
        }
    });
};

// 关闭弹窗
let closeModal = () => {
    emits("update:show", false);
};

// 新增编辑
let editModal = ref<{ show: boolean; id: string | number | null }>({
    show: false,
    id: null
});

let openEditModal = (id?: string | number | null) => {
    editModal.value.show = true;
    editModal.value.id = id || null;
};

// 删除
let onDelete = (id?: string | number) => {
    if (!id && tableSelection.value.length < 1) {
        window.$message.error("请选择要删除的数据");
        return false;
    }
    window.$dialog.warning({
        title: "警告",
        content: `确定删除${id ? "该" : "选中"}字典项吗？`,
        positiveText: "删除",
        negativeText: "取消",
        onPositiveClick: () => {
            let ids: (string | number)[];
            id ? (ids = [id]) : (ids = tableSelection.value);
            DELETE_DICT_ITEM_BATCH({ ids: ids.join(",") }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("删除成功");
                    getTableData();
                }
            });
        }
    });
};
</script>
