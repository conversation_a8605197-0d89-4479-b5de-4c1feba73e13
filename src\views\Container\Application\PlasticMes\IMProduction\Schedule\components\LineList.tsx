import { defineComponent, h, ref, watchEffect } from "vue";
import {
    GET_IM_PRODUCTION_SCHEDULE_LINE_DETAIL_LIST,
    POST_PRODUCTION_MOLDING_SCHEDULE_REPORT_FILL_FINISH
} from "@/api/application/plasticMes";
import type { DataTableColumns } from "naive-ui";
import { TableActions } from "@/components/TableActions";
import { LineDetail } from "../components";

export default defineComponent({
    name: "PlasticMesIMProductionScheduleLineList",
    props: {
        scheduleId: { type: String }
    },
    setup(props, { emit }) {
        const lineList = ref<any[]>([]);

        const getLineList = () => {
            GET_IM_PRODUCTION_SCHEDULE_LINE_DETAIL_LIST({ id: props.scheduleId }).then((res) => {
                lineList.value = res.data.data ?? [];
            });
        };

        const tableColumns = ref<DataTableColumns<any>>([
            { title: "注塑机名称", key: "machineName", align: "center" },
            { title: "注塑产品规格", key: "moldingSpec", align: "center" },
            {
                title: "注塑产品品种",
                key: "moldingVarietyName",
                align: "center",
                render: (row) => {
                    return row.moldingVarietyName ?? "/";
                }
            },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 240,
                render: (row) => {
                    return h(TableActions, {
                        type: "button",
                        buttonActions: [
                            {
                                label: "查看详情",
                                tertiary: true,
                                onClick: () => openDetailModal(row)
                            },
                            {
                                label: "完成注塑上报",
                                disabled: () => row.fillState === 2,
                                tertiary: true,
                                type: "success",
                                onClick: () => {
                                    window.$dialog.warning({
                                        title: "提示",
                                        content: "是否确认完成注塑上报？",
                                        positiveText: "确定",
                                        negativeText: "取消",
                                        onPositiveClick: () => {
                                            POST_PRODUCTION_MOLDING_SCHEDULE_REPORT_FILL_FINISH({
                                                id: row.id
                                            }).then((res) => {
                                                if (res.data.code === 0) {
                                                    window.$message.success("操作成功");
                                                    getLineList();
                                                }
                                            });
                                        }
                                    });
                                }
                            }
                        ]
                    });
                }
            }
        ]);

        const detailModal = ref<{ show: boolean; configData: any }>({ show: false, configData: {} });

        const openDetailModal = (row: any) => {
            detailModal.value.configData = row;
            detailModal.value.show = true;
        };

        watchEffect(async () => {
            if (props.scheduleId) getLineList();
        });

        return () => (
            <div>
                <n-data-table
                    columns={tableColumns.value}
                    data={lineList.value ?? []}
                    single-line={false}
                    bordered
                    remote
                    striped
                    max-height={250}
                />
                <LineDetail v-model:show={detailModal.value.show} config-data={detailModal.value.configData} />
            </div>
        );
    }
});
