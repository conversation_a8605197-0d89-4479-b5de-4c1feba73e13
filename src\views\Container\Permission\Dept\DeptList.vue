<template>
    <div class="menu-list">
        <n-card hoverable>
            <n-space class="mb">
                <n-button type="primary" @click="openEditModal()">新增</n-button>
                <n-button v-if="!displayMode" type="error" @click="onDelete()">批量删除</n-button>
                <n-button type="warning" @click="switchDisplay()">
                    {{ displayMode ? "切换列表模式" : "切换树形模式" }}
                </n-button>
            </n-space>
            <n-data-table
                v-if="!displayMode"
                :columns="tableColumns"
                :default-expanded-row-keys="['0']"
                :data="tableData"
                :loading="tableLoading"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
            <div v-else class="h-400px">
                <vue3TreeOrg
                    :data="treeData"
                    :props="{ id: 'id', pid: 'parentId', label: 'name', expand: 'expand', children: 'children' }"
                    center
                    collapsable
                    :default-expand-level="1"
                    scalable
                    disabled
                    :toolBar="{ scale: true, restore: true, expand: true, zoom: false, fullscreen: true }"
                />
            </div>
        </n-card>
        <dept-edit-modal v-model:id="editModal.id" v-model:show="editModal.show" @refresh="getTableData" />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, ref } from "vue";
import type { DataTableColumns } from "naive-ui";
import { NTag } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableActions } from "@/components/TableActions";
import { useCommonTable, useDicts } from "@/hooks";
import { DELETE_DEPT, DELETE_DEPTS, GET_DEPT_TREE } from "@/api/permission";
import DeptEditModal from "./DeptEditModal.vue";

interface RowProps<T = string | null> {
    id: T | number;
    type: T;
    parentName: T;
}

let { getDict } = useDicts();

let displayMode = ref<boolean>(false);

onMounted(async () => {
    await getDicts();
    getSearchOptions();
    getTableData();
});

// 字典
let deptTypeDict = ref([]);

let getDicts = async () => {
    deptTypeDict.value = await getDict("dept_type");
};

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    {
        prop: "deptName",
        type: "input",
        label: "组织名称"
    }
]);

let searchOptions = ref<TableSearchbarOptions>({});

let searchForm = ref<TableSearchbarData>({
    deptName: ""
});

let getSearchOptions = () => {};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, changeTableSelection } = useCommonTable<RowProps>("id");

let treeData = ref<any>();

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "组织架构名称",
        key: "name"
    },
    {
        title: "上级部门",
        key: "parentName",
        align: "center",
        render: (row) => {
            return row.parentName || "根节点";
        }
    },
    {
        title: "类型",
        key: "type",
        align: "center",
        width: 100,
        render: (row) => {
            return h(
                NTag,
                {
                    type:
                        row.type === "1"
                            ? "success"
                            : row.type === "2"
                            ? "warning"
                            : row.type === "3"
                            ? "info"
                            : row.type === "4"
                            ? "error"
                            : "default"
                },
                {
                    default: () => {
                        let deptType: any = deptTypeDict.value.find(
                            (item: { value: string }) => item.value === row.type
                        );
                        return deptType.label || "";
                    }
                }
            );
        }
    },
    {
        title: "排序",
        key: "sortOrder",
        align: "center"
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: 150,
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "编辑",
                        onClick: () => {
                            openEditModal(row.id);
                        }
                    },
                    {
                        label: "删除",
                        type: "error",
                        onClick: () => {
                            if (row.id) onDelete(row.id);
                        }
                    }
                ]
            });
        }
    }
]);

let getTableData = () => {
    tableLoading.value = true;
    GET_DEPT_TREE({
        ...searchForm.value
    }).then((res) => {
        treeData.value = res.data.data[0];
        tableData.value = res.data.data || [];
        tableLoading.value = false;
    });
};

let onSearch = () => {
    getTableData();
};

// 新增编辑
let editModal = ref<{ show: boolean; id: string | number | null }>({
    show: false,
    id: null
});

let openEditModal = (id?: string | number | null) => {
    editModal.value.show = true;
    editModal.value.id = id || null;
};

// 删除
let onDelete = (id?: string | number) => {
    if (!id && tableSelection.value.length < 1) {
        window.$message.error("请选择要删除的数据");
        return false;
    }
    window.$dialog.warning({
        title: "警告",
        content: `确定删除${id ? "该" : "选中"}组织架构吗？`,
        positiveText: "删除",
        negativeText: "取消",
        onPositiveClick: () => {
            if (id) {
                DELETE_DEPT({ id: id }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("删除成功");
                        onSearch();
                    }
                });
            } else {
                DELETE_DEPTS({ ids: tableSelection.value.join(",") }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("删除成功");
                        onSearch();
                    }
                });
            }
        }
    });
};

// 切换架构图
let switchDisplay = () => {
    displayMode.value = !displayMode.value;
};
</script>
