import { computed, defineComponent } from "vue";
import { WarehouseEntryRecord, IMReportRecord, IMSecondaryVerificationRecord } from "../components";

export default defineComponent({
    name: "PlasticMesIMProductionScheduleLDetail",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        const onClose = () => {
            changeModalShow(false);
            emit("refresh");
        };

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card title="查看产线详情" class="w-1000px" closable onClose={onClose}>
                    <n-tabs animated type="bar" defaultValue={1}>
                        <n-tab-pane name={1} tab="注塑工上报记录">
                            <IMReportRecord scheduleDetailId={props.configData.id} />
                        </n-tab-pane>
                        <n-tab-pane name={3} tab="二次核对记录">
                            <IMSecondaryVerificationRecord scheduleDetailId={props.configData.id} />
                        </n-tab-pane>
                        <n-tab-pane name={2} tab="入库记录">
                            <WarehouseEntryRecord scheduleDetailId={props.configData.id} />
                        </n-tab-pane>
                    </n-tabs>
                </n-card>
            </n-modal>
        );
    }
});
