<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card
                :title="configData.id ? '编辑供应商' : '新增供应商'"
                class="w-600px"
                closable
                @close="changeModalShow(false)"
            >
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-grid :cols="12" x-gap="16">
                        <n-form-item-gi :span="12" label="供应商名称" path="supplierName">
                            <n-input
                                v-model:value="formData.supplierName"
                                class="w-100%"
                                clearable
                                placeholder="请输入供应商名称"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="企业编码" path="socialCreditCode">
                            <n-input
                                v-model:value="formData.socialCreditCode"
                                class="w-100%"
                                clearable
                                placeholder="请输入企业编码"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="供应商状态" path="suplierStatus">
                            <n-radio-group v-model:value="formData.suplierStatus">
                                <n-space>
                                    <n-radio :value="1" label="正常合作" />
                                    <n-radio :value="0" label="终止合作" />
                                </n-space>
                            </n-radio-group>
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="列入黑名单" path="blacklistedOrNot">
                            <n-radio-group v-model:value="formData.blacklistedOrNot">
                                <n-space>
                                    <n-radio :value="1" label="是" />
                                    <n-radio :value="0" label="否" />
                                </n-space>
                            </n-radio-group>
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="可见范围" path="visibleRange">
                            <n-tree-select
                                v-model:value="formData.visibleRange"
                                :options="visibleRangeOptions"
                                clearable
                                filterable
                                multiple
                                key-field="id"
                                label-field="name"
                                placeholder="请选择可见范围"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="销售产品类型" path="saleProducutType">
                            <n-dynamic-tags type="primary" v-model:value="formData.saleProducutType" />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12">
                            <n-space>
                                <n-button type="primary" @click="onSubmit">提交</n-button>
                                <n-button @click="changeModalShow(false)">取消</n-button>
                            </n-space>
                        </n-form-item-gi>
                    </n-grid>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref, watchEffect } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { ADD_EDIT_SUPPLIER } from "@/api/application/purchase";
import { GET_DEPT_TREE } from "@/api/permission";

let props = withDefaults(defineProps<{ show: boolean; configData: UnKnownObject }>(), { show: () => false });

let emits = defineEmits(["update:show", "refresh"]);

let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

let changeModalShow = (show: boolean) => {
    emits("update:show", show);
    clearFrom();
};

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {
    supplierName: { required: true, message: "请输入供应商名称", trigger: ["input", "blur"] },
    socialCreditCode: { required: true, message: "请输入企业编码", trigger: ["input", "blur"] },
    suplierStatus: { required: true, message: "请选择供应商状态", trigger: ["blur", "change"], type: "number" },
    blacklistedOrNot: { required: true, message: "请选择是否列入黑名单", trigger: ["blur", "change"], type: "number" },
    visibleRange: { required: true, message: "请选择可见范围", trigger: ["blur", "change"], type: "array" },
    saleProducutType: { required: true, message: "请输入销售产品类型", trigger: ["blur", "change"], type: "array" }
};

// 表单数据
interface FormDataProps {
    [key: string]: any;
}

let initFormData: FormDataProps = {
    supplierName: "",
    socialCreditCode: "",
    suplierStatus: 1,
    blacklistedOrNot: 0,
    visibleRange: [],
    saleProducutType: []
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

let getDetail = () => {
    formData.value = cloneDeep({
        supplierName: props.configData.supplierName,
        socialCreditCode: props.configData.socialCreditCode,
        suplierStatus: props.configData.suplierStatus,
        blacklistedOrNot: props.configData.blacklistedOrNot,
        visibleRange: ((props.configData.visibleRange as any[]) ?? []).map((item: any) => item.id),
        saleProducutType: props.configData.saleProducutType
            ? (props.configData.saleProducutType as string).split(",")
            : []
    });
};

// 获取选项
let visibleRangeOptions = ref([]);

let getOptions = async () => {
    await GET_DEPT_TREE({ deptName: "" }).then((res) => {
        visibleRangeOptions.value = res.data.data || [];
    });
};

onMounted(async () => {
    await getOptions();
});

watchEffect(() => {
    if (props.show && props.configData.id) getDetail();
});

// 提交
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    ADD_EDIT_SUPPLIER({
        id: props.configData.id ?? null,
        ...formData.value,
        saleProducutType: formData.value.saleProducutType.join(",")
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success(props.configData.id ? "修改成功" : "新增成功");
            changeModalShow(false);
            emits("refresh");
        } else window.$message.error(res.data.message);
    });
};
</script>
