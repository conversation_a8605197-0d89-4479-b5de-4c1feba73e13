<template>
    <n-spin :show="loadingShow">
        <template #description>正在处理中，请耐心等候</template>
        <div class="flex">
            <n-card class="flex-fixed-200">
                <div class="flex-y-center mb">
                    <n-input v-model:value="treePattern" clearable placeholder="搜索" />
                </div>
                <n-tree
                    v-model:selected-keys="treeSelectKeys"
                    :cancelable="false"
                    :data="treeData"
                    :pattern="treePattern"
                    :show-irrelevant-nodes="false"
                    block-line
                    children-field="childrenList"
                    default-expand-all
                    key-field="id"
                    label-field="categoryName"
                    selectable
                    @update:selected-keys="selectTreeNode"
                />
            </n-card>
            <div class="flex-1 ml">
                <n-card>
                    <table-searchbar
                        auto-search
                        v-model:form="searchForm"
                        :config="searchConfig"
                        :options="searchOptions"
                        @search="onSearch"
                        @componentClick="onComponentClick"
                    />
                </n-card>
                <n-card class="mt">
                    <n-space class="mb">
                        <n-button secondary type="primary" @click="addTableItem">新增记录</n-button>
                        <n-button secondary type="success" @click="saveTableData">保存填写</n-button>
                    </n-space>
                    <n-data-table
                        :columns="tableColumns"
                        :data="tableData"
                        :loading="tableLoading"
                        :row-key="tableRowKey"
                        :single-line="false"
                        bordered
                        remote
                        striped
                        @update:checked-row-keys="changeTableSelection"
                    />
                </n-card>
            </div>
        </div>
    </n-spin>
</template>

<script lang="ts" setup>
import { h, onMounted, ref } from "vue";
import {
    GET_CONFIG_WORK_GROUP_LIST,
    GET_DAILY_OTHER_COST_LIST,
    GET_OTHER_CATEGORY_TREE_BY_TYPE,
    SAVE_DAILY_OTHER_COST_LIST
} from "@/api/application/reporting";
import type { DataTableColumns } from "naive-ui";
import { NButton, NInput, NTooltip } from "naive-ui";
import { useCommonTable, useReportingTree } from "@/hooks";
import { cloneDeep } from "lodash-es";
import {
    TableSearchbar,
    TableSearchbarConfig,
    TableSearchbarData,
    TableSearchbarOptions
} from "@/components/TableSearchbar";
import dayjs from "dayjs";
import { TableActions } from "@/components/TableActions";
import { useStoreReportingSearch } from "@/store";
import { CommonFileUploader } from "@/components/Uploader";
import { UPLOAD_FILES } from "@/api/public";
import { useThrottleFn } from "@vueuse/core";

let storeReportingSearch = useStoreReportingSearch();

interface RowProps {
    [key: string]: any;
}

let loadingShow = ref(false);

onMounted(async () => {
    await getTreeData();
    await getWorkGroupIdOptions();
    getTableData();
});

let certificateFlag = ref<Nullable<string | number>>(null);

// 树相关操作
let { treeData, treePattern, treeSelectKeys, treeAddDisabled, findLastLevel } = useReportingTree();

let getTreeData = async () => {
    await GET_OTHER_CATEGORY_TREE_BY_TYPE({
        wageType: 2
    }).then((res) => {
        if (res.data.code === 0) {
            treeData.value = treeAddDisabled(res.data.data ?? []);
            treeSelectKeys.value = [findLastLevel(treeData.value)[0]?.id];
            certificateFlag.value = findLastLevel(treeData.value)[0]?.certificateFlag;
        }
    });
};

let selectTreeNode = (keys?: (string | number)[], option?: any[]) => {
    treeSelectKeys.value = keys ?? [];
    certificateFlag.value = option?.[0]?.certificateFlag;
    onSearch();
};

// 搜索相关操作
let getWorkGroupIdOptions = async () => {
    await GET_CONFIG_WORK_GROUP_LIST({}).then((res) => {
        searchOptions.value.workGroupId = (res.data.data ?? []).map((item: any) => ({
            label: item.companyName + "-" + item.workshopName + "-" + item.groupName,
            value: item.id
        }));
    });
    // searchForm.value.workGroupId = searchOptions.value.workGroupId[0].value;
    searchForm.value.planDate = dayjs().format("YYYY-MM-DD");
    // if (storeReportingSearch.getSearchForm.workGroupId) {
    //     searchForm.value.workGroupId = storeReportingSearch.getSearchForm.workGroupId;
    // }
    if (storeReportingSearch.getSearchForm.planDate) {
        searchForm.value.planDate = storeReportingSearch.getSearchForm.planDate;
    }
};

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { label: "班组", type: "select", prop: "workGroupId", span: 2 },
    { label: "日期筛选", type: "date", dateFormat: "yyyy-MM-dd", prop: "planDate" }
]);

let searchOptions = ref<TableSearchbarOptions>({ workGroupId: [] });

let searchForm = ref<TableSearchbarData>({ workGroupId: null, planDate: null });

let onSearch = () => {
    // if (!searchForm.value.workGroupId) return window.$message.error("请先选择班组！");
    if (!searchForm.value.planDate) return window.$message.error("请先选择日期！");
    storeReportingSearch.setSearchForm({
        workGroupId: searchForm.value.workGroupId ?? null,
        planDate: searchForm.value.planDate ?? null
    });
    getTableData();
};

// 搜索栏自动保存逻辑
let autoSave = useThrottleFn(async () => {
    let otherCostList = tableData.value.map((item) => item);
    if (certificateFlag.value === 1) {
        let flag = otherCostList.some((item) => !item.fileIds);
        if (flag) return window.$message.error("请先上传凭证！");
    }

    SAVE_DAILY_OTHER_COST_LIST({
        workGroupId: searchForm.value.workGroupId,
        costDate: searchForm.value.planDate,
        otherCategoryId: treeSelectKeys.value[0],
        otherCostList: otherCostList
    }).then((res) => {
        if (res.data.code === 0) window.$message.success("自动保存成功");
    });
}, 1000);

let onComponentClick = async (val: TableSearchbarData) => {
    // await autoSave();
};

// 数据列表
let { tableRowKey, tableData, tableLoading, changeTableSelection } = useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    { type: "selection" },
    {
        title: "费用标题",
        align: "center",
        key: "costName",
        render: (row) => {
            return h(
                NTooltip,
                {},
                {
                    trigger: () => {
                        return h(NInput, { value: row.costName, onUpdateValue: (v) => (row.costName = v) });
                    },
                    default: () => {
                        return row.costName;
                    }
                }
            );
        }
    },
    {
        title: "费用金额（元）",
        align: "center",
        key: "cost",
        render: (row) => {
            return h(
                NTooltip,
                {},
                {
                    trigger: () => {
                        return h(NInput, {
                            value: row.cost,
                            onUpdateValue: (v) => (row.cost = v),
                            onFocus: () => {
                                if (row.cost === "0") row.cost = "";
                            },
                            onBlur: () => {
                                if (!row.cost) row.cost = "0";
                            }
                        });
                    },
                    default: () => {
                        return row.cost;
                    }
                }
            );
        }
    },

    {
        title: "备注",
        align: "center",
        key: "remark",
        render: (row) => {
            return h(
                NTooltip,
                {},
                {
                    trigger: () => {
                        return h(NInput, { value: row.remark, onUpdateValue: (v) => (row.remark = v) });
                    },
                    default: () => {
                        return row.remark;
                    }
                }
            );
        }
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: "180",
        render(row, index) {
            let buttonsList: any = [];
            if (certificateFlag.value === 0) {
                buttonsList = [
                    {
                        label: "删除",
                        tertiary: true,
                        type: "error",
                        onClick: () => deleteItem(index)
                    }
                ];
            } else {
                buttonsList = [
                    {
                        label: "删除",
                        tertiary: true,
                        type: "error",
                        onClick: () => deleteItem(index)
                    },
                    {
                        label: row.fileIds ? "查看附件" : "上传附件",
                        tertiary: true,
                        type: "warning",
                        onClick: () => onUpload(row)
                    }
                ];
            }
            return h(TableActions, {
                type: "button",
                buttonActions: [...buttonsList]
            });
        }
    }
]);

let getTableData = () => {
    tableLoading.value = true;
    GET_DAILY_OTHER_COST_LIST({
        workGroupId: searchForm.value.workGroupId,
        costDate: searchForm.value.planDate,
        otherCategoryId: treeSelectKeys.value[0]
    }).then((res) => {
        tableData.value = res.data.data;
        tableLoading.value = false;
    });
};

// 可编辑表单配置
let tableItem: RowProps = {
    id: "",
    costName: "",
    cost: "",
    remark: ""
};

//新增计划
let addTableItem = () => {
    if (!searchForm.value.planDate) {
        window.$message.error("请先选择日期！");
        return false;
    }
    tableData.value.push(cloneDeep(tableItem));
};

let deleteItem = (index: number) => {
    tableData.value.splice(index, 1);
};

// 保存全部
let saveTableData = () => {
    loadingShow.value = true;
    let otherCostList = tableData.value.map((item) => {
        return item;
    });
    if (certificateFlag.value === 1) {
        let flag = otherCostList.some((item) => !item.fileIds);
        if (flag) {
            window.$message.error("请先上传凭证！");
            return false;
        }
    }

    SAVE_DAILY_OTHER_COST_LIST({
        workGroupId: searchForm.value.workGroupId,
        costDate: searchForm.value.planDate,
        otherCategoryId: treeSelectKeys.value[0],
        otherCostList: otherCostList
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("保存成功");
            onSearch();
        }
        loadingShow.value = false;
    });
};

let fileList = ref<any[]>([]);

let onUpload = (row: RowProps) => {
    fileList.value = row.fileIds
        ? row.fileList.map((item: any, index: number) => {
              return {
                  id: item.id,
                  name: item.fileName,
                  fileId: item.id,
                  url: "/api/" + item.url,
                  status: "finished"
              };
          })
        : [];
    window.$dialog.warning({
        title: "上传凭证",
        positiveText: "上传",
        negativeText: "取消",
        content: () => {
            return h("div", { class: "py-10px" }, [
                h(CommonFileUploader, {
                    fileList: fileList.value,
                    onUpdateFileList: (v: any) => {
                        fileList.value = v;
                    }
                })
            ]);
        },
        onPositiveClick: () => {
            let formData = new FormData();
            fileList.value.forEach((file) => {
                if (file.file) {
                    formData.append("files", file.file);
                }
            });
            UPLOAD_FILES(formData).then((res) => {
                let array = res.data.filter((item: any) => item.code === 0);
                let dataArray = array.map((item: any) => {
                    return item.data;
                });
                if (row.fileIds) {
                    row.fileIds = row.fileIds + "," + dataArray.map((item: any) => item.fileId).join(",");
                } else {
                    row.fileIds = dataArray.map((item: any) => item.fileId).join(",");
                }
            });
        },
        onNegativeClick: () => {}
    });
};
</script>
