<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-600px" closable title="合同上传" @close="closeModal">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-form-item label="合同">
                        <FileValueUploader v-model:value="fileList" value-key="fileId" />
                    </n-form-item>
                    <n-form-item>
                        <n-space>
                            <n-button type="primary" @click="onSubmit">提交</n-button>
                            <n-button @click="closeModal">取消</n-button>
                        </n-space>
                    </n-form-item>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { SUBMIT_PROJECT_CONTRACT_FILE_FORM } from "@/api/application/project";

import { FileValueUploader } from "@/components/Uploader";

let props = defineProps({
    show: { type: Boolean, default: false },
    configData: { type: Object as PropType<any> }
});

let emits = defineEmits(["update:show", "refresh"]);

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {};

// 表单数据
interface FormDataProps<T = string | null> {}

let initFormData: FormDataProps = {};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    emits("update:show", false);
};

// 标书文件列表
let fileList = ref<any>(null);

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    SUBMIT_PROJECT_CONTRACT_FILE_FORM({
        projectId: props.configData.projectId,
        nodeKey: props.configData.nextNodeKey,
        fileList: fileList.value
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("合同信息已提交");
            closeModal();
            emits("refresh");
        }
    });
};
</script>
