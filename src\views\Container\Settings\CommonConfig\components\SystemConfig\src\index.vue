<template>
    <div class="ml-30px">
        <div class="text-20px mb-30px">系统配置</div>
        <div class="flex">
            <n-form ref="formRef" class="w-470px" label-placement="left">
                <n-form-item label="平台标题" path="platformTitle">
                    <n-input
                        v-model:value="storeGlobal.title.publicValue"
                        class="w-100%"
                        clearable
                        placeholder="请输入平台标题"
                    />
                </n-form-item>
                <n-form-item label="底部版权" path="bottomCopyright">
                    <n-input
                        v-model:value="storeGlobal.copyright.publicValue"
                        class="w-100%"
                        clearable
                        placeholder="请输入底部版权"
                    />
                </n-form-item>
                <!--<n-form-item label="验证码开关" path="verificationCodeSwitch">-->
                <!--    <n-switch v-model:value="formData.verificationCodeSwitch" checked-value="1" unchecked-value="0" />-->
                <!--</n-form-item>-->
                <!--<n-form-item label="系统维护模式" path="systemMaintenanceMode">-->
                <!--    <n-switch v-model:value="formData.systemMaintenanceMode" checked-value="1" unchecked-value="0" />-->
                <!--</n-form-item>-->
                <n-form-item>
                    <n-space>
                        <n-button type="primary" @click="onSubmit">应用保存</n-button>
                    </n-space>
                </n-form-item>
            </n-form>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { useStoreGlobal, useStoreUser } from "@/store";
import { UPDATE_SYSTEM_CONFIG_BATCH } from "@/api/settings";

let storeUser = useStoreUser();
let storeGlobal = useStoreGlobal();

// 提交表单
let onSubmit = async () => {
    UPDATE_SYSTEM_CONFIG_BATCH([storeGlobal.title, storeGlobal.copyright]).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("编辑成功");
        } else window.$message.error(res.data.msg);
    });
};
</script>
