<template>
    <div>
        <n-card content-style="padding-top:8px;padding-bottom:4px" hoverable>
            <n-tabs v-model:value="planMonth" animated size="small" type="bar">
                <n-tab-pane v-for="item in planMonthList" :name="item.name" :tab="item.tab" />
            </n-tabs>
        </n-card>
        <div class="flex mt">
            <n-card class="flex-fixed-200" content-style="padding:0" hoverable>
                <n-menu
                    v-model:value="tabActive"
                    :indent="20"
                    :options="tabOptions"
                    class="flex-fixed-150 border-r-1px border-[#E5E5E5]"
                    mode="vertical"
                    @update:value="changeTabActive"
                />
            </n-card>
            <n-card class="flex-1 ml" hoverable>
                <ReportingFillMonthMainAmount v-if="tabActive === 'amount'" :planMonth="planMonth" />
                <ReportingFillMonthMainCost v-if="tabActive === 'cost'" :planMonth="planMonth" />
            </n-card>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import dayjs from "dayjs";
import ReportingFillMonthMainAmount from "./ReportingFillMonthMainAmount/ReportingFillMonthMainAmount.vue";
import ReportingFillMonthMainCost from "./ReportingFillMonthMainCost.vue";

let tabActive = ref("amount");

let changeTabActive = (key: string) => {};

let tabOptions = ref<any[]>([
    { label: "产量计划", key: "amount" },
    { label: "计划人力资金", key: "cost" }
]);

// 当前月份
let planMonth = ref("");

let currentYear = dayjs().year();

let planMonthList = ref<any[]>([]);

let setPlanMonthList = () => {
    for (let i = 1; i <= 12; i++) {
        let month = String(i).padStart(2, "0");
        planMonthList.value.push({
            tab: `${month}月`,
            name: currentYear + month
        });
    }
    planMonth.value = planMonthList.value[0].name;
};

onMounted(() => {
    setPlanMonthList();
});
</script>

<style lang="scss" scoped>
::v-deep(.n-menu) {
    .n-menu-item {
        &:first-child {
            margin-top: 0;
        }

        .n-menu-item-content {
            &:before {
                left: 0;
                right: 0;
            }
        }

        .n-menu-item-content--selected {
            &:before {
                border-right: 2px solid var(--n-item-text-color-active);
            }
        }
    }
}
</style>
