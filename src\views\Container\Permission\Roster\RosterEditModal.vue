<template>
    <n-spin :show="loading">
        <template #description>努力加载中</template>
        <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="110">
            <n-tabs pane-class="pt-20px!" type="line">
                <n-tab-pane display-directive="show" name="基础信息">
                    <n-grid cols="12" x-gap="16">
                        <n-form-item-gi :span="12" label="头像">
                            <AvatarUploader v-model:file-list="avatarList" />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="姓名" path="trueName">
                            <n-input
                                v-model:value="formData.trueName"
                                class="w-100%"
                                clearable
                                placeholder="请输入姓名"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="昵称" path="nickname">
                            <n-input
                                v-model:value="formData.nickname"
                                class="w-100%"
                                clearable
                                placeholder="请输入昵称"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="职级" path="post">
                            <n-select
                                v-model:value="formData.post"
                                :options="postOptions"
                                label-field="postName"
                                placeholder="请选择职级"
                                value-field="postId"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="公司" path="companyId">
                            <n-select
                                v-model:value="formData.companyId"
                                :options="companyIdOptions"
                                clearable
                                disabled
                                filterable
                                label-field="company"
                                placeholder="请选择所属公司"
                                value-field="id"
                                @update:value="changeCompanyId"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="部门" path="deptId">
                            <n-tree-select
                                v-model:value="formData.deptId"
                                :options="deptIdOptions"
                                clearable
                                filterable
                                key-field="id"
                                label-field="name"
                                placeholder="请选择所属部门"
                                @update:value="changeDeptId"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="入职时间" path="timeOfEntry">
                            <n-date-picker
                                v-model:formatted-value="formData.timeOfEntry"
                                class="w-100%"
                                clearable
                                placeholder="请选择入职时间"
                                type="date"
                                value-format="yyyy-MM-dd"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="手机号" path="phone">
                            <n-input
                                v-model:value="formData.phone"
                                class="w-100%"
                                clearable
                                placeholder="请输入手机号"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="备注" path="remark">
                            <n-input
                                v-model:value="formData.remark"
                                class="w-100%"
                                clearable
                                placeholder="请输入备注"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="排序" path="showOrder">
                            <n-input-number
                                v-model:value="formData.showOrder"
                                class="w-100%"
                                clearable
                                placeholder="请输入排序"
                            />
                        </n-form-item-gi>
                    </n-grid>
                </n-tab-pane>
                <n-tab-pane display-directive="show" name="个人信息">
                    <n-grid cols="12" x-gap="16">
                        <n-form-item-gi :span="6" label="性别" path="sex">
                            <n-radio-group v-model:value="formData.sex">
                                <n-space>
                                    <n-radio value="男">男</n-radio>
                                    <n-radio value="女">女</n-radio>
                                </n-space>
                            </n-radio-group>
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="出生年月" path="dateOfBirth">
                            <n-date-picker
                                v-model:formatted-value="formData.dateOfBirth"
                                class="w-100%"
                                clearable
                                placeholder="请选择出生年月"
                                type="date"
                                value-format="yyyy-MM-dd HH:mm:ss"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="年龄" path="age">
                            <n-input-number
                                v-model:value="formData.age"
                                class="w-100%"
                                clearable
                                placeholder="请输入年龄"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="身份证号" path="idNumber">
                            <n-input
                                v-model:value="formData.idNumber"
                                class="w-100%"
                                clearable
                                placeholder="请输入身份证号"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="邮箱" path="email">
                            <n-input v-model:value="formData.email" class="w-100%" clearable placeholder="请输入邮箱" />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="个人简介" path="individualResume">
                            <n-input
                                v-model:value="formData.individualResume"
                                class="w-100%"
                                clearable
                                placeholder="请输入个人简介"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="籍贯" path="nativePlace">
                            <n-input
                                v-model:value="formData.nativePlace"
                                class="w-100%"
                                clearable
                                placeholder="请输入籍贯"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="政治面貌" path="politicsStatus">
                            <n-input
                                v-model:value="formData.politicsStatus"
                                class="w-100%"
                                clearable
                                placeholder="请输入政治面貌"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="婚姻状态" path="maritalStatus">
                            <n-input
                                v-model:value="formData.maritalStatus"
                                class="w-100%"
                                clearable
                                placeholder="请输入婚姻状态"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="户口类型" path="typeOfRegisteredPermanentResidence">
                            <n-input
                                v-model:value="formData.typeOfRegisteredPermanentResidence"
                                class="w-100%"
                                clearable
                                placeholder="请输入户口类型"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="健康状况" path="physicalCondition">
                            <n-input
                                v-model:value="formData.physicalCondition"
                                class="w-100%"
                                clearable
                                placeholder="请输入健康状况"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="体检时间" path="timeForPhysicalExamination">
                            <!--<n-date-picker-->
                            <!--    v-model:formatted-value="formData.timeForPhysicalExamination"-->
                            <!--    class="w-100%"-->
                            <!--    clearable-->
                            <!--    placeholder="请选择体检时间"-->
                            <!--    type="date"-->
                            <!--    value-format="yyyy-MM-dd"-->
                            <!--/>-->
                            <n-input
                                v-model:value="formData.timeForPhysicalExamination"
                                class="w-100%"
                                clearable
                                placeholder="请输入体检时间"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="交通工具" path="vehicle">
                            <n-input
                                v-model:value="formData.vehicle"
                                class="w-100%"
                                clearable
                                placeholder="请输入交通工具"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="车牌号码" path="licenseNumber">
                            <n-input
                                v-model:value="formData.licenseNumber"
                                class="w-100%"
                                clearable
                                placeholder="请输入车牌号码"
                            />
                        </n-form-item-gi>
                    </n-grid>
                </n-tab-pane>
                <n-tab-pane display-directive="show" name="工作信息">
                    <n-grid cols="12" x-gap="16">
                        <n-form-item-gi :span="6" label="职称" path="theTitleOfATechnicalPost">
                            <n-input
                                v-model:value="formData.theTitleOfATechnicalPost"
                                class="w-100%"
                                clearable
                                placeholder="请输入职称"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="职业资格" path="professionalQualification">
                            <n-input
                                v-model:value="formData.professionalQualification"
                                class="w-100%"
                                clearable
                                placeholder="请输入职业资格"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="工龄" path="workingYears">
                            <n-input-number
                                v-model:value="formData.workingYears"
                                class="w-100%"
                                clearable
                                placeholder="请输入工龄"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="原单位及岗位" path="previousEmployerAndPosition">
                            <n-input
                                v-model:value="formData.previousEmployerAndPosition"
                                class="w-100%"
                                clearable
                                placeholder="请输入原任职单位及岗位"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="参保状况" path="healthInsuranceStatus">
                            <n-radio-group v-model:value="formData.healthInsuranceStatus">
                                <n-space>
                                    <n-radio :value="0">未参保</n-radio>
                                    <n-radio :value="1">已参保</n-radio>
                                </n-space>
                            </n-radio-group>
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="参保单位" path="insuredEntity">
                            <n-input
                                v-model:value="formData.insuredEntity"
                                class="w-100%"
                                clearable
                                placeholder="请输入参保单位"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="有无公积金" path="whetherThereIsProvidentFund">
                            <n-radio-group v-model:value="formData.whetherThereIsProvidentFund">
                                <n-space>
                                    <n-radio :value="0">无</n-radio>
                                    <n-radio :value="1">有</n-radio>
                                </n-space>
                            </n-radio-group>
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="公积金参保单位" path="providentFundParticipatingUnits">
                            <n-input
                                v-model:value="formData.providentFundParticipatingUnits"
                                class="w-100%"
                                clearable
                                placeholder="请输入公积金参保单位"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="应聘方式" path="howToApply">
                            <n-input
                                v-model:value="formData.howToApply"
                                class="w-100%"
                                clearable
                                placeholder="请输入应聘方式"
                            />
                        </n-form-item-gi>
                    </n-grid>
                </n-tab-pane>
                <n-tab-pane display-directive="show" name="学历信息">
                    <n-grid cols="12" x-gap="16">
                        <n-form-item-gi :span="6" label="学历" path="educationBackground">
                            <n-input
                                v-model:value="formData.educationBackground"
                                class="w-100%"
                                clearable
                                placeholder="请输入学历"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="学位" path="degree">
                            <n-input
                                v-model:value="formData.degree"
                                class="w-100%"
                                clearable
                                placeholder="请输入学位"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="毕业院校" path="graduateInstitutions">
                            <n-input
                                v-model:value="formData.graduateInstitutions"
                                class="w-100%"
                                clearable
                                placeholder="请输入毕业院校"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="所学专业" path="majorOfStudy">
                            <n-input
                                v-model:value="formData.majorOfStudy"
                                class="w-100%"
                                clearable
                                placeholder="请输入所学专业"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="外语水平" path="foreignLanguageLevel">
                            <n-input
                                v-model:value="formData.foreignLanguageLevel"
                                class="w-100%"
                                clearable
                                placeholder="请输入外语水平"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="计算机水平" path="computerSkill">
                            <n-input
                                v-model:value="formData.computerSkill"
                                class="w-100%"
                                clearable
                                placeholder="请输入计算机水平"
                            />
                        </n-form-item-gi>
                    </n-grid>
                </n-tab-pane>
                <n-tab-pane display-directive="show" name="家庭信息">
                    <n-grid cols="12" x-gap="16">
                        <n-form-item-gi :span="6" label="家庭地址" path="contactAddress">
                            <n-input
                                v-model:value="formData.contactAddress"
                                class="w-100%"
                                clearable
                                placeholder="请输入家庭地址"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="家庭情况" path="familyBackground">
                            <n-input
                                v-model:value="formData.familyBackground"
                                class="w-100%"
                                clearable
                                placeholder="请输入家庭情况"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="紧急联系人" path="emergencyContact">
                            <n-input
                                v-model:value="formData.emergencyContact"
                                class="w-100%"
                                clearable
                                placeholder="请输入紧急联系人"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="联系人关系" path="contactRelation">
                            <n-input
                                v-model:value="formData.contactRelation"
                                class="w-100%"
                                clearable
                                placeholder="请输入联系人关系"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="联系人电话" path="phoneNumberOfTheContact">
                            <n-input
                                v-model:value="formData.phoneNumberOfTheContact"
                                class="w-100%"
                                clearable
                                placeholder="请输入联系人电话"
                            />
                        </n-form-item-gi>
                    </n-grid>
                </n-tab-pane>
                <n-tab-pane display-directive="show" name="合同信息">
                    <n-grid cols="12" x-gap="16">
                        <n-form-item-gi :span="6" label="工号" path="username">
                            <n-input
                                v-model:value="formData.username"
                                class="w-100%"
                                clearable
                                disabled
                                placeholder="请输入工号"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="档案编码" path="codeOfFile">
                            <n-input
                                v-model:value="formData.codeOfFile"
                                class="w-100%"
                                clearable
                                placeholder="请输入档案编码"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="员工状态" path="statusOfEmployees">
                            <n-select
                                filterable
                                clearable
                                v-model:value="formData.statusOfEmployees"
                                :options="dictLibs['employee_status']"
                                placeholder="请选择员工状态"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="员工类型" path="typeOfEmployee">
                            <n-select
                                filterable
                                clearable
                                v-model:value="formData.typeOfEmployee"
                                :options="dictLibs['employee_type']"
                                placeholder="请选择员工类型"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="合同编号" path="contractNo">
                            <n-input
                                v-model:value="formData.contractNo"
                                class="w-100%"
                                clearable
                                placeholder="请输入合同编号"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="当前薪资标准" path="currentSalaryScale">
                            <n-input
                                v-model:value="formData.currentSalaryScale"
                                class="w-100%"
                                clearable
                                placeholder="请输入当前薪资标准"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="职级类别" path="jobCategory">
                            <n-select
                                v-model:value="formData.jobCategory"
                                :options="jobCategoryOptions"
                                label-field="postName"
                                placeholder="请选择职级类别"
                                value-field="postId"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="岗位类型" path="typeOfJob">
                            <n-select
                                v-model:value="formData.typeOfJob"
                                :options="typeOfJobOptions"
                                label-field="postName"
                                placeholder="请选择岗位类型"
                                value-field="postId"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="岗位名称" path="nameOfThePost">
                            <n-input
                                v-model:value="formData.nameOfThePost"
                                class="w-100%"
                                clearable
                                placeholder="请输入岗位名称"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="薪资级别" path="rank">
                            <n-select
                                v-model:value="formData.rank"
                                :options="rankOptions"
                                label-field="postName"
                                placeholder="请选择薪资级别"
                                value-field="postId"
                            />
                        </n-form-item-gi>
                    </n-grid>
                </n-tab-pane>
            </n-tabs>
            <n-grid cols="12" x-gap="16">
                <n-form-item-gi :span="12">
                    <n-space>
                        <n-button type="primary" @click="onSubmit">提交</n-button>
                        <n-button @click="closeModal">取消</n-button>
                    </n-space>
                </n-form-item-gi>
            </n-grid>
        </n-form>
    </n-spin>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import type { FormInst } from "naive-ui";
import { AvatarUploader } from "@/components/Uploader";
import {
    GET_ARCHIVES_BY_ID,
    GET_COMPANY_ID_BY_DEPT_ID,
    GET_DEPT_TREE,
    GET_POST_ALL_LIST,
    GET_YD_COMPANY_LIST,
    UPDATE_ARCHIVES
} from "@/api/permission";
import { useDicts, usePublic } from "@/hooks";
import dayjs from "dayjs";

let props = defineProps({
    show: { type: Boolean, default: false },
    id: { type: String as PropType<string | number> }
});

let emits = defineEmits(["update:show", "refresh"]);

onMounted(async () => {
    loading.value = true;
    await setDictLibs();
    await getOptions();
    await getDetail();
    loading.value = false;
});

// 字典操作
let { dictLibs, getDictLibs, dictValueToLabel } = useDicts();

let setDictLibs = async () => {
    let dictName = ["post_group", "employee_status", "employee_type"];
    await getDictLibs(dictName);
};

// loading
let loading = ref(false);

// 获取选项
let companyIdOptions = ref<any[]>([]);
let deptIdOptions = ref<any[]>([]);
let postOptions = ref<any[]>([]);
let rankOptions = ref<any[]>([]);
let jobCategoryOptions = ref<any[]>([]);
let typeOfJobOptions = ref<any[]>([]);

let getOptions = async () => {
    await GET_YD_COMPANY_LIST({}).then((res) => {
        companyIdOptions.value = res.data.data || [];
    });
    await GET_DEPT_TREE({ deptName: "" }).then((res) => {
        deptIdOptions.value = res.data.data || [];
    });
    await GET_POST_ALL_LIST({ postGroup: "post" }).then((res) => {
        postOptions.value = res.data.data || [];
    });
    await GET_POST_ALL_LIST({ postGroup: "rank" }).then((res) => {
        rankOptions.value = res.data.data || [];
    });
    await GET_POST_ALL_LIST({ postGroup: "jobtype" }).then((res) => {
        jobCategoryOptions.value = res.data.data || [];
    });
    await GET_POST_ALL_LIST({ postGroup: "positiontype" }).then((res) => {
        typeOfJobOptions.value = res.data.data || [];
    });
};

let changeCompanyId = (val: string, obj: { company: string }) => {
    formData.value.company = obj.company;
};

// 获取详情
let getDetail = async () => {
    await GET_ARCHIVES_BY_ID({ id: props.id }).then((res) => {
        formData.value = {
            ...res.data.data,
            companyId: String(res.data.data.companyId),
            age: typeof res.data.data.age === "number" ? Number(res.data.data.age) : 0,
            statusOfEmployees: String(res.data.data.statusOfEmployees),
            typeOfEmployee: String(res.data.data.typeOfEmployee),
            timeOfEntry: dayjs(res.data.data.timeOfEntry).format("YYYY-MM-DD")
        };
        avatarList.value = [
            { id: new Date().getTime(), status: "finished", url: spliceImageUrl(res.data.data.avatar) }
        ];
    });
};

// 2023年10月5日21:38:40部门和公司联动查询
let changeDeptId = (val: any) => {
    GET_COMPANY_ID_BY_DEPT_ID({ deptId: val }).then((res) => {
        formData.value.companyId = res.data.data ?? null;
    });
};

// 头像
let { spliceImageUrl } = usePublic();
let avatarList = ref<any[]>([]);

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {
    trueName: { required: true, message: "请输入姓名", trigger: ["input", "blur"] },
    deptId: { required: true, message: "请选择所属部门", trigger: ["blur", "change"] },
    phone: { required: true, message: "请输入手机号", trigger: ["input", "blur"] },
    idNumber: { required: true, message: "请输入身份证号", trigger: ["input", "blur"] },
    username: { required: true, message: "请输入工号", trigger: ["input", "blur"] }
};

// 表单数据
let formData = ref<Record<string, any>>({});

// 关闭弹窗
let closeModal = () => {
    emits("update:show", false);
};

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => {
        if (errors && errors.length > 0) window.$message.error("请检查表单数据");
        return !(errors && errors.length > 0);
    });
    if (validateError) return false;
    UPDATE_ARCHIVES({
        ...formData.value,
        avatar: avatarList.value[0]?.fullPath
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("编辑成功");
            closeModal();
            emits("refresh");
        } else window.$message.error(res.data.msg);
    });
};
</script>
