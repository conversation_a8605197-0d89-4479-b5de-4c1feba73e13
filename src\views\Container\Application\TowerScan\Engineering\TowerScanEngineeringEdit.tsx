import { computed, defineComponent, h, ref, watchEffect } from "vue";
import { type FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { useStoreUser } from "@/store";
import { ADD_IRON_PROJECT, GET_IRON_PROJECT_DETAIL, UPDATE_IRON_PROJECT } from "@/api/application/TowerScan";
import { UserSelector } from "@/components/UserSelector";
import dayjs from "dayjs";

export default defineComponent({
    name: "TowerScanEngineeringEdit",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        const storeUser = useStoreUser();

        // 获取详情
        const getDetail = () => {
            GET_IRON_PROJECT_DETAIL({ projectId: props.configData.id }).then((res) => {
                if (res.data.code === 0) {
                    formData.value = {
                        contractNumber: res.data.data.contractNumber,
                        materialNo: res.data.data.materialNo,
                        operBy: res.data.data.operBy,
                        operTime: res.data.data.operTime,
                        powerLevel: res.data.data.powerLevel,
                        projectAs: res.data.data.projectAs,
                        projectName: res.data.data.projectName,
                        technicalSpecificationRemark: res.data.data.technicalSpecificationRemark
                    };
                }
            });
        };

        // 表单数据
        interface FormDataProps {
            [key: string]: any;
        }

        const formRef = ref<FormInst | null>(null);

        const initFormData: FormDataProps = {
            contractNumber: null,
            materialNo: null,
            operBy: storeUser.getUserData.sysUser?.username,
            operTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
            powerLevel: null,
            projectAs: null,
            projectName: null,
            technicalSpecificationRemark: null
        };

        const formRules = computed(() => ({
            operTime: [{ required: true, message: "请选择操作时间", trigger: ["blur", "change"] }],
            projectName: [{ required: true, message: "请输入工程名称", trigger: ["input", "blur"] }],
            projectAs: [{ required: true, message: "请输入工程简称", trigger: ["input", "blur"] }],
            powerLevel: [{ required: true, message: "请输入电压等级", trigger: ["input", "blur"] }]
        }));

        const formData = ref(cloneDeep(initFormData));

        const clearForm = () => {
            formData.value = cloneDeep(initFormData);
        };

        const onClose = () => {
            clearForm();
            changeModalShow(false);
            emit("refresh");
        };

        const onSubmit = async () => {
            let validateError = await formRef.value?.validate((errors) => !!errors);
            if (validateError) return false;

            const submitData = {
                ...formData.value
            };

            if (props.configData.id) {
                await UPDATE_IRON_PROJECT({
                    id: props.configData.id,
                    ...submitData
                }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("编辑成功");
                        onClose();
                    }
                });
            } else {
                await ADD_IRON_PROJECT({
                    ...submitData
                }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("新增成功");
                        onClose();
                    }
                });
            }
        };

        watchEffect(async () => {
            if (show.value) {
                if (props.configData.id) getDetail();
            }
        });

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card
                    title={props.configData.id ? "编辑工程" : "新增工程"}
                    class="w-1000px"
                    closable
                    onClose={onClose}
                >
                    <n-form
                        ref={formRef}
                        model={formData.value}
                        rules={formRules.value}
                        label-placement="left"
                        label-width="auto"
                    >
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi span={6} label="操作人" path="operBy" required>
                                <UserSelector
                                    value={formData.value.operBy}
                                    class="w-100%"
                                    disabled
                                    key-name="username"
                                    placeholder="请选择操作人"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="操作时间" path="operTime">
                                <n-date-picker
                                    v-model:formatted-value={formData.value.operTime}
                                    class="w-100%"
                                    clearable
                                    placeholder="请选择操作时间"
                                    type="datetime"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="工程名称" path="projectName">
                                <n-input
                                    v-model:value={formData.value.projectName}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入工程名称"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="合同号" path="contractNumber">
                                <n-input
                                    v-model:value={formData.value.contractNumber}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入合同号"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="工程简称" path="projectAs">
                                <n-input
                                    v-model:value={formData.value.projectAs}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入工程简称"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="材料表号" path="materialNo">
                                <n-input
                                    v-model:value={formData.value.materialNo}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入材料表号"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="电压等级" path="powerLevel">
                                <n-input
                                    v-model:value={formData.value.powerLevel}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入电压等级"
                                    v-slots={{
                                        suffix: () => "KV"
                                    }}
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="技术规范要求" path="technicalSpecificationRemark">
                                <n-input
                                    v-model:value={formData.value.technicalSpecificationRemark}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入技术规范要求"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={12}>
                                <n-space>
                                    <n-button type="primary" onClick={onSubmit}>
                                        提交
                                    </n-button>
                                    <n-button onClick={onClose}>取消</n-button>
                                </n-space>
                            </n-form-item-gi>
                        </n-grid>
                    </n-form>
                </n-card>
            </n-modal>
        );
    }
});
