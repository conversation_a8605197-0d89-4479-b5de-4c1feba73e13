<template>
    <div class="w-100%">
        <n-data-table :columns="tableColumns" :data="tableData" :single-line="false" bordered striped />
        <div class="flex-x-center mt" v-if="showButtons">
            <n-space>
                <n-button type="primary" @click="addTableItem">新增一行</n-button>
                <n-button type="success" @click="onSubmit">保存全部</n-button>
            </n-space>
        </div>
    </div>
</template>

<script lang="ts" setup>
import type { VNode } from "vue";
import { computed, h, onMounted, ref } from "vue";
import type { DataTableColumns, SelectOption } from "naive-ui";
import { NInput, NSelect, NTooltip } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { useDicts } from "@/hooks";

let props = withDefaults(defineProps<{ value: RowProps[]; showButtons?: boolean }>(), {
    value: () => [],
    showButtons: true
});

let emits = defineEmits(["confirm", "update:value"]);

let tableData = computed({ get: () => props.value, set: (val) => emits("update:value", val) });

onMounted(async () => {
    await setDictLibs();
});

// 获取字典
let { dictLibs, getDictLibs } = useDicts();

let setDictLibs = async () => {
    let dictName = ["load_type", "make_method", "uprightMethod"];
    await getDictLibs(dictName);
};

// 表单数据
interface RowProps {
    reinforceBar: string; // 配筋
    nameSuffix: string; // 后缀
    poleName: string; // 名称
    poleCode: string; // 名称代号
    sharpName: string; // 外形名称
    sharpCode: string; // 外形代号
    loadType: Nullable<string>; // 类型
    loadValue: string; // 荷载代号/弯矩数值
    makeMethod: Nullable<string>; // 组装方式
    uprightMethod: Nullable<string>; // 立杆方式
    standardNumber: string; // 标准编号
    diameter: string; // 梢径或直径(mm)
    poleLength: string; // 杆长(m
    poleWidth: string; // 方形杆宽(mm)
    poleExtent: string; // 方形杆长(mm))
    wallThickness: string; // 壁厚(mm)
    concreteAmount: string; // 扣除钢筋混凝土用量(m³)

    [key: string]: any;
}

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        title: "配筋",
        key: "reinforceBar",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.reinforceBar,
                onUpdateValue: (v) => (row.reinforceBar = v)
            });
        }
    },
    {
        title: "后缀",
        key: "nameSuffix",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.nameSuffix,
                onUpdateValue: (v) => (row.nameSuffix = v)
            });
        }
    },
    {
        title: "名称",
        key: "poleName",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.poleName,
                onUpdateValue: (v) => (row.poleName = v)
            });
        }
    },
    {
        title: "名称代号",
        key: "poleCode",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.poleCode,
                onUpdateValue: (v) => (row.poleCode = v)
            });
        }
    },
    {
        title: "外形名称",
        key: "sharpName",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.sharpName,
                onUpdateValue: (v) => (row.sharpName = v)
            });
        }
    },
    {
        title: "外形代号",
        key: "sharpCode",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.sharpCode,
                onUpdateValue: (v) => (row.sharpCode = v)
            });
        }
    },
    {
        title: "类型",
        key: "loadType",
        align: "center",
        render: (row) => {
            return h(NSelect, {
                options: dictLibs["load_type"] as any[],
                clearable: true,
                filterable: true,
                placeholder: "请选择类型",
                value: row.loadType,
                onUpdateValue: (v) => (row.loadType = v),
                renderOption: ({ node, option }: { node: VNode; option: SelectOption }) => {
                    return h(
                        NTooltip,
                        {},
                        {
                            trigger: () => node,
                            default: () => option.label
                        }
                    );
                }
            });
        }
    },
    {
        title: "荷载代号/弯矩数值",
        key: "loadValue",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.loadValue,
                onUpdateValue: (v) => (row.loadValue = v)
            });
        }
    },
    {
        title: "组装方式",
        key: "makeMethod",
        align: "center",
        render: (row) => {
            return h(NSelect, {
                options: dictLibs["make_method"] as any[],
                clearable: true,
                filterable: true,
                placeholder: "请选择组装方式",
                value: row.makeMethod,
                onUpdateValue: (v) => (row.makeMethod = v),
                renderOption: ({ node, option }: { node: VNode; option: SelectOption }) => {
                    return h(
                        NTooltip,
                        {},
                        {
                            trigger: () => node,
                            default: () => option.label
                        }
                    );
                }
            });
        }
    },
    {
        title: "立杆方式",
        key: "uprightMethod",
        align: "center",
        render: (row) => {
            return h(NSelect, {
                options: dictLibs["uprightMethod"] as any[],
                clearable: true,
                filterable: true,
                placeholder: "请选择立杆方式",
                value: row.uprightMethod,
                onUpdateValue: (v) => (row.uprightMethod = v),
                renderOption: ({ node, option }: { node: VNode; option: SelectOption }) => {
                    return h(
                        NTooltip,
                        {},
                        {
                            trigger: () => node,
                            default: () => option.label
                        }
                    );
                }
            });
        }
    },
    {
        title: "标准编号",
        key: "standardNumber",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.standardNumber,
                onUpdateValue: (v) => (row.standardNumber = v)
            });
        }
    },
    {
        title: "梢径或直径(mm)",
        key: "diameter",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.diameter,
                onUpdateValue: (v) => (row.diameter = v)
            });
        }
    },
    {
        title: "方形杆长(mm)",
        key: "poleExtent",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.poleExtent,
                onUpdateValue: (v) => (row.poleExtent = v)
            });
        }
    },
    {
        title: "方形杆宽(mm)",
        key: "poleWidth",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.poleWidth,
                onUpdateValue: (v) => (row.poleWidth = v)
            });
        }
    },
    {
        title: "杆长(m)",
        key: "poleLength",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.poleLength,
                onUpdateValue: (v) => (row.poleLength = v)
            });
        }
    },
    {
        title: "壁厚(mm)",
        key: "wallThickness",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.wallThickness,
                onUpdateValue: (v) => (row.wallThickness = v)
            });
        }
    },
    {
        title: "扣除钢筋混凝土用量(m³)",
        key: "concreteAmount",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.concreteAmount,
                onUpdateValue: (v) => (row.concreteAmount = v)
            });
        }
    }
]);

// 可编辑表单配置
let tableItem: RowProps = {
    reinforceBar: "",
    nameSuffix: "",
    poleName: "",
    poleCode: "",
    sharpName: "",
    sharpCode: "",
    loadType: null,
    loadValue: "",
    makeMethod: null,
    uprightMethod: null,
    standardNumber: "",
    diameter: "",
    poleLength: "",
    poleWidth: "",
    poleExtent: "",
    wallThickness: "",
    concreteAmount: ""
};

let addTableItem = () => {
    tableData.value.push(cloneDeep(tableItem));
};

// let tableData = ref<RowProps[]>([]);

// 提交
let onSubmit = () => {
    emits("confirm", tableData.value);
};
</script>
