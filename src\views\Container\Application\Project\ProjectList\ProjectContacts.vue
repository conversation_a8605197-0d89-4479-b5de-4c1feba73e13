<template>
    <div>
        <n-data-table
            :columns="tableColumns"
            :data="tableData"
            :loading="tableLoading"
            :row-key="tableRowKey"
            :single-line="false"
            bordered
            remote
            striped
        />
        <n-button block class="mt-20px h-45px" @click="addModalShow = true">
            <template #icon>
                <n-element tag="span">
                    <dynamic-icon class="color-[var(--primary-color)]" icon="PlusCircleOutlined" />
                </n-element>
            </template>
            <n-element class="color-[var(--primary-color)]" tag="span">新增联系信息</n-element>
        </n-button>
        <!--新增联系人-->
        <n-modal v-model:show="addModalShow" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-600px" closable title="新增联系信息" @close="closeAddModal">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-form-item label="联系人姓名" path="contactName">
                        <n-input
                            v-model:value="formData.contactName"
                            class="w-100%"
                            clearable
                            placeholder="请输入联系人姓名"
                        />
                    </n-form-item>
                    <n-form-item label="联系人职位" path="post">
                        <n-input
                            v-model:value="formData.post"
                            class="w-100%"
                            clearable
                            placeholder="请输入联系人职位"
                        />
                    </n-form-item>
                    <n-form-item label="联系人电话" path="phone">
                        <n-input
                            v-model:value="formData.phone"
                            class="w-100%"
                            clearable
                            placeholder="请输入联系人电话"
                        />
                    </n-form-item>
                    <n-form-item>
                        <n-space>
                            <n-button type="primary" @click="onSubmit">提交</n-button>
                            <n-button @click="closeAddModal">取消</n-button>
                        </n-space>
                    </n-form-item>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns, FormInst } from "naive-ui";
import { DynamicIcon } from "@/components/DynamicIcon";
import { ADD_CUSTOMER_CONTACTS, GET_CUSTOMER_CONTACTS } from "@/api/application/customer";
import { cloneDeep } from "lodash-es";

let props = defineProps({
    id: {
        type: String as PropType<any>
    }
});

// 数据列表
interface RowProps<T = string | null> {
    contactId?: T | number;
    contactName: T;
    post: T;
    phone: T | number;
}

let { tableRowKey, tableData, tableLoading } = useCommonTable<RowProps>("contactId");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        title: "联系人姓名",
        key: "contactName",
        align: "center"
    },
    {
        title: "联系人职位",
        key: "post",
        align: "center"
    },
    {
        title: "联系人电话",
        key: "phone",
        align: "center"
    }
]);

let getTableData = () => {
    tableLoading.value = true;
    GET_CUSTOMER_CONTACTS({ id: props.id }).then((res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data;
            tableLoading.value = false;
        }
    });
};

watch(
    () => props.id,
    (val) => {
        if (val) getTableData();
    },
    { immediate: true }
);

// 新增联系人
let addModalShow = ref(false);

let closeAddModal = () => {
    addModalShow.value = false;
    clearForm();
};

let formRef = ref<FormInst | null>(null);

let formRules = {
    contactName: {
        required: true,
        message: "请输入联系人姓名",
        trigger: ["input", "blur"]
    },
    post: {
        required: true,
        message: "请输入联系人职位",
        trigger: ["input", "blur"]
    },
    phone: {
        required: true,
        message: "请输入联系人电话",
        trigger: ["input", "blur"]
    }
};

let initFormData: RowProps = {
    contactName: null,
    post: null,
    phone: null
};

let formData = ref(cloneDeep(initFormData));

let clearForm = () => {
    formData.value = cloneDeep(initFormData);
};

let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    ADD_CUSTOMER_CONTACTS({
        customerId: props.id,
        ...formData.value
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("新增成功");
            closeAddModal();
            getTableData();
        }
    });
};
</script>
