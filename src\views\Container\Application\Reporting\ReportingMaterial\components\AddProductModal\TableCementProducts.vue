<template>
    <div class="w-100%">
        <n-data-table :columns="tableColumns" :data="tableData" :single-line="false" bordered striped />
        <div class="flex-x-center mt" v-if="showButtons">
            <n-space>
                <n-button type="primary" @click="addTableItem">新增一行</n-button>
                <n-button type="success" @click="onSubmit">保存全部</n-button>
            </n-space>
        </div>
    </div>
</template>

<script lang="ts" setup>
import type { VNode } from "vue";
import { computed, h, onMounted, ref } from "vue";
import type { DataTableColumns, SelectOption } from "naive-ui";
import { NInput, NSelect, NTooltip } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { useDicts } from "@/hooks";

let props = withDefaults(defineProps<{ value: RowProps[]; showButtons?: boolean }>(), {
    value: () => [],
    showButtons: true
});

let emits = defineEmits(["confirm", "update:value"]);

let tableData = computed({ get: () => props.value, set: (val) => emits("update:value", val) });

onMounted(async () => {
    await setDictLibs();
});

// 获取字典
let { dictLibs, getDictLibs } = useDicts();

let setDictLibs = async () => {
    let dictName = ["fill_method"];
    await getDictLibs(dictName);
};

// 表单数据
interface RowProps {
    reinforceBar: string; // 配筋
    fillMethod: Nullable<string>; // 填写方式
    cementDiameter: string; // 直径(mm)
    cementExtent: string; // 长(mm)
    cementWidth: string; // 宽(mm)
    cementHeight: string; // 高(mm)
    cementSuffix: string; // 后缀-水泥制品用cementSuffix，其他用nameSuffix
    wallThickness: string; // 壁厚(mm)
    concreteAmount: string; // 扣除钢筋混凝土用量(m³)

    [key: string]: any;
}

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        title: "配筋",
        key: "reinforceBar",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.reinforceBar,
                onUpdateValue: (v) => (row.reinforceBar = v)
            });
        }
    },
    {
        title: "填写方式",
        key: "fillMethod",
        align: "center",
        render: (row) => {
            return h(NSelect, {
                options: dictLibs["fill_method"] as any[],
                clearable: true,
                filterable: true,
                placeholder: "请选择填写方式",
                value: row.fillMethod,
                onUpdateValue: (v) => (row.fillMethod = v),
                renderOption: ({ node, option }: { node: VNode; option: SelectOption }) => {
                    return h(
                        NTooltip,
                        {},
                        {
                            trigger: () => node,
                            default: () => option.label
                        }
                    );
                }
            });
        }
    },
    {
        title: "直径(mm)",
        key: "cementDiameter",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.cementDiameter,
                onUpdateValue: (v) => (row.cementDiameter = v)
            });
        }
    },
    {
        title: "长度(mm)",
        key: "cementExtent",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.cementExtent,
                onUpdateValue: (v) => (row.cementExtent = v)
            });
        }
    },
    {
        title: "宽度(mm)",
        key: "cementWidth",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.cementWidth,
                onUpdateValue: (v) => (row.cementWidth = v)
            });
        }
    },
    {
        title: "高度(mm)",
        key: "cementHeight",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.cementHeight,
                onUpdateValue: (v) => (row.cementHeight = v)
            });
        }
    },
    {
        title: "后缀",
        key: "cementSuffix",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.cementSuffix,
                onUpdateValue: (v) => (row.cementSuffix = v)
            });
        }
    },
    {
        title: "壁厚(mm)",
        key: "wallThickness",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.wallThickness,
                onUpdateValue: (v) => (row.wallThickness = v)
            });
        }
    },
    {
        title: "扣除钢筋混凝土用量(m³)",
        key: "concreteAmount",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.concreteAmount,
                onUpdateValue: (v) => (row.concreteAmount = v)
            });
        }
    }
]);

// 可编辑表单配置
let tableItem: RowProps = {
    reinforceBar: "",
    fillMethod: null,
    cementDiameter: "",
    cementExtent: "",
    cementWidth: "",
    cementHeight: "",
    cementSuffix: "",
    wallThickness: "",
    concreteAmount: ""
};

let addTableItem = () => {
    tableData.value.push(cloneDeep(tableItem));
};

// let tableData = ref<RowProps[]>([]);

// 提交
let onSubmit = () => {
    emits("confirm", tableData.value);
};
</script>
