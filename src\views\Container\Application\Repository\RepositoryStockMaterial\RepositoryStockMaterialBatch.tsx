import { computed, defineComponent, reactive, ref, watchEffect } from "vue";
import { useCommonTable, useDicts } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import { GET_PRODUCTION_MATERIAL_LIST } from "@/api/application/plasticMes";
import { GET_REPOSITORY_STORE_ROOM_GOODS_NUMBER_LIST } from "@/api/application/repository";

export default defineComponent({
    name: "RepositoryStockMaterialBatch",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        // 字典操作
        const { dictLibs, getDictLibs } = useDicts();

        const setDictLibs = async () => {
            let dictName = ["common_units"];
            await getDictLibs(dictName);
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { title: "原材料名称", key: "goodsName", align: "center" },
            { title: "批次号", key: "batchNumber", align: "center" },
            { title: "单价", key: "unitPrice", align: "center" },
            { title: "批次入库数", key: "storageCapacity", align: "center" },
            { title: "入库时间", key: "createTime", align: "center" },
            { title: "操作人", key: "createByName", align: "center" }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_REPOSITORY_STORE_ROOM_GOODS_NUMBER_LIST({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                goodsId: props.configData.goodsId,
                storeroomId: props.configData.storeroomId
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        const onClose = () => {
            changeModalShow(false);
        };

        watchEffect(async () => {
            if (show.value) {
                await setDictLibs();
                getTableData();
            }
        });

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card title="查看入库批次清单" class="w-800px" closable onClose={onClose}>
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        pagination={tablePagination}
                        row-key={tableRowKey}
                        single-line={false}
                        bordered
                        remote
                        striped
                        onUpdate:checked-row-keys={changeTableSelection}
                    />
                </n-card>
            </n-modal>
        );
    }
});
