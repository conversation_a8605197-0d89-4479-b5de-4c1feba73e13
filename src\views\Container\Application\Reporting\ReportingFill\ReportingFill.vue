<template>
    <div>
        <div v-if="active === 0" style="height: calc(100vh - 145px)">
            <div class="w-100% h-100% flex-center">
                <n-space :size="50">
                    <n-button block class="w-250px! h-250px! hover:bg-#fff!" @click="changeActive(1)">
                        <div class="w-100% h-100%">
                            <div>
                                <dynamic-icon icon="EditOutlined" size="80" />
                            </div>
                            <div class="mt-20px text-30px">每日填报</div>
                        </div>
                    </n-button>
                    <n-button block class="w-250px! h-250px! hover:bg-#fff!" @click="changeActive(2)">
                        <div class="w-100% h-100%">
                            <div>
                                <dynamic-icon icon="CalendarTwotone" size="80" />
                            </div>
                            <div class="mt-20px text-30px">月计划</div>
                        </div>
                    </n-button>
                    <n-button block class="w-250px! h-250px! hover:bg-#fff!" @click="changeActive(3)">
                        <div class="w-100% h-100%">
                            <div>
                                <dynamic-icon icon="LineChartOutlined" size="80" />
                            </div>
                            <div class="mt-20px text-30px">年计划</div>
                        </div>
                    </n-button>
                </n-space>
            </div>
        </div>
        <ReportingFillDay v-else-if="active === 1" v-model:active="active" />
        <ReportingFillMonth v-else-if="active === 2" v-model:active="active" />
        <ReportingFillYear v-else-if="active === 3" v-model:active="active" />
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { DynamicIcon } from "@/components/DynamicIcon";
import ReportingFillDay from "./ReportingFillDay/ReportingFillDay.vue";
import ReportingFillMonth from "./ReportingFillMonth/ReportingFillMonth.vue";
import ReportingFillYear from "./ReportingFillYear/ReportingFillYear.vue";

let active = ref(0);

let changeActive = (item: number) => {
    active.value = item;
};
</script>
