<template>
    <div>
        <n-card content-style="padding-top:16px;padding-bottom:8px" hoverable>
            <n-tabs v-model:value="tabActive" animated type="bar">
                <n-tab-pane :name="1" tab="合同项目回款确认" />
                <n-tab-pane :name="2" tab="无合同项目回款确认" />
            </n-tabs>
        </n-card>
        <div class="mt">
            <FinancePaymentListPendingHave v-if="tabActive === 1" />
            <FinancePaymentListPendingNon v-if="tabActive === 2" />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import FinancePaymentListPendingHave from "./FinancePaymentListPendingHave.vue";
import FinancePaymentListPendingNon from "./FinancePaymentListPendingNon.vue";

let tabActive = ref(1);
</script>
