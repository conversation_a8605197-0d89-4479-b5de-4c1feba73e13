import { computed, defineComponent, ref, watchEffect } from "vue";
import { type FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import {
    ADD_IRON_TECHNIQUE,
    GET_IRON_TECHNIQUE_DETAIL,
    GET_IRON_TECHNIQUE_TREE_LIST,
    UPDATE_IRON_TECHNIQUE
} from "@/api/application/TowerScan";

export default defineComponent({
    name: "TowerScanTechniqueEdit",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => {
            emit("update:show", show);
            if (!show) {
                clearForm();
            }
        };

        // 表单数据
        interface FormDataProps {
            [key: string]: any;
        }

        const formRef = ref<FormInst | null>(null);

        // 表单校验规则
        const formRules = computed(() => ({
            techniqueName: { required: true, message: "请输入工艺名称", trigger: ["input", "blur"] },
            techniqueType: { required: true, message: "请选择工艺类型", trigger: ["blur", "change"], type: "number" },
            parentId: { required: true, message: "请选择父级工艺", trigger: ["blur", "change"] },
            lineId: { required: false, message: "请选择绑定工艺分类", trigger: ["blur", "change"] },
            materialClassify: { required: true, message: "请选择分类", trigger: ["blur", "change"] },
            displayFlag: { required: true, message: "请选择是否显示", trigger: ["blur", "change"], type: "number" },
            lineFlag: { required: true, message: "请选择电压等级计算", trigger: ["blur", "change"], type: "number" },
            baseRelated: {
                required: true,
                message: "请选择是否与基数有关",
                trigger: ["blur", "change"],
                type: "number"
            },
            baseValue: { required: true, message: "请输入基数", trigger: ["input", "blur"] },
            maxPowerLevel: { required: false, message: "请输入最大电压", trigger: ["input", "blur"] },
            minPowerLevel: { required: false, message: "请输入最小电压", trigger: ["input", "blur"] }
        }));

        const initFormData: FormDataProps = {
            techniqueName: "",
            techniqueType: null,
            parentId: 0,
            lineId: null,
            materialClassify: "1",
            displayFlag: 1,
            lineFlag: 0,
            baseRelated: 0,
            baseValue: null,
            maxPowerLevel: null,
            minPowerLevel: null
        };

        const formData = ref(cloneDeep(initFormData));

        const clearForm = () => {
            formData.value = cloneDeep(initFormData);
        };

        // 表单选项
        const techniqueTypeOptions = [
            { label: "制孔工艺", value: 1 },
            { label: "计件工艺", value: 3 },
            { label: "按量计算工艺", value: 4 }
        ];

        const parentIdOptions = ref<any[]>([]);

        const getParentOptions = async () => {
            GET_IRON_TECHNIQUE_TREE_LIST({ processType: props.configData.processType }).then((res) => {
                if (res.data.code === 0) {
                    const initOption = { techniqueName: "顶级", id: "0" };
                    parentIdOptions.value = res.data.data ? [initOption, ...res.data.data] : [initOption];
                }
            });
        };

        // 获取详情
        const getDetail = () => {
            GET_IRON_TECHNIQUE_DETAIL({ id: props.configData.id }).then((res) => {
                if (res.data.code === 0) {
                    const rowItem: FormDataProps = res.data.data;
                    formData.value = {
                        techniqueName: rowItem.techniqueName,
                        techniqueType: rowItem.techniqueType,
                        parentId: rowItem.parentId,
                        lineId: rowItem.lineId,
                        materialClassify: String(rowItem.materialClassify),
                        displayFlag: rowItem.displayFlag,
                        lineFlag: rowItem.lineFlag,
                        baseRelated: rowItem.baseRelated,
                        baseValue: rowItem.baseValue,
                        maxPowerLevel: rowItem.maxPowerLevel,
                        minPowerLevel: rowItem.minPowerLevel
                    };
                }
            });
        };

        watchEffect(() => {
            if (show.value) {
                if (props.configData.id) {
                    getDetail();
                }
                getParentOptions();
            }
        });

        const onClose = () => {
            changeModalShow(false);
            emit("refresh");
        };

        const onSubmit = async () => {
            // 工序流程种类（1：零件工艺；2：配方工艺）
            let flowType: Nullable<number> = null;
            const processType = String(props.configData.processType);
            const processTypeArray = [processType];

            if (["0", "2", "3", "4"].some((value) => processTypeArray.includes(value))) {
                flowType = 1;
            } else if (["5", "6"].some((value) => processTypeArray.includes(value))) {
                flowType = 2;
            }

            const validateError = await formRef.value?.validate((errors) => !!errors);
            if (validateError) return false;

            if (props.configData.id) {
                await UPDATE_IRON_TECHNIQUE({
                    id: props.configData.id,
                    ...formData.value,
                    processType: props.configData.processType,
                    flowType
                }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("编辑成功");
                        onClose();
                    }
                });
            } else {
                await ADD_IRON_TECHNIQUE({
                    ...formData.value,
                    processType: props.configData.processType,
                    flowType
                }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("新增成功");
                        onClose();
                    }
                });
            }
        };

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card
                    title={props.configData.id ? "编辑工艺" : "新增工艺"}
                    class="w-800px"
                    closable
                    onClose={() => changeModalShow(false)}
                >
                    <n-form
                        ref={formRef}
                        model={formData.value}
                        rules={formRules.value}
                        label-placement="left"
                        label-width="auto"
                    >
                        <n-grid cols={24} x-gap={16}>
                            <n-form-item-gi span={12} label="父级工艺" path="parentId">
                                <n-tree-select
                                    v-model:value={formData.value.parentId}
                                    options={parentIdOptions.value}
                                    children-field="childrenList"
                                    clearable
                                    filterable
                                    key-field="id"
                                    label-field="techniqueName"
                                    placeholder="请选择父级工艺"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={12} label="绑定工艺分类" path="lineId">
                                <n-tree-select
                                    v-model:value={formData.value.lineId}
                                    options={parentIdOptions.value.filter((item, index) => index !== 0)}
                                    children-field="childrenList"
                                    clearable
                                    filterable
                                    key-field="id"
                                    label-field="techniqueName"
                                    placeholder="请选择绑定工艺分类"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={12} label="工艺名称" path="techniqueName">
                                <n-input
                                    v-model:value={formData.value.techniqueName}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入工艺名称"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={12} label="工艺类型" path="techniqueType">
                                <n-select
                                    v-model:value={formData.value.techniqueType}
                                    options={techniqueTypeOptions}
                                    clearable
                                    filterable
                                    placeholder="请选择工艺类型"
                                />
                            </n-form-item-gi>
                            {String(props.configData.processType) !== "5" &&
                                String(props.configData.processType) !== "6" && (
                                    <n-form-item-gi span={12} label="分类" path="materialClassify">
                                        <n-radio-group v-model:value={formData.value.materialClassify}>
                                            <n-space>
                                                <n-radio label="角钢" value="1" />
                                                <n-radio label="钢板" value="2" />
                                                <n-radio label="圆钢" value="3" />
                                                <n-radio label="圆管" value="4" />
                                                <n-radio label="槽钢" value="5" />
                                            </n-space>
                                        </n-radio-group>
                                    </n-form-item-gi>
                                )}
                            <n-form-item-gi span={12} label="是否显示" path="displayFlag">
                                <n-switch
                                    v-model:value={formData.value.displayFlag}
                                    checked-value={1}
                                    unchecked-value={0}
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={12} label="电压等级计算" path="lineFlag">
                                <n-switch
                                    v-model:value={formData.value.lineFlag}
                                    checked-value={1}
                                    unchecked-value={0}
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={12} label="与基数有关" path="baseRelated">
                                <n-switch
                                    v-model:value={formData.value.baseRelated}
                                    checked-value={1}
                                    unchecked-value={0}
                                />
                            </n-form-item-gi>
                            {formData.value.baseRelated === 1 && (
                                <n-form-item-gi span={12} label="基数" path="baseValue">
                                    <n-input
                                        v-model:value={formData.value.baseValue}
                                        class="w-100%"
                                        clearable
                                        placeholder="请输入基数"
                                    />
                                </n-form-item-gi>
                            )}
                            {formData.value.lineFlag === 1 && [
                                <n-form-item-gi span={12} label="最大电压" path="maxPowerLevel" key="maxPowerLevel">
                                    <n-input
                                        v-model:value={formData.value.maxPowerLevel}
                                        class="w-100%"
                                        clearable
                                        placeholder="请输入最大电压"
                                    >
                                        {{
                                            suffix: () => "KV"
                                        }}
                                    </n-input>
                                </n-form-item-gi>,
                                <n-form-item-gi span={12} label="最小电压" path="minPowerLevel" key="minPowerLevel">
                                    <n-input
                                        v-model:value={formData.value.minPowerLevel}
                                        class="w-100%"
                                        clearable
                                        placeholder="请输入最小电压"
                                    >
                                        {{
                                            suffix: () => "KV"
                                        }}
                                    </n-input>
                                </n-form-item-gi>
                            ]}
                            <n-form-item-gi span={24}>
                                <n-space>
                                    <n-button type="primary" onClick={onSubmit}>
                                        提交
                                    </n-button>
                                    <n-button onClick={() => changeModalShow(false)}>取消</n-button>
                                </n-space>
                            </n-form-item-gi>
                        </n-grid>
                    </n-form>
                </n-card>
            </n-modal>
        );
    }
});
