<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-1000px" closable title="新建排产任务" @close="changeModalShow(false)">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-grid :cols="12" :x-gap="16">
                        <n-form-item-gi :span="6" label="生产线路" path="prodLineId">
                            <n-select
                                v-model:value="formData.prodLineId"
                                :options="prodLineOptions"
                                label-field="lineName"
                                placeholder="请选择生产线路"
                                value-field="id"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi v-if="prodLineRecommend" :span="6">
                            <n-text type="success">【智能推荐线路】：{{ prodLineRecommend }}线目前空闲</n-text>
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="规格型号" path="potId">
                            <n-select
                                v-model:value="formData.potId"
                                :options="potIdOptions"
                                label-field="specification"
                                placeholder="请选择规格型号"
                                value-field="id"
                                @update:value="changeSpecification"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="生产数量" path="prodCount">
                            <n-input-number
                                v-model:value="formData.prodCount"
                                class="w-100%"
                                clearable
                                placeholder="请输入生产数量"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="领料单" path="materialApplId">
                            <n-select
                                v-model:value="formData.materialApplId"
                                :options="materialApplIdOptions"
                                label-field="businessNumber"
                                placeholder="请选择领料单"
                                value-field="id"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="排产时间" path="prodScheDate">
                            <n-date-picker
                                v-model:formatted-value="formData.prodScheDate"
                                class="w-100%"
                                clearable
                                placeholder="请选择排产时间"
                                type="datetime"
                                value-format="yyyy-MM-dd HH:mm:ss"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" class="mt-10px">
                            <n-space>
                                <n-button type="primary" @click="onSubmit">提交</n-button>
                                <n-button @click="changeModalShow(false)">取消</n-button>
                            </n-space>
                        </n-form-item-gi>
                    </n-grid>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import type { FormInst } from "naive-ui";
import { ref, watchEffect } from "vue";
import { cloneDeep } from "lodash-es";
import {
    ADD_PRODUCTION_ARRANGE,
    GET_PO_MATERIAL_RECORD,
    GET_PRODUCTION_LINE_ALL,
    GET_PRODUCTION_ORDER_SPECIFICATIONS
} from "@/api/application/production";

let props = withDefaults(defineProps<{ show: Boolean; configData: UnKnownObject }>(), {
    show: () => false
});

let emits = defineEmits(["update:show", "refresh"]);

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {
    prodLineId: { required: true, message: "请选择生产线路", trigger: ["blur", "change"] },
    potId: { required: true, message: "请选择规格型号", trigger: ["blur", "change"] },
    prodCount: { required: true, message: "请输入生产数量", type: "number", trigger: ["input", "blur"] },
    materialApplId: { required: true, message: "请选择领料单", trigger: ["blur", "change"] },
    prodScheDate: { required: true, message: "请选择排产时间", trigger: ["blur", "change"] }
};

// 表单数据
interface FormDataProps {
    prodLineId: Nullable<string | number>; // 生产线路
    potId: Nullable<string | number>; // 规格型号
    ptId: Nullable<string | number>; // 生产任务
    prodCount: number; // 生产数量
    materialApplId: Nullable<string | number>; // 领料单
    prodScheDate: Nullable<string>; // 排产时间
}

let initFormData: FormDataProps = {
    prodLineId: null,
    potId: null,
    ptId: null,
    prodCount: 0,
    materialApplId: null,
    prodScheDate: null
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 表单选项
let prodLineOptions = ref<any[]>([]);
let prodLineRecommend = ref("");
let potIdOptions = ref<any[]>([]);
let materialApplIdOptions = ref<any[]>([]);

let getOptions = async () => {
    await GET_PRODUCTION_LINE_ALL({}).then((res) => {
        if (res.data.code === 0) {
            prodLineOptions.value = res.data.data?.allLineList ?? [];
            prodLineRecommend.value = res.data.data?.recLineName ?? "";
        }
    });
    await GET_PRODUCTION_ORDER_SPECIFICATIONS({ poId: props.configData.poId }).then((res) => {
        if (res.data.code === 0) potIdOptions.value = res.data.data ?? [];
    });
    await GET_PO_MATERIAL_RECORD({ poId: props.configData.poId }).then((res) => {
        if (res.data.code === 0) materialApplIdOptions.value = res.data.data ?? [];
    });
};

let changeSpecification = (value: string | number, object: UnKnownObject) => {
    formData.value.ptId = object.ptId as string | number;
};

watchEffect(async () => {
    if (props.show) await getOptions();
});

// 弹窗状态更改
let changeModalShow = (show: boolean) => emits("update:show", show);

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    emits("refresh");
    emits("update:show", false);
};

// 提交
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    ADD_PRODUCTION_ARRANGE({ ...formData.value }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("提交成功");
            closeModal();
        }
    });
};
</script>
