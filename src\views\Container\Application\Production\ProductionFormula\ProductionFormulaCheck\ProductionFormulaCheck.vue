<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <!--查看详情-->
        <DetailModal v-model:show="detailModal.show" :config-data="detailModal.configData" @refresh="getTableData" />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from "vue";
import type { DataTableColumns } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import {
    GET_PRODUCTION_FORMULA_WAIT_CHECK,
    PUT_PRODUCTION_FORMULA_STORAGE_TECH_CONFIRM,
    PUT_PRODUCTION_FORMULA_STORAGE_TECH_REFUSE
} from "@/api/application/production";
import { TableActions } from "@/components/TableActions";
import { DetailModal } from "@/views/Container/Application/Production/ProductionTask/ProductionTaskList/Modal";

interface RowProps {
    id: string | number;
    poId: string | number;
    ptId: string | number;
    specification: string;
}

onMounted(() => {
    getSearchOptions();
    getTableData();
});

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([]);

let searchOptions = ref<TableSearchbarOptions>({});

let searchForm = ref<TableSearchbarData>({});

let getSearchOptions = () => {};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("poId");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "订单号",
        key: "pomNumber",
        align: "center"
    },
    {
        title: "生产任务单号",
        key: "ptId",
        align: "center"
    },
    {
        title: "规格型号",
        key: "specification",
        align: "center"
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: 300,
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "查看详情",
                        tertiary: true,
                        type: "primary",
                        onClick: () => openDetailModal(row)
                    },
                    {
                        label: "已知晓",
                        tertiary: true,
                        type: "success",
                        onClick: () => onKnown(row)
                    },
                    {
                        label: "数据有误",
                        tertiary: true,
                        type: "error",
                        onClick: () => onError(row)
                    }
                ]
            });
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_PRODUCTION_FORMULA_WAIT_CHECK({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        ...searchForm.value
    }).then((res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data.records;
            tablePagination.itemCount = res.data.data.total;
            tableLoading.value = false;
        }
    });
};

// 搜索
let onSearch = () => {
    getTableData();
};

// 已知晓
let onKnown = (row: RowProps) => {
    window.$dialog.warning({
        title: "提示",
        content: "确认已知晓？",
        positiveText: "确认",
        negativeText: "取消",
        onPositiveClick: () => {
            PUT_PRODUCTION_FORMULA_STORAGE_TECH_CONFIRM({ id: row.id }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("操作成功");
                    getTableData();
                }
            });
        }
    });
};

// 数据有误
let onError = (row: RowProps) => {
    window.$dialog.warning({
        title: "提示",
        content: "确认该数据有误？",
        positiveText: "确认",
        negativeText: "取消",
        onPositiveClick: () => {
            // 2023年6月29日23:04:20 临时处理
            PUT_PRODUCTION_FORMULA_STORAGE_TECH_REFUSE({
                id: row.id,
                techFeedbackRemark: "数据有误"
            }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("操作成功");
                    getTableData();
                }
            });
        }
    });
};

// 查看详情
let detailModal = ref<{ show: boolean; configData: Record<string, any> }>({ show: false, configData: {} });

let openDetailModal = (row: RowProps) => {
    detailModal.value = {
        show: true,
        configData: row
    };
};
</script>
