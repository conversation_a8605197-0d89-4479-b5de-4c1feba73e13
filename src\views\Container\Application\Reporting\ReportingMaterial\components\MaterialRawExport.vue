<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-500px" closable title="原材料调价表导出" @close="changeModalShow(false)">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-form-item label="所属公司" path="companyId">
                        <n-select
                            class="w-100%"
                            v-model:value="formData.companyId"
                            :options="companyIdOptions"
                            placeholder="请选择所属公司"
                        />
                    </n-form-item>
                    <n-form-item label="原材料名称" path="materialName">
                        <n-input class="w-100%" v-model:value="formData.materialName" placeholder="请输入原材料名称" />
                    </n-form-item>
                    <n-form-item class="flex-center">
                        <n-space>
                            <n-button type="primary" @click="getForm">导出</n-button>
                            <n-button @click="closeModal">取消</n-button>
                        </n-space>
                    </n-form-item>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref, watch, watchEffect } from "vue";
import {
    DOWNLOAD_REPORT,
    EXPORT_MATERIAL_SPEC,
    EXPORT_MONTH_REPORT,
    GET_CONFIG_COMPANY_LIST
} from "@/api/application/reporting";
import { GET_WORK_GROUP_PAGE_LIST } from "@/api/application/production";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";

let props = withDefaults(defineProps<{ show: boolean; configData: UnKnownObject }>(), {
    show: () => false
});

let emits = defineEmits(["update:show", "refresh"]);

let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

watchEffect(() => {
    if (props.show) {
        getCompanyIdOptions();
    }
});

let changeModalShow = (show: boolean) => emits("update:show", show);

let companyIdOptions = ref<any[]>([]);

let getCompanyIdOptions = async () => {
    await GET_CONFIG_COMPANY_LIST({ needFill: 1 }).then((res) => {
        companyIdOptions.value = (res.data.data || []).map((item: any) => ({
            label: item.companyName,
            value: item.id
        }));
    });
};

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {
    companyId: [{ required: false, message: "请选择所属公司", trigger: ["blur", "change"] }],
    materialName: [{ required: false, message: "请选择原材料", trigger: ["input", "blur"] }]
};

// 表单数据
type FormDataProps = Record<string, any>;

let initFormData: FormDataProps = {
    companyId: null,
    materialName: null
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

onMounted(() => {});

let getForm = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    EXPORT_MATERIAL_SPEC({ ...formData.value }).then((res) => {
        DOWNLOAD_REPORT({
            fileName: res.data.data
        }).then((cres) => {
            let blob = new Blob([cres.data]);
            let a = document.createElement("a");
            a.href = URL.createObjectURL(blob);
            a.download = res.data.data || "统计报表";
            a.style.display = "none";
            document.body.appendChild(a);
            a.click();
            a.remove();
        });
    });
};

let closeModal = () => {
    changeModalShow(false);
    clearFrom();
};
</script>
