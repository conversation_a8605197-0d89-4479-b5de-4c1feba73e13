<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <!--选择/新增技术配方-->
        <ChoiceOrAddModal
            v-model:show="choiceOrAddModal.show"
            :configData="choiceOrAddModal.configData"
            @refresh="getTableData"
        />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from "vue";
import type { DataTableColumns } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import { GET_PRODUCTION_FORMULA_WAIT_FEEDBACKS, PUSH_PRODUCTION_FORMULA } from "@/api/application/production";
import { TableActions } from "@/components/TableActions";
import { ChoiceOrAddModal } from "./components";

interface RowProps {
    id: string | number;
    pomNumber: string | number;
    poId: string | number;
    ptId: string | number;
    specification: string;
    formulaName: string;
}

onMounted(() => {
    getSearchOptions();
    getTableData();
});

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([]);

let searchOptions = ref<TableSearchbarOptions>({});

let searchForm = ref<TableSearchbarData>({});

let getSearchOptions = () => {};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "订单号",
        key: "pomNumber",
        align: "center",
        render: (row) => {
            return row.pomNumber || "/";
        }
    },
    {
        title: "规格型号",
        key: "specification",
        align: "center"
    },
    {
        title: "当前配方",
        key: "formulaName",
        align: "center",
        render(row) {
            return row.formulaName || "暂无";
        }
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: 200,
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "选择/新增",
                        tertiary: true,
                        type: "primary",
                        onClick: () => openChoiceOrAddModal(row)
                    },
                    {
                        label: "推送",
                        tertiary: true,
                        type: "error",
                        onClick: () => onPush(row)
                    }
                ]
            });
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_PRODUCTION_FORMULA_WAIT_FEEDBACKS({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        ...searchForm.value
    }).then((res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data.records;
            tablePagination.itemCount = res.data.data.total;
            tableLoading.value = false;
        }
    });
};

// 搜索
let onSearch = () => {
    getTableData();
};

// 选择/新增
let choiceOrAddModal = ref<{ show: boolean; configData: Record<string, any> }>({
    show: false,
    configData: {}
});

let openChoiceOrAddModal = (row: RowProps) => {
    choiceOrAddModal.value.show = true;
    choiceOrAddModal.value.configData = row;
};

// 推送
let onPush = (row: RowProps) => {
    window.$dialog.warning({
        title: "提示",
        content: "确定要推送吗？",
        positiveText: "确定",
        negativeText: "取消",
        onPositiveClick: () => {
            PUSH_PRODUCTION_FORMULA({ potId: row.id }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("操作成功");
                    getTableData();
                }
            });
        }
    });
};
</script>
