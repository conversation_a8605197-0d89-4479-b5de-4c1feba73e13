import { defineComponent, ref } from "vue";
import RepositoryCheckMoveCheckList from "./RepositoryCheckMoveCheckList";
import RepositoryCheckMoveCheckEdit from "./RepositoryCheckMoveCheckEdit";
import RepositoryCheckMoveMoveList from "./RepositoryCheckMoveMoveList";
import RepositoryCheckMoveMoveEdit from "@/views/Container/Application/Repository/RepositoryCheckMove/RepositoryCheckMoveMoveEdit";

export default defineComponent({
    name: "RepositoryCheckMove",
    setup() {
        const tabActive = ref(1);

        const tabList = [
            { label: "盘点任务清单", value: 1 },
            { label: "移库任务清单", value: 2 }
        ];

        // 编辑弹窗
        const checkModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const openCheckModal = () => {
            checkModal.value.show = true;
            checkModal.value.configData = {};
        };

        const moveModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const openMoveModal = () => {
            moveModal.value.show = true;
            moveModal.value.configData = {};
        };

        // 刷新组件
        const checkListRef = ref<any>(null);
        const moveListRef = ref<any>(null);

        const refreshComponents = () => {
            checkListRef.value?.refresh();
            moveListRef.value?.refresh();
        };

        return () => (
            <div class="repository-page">
                <n-card hoverable>
                    <n-tabs v-model:value={tabActive.value} animated type="bar">
                        {tabList.map((item) => (
                            <n-tab-pane name={item.value} tab={item.label} />
                        ))}
                    </n-tabs>
                    <n-space>
                        <n-button secondary type="primary" onClick={() => openCheckModal()}>
                            新建盘点任务
                        </n-button>
                        <n-button secondary type="success" onClick={() => openMoveModal()}>
                            新建移库任务
                        </n-button>
                    </n-space>
                </n-card>
                <n-card class="mt" hoverable>
                    {tabActive.value === 1 && <RepositoryCheckMoveCheckList ref={checkListRef} />}
                    {tabActive.value === 2 && <RepositoryCheckMoveMoveList ref={moveListRef} />}
                </n-card>
                <RepositoryCheckMoveCheckEdit
                    v-model:show={checkModal.value.show}
                    config-data={checkModal.value.configData}
                    onRefresh={refreshComponents}
                />
                <RepositoryCheckMoveMoveEdit
                    v-model:show={moveModal.value.show}
                    config-data={moveModal.value.configData}
                    onRefresh={refreshComponents}
                />
            </div>
        );
    }
});
