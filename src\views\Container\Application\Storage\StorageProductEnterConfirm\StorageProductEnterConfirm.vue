<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <!--审批单-->
        <ProcessDetail v-model:show="processDetailModal.show" :config-data="processDetailModal.configData" />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from "vue";
import { DataTableColumns, NText } from "naive-ui";
import { NButton } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import {
    GET_STOCK_CHECK_WAIT_STORAGE_CONFIRM_CHECK_IN_LIST,
    STOCK_CHECK_STORAGE_CHECK_IN_CONFIRM
} from "@/api/application/production";
import { TableActions } from "@/components/TableActions";
import { ProcessDetail } from "@/views/Container/Application/Process/components";
import { GET_OA_INSTANCE_FORM } from "@/api/application/oa";

interface RowProps {
    [key: string]: any;
}

onMounted(() => {
    getSearchOptions();
    getTableData();
});

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    {
        prop: "projectName",
        type: "input",
        label: "项目名称"
    }
]);

let searchOptions = ref<TableSearchbarOptions>({});

let searchForm = ref<TableSearchbarData>({
    projectName: ""
});

let getSearchOptions = () => {};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("poId");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "订单号",
        key: "pomNumber",
        align: "center"
    },
    {
        title: "生产规格型号",
        key: "specification",
        align: "center"
    },
    {
        title: "产品品质",
        align: "center",
        key: "prodQuality",
        render: (row) => h(NText, { type: "primary" }, () => row.prodQuality)
    },
    // {
    //     title: "生产线路",
    //     key: "prodLineName",
    //     align: "center"
    // },
    {
        title: "生产数量",
        key: "prodCount",
        align: "center"
    },
    {
        title: "审批单",
        key: "processInstanceId",
        align: "center",
        render(row) {
            return h(
                NButton,
                {
                    type: "primary",
                    text: true,
                    onClick: () => openProcessDetailModal(row.processInstanceId)
                },
                () => "点击查看"
            );
        }
    },
    {
        title: "操作",
        key: "action",
        align: "center",
        width: 120,
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "确认单据",
                        tertiary: true,
                        type: "primary",
                        onClick: () => openConfirmModal(row)
                    }
                ]
            });
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_STOCK_CHECK_WAIT_STORAGE_CONFIRM_CHECK_IN_LIST({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        ...searchForm.value
    }).then((res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data.records;
            tablePagination.itemCount = res.data.data.total;
            tableLoading.value = false;
        }
    });
};

// 搜索
let onSearch = () => {
    tablePagination.page = 1;
    tablePagination.pageSize = 10;
    getTableData();
};

// 查看审批单
let processDetailModal = ref<{ show: boolean; configData: Record<string, any> }>({
    show: false,
    configData: {}
});

let openProcessDetailModal = (id: string | number) => {
    GET_OA_INSTANCE_FORM({ processInstId: id }).then((res) => {
        if (res.data.code === 0) {
            processDetailModal.value.show = true;
            processDetailModal.value.configData = res.data.data;
        } else {
            window.$message.error("该审批单不存在");
        }
    });
};

// 确认单据
let openConfirmModal = (row: RowProps) => {
    window.$dialog.warning({
        title: "提示",
        content: "确认入库吗？",
        positiveText: "确认",
        negativeText: "取消",
        onPositiveClick: () => {
            STOCK_CHECK_STORAGE_CHECK_IN_CONFIRM({ reqId: row.id }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("操作成功");
                    getTableData();
                }
            });
        }
    });
};
</script>
