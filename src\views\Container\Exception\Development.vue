<template>
    <n-card hoverable style="height: calc(100vh - 145px)">
        <div class="w-100% h-100% flex-center">
            <n-result size="large">
                <template #icon>
                    <div class="w-400px">
                        <img class="w-100%" src="@/assets/img/exception/development.png" />
                    </div>
                </template>
                <template #default>
                    <div class="text-center">
                        <n-element class="mt-30px text-30px color-[var(--primary-color)]">该页面正在开发中</n-element>
                        <n-button size="large" class="w-270px mt-30px" type="primary" @click="onBack">
                            返回工作台
                        </n-button>
                    </div>
                </template>
            </n-result>
        </div>
    </n-card>
</template>

<script lang="ts" setup>
import { usePublic } from "@/hooks";

let { $router } = usePublic();

let onBack = () => {
    $router.push({ name: "Workbench" });
};
</script>
