import { computed, defineComponent, reactive, ref, watchEffect } from "vue";
import {
    TableSearchbar,
    type TableSearchbarConfig,
    type TableSearchbarData,
    type TableSearchbarOptions
} from "@/components/TableSearchbar";
import {
    GET_IRON_PROJECT_DETAIL,
    GET_IRON_PROJECT_TYPE_PAGE_LIST,
    EXPORT_IRON_PROJECT,
    DOWNLOAD_IRON_TOWER,
    DELETE_IRON_PROJECT_TYPE
} from "@/api/application/TowerScan";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import { TableActions } from "@/components/TableActions";
import TowerScanEngineeringTypeEdit from "./Type/TowerScanEngineeringTypeEdit";
import TowerScanEngineeringTypeDrawing from "./Type/TowerScanEngineeringTypeDrawing";
import TowerScanEngineeringMaterialList from "./Material/TowerScanEngineeringMaterialList";
import TowerScanEngineeringProcessesList from "@/views/Container/Application/TowerScan/Engineering/Processes/TowerScanEngineeringProcessesList";

export default defineComponent({
    name: "TowerScanEngineeringDetail",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        // 获取详情
        interface DetailDataProps {
            [key: string]: any;
        }

        const detailData = ref<DetailDataProps>({});

        const getDetail = () => {
            GET_IRON_PROJECT_DETAIL({ projectId: props.configData.id }).then((res) => {
                if (res.data.code === 0) {
                    detailData.value = res.data.data;
                }
            });
        };

        const onClose = () => {
            changeModalShow(false);
            emit("refresh");
        };

        // 搜索项
        const searchConfig = ref<TableSearchbarConfig>([
            { prop: "typeName", label: "塔型", type: "input" },
            { prop: "productStatus", label: "塔型状态", type: "select" }
        ]);

        const searchOptions = ref<TableSearchbarOptions>({
            productStatus: [
                { label: "待生产", value: 1 },
                { label: "生产中", value: 2 },
                { label: "已完成", value: 3 }
            ]
        });

        const getSearchOptions = async () => {};

        const searchForm = ref<TableSearchbarData>({
            typeName: null,
            productStatus: null
        });

        const onSearch = () => {
            tablePagination.page = 1;
            tablePagination.pageSize = 10;
            getTableData();
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            { title: "塔型", key: "typeName", align: "center" },
            {
                title: "塔型状态",
                key: "productStatus",
                align: "center",
                render(row) {
                    switch (row.productStatus) {
                        case 1:
                            return <n-text type="error">待生产</n-text>;
                        case 2:
                            return <n-text type="info">生产中</n-text>;
                        case 3:
                            return <n-text type="success">已完成</n-text>;
                        default:
                            return <n-text>/</n-text>;
                    }
                }
            },
            {
                title: "材料表状态",
                key: "materialFinishStatus",
                align: "center",
                render(row) {
                    switch (row.materialFinishStatus) {
                        case 1:
                            return <n-text type="error">未导入</n-text>;
                        case 2:
                            return <n-text type="success">已导入</n-text>;
                        default:
                            return <n-text>/</n-text>;
                    }
                }
            },
            {
                title: "工序表状态",
                key: "processFinishStatus",
                align: "center",
                render(row) {
                    switch (row.processFinishStatus) {
                        case 1:
                            return <n-text type="error">未导入</n-text>;
                        case 2:
                            return <n-text type="success">已导入</n-text>;
                        default:
                            return <n-text>/</n-text>;
                    }
                }
            },
            {
                title: "塔型图纸",
                key: "typeDrawingList",
                align: "center",
                render(row) {
                    if (!row.drawExistFlag) return <n-text>暂无图纸</n-text>;
                    return (
                        <n-button type="primary" text onClick={() => openDrawingModal(row)}>
                            点击查看
                        </n-button>
                    );
                }
            },
            { title: "加工基数", key: "towerBase", align: "center" },
            { title: "备注", key: "remark", align: "center" },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 480,
                render: (row) => {
                    return (
                        <TableActions
                            type="button"
                            buttonActions={[
                                {
                                    label: "编辑塔型",
                                    tertiary: true,
                                    type: "primary",
                                    disabled: () => row.productStatus === 3,
                                    onClick: () => openEditModal(row)
                                },
                                {
                                    label: "材料表",
                                    tertiary: true,
                                    type: "success",
                                    onClick: () => openMaterialListModal(row)
                                },
                                {
                                    label: "工序表",
                                    tertiary: true,
                                    type: "info",
                                    onClick: () => openProcessesListModal(row)
                                },
                                {
                                    label: "导出入库工序",
                                    tertiary: true,
                                    type: "warning",
                                    onClick: () => exportStorageProcess(row)
                                },
                                {
                                    label: "删除塔型",
                                    tertiary: true,
                                    type: "error",
                                    disabled: () => row.productStatus === 3,
                                    onClick: () => deleteTowerType(row)
                                }
                            ]}
                        />
                    );
                }
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_IRON_PROJECT_TYPE_PAGE_LIST({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                projectId: props.configData.id,
                ...searchForm.value
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        // 新增编辑弹窗
        const editModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const openEditModal = (row?: RowProps) => {
            editModal.value = {
                show: true,
                configData: {
                    ...row,
                    projectId: props.configData.id
                }
            };
        };

        // 图纸查看弹窗
        const drawingModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const openDrawingModal = (row: RowProps) => {
            drawingModal.value = {
                show: true,
                configData: row
            };
        };

        // 材料表弹窗
        const materialListModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });
        const openMaterialListModal = (row?: RowProps) => {
            materialListModal.value = {
                show: true,
                configData: {
                    projectId: props.configData.id,
                    ...row
                }
            };
        };

        // 工序表弹窗
        const processesListModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const openProcessesListModal = (row?: RowProps) => {
            processesListModal.value = {
                show: true,
                configData: {
                    projectId: props.configData.id,
                    ...row
                }
            };
        };

        // 导出入库工序
        const exportStorageProcess = async (row: RowProps) => {
            try {
                window.$message.loading("正在导出入库工序...", { duration: 0 });

                const exportRes = await EXPORT_IRON_PROJECT({
                    projectId: props.configData.id,
                    typeId: row.id
                });

                if (exportRes.data.code === 0) {
                    // 导出成功后下载文件
                    const downloadRes = await DOWNLOAD_IRON_TOWER({
                        fileName: exportRes.data.data || `${row.typeName}_入库工序.xlsx`
                    });

                    // 创建下载链接
                    const blob = new Blob([downloadRes.data], {
                        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    });
                    const url = window.URL.createObjectURL(blob);
                    const link = document.createElement("a");
                    link.href = url;
                    link.download = exportRes.data.data || `${row.typeName}_入库工序.xlsx`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    window.URL.revokeObjectURL(url);

                    window.$message.destroyAll();
                    window.$message.success("入库工序导出成功");
                } else {
                    window.$message.destroyAll();
                    window.$message.error(exportRes.data.message || "导出失败");
                }
            } catch (error) {
                window.$message.destroyAll();
                window.$message.error("导出失败");
                console.error("导出入库工序错误:", error);
            }
        };

        // 删除塔型
        const deleteTowerType = (row: RowProps) => {
            window.$dialog.warning({
                title: "确认删除",
                content: `确定要删除塔型「${row.typeName}」吗？删除后不可恢复！`,
                positiveText: "删除",
                negativeText: "取消",
                onPositiveClick: async () => {
                    try {
                        window.$message.loading("正在删除塔型...", { duration: 0 });

                        const res = await DELETE_IRON_PROJECT_TYPE({
                            typeId: row.id
                        });

                        if (res.data.code === 0) {
                            window.$message.destroyAll();
                            window.$message.success("塔型删除成功");
                            getTableData();
                        } else {
                            window.$message.destroyAll();
                            window.$message.error(res.data.msg || "删除失败");
                        }
                    } catch (error) {
                        window.$message.destroyAll();
                        window.$message.error("删除失败");
                        console.error("删除塔型错误:", error);
                    }
                }
            });
        };

        watchEffect(async () => {
            if (show.value) {
                await getSearchOptions();
                if (props.configData.id) {
                    getDetail();
                    getTableData();
                }
            }
        });

        return () => (
            <div>
                <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                    <n-card
                        title={`${detailData.value?.projectName} - 塔型列表`}
                        class="w-1200px"
                        closable
                        onClose={onClose}
                    >
                        <n-form label-placement="left" label-width="auto">
                            <n-grid cols={12} x-gap={16}>
                                <n-form-item-gi required span={4} label="工程名称">
                                    {detailData.value?.projectName ?? "/"}
                                </n-form-item-gi>{" "}
                                <n-form-item-gi required span={4} label="工程简称">
                                    {detailData.value?.projectAs ?? "/"}
                                </n-form-item-gi>{" "}
                                <n-form-item-gi required span={4} label="合同号">
                                    {detailData.value?.contractNumber ?? "/"}
                                </n-form-item-gi>
                                <n-form-item-gi span={12}>
                                    <div>
                                        <div class="text-18px font-bold">技术规范要求：</div>
                                        <div class="mt">{detailData.value?.technicalSpecificationRemark ?? "/"}</div>
                                    </div>
                                </n-form-item-gi>
                            </n-grid>
                        </n-form>
                        <n-card>
                            <TableSearchbar
                                form={searchForm.value}
                                config={searchConfig.value}
                                options={searchOptions.value}
                                onSearch={onSearch}
                                buttonAlign="left"
                                v-slots={{
                                    buttons: () => (
                                        <n-button type="success" onClick={() => openEditModal()}>
                                            新增塔型
                                        </n-button>
                                    )
                                }}
                            />
                        </n-card>
                        <n-card class="mt">
                            {/*<n-space class="mb">*/}
                            {/*    <n-button type="primary" onClick={() => openEditModal()}>*/}
                            {/*        新增塔型*/}
                            {/*    </n-button>*/}
                            {/*</n-space>*/}
                            <n-data-table
                                columns={tableColumns.value}
                                data={tableData.value}
                                loading={tableLoading.value}
                                pagination={tablePagination}
                                row-key={tableRowKey}
                                single-line={false}
                                bordered
                                remote
                                striped
                                onUpdate:checked-row-keys={changeTableSelection}
                            />
                        </n-card>
                        <TowerScanEngineeringTypeEdit
                            v-model:show={editModal.value.show}
                            config-data={editModal.value.configData}
                            onRefresh={getTableData}
                        />
                        <TowerScanEngineeringTypeDrawing
                            v-model:show={drawingModal.value.show}
                            config-data={drawingModal.value.configData}
                        />
                    </n-card>
                </n-modal>
                <TowerScanEngineeringMaterialList
                    v-model:show={materialListModal.value.show}
                    config-data={materialListModal.value.configData}
                />
                <TowerScanEngineeringProcessesList
                    v-model:show={processesListModal.value.show}
                    config-data={processesListModal.value.configData}
                />
            </div>
        );
    }
});
