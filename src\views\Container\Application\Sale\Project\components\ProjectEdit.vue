<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card
                :title="configData.id ? '编辑项目' : '新增项目'"
                class="w-800px"
                closable
                @close="changeModalShow(false)"
            >
                <n-tabs type="line">
                    <n-tab-pane name="基础信息">
                        <n-form
                            ref="formRef"
                            :model="formData"
                            :rules="formRules"
                            label-placement="left"
                            label-width="auto"
                        >
                            <n-grid :cols="24" x-gap="16">
                                <n-form-item-gi :span="12" label="项目名称" path="projectName">
                                    <n-input
                                        v-model:value="formData.projectName"
                                        class="w-100%"
                                        clearable
                                        placeholder="请输入项目名称"
                                    />
                                </n-form-item-gi>
                                <n-form-item-gi :span="12" label="招投标项目编号" path="projectCode">
                                    <n-input
                                        v-model:value="formData.projectCode"
                                        :disabled="!!configData.id"
                                        class="w-100%"
                                        clearable
                                        placeholder="请输入招投标项目编号"
                                    />
                                </n-form-item-gi>
                                <n-form-item-gi :span="12" label="项目负责人" path="belongUsername">
                                    <UserSelector
                                        v-model:value="formData.projectLeader"
                                        :multiple="false"
                                        class="w-100%"
                                        clearable
                                        key-name="username"
                                        placeholder="请选择项目负责人"
                                    />
                                </n-form-item-gi>
                                <n-form-item-gi :span="12" label="项目责任单位" path="responsibleDeptId">
                                    <n-select
                                        v-model:value="formData.responsibleDeptId"
                                        :options="companyIdOptions"
                                        class="w-100%"
                                        clearable
                                        filterable
                                        label-field="company"
                                        placeholder="请选择项目责任单位"
                                        value-field="id"
                                    />
                                </n-form-item-gi>
                                <n-form-item-gi :span="12" label="信息首报人" path="firstRecorder">
                                    <UserSelector
                                        v-model:value="formData.firstRecorder"
                                        :multiple="false"
                                        class="w-100%"
                                        clearable
                                        key-name="username"
                                        placeholder="请选择信息首报人"
                                    />
                                </n-form-item-gi>
                                <n-form-item-gi :span="12" label="项目相关人" path="focusedUsers">
                                    <UserSelector
                                        v-model:value="formData.focusedUsers"
                                        :multiple="true"
                                        class="w-100%"
                                        clearable
                                        key-name="username"
                                        placeholder="请选择项目相关人"
                                    />
                                </n-form-item-gi>
                                <n-form-item-gi :span="12" label="所属客户" path="customerId">
                                    <n-select
                                        v-model:value="formData.customerId"
                                        :disabled="!!configData.id && hasCustomer"
                                        :options="customerOptions"
                                        clearable
                                        filterable
                                        label-field="customerName"
                                        placeholder="请选择所属客户"
                                        value-field="customerId"
                                    />
                                </n-form-item-gi>
                                <n-form-item-gi :span="12" label="无合同项目" path="nonContractFlag">
                                    <n-radio-group
                                        v-model:value="formData.nonContractFlag"
                                        @update:value="getNodeDirectorList"
                                    >
                                        <n-space>
                                            <n-radio :value="1">是</n-radio>
                                            <n-radio :value="0">否</n-radio>
                                        </n-space>
                                    </n-radio-group>
                                </n-form-item-gi>
                                <n-form-item-gi
                                    v-if="formData.nonContractFlag === 0"
                                    :span="12"
                                    label="需要投标"
                                    path="winBidFlag"
                                >
                                    <n-radio-group
                                        v-model:value="formData.winBidFlag"
                                        @update:value="getNodeDirectorList"
                                    >
                                        <n-space>
                                            <n-radio :value="1">是</n-radio>
                                            <n-radio :value="0">否</n-radio>
                                        </n-space>
                                    </n-radio-group>
                                </n-form-item-gi>
                                <n-form-item-gi :span="24" label="项目类型" path="projectType">
                                    <n-radio-group
                                        v-model:value="formData.projectType"
                                        :disabled="!!configData.id"
                                        name="projectType"
                                    >
                                        <n-radio
                                            v-for="item in dictLibs.project_type"
                                            :key="item.value"
                                            :value="item.value"
                                        >
                                            {{ item.label }}
                                        </n-radio>
                                    </n-radio-group>
                                </n-form-item-gi>
                                <!--2025年3月新增-->
                                <n-form-item-gi :span="24">
                                    <div class="w-100% pb-2 text-20px b-b-1px b-b-solid b-[var(--n-border-color)]">
                                        招标信息
                                    </div>
                                </n-form-item-gi>
                                <n-form-item-gi :span="12" label="招标人" path="tenderer">
                                    <n-input
                                        v-model:value="formData.tenderer"
                                        class="w-100%"
                                        clearable
                                        placeholder="请输入招标人"
                                    />
                                </n-form-item-gi>
                                <n-form-item-gi :span="12" label="招标代理机构" path="tenderAgency">
                                    <n-input
                                        v-model:value="formData.tenderAgency"
                                        class="w-100%"
                                        clearable
                                        placeholder="请输入招标代理机构"
                                    />
                                </n-form-item-gi>
                                <n-form-item-gi :span="12" label="招投标项目编号" path="tenderProjectNumber">
                                    <n-input
                                        v-model:value="formData.tenderProjectNumber"
                                        class="w-100%"
                                        clearable
                                        placeholder="请输入招投标项目编号"
                                    />
                                </n-form-item-gi>

                                <n-form-item-gi :span="12" label="标段/标包数量" path="numberOfBidSections">
                                    <n-input
                                        v-model:value="formData.numberOfBidSections"
                                        class="w-100%"
                                        clearable
                                        placeholder="请输入标段/标包数量"
                                    />
                                </n-form-item-gi>
                                <n-form-item-gi :span="12" label="招标信息发布时间" path="informationReleaseTime">
                                    <n-date-picker
                                        v-model:formatted-value="formData.informationReleaseTime"
                                        class="w-100%"
                                        clearable
                                        type="datetime"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                    />
                                </n-form-item-gi>
                                <n-form-item-gi :span="12" label="所属省市" path="provinceAndCity">
                                    <n-cascader
                                        v-model:value="formData.provinceAndCity"
                                        :options="provinceAndCityOptions"
                                        children-field="childList"
                                        class="w-100%"
                                        clearable
                                        filterable
                                        label-field="name"
                                        placeholder="请选择所属省市"
                                        separator=""
                                        value-field="name"
                                    />
                                </n-form-item-gi>
                                <n-form-item-gi :span="12" label="购买标书截止时间" path="bidPurchaseDeadline">
                                    <n-date-picker
                                        v-model:formatted-value="formData.bidPurchaseDeadline"
                                        class="w-100%"
                                        clearable
                                        type="datetime"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                    />
                                </n-form-item-gi>
                                <n-form-item-gi :span="12" label="递交标书截止时间" path="bidSubmissionDeadline">
                                    <n-date-picker
                                        v-model:formatted-value="formData.bidSubmissionDeadline"
                                        class="w-100%"
                                        clearable
                                        type="datetime"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                    />
                                </n-form-item-gi>
                                <n-form-item-gi :span="12" label="报备人" path="reportUser">
                                    <UserSelector
                                        v-model:value="formData.reportUser"
                                        :multiple="false"
                                        class="w-100%"
                                        clearable
                                        key-name="username"
                                        placeholder="请选择报备人"
                                    />
                                </n-form-item-gi>
                                <n-form-item-gi :span="12" label="报备日期" path="reportDate">
                                    <n-date-picker
                                        v-model:formatted-value="formData.reportDate"
                                        class="w-100%"
                                        clearable
                                        type="date"
                                        value-format="yyyy-MM-dd"
                                    />
                                </n-form-item-gi>
                                <n-form-item-gi :span="12" label="标书购买费用" path="bidPurchaseFee">
                                    <n-input-number
                                        v-model:value="formData.bidPurchaseFee"
                                        class="w-100%"
                                        clearable
                                        placeholder="请输入标书购买费用"
                                    >
                                        <template #suffix>元</template>
                                    </n-input-number>
                                </n-form-item-gi>
                                <n-form-item-gi :span="12" label="投标保证金" path="bidBond">
                                    <n-input-number
                                        v-model:value="formData.bidBond"
                                        class="w-100%"
                                        clearable
                                        placeholder="请输入投标保证金"
                                    >
                                        <template #suffix>元</template>
                                    </n-input-number>
                                </n-form-item-gi>
                                <n-form-item-gi :span="12" label="投标单位" path="bidder">
                                    <n-input
                                        v-model:value="formData.bidder"
                                        class="w-100%"
                                        clearable
                                        placeholder="请输入投标单位"
                                    />
                                </n-form-item-gi>
                                <n-form-item-gi :span="24" label="招标文件上传" path="tenderDocuments">
                                    <FileValueUploader
                                        v-model:value="formData.tenderDocuments"
                                        value-key="fileId"
                                        :max-files="1"
                                        :multiple="false"
                                    />
                                </n-form-item-gi>
                                <n-form-item-gi :span="24">
                                    <n-space>
                                        <n-button type="primary" @click="onSubmit">提交</n-button>
                                        <n-button @click="changeModalShow(false)">取消</n-button>
                                    </n-space>
                                </n-form-item-gi>
                            </n-grid>
                        </n-form>
                    </n-tab-pane>
                    <n-tab-pane name="节点负责人设置">
                        <n-form v-if="nodeDirectorList.length" label-placement="left" label-width="auto">
                            <n-grid :cols="24" x-gap="16">
                                <template v-for="(item, index) in nodeDirectorList" :key="index">
                                    <n-form-item-gi
                                        v-if="item.nodeKey !== 'MatchContractOrderList'"
                                        :label="item.nodeName"
                                        :span="12"
                                    >
                                        <UserSelector
                                            v-model:value="item.nodeDirector"
                                            :multiple="false"
                                            class="w-100%"
                                            key-name="username"
                                            placeholder="请选择节点负责人"
                                        />
                                    </n-form-item-gi>
                                </template>

                                <n-form-item-gi :span="24">
                                    <n-space>
                                        <n-button type="primary" @click="onSubmit">提交</n-button>
                                        <n-button @click="changeModalShow(false)">取消</n-button>
                                    </n-space>
                                </n-form-item-gi>
                            </n-grid>
                        </n-form>
                        <n-result v-else class="py-66px" status="404" title="请先选择项目类型" />
                    </n-tab-pane>
                </n-tabs>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref, watchEffect } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { GET_CUSTOMER_LIST } from "@/api/application/customer";
import { useDicts } from "@/hooks";
import { UserSelector } from "@/components/UserSelector";
import { ADD_PROJECT, GET_NODES_BY_TYPE, GET_PROJECT_BY_ID, UPDATE_PROJECT } from "@/api/application/sale";
import { GET_YD_COMPANY_LIST } from "@/api/permission";
import { useStoreBusiness } from "@/store";
import { GET_REGION_TREE } from "@/api/public";
import { FileValueUploader } from "@/components/Uploader";

const props = withDefaults(defineProps<{ show: boolean; configData: UnKnownObject }>(), {
    show: () => false
});

const emits = defineEmits(["update:show", "refresh"]);

const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

const changeModalShow = (show: boolean) => {
    emits("update:show", show);
    if (!show) {
        clearFrom();
    }
};

const { dictLibs, getDictLibs, dictValueToLabel } = useDicts();

const storeBusiness = useStoreBusiness();

// 表单实例
const formRef = ref<FormInst | null>(null);

// 表单校验
const formRules = {
    projectName: { required: true, message: "请输入项目名称", trigger: ["input", "blur"] },
    customerId: { required: true, message: "请选择所属客户", trigger: ["blur", "change"] },
    firstRecorder: { required: true, message: "请选择信息首报人", trigger: ["blur", "change"] },
    // 2025年3月新增
    tenderer: { required: true, message: "请输入招标人", trigger: ["input", "blur"] },
    tenderProjectNumber: { required: true, message: "请输入招投标项目编号", trigger: ["input", "blur"] },
    tenderAgency: { required: false, message: "请输入招标代理机构", trigger: ["input", "blur"] },
    numberOfBidSections: { required: false, message: "请输入标段/标包数量", trigger: ["input", "blur"] },
    provinceAndCity: { required: false, message: "请选择所属省市", trigger: ["blur", "change"] },
    informationReleaseTime: { required: true, message: "请选择招标信息发布时间", trigger: ["blur", "change"] },
    bidPurchaseDeadline: { required: true, message: "请选择购买标书截止时间", trigger: ["blur", "change"] },
    bidSubmissionDeadline: { required: false, message: "请选择递交标书截止时间", trigger: ["blur", "change"] },
    tenderDocuments: { required: true, message: "选择招标文件", trigger: ["blur", "change"] },
    // 2025年3月二批新增
    reportUser: { required: true, message: "请选择报备人", trigger: ["blur", "change"] },
    reportDate: { required: true, message: "请选择报备日期", trigger: ["blur", "change"] },
    bidPurchaseFee: { required: false, message: "请输入标书购买费用", trigger: ["input", "blur"], type: "number" },
    bidBond: { required: false, message: "请输入投标保证金", trigger: ["input", "blur"], type: "number" },
    bidder: { required: false, message: "请输入投标单位", trigger: ["input", "blur"] }
};

// 表单数据
interface FormDataProps {
    [key: string]: any;
}

const initFormData: FormDataProps = {
    customerId: null,
    projectName: null,
    projectCode: null,
    projectType: null,
    projectLeader: null,
    responsibleDeptId: null,
    firstRecorder: null,
    focusedUsers: null,
    nonContractFlag: 0,
    winBidFlag: 0,
    // 2025年3月新增
    tenderer: null,
    tenderProjectNumber: null,
    tenderAgency: null,
    numberOfBidSections: null,
    provinceAndCity: null,
    informationReleaseTime: null,
    bidPurchaseDeadline: null,
    bidSubmissionDeadline: null,
    tenderDocuments: null,
    // 2025年3月二批新增
    reportUser: null,
    reportDate: null,
    bidPurchaseFee: null,
    bidBond: null,
    bidder: null
};

const formData = ref(cloneDeep(initFormData));

const clearFrom = () => {
    formData.value = cloneDeep(initFormData);
    nodeDirectorList.value = [];
};

const setDictLibs = async () => {
    const dictName = ["project_type"];
    await getDictLibs(dictName);
};

// 编辑时获取详情
watchEffect(async () => {
    if (props.show) {
        await getOptions();
        await getNodeDirectorList();
        props.configData.id && getDetail();
    }
});

// 是否存在客户
const hasCustomer = ref(false);

// 获取详情
const getDetail = () => {
    GET_PROJECT_BY_ID({ id: props.configData.id }).then((res) => {
        const rowItem: FormDataProps = res.data.data;
        hasCustomer.value = !!rowItem.customerId;
        formData.value = {
            customerId: rowItem.customerId,
            projectName: rowItem.projectName,
            projectCode: rowItem.projectCode,
            projectLeader: rowItem.projectLeader,
            responsibleDeptId: rowItem.responsibleDeptId,
            firstRecorder: rowItem.firstRecorder,
            focusedUsers: rowItem.focusedUsers,
            nonContractFlag: rowItem.nonContractFlag,
            winBidFlag: rowItem.winBidFlag,
            projectType: String(rowItem.projectType),
            // 2025年3月新增
            tenderer: rowItem.projectTenderInformation?.tenderer,
            tenderProjectNumber: rowItem.projectTenderInformation?.tenderProjectNumber,
            tenderAgency: rowItem.projectTenderInformation?.tenderAgency,
            numberOfBidSections: String(rowItem.projectTenderInformation?.numberOfBidSections),
            provinceAndCity: rowItem.projectTenderInformation?.provinceAndCity,
            informationReleaseTime: rowItem.projectTenderInformation?.informationReleaseTime,
            bidPurchaseDeadline: rowItem.projectTenderInformation?.bidPurchaseDeadline,
            bidSubmissionDeadline: rowItem.projectTenderInformation?.bidSubmissionDeadline,
            tenderDocuments: rowItem.projectTenderInformation?.tenderDocuments,
            // 2025年3月二批新增
            reportUser: rowItem.projectTenderInformation?.reportUser,
            reportDate: rowItem.projectTenderInformation?.reportDate,
            bidPurchaseFee: Number(rowItem.projectTenderInformation?.bidPurchaseFee),
            bidBond: Number(rowItem.projectTenderInformation?.bidBond),
            bidder: rowItem.projectTenderInformation?.bidder
        };
        nodeDirectorList.value = rowItem.nodeDirectorList || [];
    });
};

// 表单选项
const customerOptions = ref<UnKnownObject[]>([]);
const companyIdOptions = ref<UnKnownObject[]>([]);
const provinceAndCityOptions = ref<UnKnownObject[]>([]);

const getOptions = async () => {
    await setDictLibs();
    await GET_CUSTOMER_LIST({ current: 1, size: 1000 }).then(
        (res) => (customerOptions.value = res.data.data.records || [])
    );
    await GET_YD_COMPANY_LIST({}).then((res) => {
        companyIdOptions.value = res.data.data || [];
    });
    GET_REGION_TREE({}).then((res) => (provinceAndCityOptions.value = res.data.data ?? []));
    // 新增的时候默认选中第一个项目类型
    if (!props.configData.id) formData.value.projectType = dictLibs["project_type"][0].value || null;
};

/*
 * 根据项目类型获取流程节点列表
 */
interface NodeDirectorProps {
    nodeId: string | number;
    nodeName: string;
    nodeKey: string;
    formKey: string;
    nodeDirector: Nullable<string>;
}

const nodeDirectorList = ref<NodeDirectorProps[]>([]);

const getNodeDirectorList = async () => {
    await GET_NODES_BY_TYPE({
        nonContractFlag: formData.value.nonContractFlag,
        winBidFlag: formData.value.winBidFlag
    }).then((res) => {
        nodeDirectorList.value = (res.data.data || []).map((i: NodeDirectorProps) => ({ ...i, nodeDirector: null }));
    });
    autoFillNodeDirector();
};

// 自动填充节点负责人
const autoFillNodeDirector = () => {
    if (storeBusiness.projectNodeDirectors.length) {
        (storeBusiness.projectNodeDirectors || [])
            .filter((item) => item.nodeKey !== "MatchContractOrderList")
            .forEach((item) => {
                const node = nodeDirectorList.value.find((i) => i.nodeKey === item.nodeKey);
                if (node) node.nodeDirector = item.nodeDirector;
            });
    }
};

/*
 * 提交表单
 */
const onSubmit = async () => {
    const validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    if (!props.configData.id) {
        ADD_PROJECT({
            ...formData.value,
            nodeDirectorList: nodeDirectorList.value
        }).then((res) => {
            if (res.data.code === 0) {
                storeBusiness.setProjectNodeDirectors(nodeDirectorList.value || []);
                window.$message.success("新增成功");
                changeModalShow(false);
                emits("refresh");
            }
        });
    } else {
        UPDATE_PROJECT({
            projectId: props.configData.id,
            ...formData.value,
            nodeDirectorList: nodeDirectorList.value
        }).then((res) => {
            if (res.data.code === 0) {
                storeBusiness.setProjectNodeDirectors(nodeDirectorList.value || []);
                window.$message.success("编辑成功");
                changeModalShow(false);
                emits("refresh");
            }
        });
    }
};
</script>
