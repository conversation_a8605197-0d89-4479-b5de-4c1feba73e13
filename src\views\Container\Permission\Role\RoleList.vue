<template>
    <div class="user-list">
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-space class="mb">
                <n-button secondary type="primary" @click="openEditModal()">新增</n-button>
            </n-space>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <role-edit-modal v-model:id="editModal.id" v-model:show="editModal.show" @refresh="getTableData" />
        <role-menu-permission-modal
            v-model:id="menuPermissionModal.id"
            v-model:show="menuPermissionModal.show"
            @refresh="getTableData"
        />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from "vue";
import type { DataTableColumns } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { TableActions } from "@/components/TableActions";
import { DELETE_ROLE, GET_ROLE_LIST } from "@/api/permission";
import { useCommonTable } from "@/hooks";
import RoleEditModal from "./RoleEditModal.vue";
import RoleMenuPermissionModal from "./RoleMenuPermissionModal.vue";

interface RowProps<T = string | null> {
    roleId: T | number;
    roleName: T;
    roleCode: T;
    roleDesc: T;
}

onMounted(() => {
    getSearchOptions();
    getTableData();
});

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    {
        prop: "roleName",
        type: "input",
        label: "角色名"
    }
]);

let searchOptions = ref<TableSearchbarOptions>({});

let searchForm = ref<TableSearchbarData>({
    roleName: null
});

let getSearchOptions = () => {};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("roleId");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "角色名",
        key: "roleName",
        align: "center"
    },
    {
        title: "角色标识",
        key: "roleCode",
        align: "center"
    },
    {
        title: "角色描述",
        key: "roleDesc",
        align: "center"
    },
    {
        title: "所属角色组",
        key: "roleGroup",
        align: "center"
    },
    {
        title: "创建时间",
        key: "createTime",
        align: "center"
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: 220,
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "菜单权限",
                        tertiary: true,
                        type: "warning",
                        onClick: () => {
                            openMenuPermissionModal(row.roleId);
                        }
                    },
                    {
                        label: "编辑",
                        tertiary: true,
                        onClick: () => {
                            openEditModal(row.roleId);
                        }
                    },
                    {
                        label: "删除",
                        tertiary: true,
                        type: "error",
                        onClick: () => {
                            onDelete(row.roleId);
                        }
                    }
                ]
            });
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_ROLE_LIST({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        ...searchForm.value
    }).then((res) => {
        if (res.data.code === 0) {
            console.log("角色列表", res.data);
            tableData.value = res.data.data.records;
            tablePagination.itemCount = res.data.data.total;
            tableLoading.value = false;
        }
    });
};

// 搜索
let onSearch = () => {
    tablePagination.page = 1;
    tablePagination.pageSize = 10;
    getTableData();
};

// 新增编辑
let editModal = ref<{ show: boolean; id: string | number | null }>({
    show: false,
    id: null
});

let openEditModal = (id?: string | number | null) => {
    editModal.value.show = true;
    editModal.value.id = id || null;
    console.log(11111, editModal.value.id);
};

// 删除
let onDelete = (id?: string | number | null) => {
    let menuId = id ? id : tableSelection.value[0];
    window.$dialog.warning({
        title: "警告",
        content: "确定删除该角色吗？",
        positiveText: "删除",
        negativeText: "取消",
        onPositiveClick: () => {
            DELETE_ROLE({ id: menuId }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("删除成功");
                    onSearch();
                } else {
                    window.$message.error(res.data.msg);
                }
            });
        }
    });
};

// 菜单权限
let menuPermissionModal = ref<{ show: boolean; id: string | number | null }>({
    show: false,
    id: null
});

let openMenuPermissionModal = (id?: string | number | null) => {
    menuPermissionModal.value.show = true;
    menuPermissionModal.value.id = id || null;
};
</script>
