import { defineComponent, onMounted, reactive, ref } from "vue";
import {
    TableSearchbar,
    TableSearchbarConfig,
    TableSearchbarData,
    TableSearchbarOptions
} from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import {
    GET_OPERATION_TABLE_EXPORT_PAGE_LIST,
    EXPORT_OPERATION_TABLE_EXPORT,
    DOWNLOAD_IRON_TOWER,
    GET_IRON_TECHNIQUE_PAGE_LIST,
    GET_IRON_PRODUCTION_PLAN_PAGE_LIST
} from "@/api/application/TowerScan";
import { TableActions } from "@/components/TableActions";

export default defineComponent({
    name: "TowerScanExportPlan",
    setup(props) {
        // 导出按钮配置
        const exportButtonConfigs = [
            { label: "下料表", processType: "2", status: 1 },
            { label: "后处理表", processType: "4", status: 1 },
            { label: "制孔表", processType: "3", status: 1 },
            { label: "非技术工艺表", processType: undefined, status: 2 },
            { label: "组装电焊表", processType: undefined, status: 3 }
            // { label: "零件入库表", processType: "0", status: 0 }
        ];

        // 搜索项
        const searchConfig = ref<TableSearchbarConfig>([
            { label: "计划名称", prop: "planName", type: "input" },
            {
                label: "计划开始时间",
                prop: "issuedDateStart",
                type: "date",
                dateFormat: "yyyy-MM-dd",
                labelWidth: "100"
            },
            { label: "计划结束时间", prop: "issuedDateEnd", type: "date", dateFormat: "yyyy-MM-dd", labelWidth: "100" }
            // { label: "计划状态", prop: "planStatus", type: "select" }
        ]);

        const searchOptions = ref<TableSearchbarOptions>({
            planStatus: [
                { label: "未开始", value: 1 },
                { label: "进行中", value: 2 },
                { label: "已完成", value: 3 },
                { label: "已结束", value: 4 }
            ]
        });

        const getSearchOptions = async () => {
            // 不再需要设置字典数据
        };

        const searchForm = ref<TableSearchbarData>({
            planName: null,
            planStatus: 2,
            planStartTime: null,
            planEndTime: null
        });

        const onSearch = () => {
            tablePagination.page = 1;
            getTableData();
        };

        // 按工序导出表单数据
        const processExportForm = ref({
            planId: null,
            techniqueId: null,
            status: 1
        });

        // 工序选项和计划选项
        const techniqueOptions = ref<any[]>([]);
        const planOptions = ref<any[]>([]);

        // 状态选项（写死）
        const statusOptions = [
            { label: "标准", value: 1 },
            { label: "非标准", value: 2 },
            { label: "电焊组装", value: 3 },
            { label: "零件入库", value: 0 }
        ];

        // 获取工序选项
        const getTechniqueOptions = async () => {
            try {
                const res = await GET_IRON_TECHNIQUE_PAGE_LIST({
                    current: 1,
                    size: 999
                });
                if (res.data.code === 0) {
                    techniqueOptions.value = res.data.data.records.map((item: any) => ({
                        label: item.techniqueName,
                        value: item.id
                    }));
                }
            } catch (error) {
                console.error("获取工序选项失败:", error);
            }
        };

        // 获取计划选项
        const getPlanOptions = async () => {
            try {
                const res = await GET_IRON_PRODUCTION_PLAN_PAGE_LIST({
                    current: 1,
                    size: 999
                });
                if (res.data.code === 0) {
                    planOptions.value = res.data.data.records.map((item: any) => ({
                        label: item.planName,
                        value: item.id
                    }));
                }
            } catch (error) {
                console.error("获取计划选项失败:", error);
            }
        };

        // 按工序导出
        const exportByProcess = async () => {
            if (!processExportForm.value.techniqueId) {
                window.$message.warning("请选择工序名称");
                return;
            }

            let exportRes: any;

            try {
                window.$message.loading("正在导出工序清单...", { duration: 0 });

                exportRes = await EXPORT_OPERATION_TABLE_EXPORT({
                    planId: processExportForm.value.planId,
                    techniqueId: processExportForm.value.techniqueId,
                    status: processExportForm.value.status
                });

                if (exportRes.data.code === 0) {
                    // 导出成功后下载文件
                    const downloadRes = await DOWNLOAD_IRON_TOWER({
                        fileName: exportRes.data.data.fileName || "工序清单.xlsx"
                    });

                    // 创建下载链接
                    const blob = new Blob([downloadRes.data], {
                        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    });
                    const url = window.URL.createObjectURL(blob);
                    const link = document.createElement("a");
                    link.href = url;
                    link.download = exportRes.data.data.fileName || "工序清单.xlsx";
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    window.URL.revokeObjectURL(url);

                    window.$message.destroyAll();
                    window.$message.success("导出成功");
                } else {
                    window.$message.destroyAll();
                    window.$message.error(exportRes.data.msg || "导出失败");
                }
            } catch (error) {
                window.$message.destroyAll();
                window.$message.error(exportRes.data.msg || "导出失败");
            }
        };

        // 导出工序表
        const exportOperationTable = async (
            row: RowProps,
            exportConfig: { label: string; processType?: string; status: number }
        ) => {
            let exportRes: any;
            try {
                window.$message.loading("正在导出工序表...", { duration: 0 });

                const requestParams: any = {
                    /*
                     * TODO
                     * 待验证
                     * 原：planId: row.id
                     * 现：planLineId: row.planLineId
                     */
                    planLineId: row.planLineId,
                    status: exportConfig.status
                };

                // 只有在processType存在时才添加到请求参数中
                if (exportConfig.processType) {
                    requestParams.processType = exportConfig.processType;
                }

                exportRes = await EXPORT_OPERATION_TABLE_EXPORT(requestParams);

                if (exportRes.data.code === 0) {
                    // 导出成功后下载文件
                    const downloadRes = await DOWNLOAD_IRON_TOWER({
                        fileName: exportRes.data.data.fileName || `${exportConfig.label}_${row.planName}.xlsx`
                    });

                    // 创建下载链接
                    const blob = new Blob([downloadRes.data], {
                        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    });
                    const url = window.URL.createObjectURL(blob);
                    const link = document.createElement("a");
                    link.href = url;
                    link.download = exportRes.data.data.fileName || `${exportConfig.label}_${row.planName}.xlsx`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    window.URL.revokeObjectURL(url);

                    window.$message.destroyAll();
                    window.$message.success("导出成功");
                } else {
                    window.$message.destroyAll();
                    window.$message.error(exportRes.data.msg || "导出失败");
                }
            } catch (error) {
                window.$message.destroyAll();
                window.$message.error(exportRes.data.msg || "导出失败");
            }
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            { title: "计划名称", key: "planName", align: "center" },
            { title: "计划下发人", key: "issuedByName", align: "center", render: (row) => row.issuedByName ?? "/" },
            {
                title: "计划执行人",
                key: "planImplementorNames",
                align: "center",
                render: (row) => row.planImplementorNames ?? "/"
            },
            { title: "下发日期", key: "issuedDate", align: "center", render: (row) => row.issuedDate ?? "/" },
            {
                title: "生产计划状态",
                key: "planStatus",
                align: "center",
                render: (row) => {
                    switch (row.planStatus) {
                        case 1:
                            return <n-text type="error">未开始</n-text>;
                        case 2:
                            return <n-text type="info">进行中</n-text>;
                        case 3:
                            return <n-text type="success">已完成</n-text>;
                        case 4:
                            return <n-text type="warning">已结束</n-text>;
                        default:
                            return <n-text>/</n-text>;
                    }
                }
            },
            {
                title: "导出工序表",
                key: "action",
                align: "center",
                width: 420,
                render: (row) => {
                    const buttonActions = exportButtonConfigs.map((config) => ({
                        label: `导出${config.label}`,
                        tertiary: true,
                        type: "primary" as const,
                        onClick: () => exportOperationTable(row, config)
                    }));

                    return <TableActions type="button" buttonActions={buttonActions} />;
                }
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_OPERATION_TABLE_EXPORT_PAGE_LIST({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                ...searchForm.value
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        onMounted(async () => {
            await getSearchOptions();
            await getTechniqueOptions();
            await getPlanOptions();
            getTableData();
        });

        return () => (
            <div class="tower-scan-production-line-list">
                <n-card>
                    <TableSearchbar
                        form={searchForm.value}
                        config={searchConfig.value}
                        options={searchOptions.value}
                        onSearch={onSearch}
                    />
                </n-card>
                <n-card class="mt">
                    <n-tabs animated type="bar" defaultValue={1}>
                        <n-tab-pane name={1} tab="按计划导出">
                            <n-tabs
                                animated
                                type="bar"
                                v-model:value={searchForm.value.planStatus}
                                onUpdate:value={() => onSearch()}
                            >
                                <n-tab-pane name={2} tab="未完成计划数" />
                                <n-tab-pane name={3} tab="已完成计划数" />
                                <n-tab-pane name={4} tab="已结束计划数" />
                            </n-tabs>
                            <n-data-table
                                columns={tableColumns.value}
                                data={tableData.value}
                                loading={tableLoading.value}
                                pagination={tablePagination}
                                row-key={tableRowKey}
                                single-line={false}
                                bordered
                                remote
                                striped
                                onUpdate:checked-row-keys={changeTableSelection}
                            />
                        </n-tab-pane>
                        <n-tab-pane name={2} tab="按工序导出">
                            <n-form label-placement="left" label-width="100" style="padding: 24px 0;">
                                <n-form-item label="所属计划">
                                    <n-select
                                        v-model:value={processExportForm.value.planId}
                                        options={planOptions.value}
                                        placeholder="请选择计划"
                                        clearable
                                        filterable
                                        label-field="label"
                                        value-field="value"
                                    />
                                </n-form-item>
                                <n-form-item label="工序名称" required>
                                    <n-select
                                        v-model:value={processExportForm.value.techniqueId}
                                        options={techniqueOptions.value}
                                        placeholder="请选择工序"
                                        clearable
                                        filterable
                                        label-field="label"
                                        value-field="value"
                                    />
                                </n-form-item>
                                <n-form-item label="工序状态">
                                    <n-select
                                        v-model:value={processExportForm.value.status}
                                        options={statusOptions}
                                        placeholder="请选择工序状态"
                                        label-field="label"
                                        value-field="value"
                                    />
                                </n-form-item>
                                <n-form-item>
                                    <n-button type="primary" onClick={exportByProcess}>
                                        导出工序清单
                                    </n-button>
                                </n-form-item>
                            </n-form>
                        </n-tab-pane>
                    </n-tabs>
                </n-card>
            </div>
        );
    }
});
