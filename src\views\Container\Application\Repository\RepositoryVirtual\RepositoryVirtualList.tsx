import { defineComponent, h, onMounted, reactive, ref } from "vue";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable, useDicts } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import { GET_REPOSITORY_VIRTUAL_STOREROOM_LIST } from "@/api/application/repository";
import RepositoryVirtualDetail from "@/views/Container/Application/Repository/RepositoryVirtual/RepositoryVirtualDetail";
import { TableActions } from "@/components/TableActions";

export default defineComponent({
    name: "RepositoryVirtualList",
    setup(props, { expose }) {
        // 字典操作
        const { dictLibs, getDictLibs } = useDicts();

        const setDictLibs = async () => {
            let dictName = ["common_units"];
            await getDictLibs(dictName);
        };

        // 搜索项
        const searchConfig = ref<TableSearchbarConfig>([{ label: "入库单", prop: "enterRecordId", type: "input" }]);

        const searchOptions = ref<TableSearchbarOptions>({});

        const getSearchOptions = async () => {};

        const searchForm = ref<TableSearchbarData>({
            enterRecordId: null
        });

        const onSearch = () => {
            getTableData();
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            { title: "入库单", key: "enterRecordId", align: "center" },
            { title: "申请人", key: "enterRecord.applyByName", align: "center" },
            { title: "核对人", key: "enterRecord.confirmByName", align: "center" },
            { title: "申请入库时间", key: "enterRecord.applyTime", align: "center" },
            { title: "入库核对时间", key: "enterRecord.confirmTime", align: "center" },
            { title: "备注", key: "enterRecord.remark", align: "center" },
            {
                title: "操作",
                key: "actions",
                align: "center",
                width: 120,
                render(row) {
                    return h(TableActions, {
                        type: "button",
                        buttonActions: [
                            {
                                label: "查看详情",
                                tertiary: true,
                                onClick: () => openDetailModal(row)
                            }
                        ]
                    });
                }
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_REPOSITORY_VIRTUAL_STOREROOM_LIST({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                ...searchForm.value
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        // 详情弹窗
        const detailModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const openDetailModal = (row: RowProps) => {
            detailModal.value.show = true;
            detailModal.value.configData = row;
        };

        onMounted(async () => {
            await getSearchOptions();
            await setDictLibs();
            getTableData();
        });

        expose({
            refresh: getTableData
        });

        return () => (
            <div class="plastic-mes-raw-material">
                <n-card>
                    <TableSearchbar
                        form={searchForm.value}
                        config={searchConfig.value}
                        options={searchOptions.value}
                        onSearch={onSearch}
                    />
                </n-card>
                <n-card class="mt">
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        pagination={tablePagination}
                        row-key={tableRowKey}
                        single-line={false}
                        bordered
                        remote
                        striped
                        onUpdate:checked-row-keys={changeTableSelection}
                    />
                </n-card>
                <RepositoryVirtualDetail
                    v-model:show={detailModal.value.show}
                    config-data={detailModal.value.configData}
                />
            </div>
        );
    }
});
