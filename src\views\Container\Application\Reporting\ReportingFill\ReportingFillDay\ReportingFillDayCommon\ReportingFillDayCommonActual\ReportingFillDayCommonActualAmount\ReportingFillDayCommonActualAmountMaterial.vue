<template>
    <n-spin :show="loadingShow">
        <template #description>正在处理中，请耐心等候</template>
        <n-card>
            <table-searchbar
                auto-search
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
                @componentClick="onComponentClick"
            />
        </n-card>
        <n-card class="mt">
            <n-space class="mb">
                <n-button secondary type="success" @click="saveTableData">保存填写</n-button>
                <n-button secondary type="warning" @click="onExpand()">一键展开/收缩</n-button>
            </n-space>
            <n-tabs animated type="bar">
                <n-tab-pane name="原材料清单">
                    <n-card
                        v-if="
                            !materialData.manufactureFormulaSpecAmountList ||
                            materialData.manufactureFormulaSpecAmountList <= 0
                        "
                    >
                        <n-result class="py-50px" status="404" title="暂无数据" />
                    </n-card>
                    <n-collapse v-else v-model:expanded-names="expandedData.manufactureFormulaSpecAmountList">
                        <n-collapse-item
                            v-for="(item, index) in materialData.manufactureFormulaSpecAmountList"
                            :name="index"
                            :title="item.materialName"
                        >
                            <n-data-table
                                :columns="tableColumns"
                                :data="item.specAmountList"
                                :single-line="false"
                                bordered
                                remote
                                striped
                            />
                        </n-collapse-item>
                    </n-collapse>
                </n-tab-pane>
                <n-tab-pane name="独立原材料清单">
                    <n-card
                        v-if="!materialData.manufactureSpecAmountList || materialData.manufactureSpecAmountList <= 0"
                    >
                        <n-result class="py-50px" status="404" title="暂无数据" />
                    </n-card>
                    <n-collapse v-else v-model:expanded-names="expandedData.manufactureSpecAmountList">
                        <n-collapse-item
                            v-for="(item, index) in materialData.manufactureSpecAmountList"
                            :name="index"
                            :title="item.materialName"
                        >
                            <n-data-table
                                :columns="tableColumns"
                                :data="item.specAmountList"
                                :single-line="false"
                                bordered
                                remote
                                striped
                            />
                        </n-collapse-item>
                    </n-collapse>
                </n-tab-pane>
                <n-tab-pane name="半成品清单">
                    <n-data-table
                        :columns="semiTableColumns"
                        :data="materialData.manufactueSemiAmountList"
                        :single-line="false"
                        bordered
                        remote
                        striped
                    />
                </n-tab-pane>
            </n-tabs>
        </n-card>
        <ReportingFillDayCommonActualAmountMaterialCheckModal
            v-model:show="checkModal.show"
            :config-data="checkModal.configData"
            @refresh="excessCheckRefresh"
            @update:show="excessCheckUpdateShow"
        />
    </n-spin>
</template>

<script lang="ts" setup>
import { h, onMounted, ref } from "vue";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import {
    CHECK_DAILY_SPEC_AMOUNT_LIST,
    GET_CONFIG_WORK_GROUP_LIST,
    GET_DAILY_SPEC_AMOUNT_LIST,
    SAVE_DAILY_SPEC_AMOUNT_LIST
} from "@/api/application/reporting";
import dayjs from "dayjs";
import type { DataTableColumns } from "naive-ui";
import { NButton, NInput, NInputGroup, NInputGroupLabel, NRadio, NRadioGroup, NTooltip } from "naive-ui";
import ReportingFillDayCommonActualAmountMaterialCheckModal from "./ReportingFillDayCommonActualAmountMaterialCheckModal.vue";
import { useStoreReportingSearch } from "@/store";
import { UnitSelector } from "@/views/Container/Application/Reporting/components";
import { useDicts } from "@/hooks";
import { cloneDeep } from "lodash-es";
import { useThrottleFn } from "@vueuse/core";

let storeReportingSearch = useStoreReportingSearch();

let loadingShow = ref(false);

// 字典操作
let { dictLibs, getDictLibs } = useDicts();

let setDictLibs = async () => {
    let dictName = ["common_units"];
    await getDictLibs(dictName);
};

onMounted(async () => {
    await getWorkGroupIdOptions();
    await setDictLibs();
    getTableData();
});

let getWorkGroupIdOptions = async () => {
    await GET_CONFIG_WORK_GROUP_LIST({}).then((res) => {
        searchOptions.value.workGroupId = (res.data.data ?? []).map((item: any) => ({
            label: item.companyName + "-" + item.workshopName + "-" + item.groupName,
            value: item.id
        }));
        searchForm.value.workGroupId = searchOptions.value.workGroupId[0].value;
        searchForm.value.planDate = dayjs().format("YYYY-MM-DD");
        if (storeReportingSearch.getSearchForm.workGroupId) {
            searchForm.value.workGroupId = storeReportingSearch.getSearchForm.workGroupId;
        }
        if (storeReportingSearch.getSearchForm.planDate) {
            searchForm.value.planDate = storeReportingSearch.getSearchForm.planDate;
        }
    });
};

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { label: "班组", type: "select", prop: "workGroupId", span: 2 },
    { label: "日期筛选", type: "date", dateFormat: "yyyy-MM-dd", prop: "planDate" }
]);

let searchOptions = ref<TableSearchbarOptions>({ workGroupId: [] });

let searchForm = ref<TableSearchbarData>({ workGroupId: null, planDate: null });

let onSearch = () => {
    storeReportingSearch.setSearchForm({
        workGroupId: searchForm.value.workGroupId ?? null,
        planDate: searchForm.value.planDate ?? null
    });
    getTableData();
};

// 搜索栏自动保存逻辑
let autoSave = useThrottleFn(async () => {
    materialData.value.manufactureFormulaSpecAmountList.forEach((item: any) => {
        item.specAmountList.forEach((row: any) => {
            if (row.temporaryUnit) {
                if (row.temporaryUnit === "0" && row.amountUnit === "1") {
                    row.trueAmount = (row.trueAmount / 1000).toFixed(2);
                } else if (row.temporaryUnit === "1" && row.amountUnit === "0") {
                    row.trueAmount = (row.trueAmount * 1000).toFixed(2);
                }
            }
        });
    });
    materialData.value.manufactureSpecAmountList.forEach((item: any) => {
        item.specAmountList.forEach((row: any) => {
            if (row.temporaryUnit) {
                if (row.temporaryUnit === "0" && row.amountUnit === "1") {
                    row.trueAmount = (row.trueAmount / 1000).toFixed(2);
                } else if (row.temporaryUnit === "1" && row.amountUnit === "0") {
                    row.trueAmount = (row.trueAmount * 1000).toFixed(2);
                }
            }
        });
    });
    let checkRes = await CHECK_DAILY_SPEC_AMOUNT_LIST({ ...materialData.value });
    let checkData = checkRes.data.data ?? {
        manufactureFormulaSpecAmountList: [],
        manufactureSpecAmountList: [],
        manufactueSemiAmountList: []
    };
    if (!!checkCheckFlag(checkData)) {
        openCheckModal(checkData);
        return false;
    }
    await SAVE_DAILY_SPEC_AMOUNT_LIST({ ...materialData.value }).then((res) => {
        if (res.data.code === 0) window.$message.success("自动保存成功");
    });
}, 1000);

let onComponentClick = async (val: TableSearchbarData) => {
    // await autoSave();
};

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        title: "规格型号",
        align: "center",
        key: "spec",
        render: (row) => {
            return `${row.materialName} - ${row.spec}`;
        }
    },
    // 2023年9月8日单位变更需要对接数据-已处理
    {
        title: "定额用量",
        align: "center",
        key: "amount",
        render: (row) => {
            let temporaryAmount = cloneDeep(row.amount);
            if (row.temporaryUnit === "0" && row.amountUnit === "1") {
                temporaryAmount = (temporaryAmount * 1000).toFixed(2);
            } else if (row.temporaryUnit === "1" && row.amountUnit === "0") {
                temporaryAmount = (temporaryAmount / 1000).toFixed(2);
            }
            return h("div", { class: "flex-center" }, [
                h("span", {}, temporaryAmount),
                h(UnitSelector, {
                    type: "text",
                    value: row.temporaryUnit,
                    options: dictLibs["common_units"] ?? []
                })
            ]);
        }
    },
    {
        title: "实际数量",
        align: "center",
        key: "trueAmount",
        render: (row) => {
            return h(
                NTooltip,
                {},
                {
                    trigger: () => {
                        row.temporaryUnit = cloneDeep(row.amountUnit);
                        return h(NInputGroup, {}, () => [
                            h(NInput, {
                                value: row.trueAmount,
                                onUpdateValue: (v) => (row.trueAmount = v),
                                onFocus: () => {
                                    if (row.trueAmount === "0") row.trueAmount = "";
                                },
                                onBlur: () => {
                                    if (!row.trueAmount) row.trueAmount = "0";
                                }
                            }),
                            row.amountUnit === "0" || row.amountUnit === "1"
                                ? h(NInputGroupLabel, {}, () => [
                                      h(
                                          NRadioGroup,
                                          {
                                              value: row.temporaryUnit,
                                              onUpdateValue: (v) => {
                                                  row.temporaryUnit = v;
                                              }
                                          },
                                          () => [
                                              h(NRadio, { label: "KG", value: "0" }),
                                              h(NRadio, { label: "吨", value: "1" })
                                          ]
                                      )
                                  ])
                                : h(NInputGroupLabel, {}, () => [
                                      h(UnitSelector, {
                                          type: "text",
                                          value: row.amountUnit,
                                          options: dictLibs["common_units"] ?? []
                                      })
                                  ])
                        ]);
                    },
                    default: () => {
                        return row.trueAmount;
                    }
                }
            );
        }
    }
]);

let semiTableColumns = ref<DataTableColumns<RowProps>>([
    { title: "名称", align: "center", key: "semiPoleName" },
    { title: "规格型号", align: "center", key: "semiProductModel" },
    {
        title: "定额用量",
        align: "center",
        key: "amount",
        render: (row) => {
            return h("div", { class: "flex-center" }, [
                h("span", {}, row.amount),
                h(UnitSelector, {
                    type: "text",
                    value: row.semiUnit,
                    options: dictLibs["common_units"] ?? []
                })
            ]);
        }
    },
    {
        title: "实际数量",
        align: "center",
        key: "trueAmount",
        render: (row) => {
            return h(
                NTooltip,
                {},
                {
                    trigger: () => {
                        return h(NInputGroup, {}, () => [
                            h(NInput, {
                                value: row.trueAmount,
                                onUpdateValue: (v) => (row.trueAmount = v),
                                onFocus: () => {
                                    if (row.trueAmount === "0") row.trueAmount = "";
                                },
                                onBlur: () => {
                                    if (!row.trueAmount) row.trueAmount = "0";
                                }
                            }),
                            h(NInputGroupLabel, {}, () => [
                                h(UnitSelector, {
                                    type: "text",
                                    value: row.semiUnit,
                                    options: dictLibs["common_units"] ?? []
                                })
                            ])
                        ]);
                    },
                    default: () => {
                        return row.trueAmount;
                    }
                }
            );
        }
    }
]);

let expandedData = ref<any>({
    manufactureFormulaSpecAmountList: [],
    manufactureSpecAmountList: []
});

let getTableData = () => {
    GET_DAILY_SPEC_AMOUNT_LIST({
        ...searchForm.value
    }).then((res) => {
        materialData.value = res.data.data ?? {
            manufactureFormulaSpecAmountList: [],
            manufactureSpecAmountList: [],
            manufactueSemiAmountList: []
        };
        // 默认展开
        materialData.value.manufactureFormulaSpecAmountList.forEach((_: any, i: number) =>
            expandedData.value.manufactureFormulaSpecAmountList.push(i)
        );
        materialData.value.manufactureSpecAmountList.forEach((_: any, i: number) =>
            expandedData.value.manufactureSpecAmountList.push(i)
        );
        isExpand.value = true;
    });
};

let excessCheckRefresh = () => {
    loadingShow.value = false;
    getTableData();
};

let excessCheckUpdateShow = (val: boolean) => {
    if (!val) {
        loadingShow.value = false;
    }
};

// 展开/收缩
let isExpand = ref(false);

let onExpand = () => {
    if (isExpand.value) {
        expandedData.value.manufactureFormulaSpecAmountList = [];
        expandedData.value.manufactureSpecAmountList = [];
    } else {
        getTableData();
    }
    isExpand.value = !isExpand.value;
};

interface RowProps {
    [key: string]: any;
}

let materialData = ref<any>({
    manufactureFormulaSpecAmountList: [],
    manufactureSpecAmountList: [],
    manufactueSemiAmountList: []
});

//保存填写
let checkCheckFlag = (jsonData: any) => {
    if (typeof jsonData === "object") {
        if (Array.isArray(jsonData)) {
            for (let i = 0; i < jsonData.length; i++) {
                if (checkCheckFlag(jsonData[i])) {
                    return true;
                }
            }
        } else {
            for (let key in jsonData) {
                if (jsonData.hasOwnProperty(key)) {
                    if (key === "checkFlag" && jsonData[key] === 0) {
                        return true;
                    } else if (checkCheckFlag(jsonData[key])) {
                        return true;
                    }
                }
            }
        }
    }
    return false;
};

let saveTableData = async () => {
    loadingShow.value = true;
    materialData.value.manufactureFormulaSpecAmountList.forEach((item: any) => {
        item.specAmountList.forEach((row: any) => {
            if (row.temporaryUnit) {
                if (row.temporaryUnit === "0" && row.amountUnit === "1") {
                    row.trueAmount = (row.trueAmount / 1000).toFixed(2);
                } else if (row.temporaryUnit === "1" && row.amountUnit === "0") {
                    row.trueAmount = (row.trueAmount * 1000).toFixed(2);
                }
            }
        });
    });
    materialData.value.manufactureSpecAmountList.forEach((item: any) => {
        item.specAmountList.forEach((row: any) => {
            if (row.temporaryUnit) {
                if (row.temporaryUnit === "0" && row.amountUnit === "1") {
                    row.trueAmount = (row.trueAmount / 1000).toFixed(2);
                } else if (row.temporaryUnit === "1" && row.amountUnit === "0") {
                    row.trueAmount = (row.trueAmount * 1000).toFixed(2);
                }
            }
        });
    });
    let checkRes = await CHECK_DAILY_SPEC_AMOUNT_LIST({ ...materialData.value });
    let checkData = checkRes.data.data ?? {
        manufactureFormulaSpecAmountList: [],
        manufactureSpecAmountList: [],
        manufactueSemiAmountList: []
    };
    if (!!checkCheckFlag(checkData)) {
        openCheckModal(checkData);
        return false;
    }
    await SAVE_DAILY_SPEC_AMOUNT_LIST({ ...materialData.value }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("保存成功");
            onSearch();
        }
    });
    loadingShow.value = false;
};

// 校验弹窗
let checkModal = ref<{ show: boolean; configData: Record<string, any> }>({
    show: false,
    configData: {}
});

let openCheckModal = (row: RowProps) => {
    checkModal.value.show = true;
    checkModal.value.configData = row;
};
</script>
