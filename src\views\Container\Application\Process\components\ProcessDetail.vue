<template>
    <n-drawer v-model:show="show" :close-on-esc="false" :mask-closable="false" placement="right" width="100vw">
        <div class="h-100vh flex flex-col">
            <n-scrollbar>
                <n-spin :size="66" :show="loading" class="min-h-100vh">
                    <template #description>
                        <div>
                            <div class="mt text-20px">正在加载中，请稍后</div>
                            <div class="mt flex-center">
                                <n-button type="error" size="small" @click="closeModal">关闭</n-button>
                            </div>
                        </div>
                    </template>
                    <div class="p-20px" :class="{ hidden: loading }">
                        <div class="sticky top-0 z-10">
                            <n-card :title="processDetail.processFormVO.wcfName" closable hoverable @close="closeModal">
                                <div class="flex-y-center">
                                    <div class="flex-1">
                                        <n-grid :cols="24" :x-gap="16" :y-gap="16">
                                            <n-grid-item
                                                v-if="processDetail.businessNumber != null"
                                                :span="12"
                                                class="text-14px"
                                            >
                                                审批单号：{{ processDetail.businessNumber }}
                                            </n-grid-item>
                                            <n-grid-item v-else :span="12" class="text-14px">
                                                审批单号：{{ processDetail.businessKey }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                申请人：{{ processDetail.starterTrueName }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                提交时间：{{ processDetail.startTime }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">备注：暂无</n-grid-item>
                                            <n-grid-item v-if="processDetail.status === 5" :span="12" class="text-14px">
                                                撤回原因：{{ processDetail.deleteReason }}
                                            </n-grid-item>
                                        </n-grid>
                                    </div>
                                    <div class="ml-a flex-y-center flex-fixed-200">
                                        <div class="flex-1 text-center">
                                            <div class="text-14px">单据状态</div>
                                            <div class="mt-5px text-30px">
                                                <n-element class="mt-5px text-30px" tag="div">
                                                    <b
                                                        :style="`color:${
                                                            dictValueToAll(
                                                                processDetail.status,
                                                                'process_business_status'
                                                            ).color || ''
                                                        }`"
                                                    >
                                                        {{
                                                            dictValueToLabel(
                                                                processDetail.status,
                                                                "process_business_status"
                                                            )
                                                        }}
                                                    </b>
                                                </n-element>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </n-card>
                        </div>
                        <n-card class="mt-20px" hoverable>
                            <n-tabs animated type="card" size="medium" tab-style="min-width: 100px;font-weight: bold;">
                                <n-tab-pane name="单据">
                                    <div id="printBox" class="relative">
                                        <!--<div class="absolute z-999 w-100% h-100% cursor-not-allowed" />-->
                                        <dynamic-form
                                            v-model="processDetail.processFormVO.elements"
                                            :options="processDetail.processFormVO.wcfOptions"
                                            style="width: calc(100% - 2px)"
                                        />
                                    </div>
                                </n-tab-pane>
                                <n-tab-pane name="流程">
                                    <div class="pt-20px pl-1">
                                        <n-steps vertical>
                                            <template v-for="(item, index) in processDetail.processHistoryVoList">
                                                <!-- 驳回状态 - 优先检查 approvalType -->
                                                <n-step
                                                    v-if="item.approvalType === '3'"
                                                    :title="item.name"
                                                    status="error"
                                                    class="error-step"
                                                >
                                                    <template #icon>
                                                        <DynamicIcon
                                                            icon="CloseOutlined"
                                                            style="color: var(--error-color, #d03050)"
                                                        />
                                                    </template>
                                                    <div class="mt-1 text-red-500">{{ item.comment || "已驳回" }}</div>
                                                    <div v-if="index !== 0" class="mt-1">
                                                        审批人：{{ item.assignee }}
                                                    </div>
                                                    <div v-else class="mt-1">
                                                        发起人：{{ item.starter ?? item.assignee }}
                                                    </div>
                                                    <div v-if="item.endTime" class="mt-1">
                                                        审批时间：{{ item.endTime }}
                                                    </div>
                                                </n-step>
                                                <!-- 未处理状态 -->
                                                <n-step
                                                    v-else-if="item.status !== '已处理'"
                                                    :title="item.name"
                                                    status="wait"
                                                >
                                                    <template #icon>
                                                        <DynamicIcon icon="EditOutlined" />
                                                    </template>
                                                    <div class="mt-1">未处理</div>
                                                    <div v-if="index !== 0" class="mt-1">
                                                        审批人：{{ item.assignee }}
                                                    </div>
                                                    <div v-else class="mt-1">
                                                        发起人：{{ item.starter ?? item.assignee }}
                                                    </div>
                                                </n-step>
                                                <!-- 已处理状态 -->
                                                <n-step v-else status="process" :title="item.name">
                                                    <template #icon>
                                                        <DynamicIcon icon="CheckOutlined" />
                                                    </template>
                                                    <div class="mt-1">{{ item.comment }}</div>
                                                    <div v-if="index !== 0" class="mt-1">
                                                        审批人：{{ item.assignee }}
                                                    </div>
                                                    <div v-else class="mt-1">
                                                        发起人：{{ item.starter ?? item.assignee }}
                                                    </div>
                                                    <div class="mt-1">审批时间：{{ item.endTime }}</div>
                                                </n-step>
                                            </template>
                                            <template #error-icon>
                                                <DynamicIcon icon="CloseOutlined" />
                                            </template>
                                            <template #finish-icon>
                                                <DynamicIcon icon="CopyOutlined" />
                                            </template>
                                            <n-step
                                                v-if="processDetail.status === 5"
                                                title="发起人已撤回"
                                                status="error"
                                            >
                                                <div class="mt-1">撤回人：{{ processDetail.starterTrueName }}</div>
                                                <div class="mt-1">撤回时间：{{ processDetail.updateTime }}</div>
                                            </n-step>
                                            <n-step v-if="!!processDetail.ccPersons" title="" status="finish">
                                                <template #title>
                                                    <n-element tag="div" class="color-[var(--primary-color)]">
                                                        抄送
                                                    </n-element>
                                                </template>
                                                <n-element tag="div" class="mt-1 color-[var(--primary-color)]">
                                                    抄送人：{{ processDetail.ccPersonsNames }}
                                                </n-element>
                                            </n-step>
                                        </n-steps>
                                    </div>
                                </n-tab-pane>
                            </n-tabs>
                            <div class="flex-center mt">
                                <n-button type="primary" size="large" @click="openExportModal">
                                    打印/导出本单据
                                </n-button>
                                <ProcessExport v-model:show="exportModal.show" :configData="exportModal.configData" />
                                <!--<n-space>-->
                                <!--    <n-button v-print="onPrint" type="primary" size="large">打印本单据</n-button>-->
                                <!--    <n-button-->
                                <!--        type="error"-->
                                <!--        size="large"-->
                                <!--        @click="generatePdf('printBox', processDetail.processFormVO.wcfName)"-->
                                <!--    >-->
                                <!--        导出PDF-->
                                <!--    </n-button>-->
                                <!--</n-space>-->
                            </div>
                        </n-card>
                    </div>
                </n-spin>
            </n-scrollbar>
        </div>
    </n-drawer>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import { useStoreUser } from "@/store";
import { DynamicForm } from "@/components/DynamicForm";
import { FormGeneratorProps } from "@/components/FormGenerator";
import { isJSON } from "@/utils/tools";
import { useDicts } from "@/hooks";
import { GET_OA_INSTANCE_FORM } from "@/api/application/oa";
import { DynamicIcon } from "@/components/DynamicIcon";
import { GET_STAFF_BY_USERNAMES } from "@/api/permission";
import ProcessExport from "./ProcessExport.vue";

let storeUser = useStoreUser();
let loading = ref(false);

let props = defineProps({
    show: { type: Boolean, default: false },
    configData: { type: Object as PropType<any> }
});

let emits = defineEmits(["update:show", "refresh"]);

// 字典操作
let { dictLibs, getDictLibs, dictValueToLabel, dictValueToAll } = useDicts();

let setDictLibs = async () => {
    let dictName = ["process_business_status"];
    await getDictLibs(dictName);
};

let processDetail = ref<any>({
    starterTrueName: "",
    startTime: "",
    processFormVO: {
        elements: [],
        wcfName: "",
        wcfOptions: {}
    },
    processHistoryVoList: []
});

watch(
    () => ({ show: props.show, configData: props.configData }),
    async (newVal) => {
        if (newVal.show) {
            loading.value = true;
            try {
                await setDictLibs();
                await GET_OA_INSTANCE_FORM({ processInstId: props.configData.processInstanceId }).then((res) => {
                    if (res.data.code === 0) {
                        processDetail.value = res.data.data;
                        processDetail.value.processFormVO.elements = res.data.data.processFormVO.elements.map(
                            (item: FormGeneratorProps) => {
                                let newModelValue: any = null;
                                if (item.modelValue && item.modelValue !== "null") {
                                    if (isJSON(item.modelValue)) {
                                        let parsedValue = JSON.parse(item.modelValue);
                                        newModelValue = typeof parsedValue === "number" ? item.modelValue : parsedValue;
                                    } else {
                                        newModelValue = item.modelValue;
                                    }
                                }

                                // 为特定组件类型提供正确的默认值
                                if (newModelValue === null) {
                                    if (
                                        item.type === "dynamicTable" ||
                                        item.type === "checkbox" ||
                                        item.type === "ProcessInstanceSelector"
                                    ) {
                                        newModelValue = [];
                                    } else if (item.type === "dynamicInput") {
                                        newModelValue = [];
                                    }
                                }

                                return {
                                    ...item,
                                    modelValue: newModelValue
                                };
                            }
                        );
                    }
                });

                if (processDetail.value.ccPersons) {
                    await GET_STAFF_BY_USERNAMES({ usernames: processDetail.value.ccPersons }).then((res) => {
                        let nameArr = (res.data.data ?? []).map((item: any) => item.trueName);
                        processDetail.value.ccPersonsNames = nameArr.join(",");
                    });
                }
            } finally {
                loading.value = false;
            }
        }
    },
    { deep: true }
);

// 关闭弹窗
let closeModal = () => {
    emits("update:show", false);
};

// 打印相关操作
let onPrint = ref({
    id: "printBox" //这里的id就是上面我们的打印区域id，实现指哪打哪
});

/*
 * 导出相关操作
 */

let exportModal = ref<{ show: boolean; configData: Record<string, any> }>({
    show: false,
    configData: {}
});

let openExportModal = () => {
    exportModal.value.show = true;
    exportModal.value.configData = processDetail.value;
};
</script>
