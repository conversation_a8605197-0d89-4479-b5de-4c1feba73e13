<template>
    <div>
        <n-drawer
            v-model:show="show"
            :close-on-esc="false"
            :height="600"
            :mask-closable="false"
            placement="bottom"
            @update:show="changeModalShow(false)"
        >
            <n-drawer-content closable title="添加产成品">
                <n-form
                    ref="formRef"
                    :model="formData"
                    :rules="formRules"
                    class="w-600px mx-a"
                    label-placement="left"
                    label-width="auto"
                >
                    <n-grid :cols="12" x-gap="16">
                        <n-form-item-gi :span="12" label="所属公司" path="companyId">
                            <n-cascader
                                v-model:value="formData.companyId"
                                :options="companyIdOptions"
                                class="w-100%"
                                clearable
                                filterable
                                label-field="companyName"
                                placeholder="请选择所属公司"
                                value-field="id"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="产成品分类" path="categoryId">
                            <n-tree-select
                                v-model:value="formData.categoryId"
                                :options="categoryIdOptions"
                                children-field="childrenList"
                                class="w-100%"
                                clearable
                                default-expand-all
                                key-field="id"
                                label-field="categoryName"
                                placeholder="请选择产成品分类"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="产成品名称" path="poleName">
                            <n-input
                                v-model:value="formData.poleName"
                                class="w-100%"
                                clearable
                                placeholder="请输入产成品名称"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="产成品类型" required>
                            <n-cascader
                                v-model:value="classValue"
                                :options="classOptions"
                                children-field="childClassifyList"
                                class="w-100%"
                                clearable
                                filterable
                                label-field="classifyName"
                                placeholder="请选择产成品类型"
                                value-field="id"
                                @update:value="changeClass"
                            />
                        </n-form-item-gi>
                    </n-grid>
                </n-form>
                <div v-if="classArray && classArray[0]">
                    <template v-if="classArray[0] === '1'">
                        <TableSquareElectricPole
                            v-model:value="tableData"
                            v-if="classArray[1] === '40'"
                            @confirm="onSubmit"
                        />
                        <TableElectricPole v-model:value="tableData" v-else @confirm="onSubmit" />
                    </template>
                    <TableEqualDiameterPole
                        v-model:value="tableData"
                        v-if="classArray[0] === '5'"
                        @confirm="onSubmit"
                    />
                    <TableCementProducts v-model:value="tableData" v-if="classArray[0] === '8'" @confirm="onSubmit" />
                </div>
            </n-drawer-content>
        </n-drawer>
    </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import {
    BATCH_SAVE_MANUFACTURE_LIST,
    GET_CONFIG_COMPANY_LIST,
    GET_MANUFACTURE_CLASSIFY_TREE_LIST,
    GET_MATERIAL_CATEGORY_TREE_BY_GENRE
} from "@/api/application/reporting";
import TableElectricPole from "./TableElectricPole.vue";
import TableEqualDiameterPole from "./TableEqualDiameterPole.vue";
import TableCementProducts from "./TableCementProducts.vue";
import TableSquareElectricPole from "./TableSquareElectricPole.vue";

let props = withDefaults(defineProps<{ show: boolean }>(), { show: () => false });

let emits = defineEmits(["update:show", "refresh"]);

// 弹窗展示
let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

let changeModalShow = (show: boolean) => emits("update:show", show);

onMounted(async () => {
    await getCategoryIdOptions();
    await getCompanyIdOptions();
    await getClassOptions();
});

// 获取公司选项-填报专属修改2023年8月9日
let companyIdOptions = ref<any[]>([]);

let getCompanyIdOptions = async () => {
    await GET_CONFIG_COMPANY_LIST({ needFill: 1 }).then((res) => {
        companyIdOptions.value = res.data.data || [];
    });
};

// 获取分类
let categoryIdOptions = ref([]);

let getCategoryIdOptions = async () => {
    await GET_MATERIAL_CATEGORY_TREE_BY_GENRE({
        productGenre: 2
    }).then((res) => {
        if (res.data.code === 0) categoryIdOptions.value = res.data.data;
    });
};

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {
    companyId: { required: true, message: "请选择所属公司", trigger: ["blur", "change"] },
    categoryId: { required: true, message: "请选择产成品分类", trigger: ["blur", "change"] },
    poleName: { required: true, message: "请输入产成品名称", trigger: ["input", "blur"] }
};

// 表单数据
interface FormDataProps {
    [key: string]: any;
}

let initFormData: FormDataProps = {
    companyId: null,
    categoryId: null,
    poleName: ""
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 类型级联处理
let classOptions = ref([]);

let getClassOptions = async () => {
    await GET_MANUFACTURE_CLASSIFY_TREE_LIST({}).then((res) => {
        if (res.data.code === 0) classOptions.value = res.data.data;
    });
};

let classValue = ref<Nullable<string>>(null);

let classArray = ref<string[]>([]);

let changeClass = (_: unknown, __: unknown, values: { id: string }[]) => {
    tableData.value = [];
    if (values) {
        if (values.length === 1) {
            window.$message.error("产成品类型至少选择两级");
            classValue.value = null;
            classArray.value = [];
        } else {
            classArray.value = values.map((i) => i.id);
        }
    } else {
        classArray.value = [];
    }
};

let tableData = ref<any[]>([]);

// 提交
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;

    if (!classArray.value || classArray.value.length < 2) {
        window.$message.error("产成品类型至少选择两级");
        return false;
    }

    let manufactureList = tableData.value.map((item) => {
        return {
            ...item,
            ...formData.value,
            firstClassifyId: classArray.value[0] ?? null,
            secondClassifyId: classArray.value[1] ?? null,
            thirdClassifyId: classArray.value[2] ?? null
        };
    });

    BATCH_SAVE_MANUFACTURE_LIST(manufactureList).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("添加成功");
            changeModalShow(false);
            clearFrom();
            emits("refresh");
        } else {
            window.$message.error(res.data.message);
        }
    });
};
</script>
