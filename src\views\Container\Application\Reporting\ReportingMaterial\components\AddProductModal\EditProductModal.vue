<template>
    <div>
        <n-drawer
            v-model:show="show"
            :close-on-esc="false"
            :height="600"
            :mask-closable="false"
            placement="bottom"
            @update:show="changeModalShow(false)"
        >
            <n-drawer-content closable title="修改规格">
                <div>
                    <template v-if="props.configData.firstClassifyId === '1'">
                        <TableSquareElectricPole
                            v-if="props.configData.secondClassifyId === '40'"
                            v-model:value="tableData"
                            :showButtons="false"
                        />
                        <TableElectricPole v-else v-model:value="tableData" :showButtons="false" />
                    </template>
                    <TableEqualDiameterPole
                        v-if="props.configData.firstClassifyId === '5'"
                        v-model:value="tableData"
                        :showButtons="false"
                    />
                    <TableCementProducts
                        v-if="props.configData.firstClassifyId === '8'"
                        v-model:value="tableData"
                        :showButtons="false"
                    />
                </div>
                <div class="flex-center mt">
                    <n-button type="success" @click="onSubmit()">修改</n-button>
                </div>
            </n-drawer-content>
        </n-drawer>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref, watchEffect } from "vue";
import TableElectricPole from "./TableElectricPole.vue";
import TableEqualDiameterPole from "./TableEqualDiameterPole.vue";
import TableCementProducts from "./TableCementProducts.vue";
import TableSquareElectricPole from "./TableSquareElectricPole.vue";
import { UPDATE_MANUFACTURE } from "@/api/application/reporting";

let props = withDefaults(defineProps<{ show: boolean; configData: Record<string, any> }>(), { show: () => false });

let emits = defineEmits(["update:show", "refresh"]);

// 弹窗展示
let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

let changeModalShow = (show: boolean) => emits("update:show", show);

let tableData = ref<any[]>([]);

watchEffect(() => {
    console.log(1111, props.configData);
    if (props.show && props.configData.id) {
        let fillMethod = String(props.configData.fillMethod);
        tableData.value = [
            {
                ...props.configData,
                fillMethod: fillMethod
            }
        ];
    }
});

// 提交
let onSubmit = async () => {
    UPDATE_MANUFACTURE({
        ...tableData.value[0]
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("修改成功");
            changeModalShow(false);
            emits("refresh");
        } else {
            window.$message.error(res.data.message);
        }
    });
};
</script>
