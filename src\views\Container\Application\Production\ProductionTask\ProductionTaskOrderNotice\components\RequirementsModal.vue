<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-600px" closable title="需求详情" @close="closeModal">
                <n-table :single-line="false" class="text-center">
                    <thead>
                        <tr>
                            <th>规格型号</th>
                            <th>单位</th>
                            <th>数量</th>
                            <th>备注</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(item, index) in props.configData.modelFormulaList">
                            <td>{{ item.specification }}</td>
                            <td>{{ item.productUnit }}</td>
                            <td>{{ item.needCount }}</td>
                            <td>{{ item.customerRemark || "/" }}</td>
                        </tr>
                    </tbody>
                </n-table>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { onMounted } from "vue";

let props = withDefaults(
    defineProps<{
        show: Boolean;
        configData: {
            modelFormulaList?: {
                specification: string;
                productUnit: string;
                needCount: string | number;
                customerRemark: string;
            }[];
        };
    }>(),
    {
        show: () => false
    }
);

let emits = defineEmits(["update:show", "refresh"]);

onMounted(() => {});

// 关闭弹窗
let closeModal = () => {
    emits("update:show", false);
};
</script>
