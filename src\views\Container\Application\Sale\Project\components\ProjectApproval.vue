<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card
                :title="`发起《${configData.nextNodeName}》流程` || '发起流程'"
                class="w-90vw"
                closable
                content-style="padding: 0"
                @close="closeModal"
            >
                <div class="h-80vh">
                    <n-scrollbar trigger="hover">
                        <div class="p-20px pt-0!">
                            {{ configData.nextFormKey }}
                            <dynamic-form v-model="formData.elements" :options="formData.wcfOptions" />
                            <n-form
                                ref="ccPersonsRef"
                                :model="ccPersonsData"
                                :rules="ccPersonsRules"
                                class="mt-8"
                                label-placement="left"
                                label-width="auto"
                            >
                                <n-grid cols="12" x-gap="16">
                                    <n-form-item-gi :span="12" label="选择抄送人：" path="ccPersons">
                                        <UserSelector
                                            v-model:value="ccPersonsData.ccPersons"
                                            class="w-100%"
                                            key-name="username"
                                            placeholder="请选择抄送人"
                                        />
                                    </n-form-item-gi>
                                </n-grid>
                            </n-form>
                            <div class="flex-center mt-24px">
                                <n-space>
                                    <n-button type="primary" @click="onSubmit">提交</n-button>
                                    <n-button type="error" @click="closeModal">取消</n-button>
                                </n-space>
                            </div>
                        </div>
                    </n-scrollbar>
                </div>
            </n-card>
        </n-modal>
        <!--选择下一节点审批人-->
        <ProcessApproverSelector
            v-model:show="approverSelector.show"
            :config-data="approverSelector.configData"
            @submit="onApproverSubmit"
        />
    </div>
</template>

<script lang="ts" setup>
import { ref, watch, watchEffect } from "vue";
import type { FormGeneratorProps } from "@/components/FormGenerator";
import { useStoreUser } from "@/store";
import { usePublic } from "@/hooks";
import { DynamicForm } from "@/components/DynamicForm";
import { isJSON } from "@/utils/tools";
import { GET_OA_FORM_BY_START, GET_OA_NEXT_NODE, SUBMIT_OA_INSTANCE } from "@/api/application/oa";
import { ProcessApproverSelector } from "@/views/Container/Application/Process/components";
import { UserSelector } from "@/components/UserSelector";
import { POST_CHILD_PROJECT_FORM, POST_PROJECT_FORM, UPDATE_PROJECT } from "@/api/application/sale";

let storeUser = useStoreUser();
let { $router } = usePublic();

let props = defineProps({
    show: { type: Boolean, default: false },
    configData: { type: Object as PropType<any> }
});

let emits = defineEmits(["update:show", "refresh"]);

let formData = ref<any>({ elements: [], wcfOptions: {}, wcfhId: null });

watch(
    () => ({ show: props.show, configData: props.configData }),
    (newVal) => {
        if (newVal.show) {
            GET_OA_FORM_BY_START({ formKey: newVal.configData.nextFormKey }).then((res) => {
                formData.value.wcfOptions = res.data.data.wcfOptions || {};
                formData.value.elements =
                    res.data.data.elements.map((item: FormGeneratorProps) => {
                        let newModelValue: any = null;
                        if (item.modelValue && item.modelValue !== "null") {
                            if (isJSON(item.modelValue)) {
                                let parsedValue = JSON.parse(item.modelValue);
                                newModelValue = typeof parsedValue === "number" ? item.modelValue : parsedValue;
                            } else {
                                newModelValue = item.modelValue;
                            }
                        }
                        return { ...item, modelValue: newModelValue };
                    }) || [];
                formData.value.wcfhId = res.data.data.wcfhId;
            });
        }
    },
    { deep: true }
);

// 选择审批人
let selectedAssignees = ref<any>(null);

// 关闭弹窗
let closeModal = () => {
    emits("update:show", false);
    selectedAssignees.value = null;
    formData.value = { elements: [], wcfOptions: {}, wcfhId: null };
    emits("refresh");
};

// 审批人选择器
let approverSelector = ref<{ show: boolean; configData: UnKnownObject }>({
    show: false,
    configData: {}
});

/*
 *抄送人
 */

// 表单实例
let ccPersonsRef = ref<any>(null);

let ccPersonsData = ref<any>({ ccPersons: null });

let ccPersonsRules = {
    ccPersons: [{ required: false, message: "请输入抄送人", trigger: ["blur", "change"] }]
};

// 提交
let onSubmit = async () => {
    let formElements = await Promise.all(
        formData.value.elements.map((item: any) => {
            return new Promise((resolve, reject) => {
                // 2023年2月5日19:38:32 表单新增必填项校验
                if (
                    item.isHide !== 1 &&
                    item.isRead === 1 &&
                    !item.modelValue &&
                    item.type !== "text" &&
                    !!item.showRequireMark
                ) {
                    window.$message.error(`${item.label}不能为空`);
                    reject("表单必填项不能为空");
                }
                // 2022年11月28日23:44:07 后台不想要null所以处理成空字符串
                let modelValue = item.modelValue;
                if (item.modelValue === null || item.modelValue === undefined) modelValue = "";
                let newModelValue = typeof modelValue === "string" ? modelValue : JSON.stringify(modelValue);
                resolve({
                    wcfdId: item.wcfdId,
                    id: item.id,
                    modelName: item.modelName,
                    modelLabel: item.modelLabel,
                    modelValue: newModelValue
                });
            });
        })
    );

    // 获取项目
    let projectDetail = formData.value.elements.find((item: any) => item.name === "项目详情")?.modelValue;
    let projectParams: any = {};

    if (props.configData.nextFormKey === "BuyBidApply") {
        projectParams = {
            projectName: projectDetail.projectName,
            tenderProjectNumber: projectDetail.tenderProjectNumber,
            bidProductList: projectDetail.bidProductList ?? [],
            bidPurchaseFee: projectDetail.bidPurchaseFee,
            bidBond: projectDetail.bidBond,
            bidder: projectDetail.bidder,
            informationCollector: projectDetail.informationCollector,
            bidPurchaseDeadline: projectDetail.bidPurchaseDeadline
        };
    } else if (props.configData.nextFormKey === "BidDutyConfirm") {
        projectParams = {
            projectName: projectDetail.projectName,
            bidPurchaseDeadline: projectDetail.bidPurchaseDeadline,
            bidSubmissionDeadline: projectDetail.bidSubmissionDeadline,
            bondHandlingDeadline: projectDetail.bondHandlingDeadline,
            administrativeCompletionDeadline: projectDetail.administrativeCompletionDeadline,
            financialCompletionDeadline: projectDetail.financialCompletionDeadline,
            productionCompletionDeadline: projectDetail.productionCompletionDeadline,
            salesCompletionDeadline: projectDetail.salesCompletionDeadline
        };
    } else if (props.configData.nextFormKey === "BidPreReview") {
        projectParams = {
            projectName: projectDetail.projectName,
            rawMaterialsPurchasePrice: projectDetail.rawMaterialsPurchasePrice,
            rawMaterialsPurchaseCycle: projectDetail.rawMaterialsPurchaseCycle,
            productionCycleAndDeliveryDate: projectDetail.productionCycleAndDeliveryDate,
            productionCostAccounting: projectDetail.productionCostAccounting,
            salesExpense: projectDetail.salesExpense,
            procurementCost: projectDetail.procurementCost,
            productionCost: projectDetail.productionCost,
            transportationCost: projectDetail.transportationCost,
            netCashFlow: projectDetail.netCashFlow
        };
    } else if (props.configData.nextFormKey === "BidInfoReport") {
        projectParams = {
            reportUser: projectDetail.reportUser,
            reportDate: projectDetail.reportDate
        };
    }

    // 提交业务表单
    let formRes: any;
    if (props.configData.nodeFlowType !== 2) {
        formRes = await POST_PROJECT_FORM({
            projectId: props.configData.projectId,
            nodeKey: props.configData.nextNodeKey,
            formKey: props.configData.nextFormKey,
            formName: formData.value.wcfOptions.wcfName,
            formParamList: formElements,
            wcfhId: formData.value.wcfhId,
            ...projectParams
        });
    } else {
        formRes = await POST_CHILD_PROJECT_FORM({
            projectId: props.configData.projectId,
            childProjectId: props.configData.childProjectId,
            nodeKey: props.configData.nextNodeKey,
            formKey: props.configData.nextFormKey,
            formName: formData.value.wcfOptions.wcfName,
            formParamList: formElements,
            wcfhId: formData.value.wcfhId
        });
    }
    if (formRes.data.code !== 0) return window.$message.error(formRes.data.msg ?? "表单提交失败");

    // 获取下一个节点信息
    let nextNodeRes = await GET_OA_NEXT_NODE({ taskId: formRes.data.data.taskId });
    if (nextNodeRes.data.code !== 0) return window.$message.error(nextNodeRes.data.msg ?? "获取下一个节点失败");
    let nextNodes = nextNodeRes.data.data.nextProcessNodeList;

    // 判断是否需要选择审批人
    let isNeedApprover = nextNodes.some((item: any) => item.approveChooseWay === 2);
    if (isNeedApprover) {
        approverSelector.value = {
            show: true,
            configData: {
                taskId: formRes.data.data.taskId,
                nextNodes
            }
        };
    } else {
        let submitRes = await SUBMIT_OA_INSTANCE({
            taskId: formRes.data.data.taskId,
            nextAssigneeList: nextNodes.map((item: any) => ({
                nodeId: item.nodeId,
                nodeName: item.nodeName,
                multipleColumn: item.multipleColumn,
                assignees: item.optionAssignees
            })),
            ccPersons: ccPersonsData.value.ccPersons
        });
        if (submitRes.data.code !== 0) return window.$message.error(submitRes.data.msg ?? "提交失败");
        window.$message.success("提交成功");
        closeModal();
    }
};

// 发起人自选的审批人提交
let onApproverSubmit = async (taskRes: any) => {
    let nextAssigneeList = taskRes.nextNodes.map((item: any) => ({
        nodeId: item.nodeId,
        nodeName: item.nodeName,
        multipleColumn: item.multipleColumn,
        assignees: item.approveChooseWay === 2 ? item.userSelectedAssignees : item.optionAssignees
    }));
    let submitRes = await SUBMIT_OA_INSTANCE({
        taskId: taskRes.taskId,
        nextAssigneeList,
        ccPersons: ccPersonsData.value.ccPersons
    });
    if (submitRes.data.code !== 0) return window.$message.error(submitRes.data.msg ?? "提交失败");
    window.$message.success("提交成功");
    closeModal();
};

watchEffect(() => {
    (formData.value.elements ?? []).forEach((item: any) => {
        if (item.name === "项目详情") {
            item.modelValue = {
                ...item.modelValue,
                id: props.configData.projectId
            };
        }
    });
});
</script>
