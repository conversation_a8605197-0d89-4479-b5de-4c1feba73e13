import { computed, defineComponent, reactive, ref, watchEffect } from "vue";
import type { DataTableColumns } from "naive-ui";
import type { PropType } from "vue";
import { GET_EQUIPMENT_PAGE_LIST_BY_REGION_ID } from "@/api/application/TowerScan";

export default defineComponent({
    name: "TowerScanRegionEquipmentModal",
    props: {
        show: { type: Boolean, default: false },
        regionId: { type: [Number, String] as PropType<string | number>, default: null },
        regionName: { type: String, default: "" }
    },
    emits: ["update:show"],
    setup(props, { emit }) {
        const show = computed({
            get: () => props.show,
            set: (val) => emit("update:show", val)
        });

        // 设备列表数据
        const equipmentData = ref<any[]>([]);
        const equipmentLoading = ref(false);

        // 分页配置
        const pagination = reactive({
            page: 1,
            pageSize: 10,
            itemCount: 0,
            onChange: (page: number) => {
                pagination.page = page;
                getEquipmentData();
            },
            onUpdatePageSize: (pageSize: number) => {
                pagination.pageSize = pageSize;
                pagination.page = 1;
                getEquipmentData();
            }
        });

        // 表格列配置
        const columns = ref<DataTableColumns<any>>([
            {
                title: "设备编号",
                key: "equipmentNumber",
                align: "center"
            },
            { title: "设备名称", key: "equipmentName", align: "center" },
            {
                title: "工序名称",
                key: "techniqueNames",
                align: "center"
            },
            {
                title: "产线名称",
                key: "lineName",
                align: "center",
                render: (row) => row.lineName || "/"
            },
            {
                title: "操作工",
                key: "operatorNames",
                align: "center"
            },
            {
                title: "备注",
                key: "remark",
                align: "center",
                render: (row) => row.remark || "/"
            }
        ]);

        // 获取设备数据
        const getEquipmentData = () => {
            if (!props.regionId) return;

            equipmentLoading.value = true;
            GET_EQUIPMENT_PAGE_LIST_BY_REGION_ID({
                current: pagination.page,
                size: pagination.pageSize,
                regionId: Number(props.regionId)
            })
                .then((res) => {
                    if (res.data.code === 0) {
                        equipmentData.value = res.data.data.records || [];
                        pagination.itemCount = res.data.data.total || 0;
                    }
                    equipmentLoading.value = false;
                })
                .catch(() => {
                    equipmentLoading.value = false;
                    window.$message.error("获取设备列表失败");
                });
        };

        // 关闭弹窗
        const onClose = () => {
            show.value = false;
            // 清空数据
            equipmentData.value = [];
            pagination.page = 1;
        };

        // 监听弹窗显示状态，自动加载数据
        watchEffect(() => {
            if (show.value && props.regionId) {
                pagination.page = 1;
                getEquipmentData();
            }
        });

        return () => (
            <n-modal v-model:show={show.value} close-on-esc mask-closable>
                <n-card title={`${props.regionName} - 区域设备`} class="w-1200px" closable onClose={onClose}>
                    <n-data-table
                        columns={columns.value}
                        data={equipmentData.value}
                        loading={equipmentLoading.value}
                        pagination={pagination}
                        single-line={false}
                        bordered
                        remote
                        striped
                    />
                </n-card>
            </n-modal>
        );
    }
});
