<template>
    <div>
        <n-card>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                auto-search
                @componentClick="onComponentClick"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt">
            <!--<div class="flex-y-center mb">-->
            <!--    <n-space>-->
            <!--        <n-button secondary type="success" @click="saveTableData">保存填写</n-button>-->
            <!--        <n-button secondary type="warning" @click="onGenerateConcretePlan">自动生成混凝土计划用量</n-button>-->
            <!--    </n-space>-->
            <!--</div>-->
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, ref } from "vue";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable, useDicts } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import { NButton, NInput, NInputGroup, NInputGroupLabel, NTooltip } from "naive-ui";
import {
    GENERATE_PLAN_CONCRETE_AMOUNT,
    GET_CONFIG_WORK_GROUP_LIST,
    GET_DAILY_PLAN_CONCRETE_AMOUNT_LIST,
    SAVE_DAILY_PLAN_CONCRETE_AMOUNT_LIST,
    SAVE_PRODUCT_AMOUNT_LIST
} from "@/api/application/reporting";
import dayjs from "dayjs";
import { useStoreReportingSearch } from "@/store";
import { useThrottleFn } from "@vueuse/core";
import { UnitSelector } from "@/views/Container/Application/Reporting/components";

let storeReportingSearch = useStoreReportingSearch();

interface RowProps {
    [key: string]: any;
}

// 字典操作
let { dictLibs, getDictLibs } = useDicts();

let setDictLibs = async () => {
    let dictName = ["common_units"];
    await getDictLibs(dictName);
};

onMounted(async () => {
    await getWorkGroupIdOptions();
    await setDictLibs();
    getTableData();
});

let getWorkGroupIdOptions = async () => {
    await GET_CONFIG_WORK_GROUP_LIST({}).then((res) => {
        searchOptions.value.workGroupId = (res.data.data ?? []).map((item: any) => ({
            label: item.companyName + "-" + item.workshopName + "-" + item.groupName,
            value: item.id
        }));
        searchForm.value.workGroupId = searchOptions.value.workGroupId[0].value;
        searchForm.value.planDate = dayjs().format("YYYY-MM-DD");
        if (storeReportingSearch.getSearchForm.workGroupId) {
            searchForm.value.workGroupId = storeReportingSearch.getSearchForm.workGroupId;
        }
        if (storeReportingSearch.getSearchForm.planDate) {
            searchForm.value.planDate = storeReportingSearch.getSearchForm.planDate;
        }
    });
};

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { label: "班组", type: "select", prop: "workGroupId", span: 2 },
    { label: "日期筛选", type: "date", dateFormat: "yyyy-MM-dd", prop: "planDate" }
]);

let searchOptions = ref<TableSearchbarOptions>({ workGroupId: [] });

let searchForm = ref<TableSearchbarData>({ workGroupId: null, planDate: null });

let onSearch = () => {
    storeReportingSearch.setSearchForm({
        workGroupId: searchForm.value.workGroupId ?? null,
        planDate: searchForm.value.planDate ?? null
    });
    getTableData();
};

// 搜索栏自动保存逻辑
let autoSave = useThrottleFn(async () => {
    let params = {
        ...searchForm.value,
        id: planId.value,
        dailyAmountList: tableData.value
    };
    await SAVE_PRODUCT_AMOUNT_LIST({ ...params }).then((res) => {
        if (res.data.code === 0) window.$message.success("自动保存成功");
    });
}, 1000);

let onComponentClick = async (val: TableSearchbarData) => {
    // await autoSave();
};

let planId = ref<Nullable<string | number>>(null);

// 数据列表
let { tableRowKey, tableData, tableLoading, changeTableSelection } = useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    { type: "selection" },
    {
        title: "名称",
        align: "center",
        key: "poleName",
        render: (row) => {
            return row.poleName ?? "未知";
        }
    },
    {
        title: "规格型号",
        align: "center",
        key: "productModel",
        render: (row) => {
            return row.productModel;
        }
    },
    {
        // 计划定额用量
        title: "定额用量合计",
        align: "center",
        key: "planConcreteQuotaAmount",
        render: (row) => {
            return `${row.planConcreteQuotaAmount}m³`;
        }
    }
    // {
    //     title: "计划用量",
    //     align: "center",
    //     key: "planConcreteAmount",
    //     render: (row) => {
    //         return h(
    //             NTooltip,
    //             {},
    //             {
    //                 trigger: () => {
    //                     return h(NInputGroup, {}, () => [
    //                         h(NInput, {
    //                             value: row.planConcreteAmount,
    //                             onUpdateValue: (v) => (row.planConcreteAmount = v),
    //                             onFocus: () => {
    //                                 if (row.planConcreteAmount === "0") row.planConcreteAmount = "";
    //                             },
    //                             onBlur: () => {
    //                                 if (!row.planConcreteAmount) row.planConcreteAmount = "0";
    //                             }
    //                         }),
    //                         h(NInputGroupLabel, {}, () => [h("span", {}, "m³")])
    //                     ]);
    //                 },
    //                 default: () => {
    //                     return row.planConcreteAmount;
    //                 }
    //             }
    //         );
    //     }
    // }
]);

let getTableData = () => {
    tableLoading.value = true;
    GET_DAILY_PLAN_CONCRETE_AMOUNT_LIST({
        ...searchForm.value
    }).then((res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data.dailyAmountList ?? [];
            planId.value = res.data.data.id ?? null;
        }
        tableLoading.value = false;
    });
};

// 可编辑表单配置
let tableItem: RowProps = {
    id: "",
    contentId: "",
    contentType: "",
    planAmount: "",
    productAmount: ""
};

//保存填写
let saveTableData = async () => {
    let params = {
        ...searchForm.value,
        id: planId.value,
        dailyAmountList: tableData.value
    };
    if (!searchForm.value.workGroupId) {
        window.$message.error("请先选择班组！");
        return false;
    }
    if (!searchForm.value.planDate) {
        window.$message.error("请先选择日期！");
        return false;
    }
    await SAVE_DAILY_PLAN_CONCRETE_AMOUNT_LIST({ ...params }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("保存成功");
            onSearch();
        }
    });
};

// 自动生成混凝土计划用量
let onGenerateConcretePlan = () => {
    GENERATE_PLAN_CONCRETE_AMOUNT({
        ...searchForm.value
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("生成成功");
            onSearch();
        }
    });
};
</script>
