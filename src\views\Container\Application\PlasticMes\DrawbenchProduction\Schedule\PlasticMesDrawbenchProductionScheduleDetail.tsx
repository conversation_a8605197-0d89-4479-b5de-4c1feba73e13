import { computed, defineComponent, ref, watchEffect } from "vue";
import { GET_DRAWBENCH_SCHEDULE_INFO, GET_DRAWBENCH_SCHEDULE_STORAGE_HIS_LIST } from "@/api/application/plasticMes";
import dayjs from "dayjs";
import { useDicts } from "@/hooks";
import { LineList } from "./components";
import { FilePreviewBeta } from "@/components/FilePreview";
import { TableActions } from "@/components/TableActions";

export default defineComponent({
    name: "PlasticMesDrawbenchProductionScheduleDetail",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        // 字典操作
        const { dictLibs, getDictLibs } = useDicts();

        const setDictLibs = async () => {
            const dictName = ["common_units", "drawbench_type"]; // 改动点1: 字典类型从crush_type改为drawbench_type
            await getDictLibs(dictName);
        };

        // 获取详情
        const detailData = ref<any>({});
        const getDetail = async () => {
            await GET_DRAWBENCH_SCHEDULE_INFO({ id: props.configData.id }).then((res) => {
                if (res.data.code === 0) {
                    detailData.value = {
                        ...res.data.data,
                        planBeginTime: dayjs(res.data.data.planBeginTime).format("YYYY-MM-DD HH:mm:00") ?? null,
                        planEndTime: dayjs(res.data.data.planEndTime).format("YYYY-MM-DD HH:mm:00") ?? null
                    };
                }
            });
        };

        // 获取入库记录
        const warehouseEntryList = ref<any[]>([]);

        const getWarehouseEntryList = async () => {
            GET_DRAWBENCH_SCHEDULE_STORAGE_HIS_LIST({ id: props.configData.id }).then((res) => {
                warehouseEntryList.value = res.data.data ?? [];
            });
        };

        // beta预览
        const previewBetaModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const onPreview = (url: string) => {
            if (url) {
                const fullUrl = import.meta.env.VITE_PREVIEW_URL + url;
                previewBetaModal.value = {
                    show: true,
                    configData: { ...props.configData?.techCheckFile, url: fullUrl }
                };
            } else {
                window.$message.error("预览失败");
            }
        };

        const onDownload = (url: string) => {
            if (url) {
                window.open(import.meta.env.VITE_API_URL + url);
            } else {
                window.$message.error("下载失败");
            }
        };

        watchEffect(async () => {
            if (show.value) {
                await setDictLibs();
                await getDetail();
                await getWarehouseEntryList();
            }
        });

        const onClose = () => {
            changeModalShow(false);
            emit("refresh");
        };

        return () => (
            <div>
                <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                    <n-card title="查看排产单详情" class="w-1000px" closable onClose={onClose}>
                        <n-form label-placement="left" label-width="auto">
                            <n-grid cols={12} x-gap={16}>
                                <n-form-item-gi required span={6} label="排产单号">
                                    {detailData.value?.id ?? "/"}
                                </n-form-item-gi>
                                <n-form-item-gi required span={6} label="拉丝类型">
                                    {detailData.value?.ptTypeName ?? "/"}
                                </n-form-item-gi>
                                <n-form-item-gi required span={6} label="计划开始时间">
                                    {detailData.value?.planBeginTime ?? "/"}
                                </n-form-item-gi>
                                <n-form-item-gi required span={6} label="计划结束时间">
                                    {detailData.value?.planEndTime ?? "/"}
                                </n-form-item-gi>
                                <n-form-item-gi required span={6} label="计划完成数量">
                                    {detailData.value?.planQuantity ?? "/"}
                                </n-form-item-gi>
                                <n-form-item-gi required span={6} label="备注">
                                    {detailData.value?.remark ?? "/"}
                                </n-form-item-gi>
                                {/*<n-form-item-gi required span={6} label="实验室反馈">*/}
                                {/*    {String(detailData.value?.techCheckState) === "0" && (*/}
                                {/*        <n-text type="error">未满足</n-text>*/}
                                {/*    )}*/}
                                {/*    {String(detailData.value?.techCheckState) === "1" && (*/}
                                {/*        <n-text type="success">已满足</n-text>*/}
                                {/*    )}*/}
                                {/*</n-form-item-gi>*/}
                            </n-grid>
                        </n-form>

                        <n-tabs animated type="bar" defaultValue={1}>
                            <n-tab-pane name={1} tab="生产情况">
                                <n-table single-line={false} class="text-center">
                                    <thead>
                                        <tr>
                                            <th>计划完成数</th>
                                            <th>拉丝完成数</th>
                                            <th>入库完成数</th>
                                            <th>计量单位</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>{detailData.value?.planQuantity ?? "0"}</td>
                                            <td>{detailData.value?.finishQuantity ?? "0"}</td>
                                            <td>{detailData.value?.storageQuantity ?? "0"}</td>
                                            <td>{detailData.value?.productionUnitName ?? "/"}</td>
                                        </tr>
                                    </tbody>
                                </n-table>
                            </n-tab-pane>
                            <n-tab-pane name={2} tab="原料清单">
                                <n-table single-line={false} class="text-center">
                                    <thead>
                                        <tr>
                                            <th>原料名称</th>
                                            <th>计划用量</th>
                                            <th>实际用量</th>
                                            <th>用量单位</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {(detailData.value?.scheduleItemList ?? []).map((item: any) => {
                                            return (
                                                <tr>
                                                    <td>{item.goodsName}</td>
                                                    <td>{item.goodsConsumption}</td>
                                                    <td>{item.fillGoodsUsage}</td>
                                                    <td>{item.goodsUnitName}</td>
                                                </tr>
                                            );
                                        })}
                                    </tbody>
                                </n-table>
                            </n-tab-pane>
                            <n-tab-pane name={3} tab="生产记录">
                                <LineList scheduleId={props.configData.id} />
                            </n-tab-pane>
                            <n-tab-pane name={4} tab="入库记录">
                                <n-table single-line={false} class="text-center">
                                    <thead>
                                        <tr>
                                            <th>申请人</th>
                                            <th>申请入库时间</th>
                                            <th>申请入库数量（合格品）</th>
                                            <th>申请入库数量（不合格品）</th>
                                            <th>仓库审批状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {(warehouseEntryList.value ?? []).map((item) => {
                                            return (
                                                <tr>
                                                    <td>{item.createByName}</td>
                                                    <td>{item.createTime}</td>
                                                    <td>{item.applyQualifiedQuantity}</td>
                                                    <td>{item.applyUnqualifiedQuantity}</td>
                                                    <td>
                                                        {item.confirmState === 1 && <n-text type="info">待确认</n-text>}
                                                        {item.confirmState === 2 && (
                                                            <n-text type="success">已确认</n-text>
                                                        )}
                                                        {item.confirmState === 3 && (
                                                            <n-text type="error">已拒绝</n-text>
                                                        )}
                                                        {item.confirmState === 4 && (
                                                            <n-text type="error">已取消</n-text>
                                                        )}
                                                    </td>
                                                </tr>
                                            );
                                        })}
                                    </tbody>
                                </n-table>
                            </n-tab-pane>
                            <n-tab-pane name={9} tab="排产排班">
                                <n-table single-line={false} class="text-center">
                                    <thead>
                                        <tr>
                                            <th>工种</th>
                                            <th>人员</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {detailData.value?.scheduleUserList.map((item: any) => {
                                            return (
                                                <tr>
                                                    <td>{item.jobTypeName}</td>
                                                    <td>{item.productUserName}</td>
                                                </tr>
                                            );
                                        })}
                                    </tbody>
                                </n-table>
                            </n-tab-pane>
                            <n-tab-pane name={10} tab="实验室反馈">
                                <n-form label-placement="left" label-width="auto">
                                    <n-grid cols={12} x-gap={16}>
                                        <n-form-item-gi span={6} label="实验室反馈">
                                            {String(detailData.value?.techCheckState) === "0" && (
                                                <n-text type="error">未满足</n-text>
                                            )}
                                            {String(detailData.value?.techCheckState) === "1" && (
                                                <n-text type="success">已满足</n-text>
                                            )}
                                        </n-form-item-gi>
                                        <n-form-item-gi span={6} label="实验室反馈信息">
                                            {detailData.value?.techCheckRemark}
                                        </n-form-item-gi>
                                        <n-form-item-gi span={12}>
                                            <n-data-table
                                                columns={[
                                                    { title: "文件名", key: "original", align: "center" },
                                                    {
                                                        title: "操作",
                                                        align: "center",
                                                        width: 160,
                                                        render: (row: any) => {
                                                            return (
                                                                <TableActions
                                                                    type="button"
                                                                    buttonActions={[
                                                                        {
                                                                            label: "预览",
                                                                            tertiary: true,
                                                                            type: "primary",
                                                                            onClick: () => onPreview(row.url)
                                                                        },
                                                                        {
                                                                            label: "下载",
                                                                            tertiary: true,
                                                                            type: "success",
                                                                            onClick: () => onDownload(row.url)
                                                                        }
                                                                    ]}
                                                                />
                                                            );
                                                        }
                                                    }
                                                ]}
                                                data={detailData.value?.techCheckFileList ?? []}
                                                single-line={false}
                                                bordered
                                                striped
                                                size="small"
                                            />
                                        </n-form-item-gi>
                                    </n-grid>
                                </n-form>
                            </n-tab-pane>
                        </n-tabs>
                    </n-card>
                </n-modal>
                <FilePreviewBeta
                    v-model:show={previewBetaModal.value.show}
                    configData={previewBetaModal.value.configData}
                />
            </div>
        );
    }
});
