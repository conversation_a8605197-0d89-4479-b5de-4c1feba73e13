import { computed, defineComponent, h, ref, watchEffect } from "vue";
import { type DataTableColumns, type FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { useStoreUser } from "@/store";
import {
    ADD_PRODUCTION_LINE,
    GET_PRODUCTION_LINE_DETAIL,
    UPDATE_PRODUCTION_LINE,
    GET_IRON_TECHNIQUE_TREE_LIST,
    GET_EQUIPMENT_PAGE_LIST
} from "@/api/application/TowerScan";
import { UserSelector } from "@/components/UserSelector";
import { TableActions } from "@/components/TableActions";
import { useDicts } from "@/hooks";
import dayjs from "dayjs";

interface DirectorList {
    [property: string]: any;
}

interface EquipmentList {
    [property: string]: any;
}

interface TechniqueList {
    [property: string]: any;
}

export default defineComponent({
    name: "ProductionLineEdit",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        const storeUser = useStoreUser();

        // 字典操作
        const { dictLibs, getDictLibs } = useDicts();

        const setDictLibs = async () => {
            const dictName = ["TechniqueProcessType"];
            await getDictLibs(dictName);
            dictLibs["TechniqueProcessType"] =
                dictLibs["TechniqueProcessType"]?.filter((item) => item.value !== "0" && item.value !== "1") || [];
        };

        // 获取详情
        const getDetail = () => {
            GET_PRODUCTION_LINE_DETAIL({ id: props.configData.lineId }).then((res) => {
                if (res.data.code === 0) {
                    const data = res.data.data;
                    const {
                        ironProductionLine,
                        directorList: directorData,
                        techniqueList: techniqueData,
                        equipmentList: equipmentData
                    } = data;

                    formData.value = {
                        lineName: ironProductionLine.lineName,
                        remark: ironProductionLine.remark,
                        operBy: ironProductionLine.operBy,
                        operTime: ironProductionLine.operTime
                    };

                    directorList.value = directorData || [];
                    equipmentList.value = (equipmentData || []).map((item: any) => ({
                        equipmentId: item.equipmentId,
                        equipmentName: item.equipmentName
                    }));
                    techniqueList.value = techniqueData || [];
                }
            });
        };

        // 表单数据
        interface FormDataProps {
            [key: string]: any;
        }

        const formRef = ref<FormInst | null>(null);

        // 工艺树数据
        const techniqueTreeData = ref<any[]>([]);
        const processTypeOptions = ref<any[]>([]);

        // 设备选项数据
        const equipmentOptions = ref<any[]>([]);

        // 递归构建树节点
        const buildTreeNode = (item: any): any => {
            const node = {
                label: item.techniqueName,
                key: item.id,
                ...item
            };

            if (item.childrenList && item.childrenList.length > 0) {
                node.children = item.childrenList.map((child: any) => buildTreeNode(child));
            } else {
                node.isLeaf = true;
            }

            return node;
        };

        // 获取表单选项
        const getFormOptions = async () => {
            await setDictLibs();
            processTypeOptions.value = dictLibs["TechniqueProcessType"] || [];

            // 获取所有工艺分类的工艺数据
            const allTechniqueData: any[] = [];
            for (const processType of processTypeOptions.value) {
                const res = await GET_IRON_TECHNIQUE_TREE_LIST({
                    processType: processType.value
                });
                if (res.data.code === 0) {
                    const categoryData = {
                        label: processType.label,
                        key: `category_${processType.value}`,
                        children: (res.data.data || []).map((item: any) => buildTreeNode(item))
                    };
                    allTechniqueData.push(categoryData);
                }
            }
            techniqueTreeData.value = allTechniqueData;

            // 获取设备列表
            const equipmentRes = await GET_EQUIPMENT_PAGE_LIST({
                current: 1,
                size: 1000
            });
            if (equipmentRes.data.code === 0) {
                equipmentOptions.value = equipmentRes.data.data.records.map((item: any) => ({
                    label: item.equipmentName,
                    value: item.equipmentId,
                    ...item
                }));
            }
        };

        const initFormData: FormDataProps = {
            lineName: null,
            remark: null,
            operBy: storeUser.getUserData.sysUser?.username,
            operTime: dayjs().format("YYYY-MM-DD HH:mm:ss")
        };

        const formRules = computed(() => ({
            lineName: [{ required: true, message: "请输入产线名称", trigger: ["input", "blur"] }]
        }));

        const formData = ref(cloneDeep(initFormData));

        const clearForm = () => {
            formData.value = cloneDeep(initFormData);
            directorList.value = [];
            equipmentList.value = [];
            techniqueList.value = [];
        };

        // 负责人管理
        const directorList = ref<DirectorList[]>([]);

        const directorTableColumns = ref<DataTableColumns<DirectorList>>([
            {
                title: "负责人",
                key: "director",
                align: "center",
                render: (row) => {
                    return (
                        <UserSelector
                            v-model:value={row.director}
                            class="w-100%"
                            key-name="username"
                            placeholder="请选择负责人"
                            multiple={false}
                        />
                    );
                }
            },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 100,
                render: (row, index) => {
                    return (
                        <TableActions
                            type="button"
                            buttonActions={[
                                {
                                    label: "删除",
                                    tertiary: true,
                                    type: "error",
                                    onClick: () => removeDirector(row, index)
                                }
                            ]}
                        />
                    );
                }
            }
        ]);

        const addDirector = () => {
            directorList.value.push({
                director: "",
                delFlag: 0
            });
        };

        const removeDirector = (row: DirectorList, index: number) => {
            if (row.id) {
                const directorIndex = directorList.value.findIndex((item) => item.id === row.id);
                if (directorIndex !== -1) {
                    directorList.value[directorIndex].delFlag = 1;
                    directorList.value.splice(directorIndex, 1);
                }
            } else {
                directorList.value.splice(index, 1);
            }
        };

        // 设备管理
        const equipmentList = ref<EquipmentList[]>([]);

        const equipmentTableColumns = ref<DataTableColumns<EquipmentList>>([
            {
                title: "设备",
                key: "equipmentId",
                align: "center",
                render: (row) => {
                    return (
                        <n-select
                            v-model:value={row.equipmentId}
                            class="w-100%"
                            options={equipmentOptions.value}
                            placeholder="请选择设备"
                            clearable
                            filterable
                        />
                    );
                }
            },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 100,
                render: (row, index) => {
                    return (
                        <TableActions
                            type="button"
                            buttonActions={[
                                {
                                    label: "删除",
                                    tertiary: true,
                                    type: "error",
                                    onClick: () => removeEquipment(index)
                                }
                            ]}
                        />
                    );
                }
            }
        ]);

        const addEquipment = () => {
            equipmentList.value.push({
                equipmentId: null
            });
        };

        const removeEquipment = (index: number) => {
            equipmentList.value.splice(index, 1);
        };

        // 工序管理
        const techniqueList = ref<TechniqueList[]>([]);

        const techniqueTableColumns = ref<DataTableColumns<TechniqueList>>([
            {
                title: "工序",
                key: "techniqueId",
                align: "center",
                render: (row) => {
                    return (
                        <n-tree-select
                            v-model:value={row.techniqueId}
                            class="w-100%"
                            options={techniqueTreeData.value}
                            clearable
                            filterable
                            placeholder="请选择工序"
                            check-strategy="all"
                            cascade
                            onUpdate:value={(value: any) => {
                                if (value && typeof value === "string" && value.startsWith("category_")) {
                                    row.techniqueId = 0;
                                    return window.$message.warning("请选择具体的工序，不能只选择工艺分类");
                                }
                                row.techniqueId = value;
                            }}
                        />
                    );
                }
            },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 100,
                render: (row, index) => {
                    return (
                        <TableActions
                            type="button"
                            buttonActions={[
                                {
                                    label: "删除",
                                    tertiary: true,
                                    type: "error",
                                    onClick: () => removeTechnique(row, index)
                                }
                            ]}
                        />
                    );
                }
            }
        ]);

        const addTechnique = () => {
            techniqueList.value.push({
                techniqueId: 0,
                delFlag: 0
            });
        };

        const removeTechnique = (row: TechniqueList, index: number) => {
            if (row.id) {
                const techniqueIndex = techniqueList.value.findIndex((item) => item.id === row.id);
                if (techniqueIndex !== -1) {
                    techniqueList.value[techniqueIndex].delFlag = 1;
                    techniqueList.value.splice(techniqueIndex, 1);
                }
            } else {
                techniqueList.value.splice(index, 1);
            }
        };

        const onClose = () => {
            clearForm();
            changeModalShow(false);
            emit("refresh");
        };

        const onSubmit = async () => {
            let validateError = await formRef.value?.validate((errors) => !!errors);
            if (validateError) return false;

            const submitData = {
                directorList: directorList.value
                    .filter((item) => item.director)
                    .map((item) => ({
                        director: item.director,
                        delFlag: item.delFlag || 0,
                        ...(item.id && { id: item.id })
                    })),
                equipmentList: equipmentList.value
                    .filter((item) => item.equipmentId > 0)
                    .map((item) => ({
                        equipmentId: item.equipmentId
                    })),
                ironProductionLine: {
                    lineId: props.configData.lineId,
                    lineName: formData.value.lineName,
                    remark: formData.value.remark,
                    operBy: formData.value.operBy,
                    operTime: formData.value.operTime,
                    delFlag: 0
                },
                techniqueList: techniqueList.value
                    .filter((item) => item.techniqueId > 0)
                    .map((item) => ({
                        techniqueId: item.techniqueId,
                        delFlag: item.delFlag || 0,
                        ...(item.id && { id: item.id })
                    }))
            };

            if (props.configData.lineId) {
                await UPDATE_PRODUCTION_LINE({
                    ...submitData
                }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("编辑成功");
                        onClose();
                    }
                });
            } else {
                await ADD_PRODUCTION_LINE({
                    ...submitData
                }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("新增成功");
                        onClose();
                    }
                });
            }
        };

        watchEffect(async () => {
            if (show.value) {
                await getFormOptions();
                if (props.configData.lineId) getDetail();
            }
        });

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card
                    title={props.configData.lineId ? "编辑产线" : "新增产线"}
                    class="w-1400px"
                    closable
                    onClose={onClose}
                >
                    <n-form
                        ref={formRef}
                        model={formData.value}
                        rules={formRules.value}
                        label-placement="left"
                        label-width="auto"
                    >
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi span={6} label="操作人" path="operBy" required>
                                <UserSelector
                                    value={formData.value.operBy}
                                    class="w-100%"
                                    disabled
                                    key-name="username"
                                    placeholder="请选择操作人"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="操作时间" path="operTime">
                                <n-date-picker
                                    v-model:formatted-value={formData.value.operTime}
                                    class="w-100%"
                                    clearable
                                    placeholder="请选择操作时间"
                                    type="datetime"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="产线名称" path="lineName">
                                <n-input
                                    v-model:value={formData.value.lineName}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入产线名称"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="备注" path="remark">
                                <n-input
                                    v-model:value={formData.value.remark}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入备注"
                                />
                            </n-form-item-gi>

                            <n-form-item-gi span={12}>
                                <div class="w-100%">
                                    <n-space class="mb">
                                        <n-button type="success" onClick={addDirector}>
                                            + 添加负责人
                                        </n-button>
                                    </n-space>
                                    <n-data-table
                                        columns={directorTableColumns.value}
                                        data={directorList.value}
                                        single-line={false}
                                        bordered
                                        striped
                                    />
                                </div>
                            </n-form-item-gi>

                            <n-form-item-gi span={12}>
                                <div class="w-100%">
                                    <n-space class="mb">
                                        <n-button type="success" onClick={addEquipment}>
                                            + 添加设备
                                        </n-button>
                                    </n-space>
                                    <n-data-table
                                        columns={equipmentTableColumns.value}
                                        data={equipmentList.value}
                                        single-line={false}
                                        bordered
                                        striped
                                    />
                                </div>
                            </n-form-item-gi>

                            <n-form-item-gi span={12}>
                                <div class="w-100%">
                                    <n-space class="mb">
                                        <n-button type="success" onClick={addTechnique}>
                                            + 添加工序
                                        </n-button>
                                    </n-space>
                                    <n-data-table
                                        columns={techniqueTableColumns.value}
                                        data={techniqueList.value}
                                        single-line={false}
                                        bordered
                                        striped
                                    />
                                </div>
                            </n-form-item-gi>

                            <n-form-item-gi span={12}>
                                <n-space>
                                    <n-button type="primary" onClick={onSubmit}>
                                        提交
                                    </n-button>
                                    <n-button onClick={onClose}>取消</n-button>
                                </n-space>
                            </n-form-item-gi>
                        </n-grid>
                    </n-form>
                </n-card>
            </n-modal>
        );
    }
});
