<template>
    <div>
        <n-table :single-line="false" class="text-center" v-if="(formData.bidFileList || []).length">
            <thead>
                <tr>
                    <th>附件名称</th>
                    <th>上传时间</th>
                    <th>上传人</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <tr v-for="(item, index) in formData.bidFileList || []" :key="index">
                    <td>{{ item.fileName }}</td>
                    <td>{{ item.createTime }}</td>
                    <td>{{ item.createByName || "未知" }}</td>
                    <td>
                        <n-text class="cursor-pointer" type="info" @click="downloadFile(item.url)"> 点击下载 </n-text>
                    </td>
                </tr>
            </tbody>
        </n-table>
        <n-result v-else class="py-50px" size="large" status="404" title="暂无附件" />
    </div>
</template>

<script lang="ts" setup>
interface FormDataProps {
    [key: string]: any;
}

let props = withDefaults(defineProps<{ formData: FormDataProps }>(), {});

let emits = defineEmits(["refresh"]);

let onRefresh = () => emits("refresh");

// 下载
let downloadFile = (url: any) => {
    window.open(import.meta.env.VITE_API_URL + url, "_blank");
};
</script>
