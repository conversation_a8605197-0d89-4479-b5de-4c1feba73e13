import { defineComponent, onMounted, ref } from "vue";
import { GET_ATTENDANCE_MODULE_SETTING_LIST } from "@/api/permission";
import AttendanceRuleCardReplacement from "@/views/Container/Permission/Attendance/Rule/AttendanceRuleCardReplacement";
import AttendanceRuleWhiteList from "@/views/Container/Permission/Attendance/Rule/AttendanceRuleWhiteList/AttendanceRuleWhiteList";
import AttendanceRuleConfig from "@/views/Container/Permission/Attendance/Rule/AttendanceRuleConfig/AttendanceRuleConfig";
import AttendanceRuleSpotCheck from "@/views/Container/Permission/Attendance/Rule/AttendanceRuleSpotCheck/AttendanceRuleSpotCheck";

export default defineComponent({
    name: "AttendanceRule",
    setup(props, { emit }) {
        const tabActive = ref<string | null>(null);

        const tabOptions = ref<any[]>([]);

        const getTabOptions = async () => {
            await GET_ATTENDANCE_MODULE_SETTING_LIST({ current: 1, size: 999 }).then((res) => {
                tabOptions.value = (res.data.data.records ?? []).map((item: any) => {
                    return {
                        label: item.companyName,
                        key: item.corpId
                    };
                });
                tabActive.value = tabOptions.value[0].key ?? null;
            });
        };

        const changeTabActive = (key: string) => {
            tabActive.value = key;
        };

        onMounted(async () => {
            await getTabOptions();
        });

        return () => (
            <div class="attendance-page">
                <n-card hoverable>
                    <div class="flex">
                        <n-menu
                            v-model:value={tabActive.value}
                            indent={20}
                            options={tabOptions.value}
                            class={"common-left-menu flex-fixed-235 border-r-1px border-[#E5E5E5]"}
                            mode="vertical"
                            onUpdate:value={changeTabActive}
                        />
                        <div class="flex-1 ml-8">
                            <n-tabs animated type="bar" defaultValue={1}>
                                <n-tab-pane name={1} tab="考勤规则设置">
                                    <AttendanceRuleConfig corpId={tabActive.value} />
                                </n-tab-pane>
                                <n-tab-pane name={2} tab="补卡规则配置">
                                    <AttendanceRuleCardReplacement corpId={tabActive.value} />
                                </n-tab-pane>
                                <n-tab-pane name={3} tab="抽查考勤配置">
                                    <AttendanceRuleSpotCheck corpId={tabActive.value} />
                                </n-tab-pane>
                                <n-tab-pane name={4} tab="考勤白名单">
                                    <AttendanceRuleWhiteList corpId={tabActive.value} />
                                </n-tab-pane>
                            </n-tabs>
                        </div>
                    </div>
                </n-card>
            </div>
        );
    }
});
