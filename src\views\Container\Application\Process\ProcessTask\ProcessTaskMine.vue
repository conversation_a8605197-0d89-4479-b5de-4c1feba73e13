<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <ProcessDetail v-model:show="processDetailModal.show" :config-data="processDetailModal.configData" />
        <ProcessWithdraw
            v-model:show="processWithdrawModal.show"
            :config-data="processWithdrawModal.configData"
            @refresh="getTableData"
        />
        <ProcessReLaunch
            v-model:show="reReLaunchModal.show"
            :config-data="reReLaunchModal.configData"
            @refresh="getTableData"
        />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from "vue";
import type { DataTableColumns } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable, useDicts } from "@/hooks";
import { GET_OA_MODEL_LIST, GET_PROCESS_INSTANCE_STARTED } from "@/api/application/oa";
import { TableActions } from "@/components/TableActions";
import { ProcessWithdraw, ProcessDetail, ProcessReLaunch } from "../components";

interface RowProps<T = string | null> {
    [propName: string]: any;
    businessNumber: T;
    businessKey: T;
}

onMounted(async () => {
    await setDictLibs();
    getSearchOptions();
    getTableData();
});

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { prop: "modelId", type: "select", label: "流程名称", span: 2 },
    {
        prop: "status",
        type: "select",
        label: "当前状态"
    },
    // {
    //     prop: "processName",
    //     type: "input",
    //     label: "流程名称"
    // },
    { prop: "paramProjectName", type: "input", label: "项目名称/主题", labelWidth: 120 },
    {
        prop: "modelLabel",
        type: "input",
        label: "关键词"
    },
    {
        prop: "businessNumber",
        type: "input",
        label: "审批单号"
    },
    {
        prop: "beginStartTime",
        type: "datetime",
        label: "发起开始时间",
        dateFormat: "yyyy-MM-dd HH:mm:ss",
        labelWidth: "100"
    },
    {
        prop: "endStartTime",
        type: "datetime",
        label: "发起截止时间",
        dateFormat: "yyyy-MM-dd HH:mm:ss",
        labelWidth: "100"
    }
    // {
    //     prop: "starterUser",
    //     type: "userSelector",
    //     label: "发起人员"
    // }
]);

let searchOptions = ref<TableSearchbarOptions>({
    status: [],
    modelId: []
});

let searchForm = ref<TableSearchbarData>({
    status: null,
    processName: null,
    paramProjectName: null,
    modelLabel: null,
    businessNumber: null,
    beginStartTime: null,
    endStartTime: null
});

let getSearchOptions = () => {
    searchOptions.value.status = dictLibs.process_business_status;
    /*
     * 时间：2025年6月5日16:28:53
     * 来源：上海公司研发群
     * 内容：modelState: 1 后台让删这个
     */
    GET_OA_MODEL_LIST({ current: 1, size: 1000 }).then((res) => {
        if (res.data.code === 0) {
            searchOptions.value.modelId = res.data.data.records.map((item: any) => {
                return {
                    label: item.name,
                    value: item.id
                };
            });
        }
    });
};

// 字典操作
let { dictLibs, getDictLibs, dictValueToLabel, dictValueToAll } = useDicts();

let setDictLibs = async () => {
    let dictName = ["process_business_status"];
    await getDictLibs(dictName);
};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("processInstanceId");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "审批单号",
        key: "businessNumber",
        align: "center",
        render: (row: RowProps) => {
            if (row.businessNumber != null) {
                return row.businessNumber;
            } else {
                return row.businessKey;
            }
        }
    },
    {
        title: "流程名称",
        align: "center",
        key: "name",
        render: (row: RowProps) => {
            if (row.businessName) {
                return `【项目名称:${row.businessName}】${row.starterUserTrueName}提交的${row.name}`;
            } else {
                return `${row.starterUserTrueName}提交的${row.name}`;
            }
        }
    },
    {
        title: "项目名称/主题",
        align: "center",
        key: "paramProjectName",
        render: (row: RowProps) => {
            return row.paramProjectName ?? "/";
            // return (row.elements || []).find((item) => item.modelName === "projectName")?.value || "暂无";
        }
    },
    {
        title: "发起时间",
        key: "startTime",
        align: "center"
    },
    {
        title: "当前状态",
        key: "status",
        align: "center",
        render: (row: RowProps) => {
            return h(
                "span",
                {
                    style: `color:${dictValueToAll(row.status, "process_business_status").color || ""}`
                },
                dictValueToLabel(row.status, "process_business_status")
            );
        }
    },
    {
        title: "操作",
        key: "action",
        align: "center",
        width: 200,
        render: (row: RowProps) => {
            if (String(row.status) === "1" || String(row.status) === "2") {
                return h(TableActions, {
                    type: "button",
                    buttonActions: [
                        {
                            label: "查看单据",
                            tertiary: true,
                            onClick: () => {
                                openProcessDetailModal(row);
                            }
                        },
                        {
                            label: "撤回审批",
                            tertiary: true,
                            type: "error",
                            onClick: () => {
                                openProcessWithdrawModal(row);
                            }
                        }
                    ]
                });
            } else {
                return h(TableActions, {
                    type: "button",
                    buttonActions: [
                        {
                            label: "查看单据",
                            tertiary: true,
                            onClick: () => {
                                openProcessDetailModal(row);
                            }
                        },
                        {
                            label: "重新发起",
                            tertiary: true,
                            disabled: () => row.businessFrom !== "workflow",
                            type: "success",
                            onClick: () => {
                                openReLaunchModal(row);
                            }
                        }
                    ]
                });
            }
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_PROCESS_INSTANCE_STARTED({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        ...searchForm.value
    }).then((res) => {
        if (res.data.code === 0) {
            console.log("流程实例", res.data.data);
            tableData.value = res.data.data.records;
            tablePagination.itemCount = res.data.data.total;
            tableLoading.value = false;
        }
    });
};

// 搜索
let onSearch = () => {
    tablePagination.page = 1;
    tablePagination.pageSize = 10;
    getTableData();
};

// 单据详情
let processDetailModal = ref<any>({
    show: false,
    configData: {}
});

let openProcessDetailModal = (row: any) => {
    processDetailModal.value.show = true;
    processDetailModal.value.configData = row;
};

// 撤回审批
let processWithdrawModal = ref<any>({
    show: false,
    configData: {}
});

let openProcessWithdrawModal = (row: any) => {
    processWithdrawModal.value.show = true;
    processWithdrawModal.value.configData = row;
};

// 重新发起流程
let reReLaunchModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

let openReLaunchModal = (row: RowProps) => {
    reReLaunchModal.value = { show: true, configData: row };
};
</script>
