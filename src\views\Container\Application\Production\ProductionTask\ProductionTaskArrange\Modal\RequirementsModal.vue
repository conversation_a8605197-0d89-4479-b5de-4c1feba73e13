<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-800px" closable title="需求明细反馈情况" @close="closeModal">
                <n-grid :col="24" :x-gap="16" :y-gap="16">
                    <n-grid-item :span="12">
                        <span>订单号：</span>
                        <span>{{ configData.pomNumber }}</span>
                    </n-grid-item>
                    <n-grid-item :span="12">
                        <span>客户备注：</span>
                        <span>{{ configData.customerRemark }}</span>
                    </n-grid-item>
                    <n-grid-item :span="12">
                        <span>技术反馈情况：</span>
                        <n-text v-if="String(configData.formulaCheckState) === '1'" type="success">已反馈</n-text>
                        <n-text v-else type="error">未反馈</n-text>
                    </n-grid-item>
                    <n-grid-item :span="12">
                        <span>技术最新反馈时间：</span>
                        <span>{{ configData.formulaCheckTime || "暂无" }}</span>
                    </n-grid-item>
                    <n-grid-item :span="12">
                        <span>仓库反馈情况：</span>
                        <template v-if="String(configData.stockCheckState) === '1'">
                            <n-text type="success">已反馈</n-text>
                            <n-button class="ml-2" text type="primary" @click="onApplyStockCheckAgain()">
                                再次申请反馈
                            </n-button>
                        </template>
                        <n-text v-else type="error">未反馈</n-text>
                    </n-grid-item>
                    <n-grid-item :span="12">
                        <span>仓库反馈日期：</span>
                        <span>{{ configData.stockCheckTime || "暂无" }}</span>
                    </n-grid-item>
                </n-grid>
                <div class="mt-30px mb-15px text-16px">规格型号详情</div>
                <n-element class="flex-y-center gap-10px" tag="div">
                    <div
                        v-for="(item, index) in specificationList"
                        :class="[
                            'pb-8px cursor-pointer border-b-2px border-transparent',
                            specificationActive === index && 'border-[var(--primary-color)]!'
                        ]"
                        @click="changeSpecificationActive(index)"
                    >
                        {{ item.specification }}
                    </div>
                </n-element>
                <div v-for="(item, index) in specificationList" class="mt-20px">
                    <n-grid v-if="specificationActive === index" :col="24" :x-gap="16" :y-gap="16">
                        <n-grid-item :span="12">
                            <span>仓库成品数：{{ item.stockCount || "未知" }}</span>
                            <span></span>
                        </n-grid-item>
                        <n-grid-item :span="12">
                            <span>原材料满足情况：</span>
                            <n-text v-if="String(item.materialIsEnough) === '1'" type="success">满足</n-text>
                            <template v-else>
                                <n-text type="error">不满足</n-text>
                                <n-button class="ml-2" text type="primary">发起购买申请</n-button>
                            </template>
                        </n-grid-item>
                        <n-grid-item :span="12">
                            <span>技术配方名称：</span>
                            <span>{{ item.formulaName || "未知" }}</span>
                        </n-grid-item>
                        <n-grid-item :span="12">
                            <span>原材料购买申请记录：</span>
                            <n-button text type="primary">点击查询</n-button>
                        </n-grid-item>
                        <n-grid-item :span="24">
                            <span>技术配方详情：</span>
                        </n-grid-item>
                        <n-grid-item :span="24">
                            <DynamicTable
                                v-model:header="item.formulaDetail.header"
                                v-model:value="item.formulaDetail.value"
                                :addable="false"
                                :data-source="dynamicTableDataSource"
                                :deletable="false"
                                :headerConfigurable="false"
                                class="w-100%"
                                disabled
                            />
                        </n-grid-item>
                    </n-grid>
                </div>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { ref, watchEffect } from "vue";
import { GET_PRODUCTION_ORDER_FORMULAS, GET_PRODUCTION_TYPE_LIST } from "@/api/application/production";
import {
    DynamicTable,
    DynamicTableDataSourceProps,
    DynamicTableHeaderProps,
    DynamicTableRowProps
} from "@/components/Dynamic";
import { isJSON } from "@/utils/tools";
import { useDicts } from "@/hooks";

let props = withDefaults(
    defineProps<{
        show: Boolean;
        configData: UnKnownObject;
    }>(),
    {
        show: () => false
    }
);

let emits = defineEmits(["update:show", "refresh"]);

// 字典操作
let { dictLibs, getDictLibs } = useDicts();

let setDictLibs = async () => {
    let dictName = ["product_type"];
    await getDictLibs(dictName);
};

let dynamicTableDataSource = ref<DynamicTableDataSourceProps[]>([]);

let setDynamicTableDataSource = async () => {
    // 规格型号
    let specifications = await GET_PRODUCTION_TYPE_LIST({ current: 1, size: 10000 }).then((res) => {
        if (res.data.code === 0) {
            return res.data.data.records.map((item: any) => ({
                label: item.specification,
                value: item.id
            }));
        }
    });

    let tubeClassification = dictLibs.product_type;

    dynamicTableDataSource.value = [
        { label: "管材分类", value: "tubeClassification", defaultOptions: tubeClassification },
        { label: "规格型号", value: "specifications", defaultOptions: specifications }
    ];
};

let specificationList = ref<
    {
        specification: Nullable<string>;
        stockCount: Nullable<number>;
        materialIsEnough: Nullable<number>;
        formulaName: Nullable<string>;
        formulaDetail: {
            header: DynamicTableHeaderProps[];
            value: DynamicTableRowProps[];
        };
    }[]
>([]);

let specificationActive = ref(0);

let changeSpecificationActive = (index: number) => {
    specificationActive.value = index;
};

watchEffect(async () => {
    if (props.configData.poId) {
        await setDictLibs();
        await setDynamicTableDataSource();
        GET_PRODUCTION_ORDER_FORMULAS({ poId: props.configData.poId }).then((res) => {
            if (res.data.code === 0 && res.data.data) {
                specificationList.value = (res.data.data.modelFormulaList || []).map((item: any) => {
                    item.formulaDetail = isJSON(item.chargerSheet)
                        ? JSON.parse(item.chargerSheet)
                        : { header: [], value: [] };

                    return item;
                });
            }
        });
    }
});

// 关闭弹窗
let closeModal = () => {
    emits("update:show", false);
};

// 再次申请仓库反馈
let onApplyStockCheckAgain = () => {
    window.$message.warning("敬请期待");
};
</script>
