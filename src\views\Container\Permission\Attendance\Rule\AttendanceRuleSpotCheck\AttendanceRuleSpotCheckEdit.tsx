import { computed, defineComponent, ref, watchEffect } from "vue";
import { type DataTableColumns, type FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import {
    GET_ALL_USER_LIST,
    GET_SPOT_CHECK_ATTENDANCE_CONFIGURATION_DETAIL,
    SAVE_SPOT_CHECK_ATTENDANCE_CONFIGURATION
} from "@/api/permission";
import { TableActions } from "@/components/TableActions";
import { UserSelector } from "@/components/UserSelector";

export default defineComponent({
    name: "AttendanceRuleSpotCheckEdit",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        // 获取详情
        const getDetail = () => {
            GET_SPOT_CHECK_ATTENDANCE_CONFIGURATION_DETAIL({ id: props.configData.id }).then((res) => {
                const resData = res.data.data;
                formData.value = {
                    checkName: resData.checkName ?? null,
                    isStatus: resData.isStatus ?? 1,
                    startTime: resData.startTime ?? null,
                    endTime: resData.endTime ?? null,
                    count: resData.count ?? 0,
                    allowTime: resData.allowTime ?? 0
                };
                itemList.value = resData.itemList ?? [];
            });
        };

        // 表单数据
        interface FormDataProps {
            [key: string]: any;
        }

        const formRef = ref<FormInst | null>(null);

        // 获取表单选项
        const getFormOptions = async () => {};

        const initFormData: FormDataProps = {
            checkName: null,
            isStatus: 1,
            startTime: null,
            endTime: null,
            count: 0,
            allowTime: 0
        };

        const formRules = computed(() => ({
            checkName: [{ required: true, message: "请输入抽查规则名称", trigger: ["input", "blur"] }],
            isStatus: [{ required: true, message: "请选择是否启用", trigger: ["blur", "change"], type: "number" }],
            startTime: [{ required: true, message: "请选择开始时间", trigger: ["blur", "change"] }],
            endTime: [{ required: true, message: "请选择结束时间", trigger: ["blur", "change"] }],
            count: [{ required: true, message: "请输入抽查次数", trigger: ["blur", "change"], type: "number" }],
            allowTime: [{ required: true, message: "请输入允许时长", trigger: ["blur", "change"], type: "number" }]
        }));

        const formData = ref(cloneDeep(initFormData));

        const clearForm = () => {
            formData.value = cloneDeep(initFormData);
        };

        // 人员相关配置
        const itemTableColumns = ref<DataTableColumns<any>>([
            { title: "姓名", key: "trueName", align: "center" },
            { title: "手机号", key: "phone", align: "center" },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 100,
                render: (row, index) => {
                    return (
                        <TableActions
                            type="button"
                            buttonActions={[
                                { label: "删除", tertiary: true, type: "error", onClick: () => deleteItem(row, index) }
                            ]}
                        />
                    );
                }
            }
        ]);

        const itemList = ref<any[]>([]);

        const users = ref<any>(null);

        const addItem = () => {
            window.$dialog.success({
                title: "选择人员",
                content: () => (
                    <UserSelector
                        v-model:value={users.value}
                        class="w-100%"
                        clearable
                        key-name="userId"
                        placeholder="请选择人员"
                    />
                ),
                positiveText: "新增",
                negativeText: "取消",
                onPositiveClick: () => {
                    GET_ALL_USER_LIST({ userIds: users.value }).then((res) => {
                        const existingUserIds = new Set(itemList.value.map((item: any) => item.userId));
                        const newUsers = res.data.data.filter((item: any) => !existingUserIds.has(item.userId));
                        itemList.value.push(...newUsers);
                    });
                    users.value = null;
                }
            });
        };

        const deleteItem = (row: any, index: number) => {
            if (row.id) {
                itemList.value.forEach((citem, cindex) => {
                    if (row.id === citem.id) citem.delFlag = 1;
                });
            } else {
                itemList.value.splice(index, 1);
            }
        };

        watchEffect(() => {
            if (props.configData.id) {
                getDetail();
            }
        });

        // 交互按钮
        const onClose = () => {
            clearForm();
            changeModalShow(false);
            emit("refresh");
        };

        const onSubmit = async () => {
            const validateError = await formRef.value?.validate((errors) => !!errors);
            if (validateError) return false;

            SAVE_SPOT_CHECK_ATTENDANCE_CONFIGURATION({
                ...(props.configData.id && { id: props.configData.id }),
                ...formData.value,
                corpId: props.configData.corpId,
                itemList: (itemList.value ?? []).map((item: any) => ({ userId: item.userId }))
            }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success(props.configData.id ? "编辑成功" : "新增成功");
                    onClose();
                } else {
                    window.$message.error(res.data.msg);
                }
            });
        };

        watchEffect(async () => {
            if (show.value) {
                await getFormOptions();
                if (props.configData.id) getDetail();
            }
        });

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card
                    title={props.configData.id ? "编辑抽查考勤规则" : "新增抽查考勤规则"}
                    class="w-1200px"
                    closable
                    onClose={onClose}
                >
                    <n-form
                        ref={formRef}
                        model={formData.value}
                        rules={formRules.value}
                        label-placement="left"
                        label-width="auto"
                    >
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi span={6} label="抽查规则名称" path="checkName">
                                <n-input
                                    v-model:value={formData.value.checkName}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入抽查规则名称"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="启用状态" path="isStatus">
                                <n-switch
                                    v-model:value={formData.value.isStatus}
                                    checked-value={1}
                                    unchecked-value={0}
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="抽查开始时间" path="startTime">
                                <n-time-picker
                                    v-model:formatted-value={formData.value.startTime}
                                    seconds={[0]}
                                    class="w-100%"
                                    clearable
                                    placeholder="请选择抽查开始时间"
                                    format="HH:mm:ss"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="抽查结束时间" path="endTime">
                                <n-time-picker
                                    v-model:formatted-value={formData.value.endTime}
                                    seconds={[0]}
                                    class="w-100%"
                                    clearable
                                    placeholder="请选择抽查结束时间"
                                    format="HH:mm:ss"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="抽查次数" path="count">
                                <n-input-number
                                    v-model:value={formData.value.count}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入抽查次数"
                                    min={0}
                                    precision={0}
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="允许超出时间" path="allowTime">
                                <n-input-number
                                    v-model:value={formData.value.allowTime}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入允许超出时间"
                                    min={0}
                                    precision={0}
                                    v-slots={{
                                        suffix: () => <>分钟</>
                                    }}
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={12}>
                                <div class="w-100%">
                                    <n-space class="mb">
                                        <n-button type="success" onClick={() => addItem()}>
                                            新增添加
                                        </n-button>
                                    </n-space>
                                    <n-data-table
                                        columns={itemTableColumns.value}
                                        data={itemList.value}
                                        single-line={false}
                                        bordered
                                        striped
                                    />
                                </div>
                            </n-form-item-gi>
                            <n-form-item-gi span={12}>
                                <n-space>
                                    <n-button type="primary" onClick={onSubmit}>
                                        提交
                                    </n-button>
                                    <n-button onClick={onClose}>取消</n-button>
                                </n-space>
                            </n-form-item-gi>
                        </n-grid>
                    </n-form>
                </n-card>
            </n-modal>
        );
    }
});
