<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card :title="props.configData.id ? '编辑分类' : '新增分类'" class="w-600px" closable @close="closeModal">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-grid cols="12" x-gap="16">
                        <n-form-item-gi :span="12" label="分类类型" required>
                            <n-radio-group v-model:value="categoryTypeActive" :disabled="!!props.configData.id">
                                <n-space>
                                    <n-radio v-for="item in categoryTypeList" :label="item.label" :value="item.value" />
                                </n-space>
                            </n-radio-group>
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="产品类型" path="productGenre" v-if="categoryTypeActive !== 4">
                            <n-radio-group :disabled="formData.parentId !== '0'" v-model:value="formData.productGenre">
                                <n-space>
                                    <n-radio v-if="categoryTypeActive === 1" :value="1" label="原材料" />
                                    <n-radio :value="2" label="产成品" />
                                    <n-radio :value="3" label="半成品" />
                                </n-space>
                            </n-radio-group>
                        </n-form-item-gi>
                        <n-form-item-gi v-if="categoryTypeActive === 2" :span="12" label="工资类型" required>
                            <n-radio-group v-model:value="wageType" :disabled="!!props.configData.id">
                                <n-space>
                                    <n-radio :value="1" label="定额工资" />
                                    <n-radio :value="2" label="非定额工资" />
                                </n-space>
                            </n-radio-group>
                        </n-form-item-gi>
                        <n-form-item-gi v-if="categoryTypeActive === 4" :span="12" label="成本类型" required>
                            <n-radio-group v-model:value="wageType" :disabled="!!props.configData.id">
                                <n-space>
                                    <n-radio :value="1" label="外购外协" />
                                    <n-radio :value="2" label="其他成本" />
                                </n-space>
                            </n-radio-group>
                        </n-form-item-gi>
                        <n-form-item-gi v-if="categoryTypeActive === 4" :span="12" label="是否需要凭证" required>
                            <n-radio-group v-model:value="certificateFlag">
                                <n-space>
                                    <n-radio :value="0" label="否" />
                                    <n-radio :value="1" label="是" />
                                </n-space>
                            </n-radio-group>
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="父级分类">
                            <n-tree-select
                                v-model:value="formData.parentId"
                                :options="parentIdOptions"
                                children-field="childrenList"
                                clearable
                                default-expand-all
                                key-field="id"
                                label-field="categoryName"
                                placeholder="请选择父级分类，不选则默认为顶层大类"
                                @update:value="changeParentId"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="分类名称" path="categoryName">
                            <n-input
                                v-model:value="formData.categoryName"
                                class="w-100%"
                                clearable
                                placeholder="请输入分类名称"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="排序" path="showOrder">
                            <n-input-number
                                v-model:value="formData.showOrder"
                                class="w-100%"
                                clearable
                                placeholder="请输入排序"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12">
                            <n-space>
                                <n-button type="primary" @click="onSubmit">提交</n-button>
                                <n-button @click="closeModal">取消</n-button>
                            </n-space>
                        </n-form-item-gi>
                    </n-grid>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref, watchEffect } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { useStoreUser } from "@/store";
import {
    ADD_FORMULA_CATEGORY,
    ADD_MATERIAL_CATEGORY,
    ADD_OTHER_CATEGORY,
    ADD_WAGE_CATEGORY,
    GET_FORMULA_CATEGORY_BY_ID,
    GET_FORMULA_CATEGORY_TREE_LIST,
    GET_MATERIAL_CATEGORY_BY_ID,
    GET_MATERIAL_CATEGORY_TREE_LIST,
    GET_OTHER_CATEGORY_BY_ID,
    GET_OTHER_CATEGORY_TREE_LIST,
    GET_WAGE_CATEGORY_BY_ID,
    GET_WAGE_CATEGORY_TREE_LIST,
    UPDATE_FORMULA_CATEGORY,
    UPDATE_MATERIAL_CATEGORY,
    UPDATE_OTHER_CATEGORY,
    UPDATE_WAGE_CATEGORY
} from "@/api/application/reporting";

let storeUser = useStoreUser();

let props = withDefaults(
    defineProps<{
        show: boolean;
        configData: UnKnownObject;
    }>(),
    {
        show: () => false
    }
);

let emits = defineEmits(["update:show", "refresh"]);

let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

let changeModalShow = (show: boolean) => emits("update:show", show);

// 分类类型
let categoryTypeList = ref([
    { label: "物料分类", value: 1 },
    { label: "工资分类", value: 2 },
    { label: "配方分类", value: 3 },
    { label: "其他分类", value: 4 }
]);

let categoryTypeActive = ref(1);

// 父级分类
let parentIdOptions = ref([]);

let changeParentId = (val: string, obj: Record<string, any>) => {
    if (!val) {
        formData.value.parentId = "0";
        formData.value.productGenre = null;
    } else {
        formData.value.productGenre = obj.productGenre;
    }
};

// // 工资类型、成本类型
let wageType = ref(1);

// 其他分类-是否需要凭证（0否1是）
let certificateFlag = ref(0);

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {
    productGenre: { required: true, type: "number", message: "请选择产品类型", trigger: ["change"] },
    categoryName: { required: true, message: "请输入分类名称", trigger: ["input", "blur"] },
    showOrder: { required: true, type: "number", message: "请输入排序", trigger: ["input", "blur"] }
};

// 表单数据
interface FormDataProps {
    productGenre: Nullable<string | number>;
    parentId?: Nullable<string>;
    categoryName: Nullable<string>;
    showOrder: Nullable<number>;
}

let initFormData: FormDataProps = {
    productGenre: null,
    parentId: "0",
    categoryName: null,
    showOrder: 0
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 获取详情
let getDetail = () => {
    if (categoryTypeActive.value === 1) {
        GET_MATERIAL_CATEGORY_BY_ID({ id: props.configData.id }).then((res) => {
            if (res.data.code === 0) {
                formData.value = {
                    productGenre: res.data.data.productGenre,
                    parentId: res.data.data.parentId && res.data.data.parentId !== "0" ? res.data.data.parentId : null,
                    categoryName: res.data.data.categoryName,
                    showOrder: res.data.data.showOrder
                };
            }
        });
    } else if (categoryTypeActive.value === 2) {
        GET_WAGE_CATEGORY_BY_ID({ id: props.configData.id }).then((res) => {
            if (res.data.code === 0) {
                formData.value = {
                    productGenre: res.data.data.productGenre,
                    parentId: res.data.data.parentId && res.data.data.parentId !== "0" ? res.data.data.parentId : null,
                    categoryName: res.data.data.categoryName,
                    showOrder: res.data.data.showOrder
                };
                wageType.value = res.data.data.wageType;
            }
        });
    } else if (categoryTypeActive.value === 3) {
        GET_FORMULA_CATEGORY_BY_ID({ id: props.configData.id }).then((res) => {
            if (res.data.code === 0) {
                formData.value = {
                    productGenre: res.data.data.productGenre,
                    parentId: res.data.data.parentId && res.data.data.parentId !== "0" ? res.data.data.parentId : null,
                    categoryName: res.data.data.categoryName,
                    showOrder: res.data.data.showOrder
                };
            }
        });
    } else if (categoryTypeActive.value === 4) {
        GET_OTHER_CATEGORY_BY_ID({ id: props.configData.id }).then((res) => {
            if (res.data.code === 0) {
                formData.value = {
                    productGenre: res.data.data.productGenre,
                    parentId: res.data.data.parentId && res.data.data.parentId !== "0" ? res.data.data.parentId : null,
                    categoryName: res.data.data.categoryName,
                    showOrder: res.data.data.showOrder,
                    certificateFlag: res.data.data.certificateFlag
                };
                wageType.value = res.data.data.wageType;
                certificateFlag.value = res.data.data.certificateFlag;
            }
        });
    }
};

let getParentIdOptions = () => {
    clearFrom();
    if (categoryTypeActive.value === 1) {
        GET_MATERIAL_CATEGORY_TREE_LIST({}).then((res) => {
            if (res.data.code === 0) parentIdOptions.value = res.data.data ?? [];
        });
    } else if (categoryTypeActive.value === 2) {
        GET_WAGE_CATEGORY_TREE_LIST({}).then((res) => {
            if (res.data.code === 0) parentIdOptions.value = res.data.data ?? [];
        });
    } else if (categoryTypeActive.value === 3) {
        GET_FORMULA_CATEGORY_TREE_LIST({}).then((res) => {
            if (res.data.code === 0) parentIdOptions.value = res.data.data ?? [];
        });
    } else if (categoryTypeActive.value === 4) {
        GET_OTHER_CATEGORY_TREE_LIST({}).then((res) => {
            if (res.data.code === 0) parentIdOptions.value = res.data.data ?? [];
        });
    }
};

watchEffect(() => {
    if (props.configData.categoryType) {
        categoryTypeActive.value = props.configData.categoryType as number;
    }
});

watchEffect(() => {
    if (props.show && categoryTypeActive.value) getParentIdOptions();
});

watchEffect(() => {
    if (props.configData.id) getDetail();
});

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    changeModalShow(false);
};

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    if (!props.configData.id) {
        if (categoryTypeActive.value === 1) {
            ADD_MATERIAL_CATEGORY({ ...formData.value }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("新增成功");
                    closeModal();
                    emits("refresh");
                }
            });
        } else if (categoryTypeActive.value === 2) {
            ADD_WAGE_CATEGORY({ ...formData.value, wageType: wageType.value }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("新增成功");
                    closeModal();
                    emits("refresh");
                }
            });
        } else if (categoryTypeActive.value === 3) {
            ADD_FORMULA_CATEGORY({ ...formData.value }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("新增成功");
                    closeModal();
                    emits("refresh");
                }
            });
        } else if (categoryTypeActive.value === 4) {
            ADD_OTHER_CATEGORY({
                ...formData.value,
                wageType: wageType.value,
                certificateFlag: certificateFlag.value
            }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("新增成功");
                    closeModal();
                    emits("refresh");
                }
            });
        }
    } else {
        if (categoryTypeActive.value === 1) {
            UPDATE_MATERIAL_CATEGORY({
                ...formData.value,
                id: props.configData.id
            }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("编辑成功");
                    closeModal();
                    emits("refresh");
                }
            });
        } else if (categoryTypeActive.value === 2) {
            UPDATE_WAGE_CATEGORY({
                ...formData.value,
                id: props.configData.id,
                wageType: wageType.value
            }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("编辑成功");
                    closeModal();
                    emits("refresh");
                }
            });
        } else if (categoryTypeActive.value === 3) {
            UPDATE_FORMULA_CATEGORY({
                ...formData.value,
                id: props.configData.id
            }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("编辑成功");
                    closeModal();
                    emits("refresh");
                }
            });
        } else if (categoryTypeActive.value === 4) {
            UPDATE_OTHER_CATEGORY({
                ...formData.value,
                id: props.configData.id,
                wageType: wageType.value,
                certificateFlag: certificateFlag.value
            }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("编辑成功");
                    closeModal();
                    emits("refresh");
                }
            });
        }
    }
};
</script>
