<template>
    <div>
        <n-data-table
            :columns="tableColumns"
            :data="tableData"
            :loading="tableLoading"
            :pagination="tablePagination"
            :row-key="tableRowKey"
            :single-line="false"
            bordered
            remote
            striped
            @update:checked-row-keys="changeTableSelection"
        />
        <!--查看详情-->
        <DetailModal v-model:show="detailModal.show" :config-data="detailModal.configData" @refresh="getTableData" />
        <!--绑定领料单-->
        <BindMaterialModal
            v-model:show="bindMaterial.show"
            :config-data="bindMaterial.configData"
            @refresh="getTableData"
        />
        <!--完成并退料-->
        <FinishAndReturnModal
            v-model:show="finishAndReturn.show"
            :config-data="finishAndReturn.configData"
            @refresh="getTableData"
        />
    </div>
</template>

<script lang="ts" setup>
import { useCommonTable } from "@/hooks";
import { h, onMounted, reactive, ref } from "vue";
import { DataTableColumns } from "naive-ui";
import { GET_PRODUCTION_TASK_IN_PRODUCTION } from "@/api/application/production";
import { TableActions } from "@/components/TableActions";
import { BindMaterialModal, FinishAndReturnModal, DetailModal } from "../Modal";

let props = withDefaults(defineProps<{ searchForm?: UnKnownObject }>(), {});

interface RowProps {
    poId?: string | number;
    pomNumber?: string | number;
    id?: string | number;
    specification?: string;
    prodLineName?: string;
    prodCount?: string | number;
}

onMounted(() => {
    getTableData();
});

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("poId");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "订单号",
        key: "pomNumber",
        align: "center",
        render: (row) => {
            return row.pomNumber || "/";
        }
    },
    {
        title: "生产任务单号",
        key: "id",
        align: "center"
    },
    {
        title: "生产规格型号",
        key: "specification",
        align: "center"
    },
    {
        title: "生产线路",
        key: "prodLineName",
        align: "center"
    },
    {
        title: "生产数量",
        key: "prodCount",
        align: "center"
    },
    // {
    //     title: "开车检查表状态",
    //     key: "",
    //     align: "center"
    // },
    // {
    //     title: "首检单",
    //     key: "",
    //     align: "center"
    // },
    // {
    //     title: "过程表单状态",
    //     key: "",
    //     align: "center"
    // },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: 300,
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "查看详情",
                        tertiary: true,
                        type: "primary",
                        onClick: () => openDetailModal(row)
                    },
                    {
                        label: "绑定领料",
                        tertiary: true,
                        type: "success",
                        onClick: () => openBindMaterial(row)
                    },
                    {
                        label: "完成并退料",
                        tertiary: true,
                        type: "error",
                        onClick: () => openFinishAndReturn(row)
                    }
                ]
            });
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_PRODUCTION_TASK_IN_PRODUCTION({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        ...props.searchForm
    }).then(async (res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data.records;
            tablePagination.itemCount = res.data.data.total;
            tableLoading.value = false;
        }
    });
};

// 绑定领料单
let bindMaterial = ref<{ show: boolean; configData: Record<string, any> }>({
    show: false,
    configData: {}
});

let openBindMaterial = (row: RowProps) => {
    bindMaterial.value = { show: true, configData: row };
};

// 完成并退料
let finishAndReturn = ref<{ show: boolean; configData: Record<string, any> }>({ show: false, configData: {} });

let openFinishAndReturn = (row: RowProps) => {
    finishAndReturn.value = { show: true, configData: row };
};

// 查看详情
let detailModal = ref<{ show: boolean; configData: Record<string, any> }>({ show: false, configData: {} });

let openDetailModal = (row: RowProps) => {
    detailModal.value = {
        show: true,
        configData: row
    };
    console.log(111, detailModal.value);
};
</script>
