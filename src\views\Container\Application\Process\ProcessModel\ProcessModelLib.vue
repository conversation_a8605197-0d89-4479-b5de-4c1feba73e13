<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card v-for="item in tableData" :title="item.title" class="mt" hoverable>
            <n-grid :col="24" :x-gap="16" :y-gap="16" item-responsive>
                <n-grid-item v-for="citem in item.list" span="24 1000:8 1500:6 2500:4">
                    <n-button block class="h-45px" secondary type="primary" @click="openLaunchModal(citem)">
                        <template #icon>
                            <dynamic-icon :icon="citem.modelIcon || 'AppstoreAddOutlined'" size="22" />
                        </template>
                        <n-ellipsis>{{ citem.name }}</n-ellipsis>
                    </n-button>
                </n-grid-item>
            </n-grid>
        </n-card>
        <process-launch v-model:show="launchModal.show" :config-data="launchModal.configData" />
    </div>
</template>

<script lang="ts" setup>
import { DynamicIcon } from "@/components/DynamicIcon";
import { onMounted, ref } from "vue";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { ProcessLaunch } from "../components";
import { useDicts } from "@/hooks";
import { GET_OA_MODEL_LIST } from "@/api/application/oa";

type RowProps = Record<string, any>;

let { getDict } = useDicts();

onMounted(async () => {
    await getSearchOptions();
    getTableData();
});

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { prop: "name", type: "input", label: "流程名称" },
    { prop: "category", type: "select", label: "流程分类" }
]);

let searchOptions = ref<TableSearchbarOptions>({
    category: []
});

let searchForm = ref<TableSearchbarData>({
    name: null,
    category: null
});

let getSearchOptions = async () => {
    searchOptions.value.category = await getDict("flow_category");
};

let onSearch = () => getTableData();

let tableData = ref<any[]>([]);

let getTableData = () => {
    GET_OA_MODEL_LIST({
        current: 1,
        size: 1000,
        modelState: 1,
        ...searchForm.value
    }).then((res) => {
        if (res.data.code === 0) {
            tableData.value = [];
            res.data.data.records.forEach((item: any) => {
                let category = item.category || "未分类";
                let categoryIndex = tableData.value.findIndex((item) => item.title === category);
                if (categoryIndex !== -1) {
                    tableData.value[categoryIndex].list.push(item);
                } else {
                    tableData.value.push({
                        title: category,
                        list: [item]
                    });
                }
            });
            tableData.value = tableData.value.filter(
                (item) => item.title !== "系统流程" && item.list && item.list.length > 0
            );
        }
    });
};

// 发起流程
let launchModal = ref<{ show: boolean; configData: RowProps }>({ show: false, configData: {} });

let openLaunchModal = (row: RowProps) => {
    launchModal.value = { show: true, configData: row };
};
</script>
