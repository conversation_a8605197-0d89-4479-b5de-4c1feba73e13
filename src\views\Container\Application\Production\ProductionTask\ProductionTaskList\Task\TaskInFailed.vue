<template>
    <div>
        <n-data-table
            :columns="tableColumns"
            :data="tableData"
            :loading="tableLoading"
            :pagination="tablePagination"
            :row-key="tableRowKey"
            :single-line="false"
            bordered
            remote
            striped
            @update:checked-row-keys="changeTableSelection"
        />
        <!--查看详情-->
        <DetailModal v-model:show="detailModal.show" :config-data="detailModal.configData" @refresh="getTableData" />
        <!--新增排产任务-->
        <ProductionArrange
            v-model:show="productionArrangeModal.show"
            :config-data="productionArrangeModal.configData"
            @refresh="getTableData"
        />
    </div>
</template>

<script lang="ts" setup>
import { useCommonTable } from "@/hooks";
import { h, onMounted, reactive, ref } from "vue";
import type { DataTableColumns } from "naive-ui";
import { NText } from "naive-ui";
import { GET_PRODUCTION_TASK_UNREACH, PRODUCTION_TASK_UNQUALIFIED_STORAGE } from "@/api/application/production";
import { ProductionArrange } from "@/views/Container/Application/Production/ProductionTask/ProductionTaskArrange/Modal";
import { TableActions } from "@/components/TableActions";
import { DetailModal } from "@/views/Container/Application/Production/ProductionTask/ProductionTaskList/Modal";

let props = withDefaults(
    defineProps<{
        searchForm?: UnKnownObject;
    }>(),
    {}
);

interface RowProps {
    poId?: string | number;
    pomNumber?: string | number;
    id?: string | number;
    specification?: string;
    prodLineName?: string;
    prodCount?: string | number;
    prodScheDate?: string;
    waterBy?: string;
}

onMounted(() => {
    getTableData();
});

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("poId");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "订单号",
        key: "pomNumber",
        align: "center",
        render: (row) => {
            return row.pomNumber || "/";
        }
    },
    {
        title: "生产任务单号",
        key: "id",
        align: "center"
    },
    {
        title: "生产规格型号",
        key: "specification",
        align: "center"
    },
    {
        title: "生产线路",
        key: "prodLineName",
        align: "center"
    },
    {
        title: "生产数量",
        key: "prodCount",
        align: "center"
    },
    {
        title: "提交人",
        key: "waterBy",
        align: "center",
        render: (row: RowProps) => row.waterBy ?? "未知"
    },
    {
        title: "完成时间",
        key: "prodScheDate",
        align: "center"
    },
    {
        title: "实验室反馈状态",
        align: "center",
        key: "",
        render: () => h(NText, { type: "error" }, () => "未达标")
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: 300,
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "查看详情",
                        tertiary: true,
                        type: "primary",
                        onClick: () => openDetailModal(row)
                    },
                    {
                        label: "快捷排产",
                        tertiary: true,
                        type: "success",
                        onClick: () => openProductionArrangeModal(row)
                    },
                    {
                        label: "废料入库",
                        tertiary: true,
                        type: "warning",
                        onClick: () => onWasteStorage(row)
                    }
                ]
            });
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_PRODUCTION_TASK_UNREACH({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        ...props.searchForm
    }).then(async (res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data.records;
            tablePagination.itemCount = res.data.data.total;
            tableLoading.value = false;
        }
    });
};

// 新增排产任务弹窗
let productionArrangeModal = ref<{ show: boolean; configData: Record<string, any> }>({
    show: false,
    configData: {}
});

let openProductionArrangeModal = (row: RowProps) => {
    productionArrangeModal.value.show = true;
    productionArrangeModal.value.configData = row;
};

// 废料入库
let onWasteStorage = (row: RowProps) => {
    window.$dialog.warning({
        title: "提示",
        content: "确定该操作吗？",
        positiveText: "确定",
        negativeText: "取消",
        onPositiveClick: () => {
            PRODUCTION_TASK_UNQUALIFIED_STORAGE({ reqId: row.id }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("操作成功");
                    getTableData();
                }
            });
        }
    });
};

// 查看详情
let detailModal = ref<{ show: boolean; configData: Record<string, any> }>({ show: false, configData: {} });

let openDetailModal = (row: RowProps) => {
    detailModal.value = {
        show: true,
        configData: row
    };
    console.log(111, detailModal.value);
};
</script>
