import { computed, defineComponent, ref, watchEffect } from "vue";
import { GET_IRON_PROJECT_TYPE_DRAWING_LIST } from "@/api/application/TowerScan";
import type { DataTableColumns } from "naive-ui";
import { FilePreviewBeta } from "@/components/FilePreview";
import { TableActions } from "@/components/TableActions";

export default defineComponent({
    name: "TowerScanEngineeringTypeDrawing",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        // 图纸数据
        interface DrawingDataProps {
            id: string;
            towerTypeId: string;
            typeDrawingId: string;
            delFlag: number;
            drawingFile: {
                id: string;
                fileName: string;
                original: string;
                url: string;
                type: string;
                fileSize: string;
                createTime: string;
                [key: string]: any;
            };
            [key: string]: any;
        }

        const drawingData = ref<DrawingDataProps[]>([]);
        const loading = ref(false);

        // 表格列配置
        const columns = ref<DataTableColumns<DrawingDataProps>>([
            {
                title: "序号",
                key: "index",
                align: "center",
                width: 80,
                render: (_, index) => index + 1
            },
            {
                title: "图纸名称",
                key: "original",
                align: "center",
                render: (row) => row.drawingFile?.original || "/"
            },
            {
                title: "文件类型",
                key: "type",
                align: "center",
                render: (row) => row.drawingFile?.type?.toUpperCase() || "/"
            },
            {
                title: "文件大小",
                key: "fileSize",
                align: "center",
                render: (row) => {
                    const size = row.drawingFile?.fileSize;
                    if (!size) return "/";
                    const sizeNum = parseInt(size);
                    if (sizeNum < 1024) return `${sizeNum}B`;
                    if (sizeNum < 1024 * 1024) return `${(sizeNum / 1024).toFixed(2)}KB`;
                    return `${(sizeNum / (1024 * 1024)).toFixed(2)}MB`;
                }
            },
            {
                title: "上传时间",
                key: "createTime",
                align: "center",
                render: (row) => row.drawingFile?.createTime || "/"
            },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 160,
                render: (row: DrawingDataProps) => {
                    return (
                        <TableActions
                            type="button"
                            buttonActions={[
                                {
                                    label: "预览",
                                    tertiary: true,
                                    type: "primary",
                                    onClick: () => onPreview(row.drawingFile?.url)
                                },
                                {
                                    label: "下载",
                                    tertiary: true,
                                    type: "success",
                                    onClick: () => onDownload(row.drawingFile?.url)
                                }
                            ]}
                        />
                    );
                }
            }
        ]);

        // 获取图纸列表
        const getDrawingList = () => {
            if (!props.configData?.id) return;

            loading.value = true;
            GET_IRON_PROJECT_TYPE_DRAWING_LIST({
                typeId: props.configData.id
            })
                .then((res) => {
                    if (res.data.code === 0) {
                        drawingData.value = res.data.data || [];
                    }
                })
                .finally(() => {
                    loading.value = false;
                });
        };

        // beta预览
        const previewBetaModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const onPreview = (url?: string) => {
            if (url) {
                const fullUrl = import.meta.env.VITE_PREVIEW_URL + url;
                previewBetaModal.value = {
                    show: true,
                    configData: { ...props.configData?.techCheckFile, url: fullUrl }
                };
            } else {
                window.$message.error("预览失败");
            }
        };

        const onDownload = (url?: string) => {
            if (url) {
                window.open(import.meta.env.VITE_API_URL + url);
            } else {
                window.$message.error("下载失败");
            }
        };
        const onClose = () => {
            changeModalShow(false);
        };

        watchEffect(() => {
            if (show.value && props.configData?.id) {
                getDrawingList();
            }
        });

        return () => (
            <div>
                <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                    <n-card
                        title={`${props.configData?.typeName || "塔型"} - 图纸列表`}
                        class="w-1000px"
                        closable
                        onClose={onClose}
                    >
                        <n-data-table
                            columns={columns.value}
                            data={drawingData.value}
                            loading={loading.value}
                            single-line={false}
                            bordered
                            striped
                            max-height={500}
                        />
                    </n-card>
                </n-modal>
                <FilePreviewBeta
                    v-model:show={previewBetaModal.value.show}
                    configData={previewBetaModal.value.configData}
                />
            </div>
        );
    }
});
