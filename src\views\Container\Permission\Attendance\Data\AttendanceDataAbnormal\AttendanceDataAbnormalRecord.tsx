import { defineComponent, onMounted, reactive, ref, watchEffect } from "vue";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import { GET_ABNORMAL_ATTENDANCE_HANDLING_RECORDS } from "@/api/permission";
import dayjs from "dayjs";
import { TableActions } from "@/components/TableActions";

export default defineComponent({
    name: "AttendanceDataAbnormalRecord",
    props: {
        corpId: { type: String as PropType<string | null>, default: null }
    },
    setup(props, { emit }) {
        // 搜索项
        const searchConfig = ref<TableSearchbarConfig>([]);

        const searchOptions = ref<TableSearchbarOptions>({});

        const getSearchOptions = async () => {};

        const searchForm = ref<TableSearchbarData>({});

        const onSearch = () => {
            getTableData();
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            {
                title: "人员名称",
                key: "trueName",
                align: "center",
                render: (row) => <n-text type="info">{row.trueName ?? "/"}</n-text>
            },
            {
                title: "异常日期",
                key: "workDate",
                align: "center",
                render: (row) => <n-text>{dayjs(row.workDate).format("YYYY-MM-DD") ?? "/"}</n-text>
            },
            {
                title: "触发考勤规则",
                key: "ruleName",
                align: "center",
                render: (row) => <n-text type="info">{row.ruleName ?? "/"}</n-text>
            },
            {
                title: "异常类型",
                key: "dstatus",
                align: "center",
                render: (row) => {
                    switch (row.dstatus) {
                        case 2:
                            return <n-text type="error">缺卡</n-text>;
                        case 3:
                            return <n-text type="warning">早退</n-text>;
                        case 4:
                            return <n-text type="warning">迟到</n-text>;
                        default:
                            return <n-text type="info">正常</n-text>;
                    }
                }
            },
            {
                title: "异常详情",
                key: "exceptionDetail",
                align: "center",
                render: (row) => <n-text>{row.exceptionDetail ?? "/"}</n-text>
            },
            {
                title: "处理人",
                key: "optBy",
                align: "center",
                render: (row) => <n-text type="info">{row.optBy ?? "/"}</n-text>
            },
            {
                title: "处理时间",
                key: "optTime",
                align: "center",
                render: (row) => <n-text>{dayjs(row.optTime).format("YYYY-MM-DD HH:mm:ss") ?? "/"}</n-text>
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_ABNORMAL_ATTENDANCE_HANDLING_RECORDS({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                corpId: props.corpId,
                ...searchForm.value
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        watchEffect(() => {
            if (props.corpId) getTableData();
        });

        onMounted(async () => {
            await getSearchOptions();
        });

        return () => (
            <div class="attendance-page">
                <n-card>
                    <TableSearchbar
                        form={searchForm.value}
                        config={searchConfig.value}
                        options={searchOptions.value}
                        onSearch={onSearch}
                    />
                </n-card>
                <n-card class="mt">
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        pagination={tablePagination}
                        row-key={tableRowKey}
                        single-line={false}
                        bordered
                        remote
                        striped
                        onUpdate:checked-row-keys={changeTableSelection}
                    />
                </n-card>
            </div>
        );
    }
});
