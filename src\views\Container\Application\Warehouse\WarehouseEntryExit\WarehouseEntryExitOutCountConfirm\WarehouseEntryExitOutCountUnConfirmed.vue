<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <!--新增编辑-->
        <OaLaunch v-model:show="editModal.show" :configData="editModal.configData" @refresh="getTableData()" />
        <!--出库数量确认-->
        <ConfirmModal
            v-model:show="confirmModal.show"
            :configData="confirmModal.configData"
            @refresh="getTableData()"
        />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from "vue";
import { DataTableColumns } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import { GET_WAREHOUSE_OUT_CONFIRM_LIST } from "@/api/application/warehouse";
import { TableActions } from "@/components/TableActions";
import OaLaunch from "./WarehouseEntryExitOutCountConfirmLaunch.vue";
import ConfirmModal from "./WarehouseEntryExitOutCountConfirmModal.vue";

interface RowProps {
    [key: string]: any;
}

onMounted(async () => {
    await getSearchOptions();
    getTableData();
});

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([]);

let searchOptions = ref<TableSearchbarOptions>({});

let searchForm = ref<TableSearchbarData>({});

let getSearchOptions = async () => {};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "项目ID",
        key: "applyFromId",
        align: "center"
    },
    {
        title: "申请类型",
        key: "applyFrom",
        align: "center",
        render: (row) => {
            return row.applyFrom === 1 ? "项目" : "子项目";
        }
    },
    {
        title: "申请部门",
        key: "applyDepartName",
        align: "center",
        render: (row) => {
            return row.applyDepartName || "/";
        }
    },
    {
        title: "申请人",
        key: "applyByName",
        align: "center",
        render: (row) => {
            return row.applyByName || "/";
        }
    },
    {
        title: "申请出库数量",
        key: "applyQuantity",
        align: "center"
    },
    {
        title: "操作",
        key: "action",
        align: "center",
        width: 280,
        render(row, index) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "提交出库申请",
                        tertiary: true,
                        type: "primary",
                        disabled: () => row.processStatus !== 0,
                        onClick: () => openEditModal(row)
                    },
                    {
                        label: "出库数量确认",
                        tertiary: true,
                        type: "success",
                        onClick: () => openConfirmModal(row)
                    }
                ]
            });
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_WAREHOUSE_OUT_CONFIRM_LIST({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        confirmState: 1,
        ...searchForm.value
    }).then((res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data.records;
            tablePagination.itemCount = res.data.data.total;
            tableLoading.value = false;
        }
    });
};

// 搜索
let onSearch = () => {
    tablePagination.page = 1;
    tablePagination.pageSize = 10;
    getTableData();
};

// 发起
let editModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

let openEditModal = (row?: RowProps) => {
    editModal.value = {
        show: true,
        configData: row ?? {}
    };
};

// 出库数量确认
let confirmModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

let openConfirmModal = (row?: RowProps) => {
    confirmModal.value = {
        show: true,
        configData: row ?? {}
    };
};
</script>
