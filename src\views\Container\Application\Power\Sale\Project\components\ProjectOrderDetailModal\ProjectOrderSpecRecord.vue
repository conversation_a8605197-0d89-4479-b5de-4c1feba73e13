<template>
    <div>
        <n-table :single-line="false" class="text-center" v-if="(formData.contractTypeList || []).length">
            <thead>
                <tr>
                    <th>规格型号</th>
                    <th>需求数量</th>
                    <th>发货数量</th>
                </tr>
            </thead>
            <tbody>
                <tr v-for="(item, index) in formData.contractTypeList || []" :key="index">
                    <td>{{ item.specification }}</td>
                    <td>{{ item.needCount || 0 }}</td>
                    <td>{{ item.deliveryCount || 0 }}</td>
                </tr>
            </tbody>
        </n-table>
        <n-result v-else class="py-50px" size="large" status="404" title="暂无规格" />
    </div>
</template>

<script lang="ts" setup>
interface FormDataProps {
    [key: string]: any;
}

let props = withDefaults(defineProps<{ formData: FormDataProps }>(), {});

let emits = defineEmits(["refresh"]);

let onRefresh = () => emits("refresh");
</script>
