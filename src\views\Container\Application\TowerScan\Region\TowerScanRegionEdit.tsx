import { computed, defineComponent, ref, watchEffect } from "vue";
import { type DataTableColumns, type FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import {
    ADD_REGION,
    GET_REGION_DETAIL,
    UPDATE_REGION,
    GET_EQUIPMENT_PAGE_LIST,
    GET_IRON_TECHNIQUE_TREE_LIST
} from "@/api/application/TowerScan";
import { useStoreUser } from "@/store";
import { UserSelector } from "@/components/UserSelector";
import { TableActions } from "@/components/TableActions";
import { useDicts } from "@/hooks";
import dayjs from "dayjs";

// API接口类型定义
interface Equipment {
    [property: string]: any;
}

interface IronRegion {
    [property: string]: any;
}

interface RegionInspector {
    [property: string]: any;
}

interface Technique {
    [property: string]: any;
}

interface SubmitRequest {
    [property: string]: any;
}

// 内部数据类型定义
interface EquipmentList {
    [property: string]: any;
}

interface RegionInspectorList {
    [property: string]: any;
}

export default defineComponent({
    name: "TowerScanRegionEdit",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => {
            emit("update:show", show);
            if (!show) {
                clearForm();
            }
        };

        const storeUser = useStoreUser();

        // Tab切换状态
        const activeTab = ref("equipment");

        // 字典操作
        const { dictLibs, getDictLibs } = useDicts();

        const setDictLibs = async () => {
            const dictName = ["TechniqueProcessType"];
            await getDictLibs(dictName);
            dictLibs["TechniqueProcessType"] =
                dictLibs["TechniqueProcessType"]?.filter((item) => item.value !== "0" && item.value !== "1") || [];
        };

        // 表单数据
        const formRef = ref<FormInst | null>(null);

        // 工艺树数据
        const techniqueTreeData = ref<any[]>([]);
        const processTypeOptions = ref<any[]>([]);

        // 设备选项数据
        const equipmentOptions = ref<any[]>([]);

        // 递归构建树节点
        const buildTreeNode = (item: any): any => {
            const node = {
                label: item.techniqueName,
                key: item.id,
                ...item
            };

            if (item.childrenList && item.childrenList.length > 0) {
                node.children = item.childrenList.map((child: any) => buildTreeNode(child));
            } else {
                node.isLeaf = true;
            }

            return node;
        };

        // 获取表单选项
        const getFormOptions = async () => {
            await setDictLibs();
            processTypeOptions.value = dictLibs["TechniqueProcessType"] || [];

            // 获取所有工艺分类的工艺数据
            const allTechniqueData: any[] = [];
            for (const processType of processTypeOptions.value) {
                const res = await GET_IRON_TECHNIQUE_TREE_LIST({
                    processType: processType.value
                });
                if (res.data.code === 0) {
                    const categoryData = {
                        label: processType.label,
                        key: `category_${processType.value}`,
                        children: (res.data.data || []).map((item: any) => buildTreeNode(item))
                    };
                    allTechniqueData.push(categoryData);
                }
            }
            techniqueTreeData.value = allTechniqueData;

            // 获取设备列表
            const equipmentRes = await GET_EQUIPMENT_PAGE_LIST({
                current: 1,
                size: 1000
            });
            if (equipmentRes.data.code === 0) {
                equipmentOptions.value = equipmentRes.data.data.records.map((item: any) => ({
                    label: item.equipmentName,
                    value: item.equipmentId,
                    techniqueNames: item.techniqueNames || "",
                    lineName: item.lineName || "",
                    ...item
                }));
            }
        };

        // 表单校验规则
        const formRules = computed(() => ({
            regionName: { required: true, message: "请输入区域名称", trigger: ["input", "blur"] },
            operBy: { required: true, message: "请选择操作人", trigger: ["blur", "change"] },
            operTime: { required: true, message: "请选择操作时间", trigger: ["blur", "change"] }
        }));

        const initFormData = {
            regionName: "",
            operBy: storeUser.getUserData.sysUser?.username || "",
            operTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
            remark: ""
        };

        const formData = ref(cloneDeep(initFormData));

        const clearForm = () => {
            formData.value = cloneDeep(initFormData);
            equipmentList.value = [];
            regionInspectorList.value = [];
        };

        // 设备管理
        const equipmentList = ref<EquipmentList[]>([]);

        const equipmentTableColumns = ref<DataTableColumns<EquipmentList>>([
            {
                title: "设备",
                key: "equipmentId",
                align: "center",
                render: (row) => {
                    return (
                        <n-select
                            v-model:value={row.equipmentId}
                            class="w-100%"
                            options={equipmentOptions.value}
                            placeholder="请选择设备"
                            clearable
                            filterable
                            onUpdate:value={(value: any) => {
                                if (value) {
                                    // 从选项中获取设备信息并自动填充
                                    const selectedEquipment = equipmentOptions.value.find((opt) => opt.value === value);
                                    if (selectedEquipment) {
                                        row.equipmentId = value;
                                        row.techniqueNames = selectedEquipment.techniqueNames || "";
                                        row.lineName = selectedEquipment.lineName || "";
                                    }
                                } else {
                                    row.equipmentId = undefined;
                                    row.techniqueNames = null;
                                    row.lineName = null;
                                }
                            }}
                        />
                    );
                }
            },
            {
                title: "设备所属工序",
                key: "techniqueNames",
                align: "center",
                render: (row) => {
                    return row.techniqueNames && row.techniqueNames.trim() !== "" ? row.techniqueNames : "/";
                }
            },
            {
                title: "设备所属产线",
                key: "lineName",
                align: "center",
                render: (row) => {
                    return row.lineName && row.lineName.trim() !== "" ? row.lineName : "/";
                }
            },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 100,
                render: (row, index) => {
                    return (
                        <TableActions
                            type="button"
                            buttonActions={[
                                {
                                    label: "删除",
                                    tertiary: true,
                                    type: "error",
                                    onClick: () => removeEquipment(index)
                                }
                            ]}
                        />
                    );
                }
            }
        ]);

        const addEquipment = () => {
            equipmentList.value.push({
                equipmentId: undefined,
                techniqueNames: null,
                lineName: null
            });
        };

        const removeEquipment = (index: number) => {
            equipmentList.value.splice(index, 1);
        };

        // 质检员管理
        const regionInspectorList = ref<RegionInspectorList[]>([]);

        const inspectorTableColumns = ref<DataTableColumns<RegionInspectorList>>([
            {
                title: "人员名称",
                key: "inspector",
                align: "center",
                render: (row) => {
                    return (
                        <UserSelector
                            v-model:value={row.inspector}
                            class="w-100%"
                            key-name="username"
                            placeholder="请选择质检员"
                            multiple={false}
                        />
                    );
                }
            },
            {
                title: "负责工艺",
                key: "techniqueIds",
                align: "center",
                render: (row) => {
                    return (
                        <n-tree-select
                            v-model:value={row.techniqueIds}
                            class="w-100%"
                            options={techniqueTreeData.value}
                            clearable
                            filterable
                            multiple
                            placeholder="请选择负责工艺"
                            check-strategy="all"
                            cascade
                            onUpdate:value={(value: any[]) => {
                                if (value && value.length > 0) {
                                    // 过滤掉工艺分类节点，只保留具体工艺
                                    const filteredValues = value.filter(
                                        (v) => typeof v === "string" && !v.startsWith("category_")
                                    );
                                    if (filteredValues.length !== value.length) {
                                        window.$message.warning("请选择具体的工艺，不能只选择工艺分类");
                                    }
                                    row.techniqueIds = filteredValues;
                                } else {
                                    row.techniqueIds = [];
                                }
                            }}
                        />
                    );
                }
            },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 100,
                render: (row, index) => {
                    return (
                        <TableActions
                            type="button"
                            buttonActions={[
                                {
                                    label: "删除",
                                    tertiary: true,
                                    type: "error",
                                    onClick: () => removeRegionInspector(row, index)
                                }
                            ]}
                        />
                    );
                }
            }
        ]);

        const addRegionInspector = () => {
            regionInspectorList.value.push({
                inspector: "",
                delFlag: 0,
                techniqueIds: [],
                techniques: []
            });
        };

        const removeRegionInspector = (row: RegionInspectorList, index: number) => {
            if (row.id) {
                const inspectorIndex = regionInspectorList.value.findIndex((item) => item.id === row.id);
                if (inspectorIndex !== -1) {
                    regionInspectorList.value[inspectorIndex].delFlag = 1;
                    regionInspectorList.value.splice(inspectorIndex, 1);
                }
            } else {
                regionInspectorList.value.splice(index, 1);
            }
        };

        // 获取详情
        const getDetail = () => {
            GET_REGION_DETAIL({ id: props.configData.regionId || props.configData.id }).then((res) => {
                if (res.data.code === 0) {
                    const data = res.data.data;
                    // 处理基本信息 - 从ironRegion对象中获取
                    const ironRegion = data.ironRegion || data;
                    formData.value = {
                        regionName: ironRegion.regionName || "",
                        operBy: ironRegion.operBy || "",
                        operTime: ironRegion.operTime || "",
                        remark: ironRegion.remark || ""
                    };
                    // 处理设备列表
                    equipmentList.value =
                        data.equipments?.map((item: any) => ({
                            equipmentId: item.equipmentId ? String(item.equipmentId) : undefined, // 确保是string类型
                            equipmentName: item.equipmentName,
                            techniqueNames: item.techniqueNames || "",
                            lineName: item.lineName || ""
                        })) || [];
                    // 处理质检员列表
                    regionInspectorList.value =
                        data.regionInspectors?.map((item: any) => ({
                            inspector: item.inspector || "",
                            delFlag: item.delFlag || 0,
                            techniqueIds: item.techniques?.map((tech: any) => String(tech.techniqueId)) || [], // 支持多个工艺ID
                            techniques:
                                item.techniques?.map((tech: any) => ({
                                    id: String(tech.id),
                                    techniqueId: String(tech.techniqueId),
                                    delFlag: tech.delFlag || 0
                                })) || [], // 保存完整的techniques数据
                            ...(item.id && { id: String(item.id) }) // 确保是string类型
                        })) || [];
                }
            });
        };

        watchEffect(async () => {
            if (show.value) {
                await getFormOptions();
                if (props.configData.regionId || props.configData.id) {
                    getDetail();
                }
            }
        });

        const onClose = () => {
            changeModalShow(false);
            emit("refresh");
        };

        const onSubmit = async () => {
            const validateError = await formRef.value?.validate((errors) => !!errors);
            if (validateError) return false;

            // 构造提交数据
            const submitData = {
                ironRegion: {
                    regionName: formData.value.regionName,
                    operBy: formData.value.operBy,
                    operTime: formData.value.operTime,
                    remark: formData.value.remark,
                    delFlag: 0,
                    // 编辑时需要传递regionId
                    ...(props.configData.regionId || props.configData.id
                        ? {
                              regionId: String(props.configData.regionId || props.configData.id)
                          }
                        : {})
                },
                equipments: equipmentList.value
                    .filter(
                        (item) => item.equipmentId !== undefined && item.equipmentId !== null && item.equipmentId !== ""
                    )
                    .map((item) => ({
                        equipmentId: String(item.equipmentId) // 确保是string类型，只提交equipmentId
                    })),
                regionInspectors: regionInspectorList.value
                    .filter((item) => item.inspector && item.inspector.trim() !== "")
                    .map((inspector) => {
                        // 构建技术数据
                        const techniques: any[] = [];

                        if (inspector.techniqueIds && inspector.techniqueIds.length > 0) {
                            inspector.techniqueIds.forEach((techniqueId: string) => {
                                // 查找是否存在原有的technique记录
                                const existingTechnique = inspector.techniques?.find(
                                    (tech: any) => String(tech.techniqueId) === String(techniqueId)
                                );

                                techniques.push({
                                    techniqueId: String(techniqueId),
                                    delFlag: 0,
                                    id: existingTechnique?.id || null // 编辑时保留原有technique的id，新增时为null
                                });
                            });
                        }

                        // 对于已删除的工艺，标记为删除状态
                        if (inspector.techniques && inspector.techniques.length > 0) {
                            inspector.techniques.forEach((tech: any) => {
                                const stillSelected = inspector.techniqueIds?.includes(String(tech.techniqueId));
                                if (!stillSelected) {
                                    techniques.push({
                                        techniqueId: String(tech.techniqueId),
                                        delFlag: 1, // 标记为删除
                                        id: tech.id
                                    });
                                }
                            });
                        }

                        return {
                            inspector: inspector.inspector,
                            delFlag: inspector.delFlag || 0,
                            techniques: techniques,
                            ...(inspector.id && { id: String(inspector.id) }) // 确保是string类型
                        };
                    })
            };

            if (props.configData.regionId || props.configData.id) {
                await UPDATE_REGION({
                    id: props.configData.regionId || props.configData.id,
                    ...submitData
                }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("编辑成功");
                        onClose();
                    }
                });
            } else {
                await ADD_REGION(submitData).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("新增成功");
                        onClose();
                    }
                });
            }
        };

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card
                    title={props.configData.regionId || props.configData.id ? "编辑区域" : "新增区域"}
                    class="w-1200px"
                    closable
                    onClose={() => changeModalShow(false)}
                >
                    <n-form
                        ref={formRef}
                        model={formData.value}
                        rules={formRules.value}
                        label-placement="left"
                        label-width="auto"
                    >
                        {/* 基本信息 */}
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi span={6} label="操作人" path="operBy" required>
                                <UserSelector
                                    v-model:value={formData.value.operBy}
                                    class="w-100%"
                                    key-name="username"
                                    placeholder="请选择操作人"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="操作时间" path="operTime">
                                <n-date-picker
                                    v-model:formatted-value={formData.value.operTime}
                                    class="w-100%"
                                    clearable
                                    placeholder="请选择操作时间"
                                    type="datetime"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="区域名称" path="regionName">
                                <n-input
                                    v-model:value={formData.value.regionName}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入区域名称"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="备注" path="remark">
                                <n-input
                                    v-model:value={formData.value.remark}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入备注"
                                />
                            </n-form-item-gi>
                        </n-grid>

                        {/* Tab切换区域 */}
                        <n-tabs v-model:value={activeTab.value} type="line" class="mt-4" animated>
                            <n-tab-pane name="equipment" tab="设备配置">
                                <div class="w-100%">
                                    <n-space class="mb">
                                        <n-button type="success" onClick={addEquipment}>
                                            + 添加设备
                                        </n-button>
                                    </n-space>
                                    <n-data-table
                                        columns={equipmentTableColumns.value}
                                        data={equipmentList.value}
                                        single-line={false}
                                        bordered
                                        striped
                                    />
                                </div>
                            </n-tab-pane>
                            <n-tab-pane name="inspector" tab="质检员配置">
                                <div class="w-100%">
                                    <n-space class="mb">
                                        <n-button type="success" onClick={addRegionInspector}>
                                            + 添加质检员
                                        </n-button>
                                    </n-space>
                                    <n-data-table
                                        columns={inspectorTableColumns.value}
                                        data={regionInspectorList.value}
                                        single-line={false}
                                        bordered
                                        striped
                                    />
                                </div>
                            </n-tab-pane>
                        </n-tabs>

                        <n-form-item class="mt-4">
                            <n-space>
                                <n-button type="primary" onClick={onSubmit}>
                                    提交
                                </n-button>
                                <n-button onClick={() => changeModalShow(false)}>取消</n-button>
                            </n-space>
                        </n-form-item>
                    </n-form>
                </n-card>
            </n-modal>
        );
    }
});
