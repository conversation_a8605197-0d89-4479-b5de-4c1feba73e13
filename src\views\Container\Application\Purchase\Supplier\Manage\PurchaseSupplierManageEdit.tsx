import { computed, defineComponent, ref, watchEffect, h } from "vue";
import { type FormInst, NInput, NButton, NSwitch, NSelect } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { useDicts } from "@/hooks";
import { UserSelector } from "@/components/UserSelector";
import { useStoreUser } from "@/store";
import dayjs from "dayjs";
import {
    ADD_SUPPLIERS_MANAGEMENT,
    EDIT_SUPPLIERS_MANAGEMENT,
    GET_SUPPLIERS_MANAGEMENT_DETAIL,
    GET_SUPPLY_CATEGORY_MANAGEMENT_LIST
} from "@/api/application/purchase";
import { FileValueUploader } from "@/components/Uploader";

interface SupContactsList {
    contactName: string;
    contactPhone: string;
    contactPosition: string;
}

interface SupDeliveryInfoList {
    deliveryAddress: string;
    receiverContact: string;
    receiverName: string;
    remarks: string;
}

interface SupInvoiceInfoList {
    bankAccountNumber: string;
    bankOfDeposit: string;
    contactPhone: string;
    invoiceTitle: string;
    isDefault: number;
    registeredAddress: string;
    taxpayerIdentificationNumber: string;
}

interface SupQualificationAttachmentsList {
    attachmentId: string;
    attachmentName: string;
    attachmentType: string;
}

interface SupSuppliers {
    businessLicenseCode: string;
    contactAddress: string;
    operationTime: string;
    operator: string;
    remark: string;
    supplierName: string;
    supplierType: number;
    supplyCategory: any[];
}

interface FormDataProps {
    supContactsList: SupContactsList[];
    supDeliveryInfoList: SupDeliveryInfoList[];
    supInvoiceInfoList: SupInvoiceInfoList[];
    supQualificationAttachmentsList: SupQualificationAttachmentsList[];
    supSuppliers: SupSuppliers;
}

export default defineComponent({
    name: "PurchaseSupplierManageEdit",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        const storeUser = useStoreUser();

        // 字典操作
        const { dictLibs, getDictLibs } = useDicts();

        const setDictLibs = async () => {
            let dictName = ["attachment_type"];
            await getDictLibs(dictName);
        };

        const formRef = ref<FormInst | null>(null);

        const getFormOptions = async () => {
            const res = await GET_SUPPLY_CATEGORY_MANAGEMENT_LIST({ current: 1, size: 999, techState: 0 });
            if (res.data.code === 0) {
                supplyCategoryOptions.value = (res.data.data.records || []).map((item: any) => ({
                    label: item.categoryName,
                    value: String(item.id)
                }));
            }
        };

        const supplyCategoryOptions = ref<{ label: string; value: string }[]>([]);

        const initFormData: FormDataProps = {
            supContactsList: [],
            supDeliveryInfoList: [],
            supInvoiceInfoList: [],
            supQualificationAttachmentsList: [],
            supSuppliers: {
                operator: storeUser.getUserData.sysUser?.username ?? "",
                operationTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
                supplierName: "",
                supplierType: 1,
                businessLicenseCode: "",
                remark: "",
                contactAddress: "",
                supplyCategory: []
            }
        };

        const formRules = computed(() => ({
            "supSuppliers.operationTime": [{ required: false, message: "请选择操作时间", trigger: ["blur", "change"] }],
            "supSuppliers.supplierName": [{ required: true, message: "请输入供应商名称", trigger: ["input", "blur"] }],
            "supSuppliers.supplierType": [
                { required: true, message: "请选择供应商类型", trigger: ["blur", "change"], type: "number" }
            ],
            "supSuppliers.businessLicenseCode": [
                {
                    required: true,
                    message: formData.value.supSuppliers.supplierType === 1 ? "请输入营业执照编码" : "请输入身份证号码",
                    trigger: ["input", "blur"]
                }
            ],
            "supSuppliers.remark": [{ required: true, message: "请输入备注", trigger: ["input", "blur"] }],
            "supSuppliers.contactAddress": [{ required: true, message: "请输入联系地址", trigger: ["input", "blur"] }],
            "supSuppliers.supplyCategory": [
                { required: true, message: "请选择供货类别", trigger: ["blur", "change"], type: "array" }
            ]
        }));

        const formData = ref(cloneDeep(initFormData));

        const clearForm = () => {
            formData.value = cloneDeep(initFormData);
        };

        // 获取详情
        const getDetail = () => {
            GET_SUPPLIERS_MANAGEMENT_DETAIL({ id: props.configData.id }).then((res) => {
                if (res.data.code === 0) {
                    formData.value = {
                        supContactsList: res.data.data?.supContactsList || [],
                        supDeliveryInfoList: res.data.data?.supDeliveryInfoList || [],
                        supInvoiceInfoList: res.data.data?.supInvoiceInfoList || [],
                        supQualificationAttachmentsList: res.data.data?.supQualificationAttachmentsList || [],
                        supSuppliers: {
                            operationTime: res.data.data?.supSuppliers?.operationTime,
                            operator: res.data.data?.supSuppliers?.operator,
                            supplierName: res.data.data?.supSuppliers?.supplierName,
                            supplierType: res.data.data?.supSuppliers?.supplierType,
                            businessLicenseCode: res.data.data?.supSuppliers?.businessLicenseCode,
                            remark: res.data.data?.supSuppliers?.remark,
                            contactAddress: res.data.data?.supSuppliers?.contactAddress,
                            supplyCategory: res.data.data?.supSuppliers?.supplyCategory
                                ? res.data.data.supSuppliers.supplyCategory.split(",")
                                : []
                        }
                    };
                }
            });
        };

        watchEffect(async () => {
            if (show.value) {
                await setDictLibs();
                await getFormOptions();
                if (props.configData.id) {
                    getDetail();
                }
            }
        });

        const onClose = () => {
            clearForm();
            changeModalShow(false);
            emit("refresh");
        };

        const onSubmit = async () => {
            let validateError = await formRef.value?.validate((errors) => !!errors);
            if (validateError) return false;

            const submitData = cloneDeep(formData.value);
            if (Array.isArray(submitData.supSuppliers.supplyCategory)) {
                submitData.supSuppliers.supplyCategory = submitData.supSuppliers.supplyCategory.join(",");
            }

            if (props.configData.id) {
                submitData.supSuppliers.id = props.configData.id;
                await EDIT_SUPPLIERS_MANAGEMENT({
                    id: props.configData.id,
                    ...submitData
                }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("编辑成功");
                        onClose();
                    }
                });
            } else {
                await ADD_SUPPLIERS_MANAGEMENT({
                    ...submitData
                }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("新增成功");
                        onClose();
                    }
                });
            }
        };

        const showUploader = ref(false);
        // 临时存储上传的文件ID和文件名
        const tempUpload = ref<{ ids: string[]; names: string[] }>({ ids: [], names: [] });
        // 处理上传回调
        const handleUploaderValue = (ids: string) => {
            tempUpload.value.ids = ids ? ids.split(",") : [];
        };
        const handleUploaderNames = (names: string[]) => {
            tempUpload.value.names = names;
        };
        const handleUploaderOk = () => {
            tempUpload.value.ids.forEach((id, idx) => {
                formData.value.supQualificationAttachmentsList.push({
                    attachmentId: id,
                    attachmentName: tempUpload.value.names[idx] || "",
                    attachmentType: null
                });
            });
            showUploader.value = false;
            tempUpload.value = { ids: [], names: [] };
        };

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card
                    title={props.configData.id ? "维护供应商信息" : "新增供应商信息"}
                    class="w-1200px"
                    closable
                    onClose={onClose}
                >
                    <n-tabs animated type="bar" defaultValue={1}>
                        <n-tab-pane name={1} tab="供应商基础信息">
                            <n-form
                                ref={formRef}
                                model={formData.value}
                                rules={formRules.value}
                                label-placement="left"
                                label-width="auto"
                            >
                                <n-grid cols={12} x-gap={16}>
                                    <n-form-item-gi span={6} label="供应商名称" path="supSuppliers.supplierName">
                                        <n-input
                                            v-model:value={formData.value.supSuppliers.supplierName}
                                            class="w-100%"
                                            clearable
                                            placeholder="请输入供应商名称"
                                        />
                                    </n-form-item-gi>
                                    <n-form-item-gi span={6} label="供应商类型" path="supSuppliers.supplierType">
                                        <n-radio-group v-model:value={formData.value.supSuppliers.supplierType}>
                                            <n-space>
                                                <n-radio value={1} label="企业" />
                                                <n-radio value={2} label="个人" />
                                            </n-space>
                                        </n-radio-group>
                                    </n-form-item-gi>
                                    <n-form-item-gi
                                        span={6}
                                        label={
                                            formData.value.supSuppliers.supplierType === 1
                                                ? "营业执照编码"
                                                : "身份证号码"
                                        }
                                        path="supSuppliers.businessLicenseCode"
                                    >
                                        <n-input
                                            v-model:value={formData.value.supSuppliers.businessLicenseCode}
                                            class="w-100%"
                                            clearable
                                            placeholder={
                                                formData.value.supSuppliers.supplierType === 1
                                                    ? "请输入营业执照编码"
                                                    : "请输入身份证号码"
                                            }
                                        />
                                    </n-form-item-gi>
                                    <n-form-item-gi span={6} label="供货类别" path="supSuppliers.supplyCategory">
                                        <n-select
                                            v-model:value={formData.value.supSuppliers.supplyCategory}
                                            class="w-100%"
                                            clearable
                                            multiple
                                            placeholder="请选择供货类别"
                                            options={supplyCategoryOptions.value}
                                        />
                                    </n-form-item-gi>
                                    <n-form-item-gi span={6} label="联系地址" path="supSuppliers.contactAddress">
                                        <n-input
                                            v-model:value={formData.value.supSuppliers.contactAddress}
                                            class="w-100%"
                                            clearable
                                            placeholder="请输入联系地址"
                                        />
                                    </n-form-item-gi>
                                    <n-form-item-gi span={6} label="备注" path="supSuppliers.remark">
                                        <n-input
                                            v-model:value={formData.value.supSuppliers.remark}
                                            class="w-100%"
                                            clearable
                                            placeholder="请输入备注"
                                        />
                                    </n-form-item-gi>
                                    <n-form-item-gi span={6} label="操作人" path="supSuppliers.operator" required>
                                        <UserSelector
                                            v-model:value={formData.value.supSuppliers.operator}
                                            class="w-100%"
                                            disabled
                                            key-name="username"
                                            placeholder="请选择操作人"
                                        />
                                    </n-form-item-gi>
                                    <n-form-item-gi span={6} label="操作时间" path="supSuppliers.operationTime">
                                        <n-date-picker
                                            v-model:formatted-value={formData.value.supSuppliers.operationTime}
                                            className="w-100%"
                                            clearable
                                            placeholder="请选择操作时间"
                                            type="datetime"
                                            value-format="yyyy-MM-dd HH:mm:ss"
                                        />
                                    </n-form-item-gi>
                                </n-grid>
                            </n-form>
                        </n-tab-pane>
                        <n-tab-pane name={2} tab="供应商联系人信息">
                            <n-space class="mb-4">
                                <n-button
                                    type="primary"
                                    onClick={() =>
                                        formData.value.supContactsList.push({
                                            contactName: "",
                                            contactPhone: "",
                                            contactPosition: ""
                                        })
                                    }
                                >
                                    添加联系人
                                </n-button>
                            </n-space>
                            <n-data-table
                                columns={[
                                    {
                                        title: "联系人姓名",
                                        key: "contactName",
                                        align: "center",
                                        render: (row: SupContactsList, index: number) => {
                                            return h(NInput, {
                                                value: row.contactName,
                                                onUpdateValue: (v) =>
                                                    (formData.value.supContactsList[index].contactName = v)
                                            });
                                        }
                                    },
                                    {
                                        title: "联系电话",
                                        key: "contactPhone",
                                        align: "center",
                                        render: (row: SupContactsList, index: number) => {
                                            return h(NInput, {
                                                value: row.contactPhone,
                                                onUpdateValue: (v) =>
                                                    (formData.value.supContactsList[index].contactPhone = v)
                                            });
                                        }
                                    },
                                    {
                                        title: "联系人职位",
                                        key: "contactPosition",
                                        align: "center",
                                        render: (row: SupContactsList, index: number) => {
                                            return h(NInput, {
                                                value: row.contactPosition,
                                                onUpdateValue: (v) =>
                                                    (formData.value.supContactsList[index].contactPosition = v)
                                            });
                                        }
                                    },
                                    {
                                        title: "操作",
                                        key: "actions",
                                        align: "center",
                                        render: (row: SupContactsList, index: number) => {
                                            return h(
                                                NButton,
                                                {
                                                    type: "error",
                                                    text: true,
                                                    onClick: () => formData.value.supContactsList.splice(index, 1)
                                                },
                                                { default: () => "删除" }
                                            );
                                        }
                                    }
                                ]}
                                data={formData.value.supContactsList}
                                single-line={false}
                                striped
                                bordered
                            />
                        </n-tab-pane>
                        <n-tab-pane name={3} tab="供应商邮寄信息">
                            <n-space class="mb-4">
                                <n-button
                                    type="primary"
                                    onClick={() =>
                                        formData.value.supDeliveryInfoList.push({
                                            receiverName: "",
                                            receiverContact: "",
                                            deliveryAddress: "",
                                            remarks: ""
                                        })
                                    }
                                >
                                    添加邮寄信息
                                </n-button>
                            </n-space>
                            <n-data-table
                                columns={[
                                    {
                                        title: "收件人姓名",
                                        key: "receiverName",
                                        align: "center",
                                        render: (row: SupDeliveryInfoList, index: number) => {
                                            return h(NInput, {
                                                value: row.receiverName,
                                                onUpdateValue: (v) =>
                                                    (formData.value.supDeliveryInfoList[index].receiverName = v)
                                            });
                                        }
                                    },
                                    {
                                        title: "收件人联系方式",
                                        key: "receiverContact",
                                        align: "center",
                                        render: (row: SupDeliveryInfoList, index: number) => {
                                            return h(NInput, {
                                                value: row.receiverContact,
                                                onUpdateValue: (v) =>
                                                    (formData.value.supDeliveryInfoList[index].receiverContact = v)
                                            });
                                        }
                                    },
                                    {
                                        title: "收件地址",
                                        key: "deliveryAddress",
                                        align: "center",
                                        render: (row: SupDeliveryInfoList, index: number) => {
                                            return h(NInput, {
                                                value: row.deliveryAddress,
                                                onUpdateValue: (v) =>
                                                    (formData.value.supDeliveryInfoList[index].deliveryAddress = v)
                                            });
                                        }
                                    },
                                    {
                                        title: "备注信息",
                                        key: "remarks",
                                        align: "center",
                                        render: (row: SupDeliveryInfoList, index: number) => {
                                            return h(NInput, {
                                                value: row.remarks,
                                                onUpdateValue: (v) =>
                                                    (formData.value.supDeliveryInfoList[index].remarks = v)
                                            });
                                        }
                                    },
                                    {
                                        title: "操作",
                                        key: "actions",
                                        align: "center",
                                        render: (row: SupDeliveryInfoList, index: number) => {
                                            return h(
                                                NButton,
                                                {
                                                    type: "error",
                                                    text: true,
                                                    onClick: () => formData.value.supDeliveryInfoList.splice(index, 1)
                                                },
                                                { default: () => "删除" }
                                            );
                                        }
                                    }
                                ]}
                                data={formData.value.supDeliveryInfoList}
                                single-line={false}
                                striped
                                bordered
                            />
                        </n-tab-pane>
                        <n-tab-pane name={4} tab="供应商资质附件">
                            <n-space class="mb-4">
                                <n-button type="primary" onClick={() => (showUploader.value = true)}>
                                    添加资质附件
                                </n-button>
                            </n-space>
                            <n-modal
                                v-model:show={showUploader.value}
                                title="上传资质附件"
                                preset="dialog"
                                class="w-500px"
                            >
                                <FileValueUploader
                                    multiple
                                    value-key="fileId"
                                    onUpdate:value={handleUploaderValue}
                                    onUpdate:fileNames={handleUploaderNames}
                                />
                                <n-space class="mt">
                                    <n-button type="primary" onClick={handleUploaderOk}>
                                        确定添加
                                    </n-button>
                                    <n-button
                                        onClick={() => {
                                            showUploader.value = false;
                                            tempUpload.value = { ids: [], names: [] };
                                        }}
                                    >
                                        取消
                                    </n-button>
                                </n-space>
                            </n-modal>
                            <n-data-table
                                columns={[
                                    {
                                        title: "附件ID",
                                        key: "attachmentId",
                                        align: "center"
                                    },
                                    {
                                        title: "附件名称",
                                        key: "attachmentName",
                                        align: "center"
                                    },
                                    {
                                        title: "附件类型",
                                        key: "attachmentType",
                                        align: "center",
                                        render: (row: SupQualificationAttachmentsList, index: number) => {
                                            return h(NSelect as any, {
                                                value: row.attachmentType,
                                                options: dictLibs['attachment_type'] ?? [],
                                                placeholder: "请选择附件类型",
                                                clearable: true,
                                                onUpdateValue: (v: string) =>
                                                    (formData.value.supQualificationAttachmentsList[
                                                        index
                                                    ].attachmentType = v)
                                            });
                                        }
                                    },
                                    {
                                        title: "操作",
                                        key: "actions",
                                        align: "center",
                                        render: (row: SupQualificationAttachmentsList, index: number) => {
                                            return h(
                                                NButton,
                                                {
                                                    type: "error",
                                                    text: true,
                                                    onClick: () =>
                                                        formData.value.supQualificationAttachmentsList.splice(index, 1)
                                                },
                                                { default: () => "删除" }
                                            );
                                        }
                                    }
                                ]}
                                data={formData.value.supQualificationAttachmentsList}
                                single-line={false}
                                striped
                                bordered
                            />
                        </n-tab-pane>
                        <n-tab-pane name={5} tab="供应商开票信息">
                            <n-space class="mb-4">
                                <n-button
                                    type="primary"
                                    onClick={() =>
                                        formData.value.supInvoiceInfoList.push({
                                            invoiceTitle: "",
                                            taxpayerIdentificationNumber: "",
                                            bankOfDeposit: "",
                                            bankAccountNumber: "",
                                            contactPhone: "",
                                            registeredAddress: "",
                                            isDefault: 0
                                        })
                                    }
                                >
                                    添加开票信息
                                </n-button>
                            </n-space>
                            <n-data-table
                                columns={[
                                    {
                                        title: "发票抬头",
                                        key: "invoiceTitle",
                                        align: "center",
                                        render: (row: SupInvoiceInfoList, index: number) => {
                                            return h(NInput, {
                                                value: row.invoiceTitle,
                                                onUpdateValue: (v) =>
                                                    (formData.value.supInvoiceInfoList[index].invoiceTitle = v)
                                            });
                                        }
                                    },
                                    {
                                        title: "纳税人识别号",
                                        key: "taxpayerIdentificationNumber",
                                        align: "center",
                                        render: (row: SupInvoiceInfoList, index: number) => {
                                            return h(NInput, {
                                                value: row.taxpayerIdentificationNumber,
                                                onUpdateValue: (v) =>
                                                    (formData.value.supInvoiceInfoList[
                                                        index
                                                    ].taxpayerIdentificationNumber = v)
                                            });
                                        }
                                    },
                                    {
                                        title: "开户银行",
                                        key: "bankOfDeposit",
                                        align: "center",
                                        render: (row: SupInvoiceInfoList, index: number) => {
                                            return h(NInput, {
                                                value: row.bankOfDeposit,
                                                onUpdateValue: (v) =>
                                                    (formData.value.supInvoiceInfoList[index].bankOfDeposit = v)
                                            });
                                        }
                                    },
                                    {
                                        title: "银行账号",
                                        key: "bankAccountNumber",
                                        align: "center",
                                        render: (row: SupInvoiceInfoList, index: number) => {
                                            return h(NInput, {
                                                value: row.bankAccountNumber,
                                                onUpdateValue: (v) =>
                                                    (formData.value.supInvoiceInfoList[index].bankAccountNumber = v)
                                            });
                                        }
                                    },
                                    {
                                        title: "联系电话",
                                        key: "contactPhone",
                                        align: "center",
                                        render: (row: SupInvoiceInfoList, index: number) => {
                                            return h(NInput, {
                                                value: row.contactPhone,
                                                onUpdateValue: (v) =>
                                                    (formData.value.supInvoiceInfoList[index].contactPhone = v)
                                            });
                                        }
                                    },
                                    {
                                        title: "注册地址",
                                        key: "registeredAddress",
                                        align: "center",
                                        render: (row: SupInvoiceInfoList, index: number) => {
                                            return h(NInput, {
                                                value: row.registeredAddress,
                                                onUpdateValue: (v) =>
                                                    (formData.value.supInvoiceInfoList[index].registeredAddress = v)
                                            });
                                        }
                                    },
                                    {
                                        title: "默认开票信息",
                                        key: "isDefault",
                                        align: "center",
                                        width: 150,
                                        render: (row: SupInvoiceInfoList, index: number) => {
                                            return h(
                                                NSwitch,
                                                {
                                                    value: row.isDefault === 1,
                                                    onUpdateValue: (v) => {
                                                        if (v) {
                                                            formData.value.supInvoiceInfoList.forEach((item: SupInvoiceInfoList, idx: number) => {
                                                                if (idx !== index) {
                                                                    item.isDefault = 0;
                                                                }
                                                            });
                                                        }
                                                        formData.value.supInvoiceInfoList[index].isDefault = v ? 1 : 0;
                                                    }
                                                },
                                                { default: () => (row.isDefault === 1 ? "是" : "否") }
                                            );
                                        }
                                    },
                                    {
                                        title: "操作",
                                        key: "actions",
                                        align: "center",
                                        render: (row: SupInvoiceInfoList, index: number) => {
                                            return h(
                                                NButton,
                                                {
                                                    type: "error",
                                                    text: true,
                                                    onClick: () => formData.value.supInvoiceInfoList.splice(index, 1)
                                                },
                                                { default: () => "删除" }
                                            );
                                        }
                                    }
                                ]}
                                data={formData.value.supInvoiceInfoList}
                                single-line={false}
                                striped
                                bordered
                            />
                        </n-tab-pane>
                    </n-tabs>
                    <n-space class="mt-4">
                        <n-button type="primary" onClick={onSubmit}>
                            提交
                        </n-button>
                        <n-button onClick={onClose}>取消</n-button>
                    </n-space>
                </n-card>
            </n-modal>
        );
    }
});
