import { defineComponent, onMounted, reactive, ref } from "vue";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import PurchaseSupplierManageDetail from "../Manage/PurchaseSupplierManageDetail";
import { DELETE_SUPPLIERS_MANAGEMENT, GET_SUPPLIERS_BLACKLIST_LIST, RESUME_REVIEW } from "@/api/application/purchase";
import { TableActions } from "@/components/TableActions";

export default defineComponent({
    name: "PurchaseSupplierBlackList",
    setup(props) {
        // 搜索项
        const searchConfig = ref<TableSearchbarConfig>([
            { prop: "supplierName", type: "input", label: "供应商名称", labelWidth: "100" },
            { prop: "supplierType", type: "select", label: "供应商类型", labelWidth: "100" }
        ]);
        const searchOptions = ref<TableSearchbarOptions>({
            supplierType: [
                { label: "企业", value: 1 },
                { label: "个人", value: 2 }
            ]
        });
        const getSearchOptions = async () => {};
        const searchForm = ref<TableSearchbarData>({
            supplierName: null,
            supplierType: null,
            supplierStatus: null
        });
        const onSearch = () => {
            tablePagination.page = 1;
            tablePagination.pageSize = 10;
            getTableData();
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            { title: "供应商名称", key: "supplierName", align: "center", render: (row) => row.supplierName ?? "/" },
            {
                title: "供应商类型",
                key: "supplierType",
                align: "center",
                render: (row) => {
                    switch (row.supplierType) {
                        case 1:
                            return <n-text type="info">企业</n-text>;
                        case 2:
                            return <n-text type="info">个人</n-text>;
                        default:
                            return <n-text type="info">/</n-text>;
                    }
                }
            },
            {
                title: "拉黑时间",
                key: "updateTime",
                align: "center"
            },
            {
                title: "拉黑操作人",
                key: "updateBy",
                align: "center"
            },
            { title: "备注", key: "remark", align: "center", render: (row) => row.remark ?? "/" },
            {
                title: "操作",
                key: "actions",
                align: "center",
                width: 300,
                render: (row) => {
                    return (
                        <TableActions
                            type="button"
                            buttonActions={[
                                {
                                    label: "恢复评审",
                                    tertiary: true,
                                    type: "success",
                                    onClick: () => onRestore(row)
                                },
                                {
                                    label: "查看详情",
                                    type: "primary",
                                    tertiary: true,
                                    onClick: () => openDetailModal(row)
                                },
                                {
                                    label: "删除供应商",
                                    tertiary: true,
                                    type: "error",
                                    onClick: () => onDelete(row)
                                }
                            ]}
                        />
                    );
                }
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_SUPPLIERS_BLACKLIST_LIST({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                techState: 0,
                ...searchForm.value
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        // 详情弹窗
        const detailModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const openDetailModal = (row: RowProps) => {
            detailModal.value = { show: true, configData: { id: row.id } };
        };

        // 删除
        const onDelete = (row: RowProps) => {
            window.$dialog.warning({
                title: "警告",
                content: "确认删除该条数据？该操作不可逆",
                positiveText: "确认删除",
                negativeText: "我再想想",
                onPositiveClick: () => {
                    DELETE_SUPPLIERS_MANAGEMENT({ id: row.id }).then((res) => {
                        if (res.data.code === 0) {
                            window.$message.success("删除成功");
                            onSearch();
                        }
                    });
                }
            });
        };

        // 恢复评审
        const onRestore = (row: RowProps) => {
            window.$dialog.warning({
                title: "警告",
                content: "确认恢复该条数据？",
                positiveText: "确认恢复",
                negativeText: "我再想想",
                onPositiveClick: () => {
                    RESUME_REVIEW({ id: row.id }).then((res) => {
                        if (res.data.code === 0) {
                            window.$message.success("操作成功");
                            onSearch();
                        }
                    });
                }
            });
        };

        onMounted(async () => {
            await getSearchOptions();
            getTableData();
        });

        return () => (
            <div class="plastic-mes-repair-inside-list">
                <n-card>
                    <TableSearchbar
                        form={searchForm.value}
                        config={searchConfig.value}
                        options={searchOptions.value}
                        onSearch={onSearch}
                    />
                </n-card>
                <n-card class="mt">
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        pagination={tablePagination}
                        row-key={tableRowKey}
                        single-line={false}
                        bordered
                        remote
                        striped
                        onUpdate:checked-row-keys={changeTableSelection}
                    />
                </n-card>
                <PurchaseSupplierManageDetail
                    v-model:show={detailModal.value.show}
                    configData={detailModal.value.configData}
                />
            </div>
        );
    }
});
