import { computed, defineComponent, h, ref, watchEffect, reactive } from "vue";
import { DataTableColumns, type FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { useDicts } from "@/hooks";
import { UserSelector } from "@/components/UserSelector";
import {
    GET_DEPT_TREE,
    GET_DINGDING_SYNCHRONIZED_RECORD_DETAILS,
    GET_DINGDING_SYNCHRONIZED_RECORD_DETAILS_EXPORT,
    GET_DINGDING_SYNCHRONIZED_RECORD_DETAILS_ITEM_LIST,
    GET_STAFF_BY_USERNAMES,
    SYNC_DINGDING_USER
} from "@/api/permission";
import dayjs from "dayjs";
import { useStoreDesign, useStoreUser } from "@/store";
import { DynamicIcon } from "@/components/DynamicIcon";
import { TableActions } from "@/components/TableActions";

export default defineComponent({
    name: "AttendanceSyncUpdate",
    props: {
        show: { type: <PERSON><PERSON>an, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "update:configData", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        const storeUser = useStoreUser();
        const storeDesign = useStoreDesign();

        // 表单数据
        interface FormDataProps {
            [key: string]: any;
        }

        // 字典操作
        const { dictLibs, getDictLibs } = useDicts();

        const setDictLibs = async () => {
            const dictName = ["common_units"];
            await getDictLibs(dictName);
        };

        // 表单实例
        const formRef = ref<FormInst | null>(null);

        // 获取表单选项
        const deptIdOptions = ref<any[]>([]);

        const getFormOptions = async () => {
            await GET_DEPT_TREE({ deptName: "" }).then((res) => {
                deptIdOptions.value = res.data.data ?? [];
            });
        };

        const initFormData: FormDataProps = {
            operator: null,
            operationTime: null,
            recordType: "1",
            deptId: null
        };

        const formRules = computed(() => ({
            operator: [{ required: true, message: "请选择操作人", trigger: ["blur", "change"] }],
            operationTime: [{ required: true, message: "请选择操作时间", trigger: ["blur", "change"] }],
            recordType: [{ required: true, message: "请选择同步类型", trigger: ["blur", "change"] }],
            deptId: [{ required: true, message: "请选择同步公司/部门", trigger: ["blur", "change"], type: "array" }]
        }));

        const formData = ref(cloneDeep(initFormData));

        const clearForm = () => {
            formData.value = cloneDeep(initFormData);
        };

        // 表单数据
        interface RowProps {
            [key: string]: any;
        }

        const tableData = ref<RowProps[]>([]);

        const tableColumns = ref<DataTableColumns<RowProps>>([
            {
                title: "姓名",
                key: "username",
                align: "center",
                render: (row) => (
                    <UserSelector
                        v-model:value={row.username}
                        class="w-100%"
                        multiple={false}
                        key-name="username"
                        placeholder="请选择用户"
                        onUpdateValue={async (value: string) => {
                            if (value) {
                                try {
                                    const res = await GET_STAFF_BY_USERNAMES({ usernames: value });
                                    const users = res.data.data[0] ?? [];
                                    row.phone = users.phone ?? "/";
                                } catch (err) {
                                    row.phone = "/";
                                } finally {
                                }
                            }
                        }}
                    />
                )
            },
            {
                title: "手机号",
                key: "phone",
                align: "center",
                render: (row) => row.phone ?? "/"
            },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 80,
                render(row, index) {
                    return h(TableActions, {
                        type: "button",
                        buttonActions: [
                            {
                                label: "删除",
                                tertiary: true,
                                type: "error",
                                onClick: () => deleteItem(row, index)
                            }
                        ]
                    });
                }
            }
        ]);

        // 可编辑表单配置
        const tableItem: RowProps = {
            username: null,
            phone: null
        };

        const addTableItem = () => {
            tableData.value.push(cloneDeep(tableItem));
        };

        const deleteItem = (row: RowProps, index: number) => {
            if (row.id) {
                tableData.value.forEach((citem, cindex) => {
                    if (row.id === citem.id) {
                        citem.delFlag = 1;
                    }
                });
            } else {
                tableData.value.splice(index, 1);
            }
        };

        // 获取详情
        const progress = ref(0);

        /*
         * 查询同步人员信息
         * 0同步中 1成功 2失败
         */
        const tabActive = ref(0);

        const tabList = ref([
            { label: "同步中", value: "0" },
            { label: "成功", value: "1" },
            { label: "失败", value: "2" }
        ]);

        const itemList = ref<any[]>([]);
        const tableLoading = ref(false);
        const tablePagination = reactive({
            page: 1,
            pageSize: 10,
            itemCount: 0,
            onChange: (page: number) => {
                tablePagination.page = page;
                getItemList();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getItemList();
            }
        });

        const getItemList = async () => {
            tableLoading.value = true;
            await GET_DINGDING_SYNCHRONIZED_RECORD_DETAILS_ITEM_LIST({
                id: props.configData.id,
                itemStatus: tabActive.value,
                current: tablePagination.page,
                size: tablePagination.pageSize
            }).then((res) => {
                if (res.data.code === 0) {
                    itemList.value = res.data.data.records ?? [];
                    tablePagination.itemCount = res.data.data.total ?? 0;
                }
                tableLoading.value = false;
            });
        };

        const getDetail = async () => {
            await GET_DINGDING_SYNCHRONIZED_RECORD_DETAILS({ id: props.configData.id }).then((res) => {
                if (res.data.code === 0) {
                    formData.value = {
                        operator: res.data.data.operator,
                        operationTime: res.data.data.operationTime,
                        recordType: String(res.data.data.recordType)
                    };
                    progress.value = res.data.data.successPercentage ?? 0;
                }
            });
            await getItemList();
        };

        // 导出
        const onExport = () => {
            window.$dialog.warning({
                title: "提示",
                content: "确定导出吗？",
                positiveText: "确定",
                negativeText: "取消",
                onPositiveClick: () => {
                    GET_DINGDING_SYNCHRONIZED_RECORD_DETAILS_EXPORT({
                        id: props.configData.id,
                        operator: formData.value.operator,
                        operationTime: formData.value.operationTime
                    }).then((res) => {
                        const blob = new Blob([res.data]);
                        const a = document.createElement("a");
                        a.href = URL.createObjectURL(blob);
                        a.download = "导出信息.xlsx";
                        a.style.display = "none";
                        document.body.appendChild(a);
                        a.click();
                        a.remove();
                    });
                }
            });
        };

        // 关闭
        const onClose = () => {
            clearForm();
            changeModalShow(false);
            emit("refresh");
        };

        // 提交
        const onSubmit = async () => {
            const validateError = await formRef.value?.validate((errors) => !!errors);
            if (validateError) return false;

            if (formData.value.recordType === "3") {
                const validData = (tableData.value ?? []).filter((item) => item.delFlag !== 1);
                if (validData.length <= 0) {
                    return window.$message.error("请选择需要同步的用户");
                }
                if (validData.some((item) => !item.phone || item.phone === "/")) {
                    return window.$message.error("存在未获取到手机号的用户，请重新选择用户");
                }
            }

            const phones = (tableData.value ?? [])
                .filter((item) => item.delFlag !== 1)
                .map((item) => item.phone)
                .join(",");

            SYNC_DINGDING_USER({
                ...formData.value,
                deptId: (formData.value.deptId ?? []).join(","),
                phones: phones ?? null
            }).then(async (res) => {
                if (res.data.code === 0) {
                    window.$message.success("操作成功");
                    if (res.data.data) {
                        setTimeout(async () => {
                            await emit("update:configData", { id: res.data.data });
                            await getDetail();
                        }, 2000);
                    } else {
                        onClose();
                    }
                }
            });
        };

        watchEffect(async () => {
            if (show.value) {
                await getFormOptions();
                formData.value.operator = storeUser.getUserData.sysUser?.username;
                formData.value.operationTime = dayjs().format("YYYY-MM-DD HH:mm:ss");

                if (props.configData.id) {
                    await getDetail();
                }
            }
        });

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card title="同步钉钉用户信息" class={["w-800px"]} closable onClose={onClose}>
                    <n-form
                        ref={formRef}
                        model={formData.value}
                        rules={formRules.value}
                        label-placement="left"
                        label-width={120}
                    >
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi span={6} label="操作人" path="operator">
                                <UserSelector
                                    v-model:value={formData.value.operator}
                                    class="w-100%"
                                    multiple={false}
                                    key-name="username"
                                    placeholder="请选择操作人"
                                    disabled
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="操作时间" path="operationTime">
                                <n-date-picker
                                    v-model:formatted-value={formData.value.operationTime}
                                    className="w-100%"
                                    clearable
                                    placeholder="请选择操作时间"
                                    type="datetime"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    disabled
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={12} label="同步类型" path="recordType">
                                <n-radio-group
                                    v-model:value={formData.value.recordType}
                                    disabled={!!props.configData.id}
                                >
                                    <n-space>
                                        <n-radio label="全量覆盖" value={"1"} />
                                        <n-radio label="按组织同步" value={"2"} />
                                        <n-radio label="按用户同步" value={"3"} />
                                    </n-space>
                                </n-radio-group>
                            </n-form-item-gi>
                            {!props.configData.id && (
                                <>
                                    {formData.value.recordType === "2" && (
                                        <n-form-item-gi span={12} label="同步公司/部门" path="deptId">
                                            <n-tree-select
                                                class="w-100%"
                                                v-model:value={formData.value.deptId}
                                                options={deptIdOptions.value}
                                                clearable
                                                filterable
                                                multiple
                                                key-field="id"
                                                label-field="name"
                                                placeholder="请选择同步公司/部门"
                                            />
                                        </n-form-item-gi>
                                    )}
                                    {formData.value.recordType === "3" && (
                                        <n-form-item-gi span={12}>
                                            <div class="w-100%">
                                                <n-button
                                                    size="small"
                                                    type="primary"
                                                    v-slots={{
                                                        icon: () => <DynamicIcon icon="PlusCircleOutlined" />
                                                    }}
                                                    onClick={() => addTableItem()}
                                                >
                                                    选择用户
                                                </n-button>
                                                <div class="mt">
                                                    <n-data-table
                                                        columns={tableColumns.value}
                                                        data={(tableData.value || []).filter(
                                                            (item) => item.delFlag !== 1
                                                        )}
                                                        single-line={false}
                                                        bordered
                                                        striped
                                                    />
                                                </div>
                                            </div>
                                        </n-form-item-gi>
                                    )}
                                    <n-form-item-gi span={12}>
                                        <n-space>
                                            <n-button onClick={() => onClose()}>取消操作</n-button>
                                            <n-button type="primary" onClick={() => onSubmit()}>
                                                提交同步
                                            </n-button>
                                        </n-space>
                                    </n-form-item-gi>
                                </>
                            )}
                            {props.configData.id && (
                                <n-form-item-gi span={12}>
                                    <div class="flex w-100%">
                                        <div>
                                            <div class="text-20px mb">同步进度</div>
                                            <el-progress
                                                type="circle"
                                                width={300}
                                                percentage={progress.value}
                                                stroke-width={20}
                                                stroke-linecap="square"
                                                color={storeDesign.themeColor}
                                            >
                                                <span class="text-40px">{progress.value.toFixed(2)}%</span>
                                            </el-progress>
                                        </div>
                                        <div class="flex-1 ml-8">
                                            <div class="text-20px mb">同步结果</div>
                                            <div>
                                                <n-tabs
                                                    v-model:value={tabActive.value}
                                                    animated
                                                    type="bar"
                                                    onUpdate:value={() => getItemList()}
                                                    class="mb"
                                                >
                                                    {tabList.value.map((item, index) => (
                                                        <n-tab-pane
                                                            name={item.value}
                                                            tab={item.label}
                                                            key={index}
                                                            class="p-0!"
                                                        />
                                                    ))}
                                                </n-tabs>
                                                <n-data-table
                                                    columns={[
                                                        { title: "姓名", key: "trueName", align: "center" },
                                                        { title: "手机号", key: "phone", align: "center" }
                                                    ]}
                                                    data={itemList.value}
                                                    single-line={false}
                                                    bordered
                                                    max-height={150}
                                                    striped
                                                    loading={tableLoading.value}
                                                    pagination={tablePagination}
                                                    remote
                                                />
                                                <div class="w-100% mt flex-center">
                                                    <n-button type="primary" onClick={() => onExport()}>
                                                        导出数据
                                                    </n-button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </n-form-item-gi>
                            )}
                        </n-grid>
                    </n-form>
                </n-card>
            </n-modal>
        );
    }
});
