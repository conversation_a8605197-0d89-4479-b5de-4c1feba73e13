<template>
    <naive-provider>
        <router-view />
    </naive-provider>
</template>

<script lang="ts" setup>
import { NaiveProvider } from "@/components/System";
import { onMounted } from "vue";
import { useStoreGlobal, useStoreUser } from "@/store";

let storeUser = useStoreUser();
let storeGlobal = useStoreGlobal();

onMounted(async () => {
    if (storeUser.getToken) {
        await storeUser.requestUserData();
    }
    await storeGlobal.setGlobalConfig();
});
</script>
