import { computed, defineComponent, onMounted, ref } from "vue";
import type { DataTableColumns } from "naive-ui";
import { useDicts } from "@/hooks";

export default defineComponent({
    name: "PlasticMesIMProductionTasksSpecList",
    props: {
        tableData: { type: Array as PropType<UnKnownObject[]>, default: () => [] }
    },
    emits: ["update:tableData"],
    setup(props, { emit, expose }) {
        // 字典操作
        const { dictLibs, getDictLibs, dictValueToLabel } = useDicts();

        const setDictLibs = async () => {
            const dictName = ["plastic_product_type"];
            await getDictLibs(dictName);
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const tableData = computed({ get: () => props.tableData, set: (v) => emit("update:tableData", v) });

        const tableColumns = ref<DataTableColumns<RowProps>>([
            {
                title: "注塑产品规格",
                key: "moldingSpec",
                align: "center",
                render: (row: RowProps) => row.moldingSpec || "/"
            },
            {
                title: "注塑产品分类",
                key: "moldingVarietyName",
                align: "center",
                render: (row: RowProps) => row.moldingVarietyName || "/"
            },
            {
                title: "需求数量",
                key: "needQuantity",
                align: "center",
                render: (row: RowProps) => row.needQuantity || "/"
            },
            {
                title: "完成数量",
                key: "finishQuantity",
                align: "center",
                render: (row: RowProps) => row.finishQuantity || "/"
            },
            {
                title: "入库数量",
                key: "storageQuantity",
                align: "center",
                render: (row: RowProps) => row.storageQuantity || "/"
            },
            {
                title: "生产单位",
                key: "productionUnitName",
                align: "center",
                render: (row: RowProps) => row.productionUnitName || "/"
            }
        ]);

        onMounted(async () => {
            await setDictLibs();
        });

        return () => (
            <n-data-table columns={tableColumns.value} data={tableData.value} single-line={false} bordered striped />
        );
    }
});
