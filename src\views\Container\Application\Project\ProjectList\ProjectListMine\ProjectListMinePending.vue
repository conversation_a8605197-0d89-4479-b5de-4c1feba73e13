<template>
    <div class="menu-list">
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <project-edit-modal v-model:id="editModal.id" v-model:show="editModal.show" @refresh="getTableData" />
        <project-detail-modal v-model:id="detailModal.id" v-model:show="detailModal.show" @refresh="getTableData" />
        <project-approval
            v-model:show="projectApprovalModal.show"
            :config-data="projectApprovalModal.configData"
            @refresh="getTableData"
        />
        <!--上传标书-->
        <ProjectBidUploadMail
            v-model:show="bidUploadModal.show"
            :config-data="bidUploadModal.configData"
            @refresh="getTableData"
        />
        <!--中标相关费用填写-->
        <ProjectIsWinBid
            v-model:show="isWinBidModal.show"
            :config-data="isWinBidModal.configData"
            @refresh="getTableData"
        />
        <!--中标相关费用填写-->
        <ProjectWinBidFee
            v-model:show="winBidFeeModal.show"
            :config-data="winBidFeeModal.configData"
            @refresh="getTableData"
        />
        <!--绑定框架合同-->
        <ProjectBindFrameContract
            v-model:show="bindFrameContractModal.show"
            :config-data="bindFrameContractModal.configData"
            @refresh="getTableData"
        />
        <!--绑定订单合同（二级市场合同签订）-->
        <ProjectBindOrderContract
            v-model:show="bindOrderContractModal.show"
            :config-data="bindOrderContractModal.configData"
            @refresh="getTableData"
        />
        <!--匹配第一个合同-->
        <ProjectMatchFirstOrder
            v-model:show="matchFirstOrderModal.show"
            :config-data="matchFirstOrderModal.configData"
            @refresh="getTableData"
        />
        <!--未中标原因-->
        <ProjectFailWinBidReason
            v-model:show="failWinBidReasonModal.show"
            :config-data="failWinBidReasonModal.configData"
            @refresh="getTableData"
        />
        <!--项目完成-->
        <ProjectFinish
            v-model:show="projectFinishModal.show"
            :config-data="projectFinishModal.configData"
            @refresh="getTableData"
        />
        <!--标准化产品合同上传-->
        <ProjectContractUpload
            v-model:show="contractUploadModal.show"
            :config-data="contractUploadModal.configData"
            @refresh="getTableData"
        />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, ref } from "vue";
import type { DataTableColumns, PaginationProps } from "naive-ui";
import { NText } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { TableActions } from "@/components/TableActions";
import { useCommonTable, useDicts } from "@/hooks";
import {
    CLOSE_PROJECT,
    GET_PRE_HANDLE_PROJECT_LIST,
    SUBMIT_FINAL_PAYMENTTRACK_CHILD_FORM,
    SUBMIT_FINAL_PAYMENTTRACK_FORM,
    SUBMIT_PROJECT_FINISH_FORM_LOSS_BID,
    SET_NEXT_NODE_DIRECTOR
} from "@/api/application/project";
import ProjectEditModal from "../ProjectEditModal.vue";
import ProjectDetailModal from "../ProjectDetailModal.vue";
import ProjectApproval from "../../ProjectApproval/ProjectApproval.vue";
import {
    ProjectBidUploadMail,
    ProjectBindFrameContract,
    ProjectBindOrderContract,
    ProjectContractUpload,
    ProjectFailWinBidReason,
    ProjectFinish,
    ProjectIsWinBid,
    ProjectMatchFirstOrder,
    ProjectWinBidFee
} from "../components";
import { UserSelector } from "@/components/UserSelector";
import { useStoreUser } from "@/store";

let storeUser = useStoreUser();

interface RowProps {
    [propName: string]: any;
}

onMounted(async () => {
    await setDictLibs();
    getSearchOptions();
    getTableData();
});

let { dictLibs, getDictLibs, dictValueToLabel } = useDicts();

let setDictLibs = async () => {
    let dictName = ["node_status", "win_bind_status", "project_type", "project_status"];
    await getDictLibs(dictName);
};

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { prop: "projectType", type: "select", label: "项目类型" },
    { prop: "projectStatus", type: "select", label: "项目状态" },
    { prop: "projectName", type: "input", label: "关键词" }
]);

let searchOptions = ref<TableSearchbarOptions>({
    projectType: [],
    projectStatus: []
});

let searchForm = ref<TableSearchbarData>({
    projectType: null,
    projectStatus: null,
    projectName: null
});

let getSearchOptions = () => {
    searchOptions.value.projectType = dictLibs.project_type;
    searchOptions.value.projectStatus = dictLibs.project_status;
};

// 下一节点负责人
let nodeDirector = ref<any>(null);

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, changeTableSelection } =
    useCommonTable<RowProps>("projectId");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "项目编号",
        key: "projectId",
        align: "center"
    },
    {
        title: "项目名称",
        key: "projectName",
        align: "center",
        render: (row: RowProps) => h(NText, { type: "primary" }, () => row.projectName || "暂无")
    },
    {
        title: "招投标项目编号",
        key: "projectCode",
        align: "center",
        render: (row: RowProps) => row.projectCode || "暂无"
    },
    {
        title: "前置节点",
        key: "nodeName",
        align: "center"
    },
    {
        title: "节点负责人",
        key: "nodeDirectorName",
        align: "center",
        render: (row: RowProps) => h(NText, { type: "primary" }, () => row.nodeDirectorName || "暂无")
    },
    {
        title: "当前流程进度",
        key: "nodeStatus",
        align: "center",
        render: (row: RowProps) => dictValueToLabel(row.nodeStatus, "node_status") || "未知"
    },
    {
        title: "项目类型",
        key: "projectType",
        align: "center",
        render: (row: RowProps) => dictValueToLabel(row.projectType, "project_type") || "未知"
    },
    {
        title: "项目状态",
        key: "projectStatus",
        align: "center",
        width: 100,
        render: (row: RowProps) => dictValueToLabel(row.projectStatus, "project_status") || "未知"
    },
    {
        title: "待处理节点",
        key: "nextNodeName",
        align: "center",
        render: (row: RowProps) => {
            let projectStatusName = dictValueToLabel(row.projectStatus, "project_status");
            if (projectStatusName === "已完结" || projectStatusName === "已关闭" || projectStatusName === "已结束") {
                return h(NText, { type: "error" }, () => `项目${projectStatusName}`);
            }
            if (row.nextNodeName) {
                return h(NText, { text: true, type: "primary" }, () => row.nextNodeName);
            }
        }
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: 260,
        render: (row: RowProps) => {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "查看",
                        tertiary: true,
                        type: "success",
                        onClick: () => openDetailModal(row.projectId)
                    },
                    {
                        label: "立即处理",
                        tertiary: true,
                        type: "primary",
                        disabled: () => {
                            return storeUser.userData.sysUser?.username !== row.nextNodeDirector;
                        },
                        onClick: () => {
                            if (row.projectStatus === 4) return window.$message.error("项目已关闭，无法进行操作");

                            if (row.nodeFlowType === 2) {
                                if (row.nextNodeKey === "FinalPaymentTrack") {
                                    window.$dialog.warning({
                                        title: "确认信息",
                                        content: "是否需要发起尾款追踪流程？",
                                        positiveText: "需要",
                                        negativeText: "不需要",
                                        onPositiveClick: () => {
                                            childFinalPay(row.projectId, row.childProjectId, row.nextNodeKey);
                                        }
                                    });
                                } else {
                                    openProjectApprovalModal(row);
                                }
                            } else {
                                if (row.nextNodeKey === "BidUpload") {
                                    openBidUploadModal(row);
                                } else if (row.nextNodeKey === "IsWinBid") {
                                    if (!row.customerId) {
                                        return window.$dialog.warning({
                                            title: "提示",
                                            content: "请先补全客户信息",
                                            positiveText: "去补全",
                                            negativeText: "取消",
                                            onPositiveClick: () => openEditModal(row.projectId)
                                        });
                                    }
                                    openIsWinBidModal(row);
                                } else if (row.nextNodeKey === "FillWinBidCost") {
                                    openWinBidFeeModal(row);
                                } else if (row.nextNodeKey === "SignContract") {
                                    openBindFrameContract(row);
                                } else if (row.nextNodeKey === "ContractSign") {
                                    openBindOrderContract(row);
                                } else if (row.nextNodeKey === "MatchFirstContractOrder") {
                                    openMatchFirstOrderModal(row);
                                } else if (row.nextNodeKey === "FailWinBidReason") {
                                    openFailWinBidReasonModal(row);
                                } else if (row.nextNodeKey === "FinalPaymentTrack") {
                                    window.$dialog.warning({
                                        title: "确认信息",
                                        content: "是否需要发起尾款追踪流程？",
                                        positiveText: "需要",
                                        negativeText: "不需要",
                                        onPositiveClick: () => finalPay(row.projectId, row.nextNodeKey)
                                    });
                                } else if (row.nextNodeKey === "ProjectEnd") {
                                    // 未中标项目完结
                                    window.$dialog.warning({
                                        title: "确认信息",
                                        content: "是否结束该未中标项目？",
                                        positiveText: "结束",
                                        negativeText: "取消",
                                        onPositiveClick: () => {
                                            SUBMIT_PROJECT_FINISH_FORM_LOSS_BID({
                                                projectId: row.projectId,
                                                nodeKey: row.nextNodeKey
                                            }).then(() => {
                                                window.$message.success("操作成功");
                                                getTableData();
                                            });
                                        }
                                    });
                                } else if (row.nextNodeKey === "ProjectFinish") {
                                    // 正常项目完结
                                    openProjectFinishModal(row);
                                } else if (row.nextNodeKey === "ContractUpload") {
                                    // 标准化产品合同上传
                                    openContractUploadModal(row);
                                } else {
                                    openProjectApprovalModal(row);
                                }
                            }
                        }
                    },
                    {
                        label: "立即推送",
                        tertiary: true,
                        type: "warning",
                        disabled: () => {
                            return (
                                storeUser.userData.sysUser?.username !== row.nodeDirector ||
                                row.nodeStatus !== 2 ||
                                !!row.nextNodeDirector ||
                                row.projectStatus === 3
                            );
                        },
                        onClick: () => {
                            nodeDirector.value = row.nextNodeDirector;
                            window.$dialog.warning({
                                title: "下个节点负责人",
                                content: () => {
                                    return h("div", { class: "py" }, [
                                        h(UserSelector, {
                                            value: nodeDirector.value,
                                            multiple: false,
                                            placeholder: "请选择下个节点负责人",
                                            keyName: "username",
                                            onUpdateValue: (value: string) => {
                                                nodeDirector.value = value;
                                            }
                                        })
                                    ]);
                                },
                                positiveText: "提交",
                                negativeText: "取消",
                                onPositiveClick: () => {
                                    if (!nodeDirector.value) return window.$message.error("请选择下个节点负责人");
                                    SET_NEXT_NODE_DIRECTOR({
                                        projectId: row.projectId,
                                        childProjectId: row.childProjectId,
                                        nodeKey: row.nextNodeKey,
                                        nodeDirector: nodeDirector.value
                                    }).then((res) => {
                                        if (res.data.code === 0) {
                                            window.$message.success("操作成功");
                                            getTableData();
                                        }
                                    });
                                },
                                onNegativeClick: () => {
                                    nodeDirector.value = null;
                                }
                            });
                        }
                    }
                ]
            });
        }
    }
]);

let tablePagination = ref<PaginationProps>({
    page: 1,
    pageSize: 10,
    itemCount: 0,
    pageSizes: [10, 50, 100],
    showSizePicker: true,
    showQuickJumper: true,
    displayOrder: ["size-picker", "pages", "quick-jumper"],
    onChange: (page: number) => {
        tablePagination.value.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.value.pageSize = pageSize;
        tablePagination.value.page = 1;
        getTableData();
    }
});

// 主项目尾款提交
let finalPay = (id: string | number | null, nodeKey: string | number | null) => {
    SUBMIT_FINAL_PAYMENTTRACK_FORM({ projectId: id, nodeKey: nodeKey }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("尾款信息已提交");
            getTableData();
        }
    });
};

// 子项目尾款提交
let childFinalPay = (id: string | number | null, cid: string | number | null, nodeKey: string | number | null) => {
    SUBMIT_FINAL_PAYMENTTRACK_CHILD_FORM({ projectId: id, childProjectId: cid, nodeKey: nodeKey }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("尾款信息已提交");
            getTableData();
        }
    });
};

let getTableData = () => {
    tableLoading.value = true;
    GET_PRE_HANDLE_PROJECT_LIST({
        current: tablePagination.value.page,
        size: tablePagination.value.pageSize,
        ...searchForm.value
    }).then((res) => {
        tableData.value = res.data.data.records || [];
        tablePagination.value.itemCount = res.data.data.total;
        tableLoading.value = false;
    });
};

let onSearch = () => {
    getTableData();
};

// 查看详情弹窗
let detailModal = ref<{ show: boolean; id: string | number | null }>({
    show: false,
    id: null
});

let openDetailModal = (id?: string | number | null) => {
    detailModal.value.show = true;
    detailModal.value.id = id || null;
};

// 新增编辑
let editModal = ref<{ show: boolean; id: string | number | null }>({
    show: false,
    id: null
});

let openEditModal = (id?: string | number | null) => {
    editModal.value.show = true;
    editModal.value.id = id || null;
};

// 关闭项目
let closeProject = (id?: string | number | null) => {
    window.$dialog.warning({
        title: "警告",
        content: `确定关闭该项目吗？`,
        positiveText: "确认",
        negativeText: "取消",
        onPositiveClick: () => {
            CLOSE_PROJECT({ projectId: id }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("关闭成功");
                    getTableData();
                }
            });
        }
    });
};

// 下一步流程
let projectApprovalModal = ref<any>({ show: false, configData: {} });

let openProjectApprovalModal = (row: any) => {
    projectApprovalModal.value.show = true;
    projectApprovalModal.value.configData = row;
};

// 上传标书
let bidUploadModal = ref<any>({ show: false, configData: {} });

let openBidUploadModal = (row: any) => {
    bidUploadModal.value.show = true;
    bidUploadModal.value.configData = row;
};

// 是否中标
let isWinBidModal = ref<any>({ show: false, configData: {} });

let openIsWinBidModal = (row: any) => {
    isWinBidModal.value.show = true;
    isWinBidModal.value.configData = row;
};

// 中标费用
let winBidFeeModal = ref<any>({ show: false, configData: {} });

let openWinBidFeeModal = (row: any) => {
    winBidFeeModal.value.show = true;
    winBidFeeModal.value.configData = row;
};

// 绑定框架合同
let bindFrameContractModal = ref<any>({ show: false, configData: {} });

let openBindFrameContract = (row: any) => {
    bindFrameContractModal.value.show = true;
    bindFrameContractModal.value.configData = row;
};

// 绑定订单合同（合同签订）
let bindOrderContractModal = ref<any>({ show: false, configData: {} });

let openBindOrderContract = (row: any) => {
    bindOrderContractModal.value.show = true;
    bindOrderContractModal.value.configData = row;
};

// 绑定订单合同
let matchFirstOrderModal = ref<any>({ show: false, configData: {} });

let openMatchFirstOrderModal = (row: any) => {
    matchFirstOrderModal.value.show = true;
    matchFirstOrderModal.value.configData = row;
};

// 未中标原因
let failWinBidReasonModal = ref<any>({ show: false, configData: {} });

let openFailWinBidReasonModal = (row: any) => {
    failWinBidReasonModal.value.show = true;
    failWinBidReasonModal.value.configData = row;
};

// 结束项目
let projectFinishModal = ref<any>({ show: false, configData: {} });

let openProjectFinishModal = (row: any) => {
    projectFinishModal.value.show = true;
    projectFinishModal.value.configData = row;
};

// 标准化产品合同上传2024年3月1日15:34:58
let contractUploadModal = ref<any>({ show: false, configData: {} });

let openContractUploadModal = (row: any) => {
    contractUploadModal.value.show = true;
    contractUploadModal.value.configData = row;
};
</script>
