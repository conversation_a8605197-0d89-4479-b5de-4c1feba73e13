<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-800px" closable title="绑定录入合同" @close="closeModal">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-grid :cols="24" :x-gap="16">
                        <n-form-item-gi :span="12" label="合同类型" path="type">
                            <n-radio-group v-model:value="formData.type" name="type" @update:value="changeType">
                                <n-radio :value="0" label="框架合同" />
                                <n-radio :value="1" label="订单合同" />
                            </n-radio-group>
                        </n-form-item-gi>
                        <n-form-item-gi v-if="formData.type === 0" :span="12" label="框架合同" path="contractId">
                            <n-select
                                v-model:value="formData.contractId"
                                :options="contractIdOptions"
                                label-field="pfcName"
                                placeholder="请选择框架合同"
                                value-field="pfcId"
                            />
                        </n-form-item-gi>
                        <template v-else-if="formData.type === 1">
                            <n-form-item-gi :span="12" label="订单合同" path="contractId">
                                <n-select
                                    v-model:value="formData.contractId"
                                    :options="orderContractIdOptions"
                                    label-field="pomName"
                                    placeholder="请选择订单合同"
                                    value-field="pomId"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi :span="12" label="负责人" path="nodeDirector">
                                <UserSelector
                                    v-model:value="formData.nodeDirector"
                                    :multiple="false"
                                    class="w-100%"
                                    clearable
                                    key-name="username"
                                    placeholder="请选择负责人"
                                />
                            </n-form-item-gi>
                        </template>
                        <n-form-item-gi :span="24">
                            <n-space>
                                <n-button type="primary" @click="onSubmit">提交</n-button>
                                <n-button @click="closeModal">取消</n-button>
                            </n-space>
                        </n-form-item-gi>
                    </n-grid>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import {
    GET_FRAME_CONTRACT_LIST,
    GET_ORDER_CONTRACT_LIST,
    POST_BIND_CONTRACT_FORM,
    POST_BIND_ORDER_CONTRACT
} from "@/api/application/power";
import { UserSelector } from "@/components/UserSelector";

let props = defineProps({
    show: { type: Boolean, default: false },
    configData: { type: Object as PropType<any> }
});

let emits = defineEmits(["update:show", "refresh"]);

// 表单实例
let formRef = ref<FormInst | null>(null);

// 获取选项
let contractIdOptions = ref<any[]>([]);

// 获取选项
let orderContractIdOptions = ref<any[]>([]);

let getOptions = () => {
    GET_FRAME_CONTRACT_LIST({ current: 1, size: 9999, pfcReviewState: 3 }).then((res) => {
        contractIdOptions.value = res.data.data.records;
    });
    GET_ORDER_CONTRACT_LIST({
        // pfcId: props.configData.contractId,
        current: 1,
        size: 9999,
        pomReviewState: 3
    }).then((res) => {
        orderContractIdOptions.value = res.data.data.records;
    });
};

// 表单校验
let formRules = {
    contractId: { required: true, message: "请选择合同", trigger: ["blur", "change"] },
    nodeDirector: { required: true, message: "请选择负责人", trigger: ["blur", "change"] }
};

// 表单数据
interface FormDataProps {
    [key: string]: any;
}

let initFormData: FormDataProps = {
    type: 0,
    contractId: null,
    nodeDirector: null
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 监听
watch(
    () => ({ configData: props.configData, show: props.show }),
    (newVal) => {
        if (newVal.show) getOptions();
    },
    { deep: true }
);

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    emits("update:show", false);
};

let changeType = () => {
    formData.value.contractId = null;
};

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    if (formData.value.type === 0) {
        POST_BIND_CONTRACT_FORM({
            projectId: props.configData.projectId,
            nodeKey: props.configData.nextNodeKey,
            contractId: formData.value.contractId,
            contractType: 1
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("提交成功");
                closeModal();
                emits("refresh");
            }
        });
    } else if (formData.value.type === 1) {
        POST_BIND_ORDER_CONTRACT({
            projectId: props.configData.projectId,
            nodeKey: props.configData.nextNodeKey,
            contractId: formData.value.contractId,
            nodeDirector: formData.value.nodeDirector
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("提交成功");
                closeModal();
                emits("refresh");
            }
        });
    }
};
</script>
