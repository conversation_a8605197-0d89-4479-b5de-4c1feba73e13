import { computed, defineComponent, ref, watchEffect } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { GET_CARD_SUPPLEMENT_RULE, SAVE_CARD_SUPPLEMENT_RULE } from "@/api/permission";

export default defineComponent({
    name: "AttendanceRuleCardReplacement",
    props: {
        corpId: { type: String as PropType<string | null>, default: null }
    },
    setup(props, { emit }) {
        // 表单数据
        interface FormDataProps {
            [key: string]: any;
        }

        const formRef = ref<FormInst | null>(null);

        const getFormOptions = async () => {};

        const initFormData: FormDataProps = {
            id: null,
            attendanceRuleId: null,
            signCount: null,
            signPeriod: null,
            signOa: null
        };

        const formRules = computed(() => ({
            signCount: [{ required: true, message: "请输入允许补卡次数", trigger: ["input", "blur"], type: "number" }],
            signPeriod: [{ required: true, message: "请选择允许补卡周期", trigger: ["blur", "change"] }],
            signOa: [{ required: true, message: "请输入补卡OA标识", trigger: ["input", "blur"] }]
        }));

        const formData = ref(cloneDeep(initFormData));

        const clearForm = () => {
            formData.value = cloneDeep(initFormData);
        };

        const getDetail = () => {
            GET_CARD_SUPPLEMENT_RULE({
                corpId: props.corpId
            }).then((res) => {
                formData.value = {
                    id: res.data.data?.id ?? null,
                    attendanceRuleId: res.data.data?.attendanceRuleId ?? null,
                    signCount: res.data.data?.signCount ?? null,
                    signPeriod: res.data.data?.signPeriod ?? null,
                    signOa: res.data.data?.signOa ?? null
                };
            });
        };

        // 提交
        const onSubmit = async () => {
            const validateError = await formRef.value?.validate((errors) => !!errors);
            if (validateError) return false;

            SAVE_CARD_SUPPLEMENT_RULE({
                ...formData.value,
                corpId: props.corpId
            }).then(async (res) => {
                if (res.data.code === 0) {
                    window.$message.success("操作成功");
                    getDetail();
                } else {
                    window.$message.error(res.data.msg);
                }
            });
        };

        watchEffect(() => {
            if (props.corpId) {
                getDetail();
            }
        });

        return () => (
            <div class="max-w-900px pt">
                <n-form
                    ref={formRef}
                    model={formData.value}
                    rules={formRules.value}
                    label-placement="left"
                    label-width="auto"
                >
                    <n-grid cols={12} x-gap={16}>
                        <n-form-item-gi span={6} label="允许补卡次数" path="signCount">
                            <n-input-number
                                v-model:value={formData.value.signCount}
                                class="w-100%"
                                clearable
                                placeholder="请输入允许补卡次数"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi span={6} label="允许补卡周期" path="signPeriod">
                            <n-select
                                v-model:value={formData.value.signPeriod}
                                options={[
                                    { label: "年", value: "年" },
                                    { label: "月", value: "月" },
                                    { label: "日", value: "日" }
                                ]}
                                clearable
                                filterable
                                placeholder="请选择允许补卡周期"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi span={6} label="补卡OA标识" path="signOa">
                            <n-input
                                v-model:value={formData.value.signOa}
                                class="w-100%"
                                clearable
                                placeholder="请输入补卡OA标识"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi span={12}>
                            <n-space>
                                <n-button type="primary" onClick={() => onSubmit()}>
                                    保存配置
                                </n-button>
                            </n-space>
                        </n-form-item-gi>
                    </n-grid>
                </n-form>
            </div>
        );
    }
});
