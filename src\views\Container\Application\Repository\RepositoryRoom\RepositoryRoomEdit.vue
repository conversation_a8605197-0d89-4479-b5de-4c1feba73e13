<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card :title="configData.id ? '编辑库房' : '新增库房'" class="w-1000px" closable @close="closeModal">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-grid :cols="12" :x-gap="16">
                        <n-form-item-gi :span="6" label="所属库点" path="pointId">
                            <n-select
                                v-model:value="formData.pointId"
                                :options="pointIdOptions"
                                placeholder="请选择所属库点"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="库房名称" path="storeroomName">
                            <n-input
                                v-model:value="formData.storeroomName"
                                class="w-100%"
                                clearable
                                placeholder="请输入库房名称"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="库房编号" path="storeroomCode">
                            <n-input
                                v-model:value="formData.storeroomCode"
                                class="w-100%"
                                clearable
                                placeholder="请输入库房编号"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="是否启用" path="lockFlag">
                            <n-radio-group v-model:value="formData.lockFlag">
                                <n-space>
                                    <n-radio :value="0">启用</n-radio>
                                    <n-radio :value="1">停用</n-radio>
                                </n-space>
                            </n-radio-group>
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="负责人" path="storeroomManager">
                            <UserSelector
                                v-model:value="formData.storeroomManager"
                                :multiple="false"
                                class="w-100%"
                                key-name="username"
                                placeholder="请输入负责人"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12">
                            <n-space>
                                <n-button type="primary" @click="onSubmit">提交</n-button>
                                <n-button @click="closeModal">取消</n-button>
                            </n-space>
                        </n-form-item-gi>
                    </n-grid>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref, watchEffect } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import {
    ADD_REPOSITORY_STOREROOM,
    GET_REPOSITORY_POINT_LIST,
    GET_REPOSITORY_STOREROOM_DETAIL,
    UPDATE_REPOSITORY_STOREROOM
} from "@/api/application/repository";
import { UserSelector } from "@/components/UserSelector";

let props = withDefaults(
    defineProps<{
        show: boolean;
        configData: UnKnownObject;
    }>(),
    {
        show: () => false
    }
);

let emits = defineEmits(["update:show", "refresh"]);

onMounted(() => {
    getOptions();
});

// 获取选项
let pointIdOptions = ref<{}[]>([]);

let getOptions = async () => {
    await GET_REPOSITORY_POINT_LIST({
        current: 1,
        size: 9999
    }).then((res) => {
        pointIdOptions.value = (res.data.data.records || []).map((item: { pointName: string; id: string }) => {
            return {
                label: item.pointName,
                value: item.id
            };
        });
    });
};

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = computed(() => {
    return {
        pointId: { required: true, message: "请选择所属库点", trigger: ["blur", "change"] },
        storeroomName: { required: true, message: "请输入库房名称", trigger: ["input", "blur"] },
        storeroomCode: { required: true, message: "请输入库房编号", trigger: ["input", "blur"] },
        lockFlag: { required: true, message: "请选择是否启用", trigger: ["blur", "change"], type: "number" },
        storeroomManager: { required: true, message: "请输入负责人", trigger: ["input", "blur"] }
    };
});

// 表单数据
interface FormDataProps {
    /**
     * 所属库点
     */
    pointId: Nullable<string>;
    /**
     * 状态（1：禁用；0：启用）
     */
    lockFlag: number;
    /**
     * 库房编号
     */
    storeroomCode: string;
    /**
     * 负责人
     */
    storeroomManager: string;
    /**
     * 库房名称
     */
    storeroomName: string;
}

let initFormData: FormDataProps = {
    pointId: null,
    lockFlag: 0,
    storeroomCode: "",
    storeroomManager: "",
    storeroomName: ""
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 获取详情
let getDetail = () => {
    GET_REPOSITORY_STOREROOM_DETAIL({ id: props.configData.id }).then((res) => {
        if (res.data.code === 0) {
            formData.value = {
                pointId: res.data.data.pointId,
                lockFlag: res.data.data.lockFlag,
                storeroomCode: res.data.data.storeroomCode,
                storeroomManager: res.data.data.storeroomManager,
                storeroomName: res.data.data.storeroomName
            };
        }
    });
};

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    emits("update:show", false);
};

// 监听
watchEffect(() => {
    if (props.show && props.configData.id) {
        getDetail();
    }
});

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;

    if (props.configData.id) {
        await UPDATE_REPOSITORY_STOREROOM({ ...formData.value, id: props.configData.id }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("编辑成功");
                closeModal();
                emits("refresh");
            }
        });
    } else {
        await ADD_REPOSITORY_STOREROOM({ ...formData.value }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("新增成功");
                closeModal();
                emits("refresh");
            }
        });
    }
};
</script>
