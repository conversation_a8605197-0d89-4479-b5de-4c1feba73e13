<template>
    <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
        <n-card
            :title="`填写其他费用【${configData.projectNumber}】`"
            class="w-800px"
            closable
            @close="changeModalShow(false)"
        >
            <n-data-table
                :columns="tableColumns"
                :data="(tableData || []).filter((item) => item.delFlag !== 1)"
                :single-line="false"
                bordered
                remote
                striped
            />
            <div class="flex-center mt">
                <n-space>
                    <n-button type="primary" @click="onSubmit">确认提交</n-button>
                    <n-button type="success" @click="addRow">新增一行</n-button>
                    <n-button type="error" @click="resetRow">重置信息</n-button>
                    <n-button @click="changeModalShow(false)">取消</n-button>
                </n-space>
            </div>
        </n-card>
    </n-modal>
</template>

<script lang="ts" setup>
import { computed, h, ref, watchEffect } from "vue";
import { DataTableColumns } from "naive-ui";
import { NInput, NButton } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { GET_INTERMEDIARY_FEE_DETAIL, POST_SAVE_INTERMEDIARY_FEE_LIST } from "@/api/application/power";

let props = withDefaults(defineProps<{ show: boolean; configData: UnKnownObject }>(), { show: () => false });

let emits = defineEmits(["update:show", "refresh"]);

let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

let changeModalShow = (show: boolean) => {
    if (!show) clearRow();
    emits("update:show", show);
};

watchEffect(() => {
    if (props.show && props.configData.projectId) getDetail();
});

// 获取详情
let getDetail = () => {
    GET_INTERMEDIARY_FEE_DETAIL({
        projectId: props.configData.projectId
    }).then((res) => {
        let feeList = res.data.data.feeList;
        feeList.length ? (tableData.value = feeList) : "";
    });
};

// 数据列表
interface RowProps {
    [key: string]: any;
}

let tableData = ref<RowProps[]>([]);

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        title: "其他费用金额",
        key: "feeAmount",
        align: "center",
        render: (row) => {
            return h(NInput, { value: row.feeAmount, onUpdateValue: (v) => (row.feeAmount = v) });
        }
    },
    {
        title: "录入人",
        key: "createByName",
        align: "center",
        render: (row) => {
            return row.createByName || "未知";
        }
    },
    {
        title: "录入时间",
        key: "createTime",
        align: "center",
        render: (row) => {
            return row.createTime || "未知";
        }
    },
    {
        title: "备注",
        key: "feeAmount",
        align: "center",
        render: (row) => {
            return h(NInput, { value: row.remark, onUpdateValue: (v) => (row.remark = v) });
        }
    },
    {
        title: "操作",
        key: "operation",
        align: "center",
        render: (row) => {
            return h(NButton, { type: "error", onClick: () => onDelete(row) }, () => "删除");
        }
    }
]);

let initRow = {
    id: null,
    feeAmount: null,
    remark: null,
    delFlag: 0
};

let addRow = () => {
    tableData.value.push(cloneDeep(initRow));
};

let resetRow = () => {
    tableData.value = [cloneDeep(initRow)];
};

let clearRow = () => {
    tableData.value = [];
};

// 提交
let onSubmit = () => {
    POST_SAVE_INTERMEDIARY_FEE_LIST({
        projectId: props.configData.projectId,
        feeList: (tableData.value || []).map((item) => {
            return {
                ...item,
                projectId: props.configData.projectId
            };
        })
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("提交成功");
            changeModalShow(false);
            emits("refresh");
        }
    });
};

let onDelete = (row: RowProps) => {
    row.delFlag = 1;
};
</script>
