<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-95vw" closable content-style="padding:0" title="项目详情" @close="changeModalShow(false)">
                <div class="h-80vh">
                    <n-scrollbar trigger="hover">
                        <div class="p-20px pt-0">
                            <n-card class="pb-20px" hoverable>
                                <n-breadcrumb>
                                    <n-breadcrumb-item v-for="item in $route.meta.breadcrumb">
                                        {{ item }}
                                    </n-breadcrumb-item>
                                    <n-breadcrumb-item>{{ formData.projectName }}</n-breadcrumb-item>
                                </n-breadcrumb>
                                <div class="flex-y-center mt-20px">
                                    <div class="flex-1">
                                        <div class="flex-y-center">
                                            <div class="text-25px">
                                                <b>项目名称：{{ formData.projectName }}</b>
                                            </div>
                                        </div>
                                        <n-grid :cols="24" :x-gap="16" :y-gap="16" class="mt-20px">
                                            <n-grid-item :span="8" class="text-14px">
                                                项目编号：{{ formData.projectNumber }}
                                            </n-grid-item>
                                            <n-grid-item :span="8" class="text-14px color-[var(--primary-color)]">
                                                项目市场类型：
                                                {{ dictValueToLabel(formData.projectType, "project_type") }}
                                            </n-grid-item>
                                            <n-grid-item :span="8" class="text-14px">
                                                框架合同编号：{{ formData.contractNumber || "暂无合同号" }}
                                            </n-grid-item>
                                            <n-grid-item :span="8" class="text-14px">
                                                创建时间：{{ formData.createTime }}
                                            </n-grid-item>
                                            <n-grid-item :span="8" class="text-14px">
                                                <span>所属客户：</span>
                                                <span class="color-[var(--primary-color)]">
                                                    {{ formData.customer?.customerCompanyName ?? "/" }}
                                                </span>
                                            </n-grid-item>
                                            <n-grid-item :span="8" class="text-14px">
                                                项目负责人：{{ formData.projectLeaderName ?? "/" }}
                                            </n-grid-item>
                                            <n-grid-item v-if="formData.isWinBid" :span="8" class="text-14px">
                                                是否中标：
                                                <n-text v-if="formData.isWinBid === 0" type="error">未中标</n-text>
                                                <n-text v-if="formData.isWinBid === 1" type="success">已中标</n-text>
                                            </n-grid-item>
                                            <n-grid-item v-if="formData.bidUploadType" :span="8" class="text-14px">
                                                是否上传/邮寄标书：
                                                <n-text v-if="formData.bidUploadType === 1" type="success">
                                                    线下邮寄
                                                </n-text>
                                                <n-text v-if="formData.bidUploadType === 2" type="success">
                                                    线上上传
                                                </n-text>
                                            </n-grid-item>
                                            <n-grid-item :span="8" class="text-14px">
                                                项目总金额：{{ formData.contractAmount || "0" }}（万元）
                                            </n-grid-item>
                                            <n-grid-item :span="8" class="text-14px">
                                                项目发起人：{{ formData.createByName ?? "/" }}
                                            </n-grid-item>
                                        </n-grid>
                                    </div>
                                    <div class="ml-a flex-y-center flex-fixed-600">
                                        <div class="flex-1 text-center">
                                            <div class="text-14px">订单数量</div>
                                            <div class="mt-5px text-30px color-[#f60]">
                                                <b>{{ formData?.orderNum }}</b>
                                            </div>
                                        </div>
                                        <div class="flex-1 text-center">
                                            <div class="text-14px">项目当前状态</div>
                                            <div
                                                :style="`color:${
                                                    dictValueToAll(formData.projectStatus, 'project_status').color || ''
                                                }`"
                                                class="mt-5px text-30px"
                                            >
                                                <b>{{ dictValueToLabel(formData.projectStatus, "project_status") }}</b>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </n-card>
                            <ProjectDetailProcess :form-data="formData" />
                            <n-card class="w-100% mt-15px" hoverable>
                                <n-tabs pane-class="pt!" type="line">
                                    <n-tab-pane name="招标信息">
                                        <n-grid :cols="24" :x-gap="16" :y-gap="16">
                                            <n-grid-item :span="12" class="text-14px">
                                                招标人：{{ formData.tenderer ?? "/" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                招标代理机构：{{ formData.tenderAgency ?? "/" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                招投标项目编号：{{ formData.tenderProjectNumber ?? "/" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                标段/标包数量：{{ formData.numberOfBidSections ?? "/" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                所属省市：{{ formData.provinceAndCity ?? "/" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                招标信息发布时间：{{ formData.informationReleaseTime ?? "/" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                购买标书截止时间：{{ formData.bidPurchaseDeadline ?? "/" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                递交标书截止时间：{{ formData.bidSubmissionDeadline ?? "/" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                报备人：{{ formData.reportUserName ?? "/" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                报备日期：{{ formData.reportDate ?? "/" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                标书购买费用：{{ formData.bidPurchaseFee ?? "/" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                投标保证金：{{ formData.bidBond ?? "/" }}
                                            </n-grid-item>
                                            <n-grid-item :span="12" class="text-14px">
                                                投标单位：{{ formData.bidder ?? "/" }}
                                            </n-grid-item>
                                            <n-grid-item v-if="formData.tenderDocuments" :span="12" class="text-14px">
                                                招标文件：
                                                <n-button text type="primary" @click="previewFileBeta">
                                                    点击预览
                                                </n-button>
                                            </n-grid-item>
                                        </n-grid>
                                    </n-tab-pane>
                                    <n-tab-pane name="订单列表">
                                        <ProjectDetailOrderList :form-data="formData" @refresh="getDetail" />
                                    </n-tab-pane>
                                    <n-tab-pane name="节点负责人">
                                        <n-scrollbar x-scrollable>
                                            <div>
                                                <n-table :single-line="false" class="text-center">
                                                    <thead>
                                                        <tr>
                                                            <th
                                                                v-for="(item, index) in formData.nodeDirectorList || []"
                                                                :key="index"
                                                            >
                                                                {{ item.nodeName || "/" }}
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td
                                                                v-for="(item, index) in formData.nodeDirectorList || []"
                                                                :key="index"
                                                            >
                                                                {{ item.nodeDirectorName || "/" }}
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </n-table>
                                            </div>
                                        </n-scrollbar>
                                    </n-tab-pane>
                                    <n-tab-pane
                                        v-if="(formData.unBidFeedbackList || []).length > 0"
                                        name="未中标原因反馈"
                                    >
                                        <n-table :single-line="false" class="text-center">
                                            <thead>
                                                <tr>
                                                    <th>反馈人</th>
                                                    <th>反馈原因</th>
                                                    <th>反馈时间</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr v-for="item in formData.unBidFeedbackList">
                                                    <td>{{ item.dutyUserTrueName || "/" }}</td>
                                                    <td>{{ item.feedbackMessage || "/" }}</td>
                                                    <td>{{ item.createTime || "/" }}</td>
                                                </tr>
                                            </tbody>
                                        </n-table>
                                    </n-tab-pane>
                                    <n-tab-pane name="标书附件">
                                        <ProjectBidFiles :form-data="formData" @refresh="getDetail" />
                                    </n-tab-pane>
                                    <n-tab-pane v-if="formData.buyBidCosts" name="投标相关费用">
                                        <ProjectBidCost :fee="formData.buyBidCosts" />
                                    </n-tab-pane>
                                    <n-tab-pane v-if="formData.bidCosts" name="中标相关费用">
                                        <ProjectBidCost :fee="formData.bidCosts" />
                                    </n-tab-pane>
                                </n-tabs>
                            </n-card>
                        </div>
                    </n-scrollbar>
                </div>
            </n-card>
        </n-modal>
        <FilePreviewBeta v-model:show="previewBetaModal.show" :configData="previewBetaModal.configData" />
    </div>
</template>

<script lang="ts" setup>
import { computed, ref, watchEffect } from "vue";
import { GET_PROJECT_DETAIL_V2 } from "@/api/application/sale";
import { useDicts } from "@/hooks";
import { ProjectBidCost, ProjectBidFiles, ProjectDetailOrderList, ProjectDetailProcess } from "../ProjectDetailModal";
import { GET_FILES_BY_ID } from "@/api/public";
import { FilePreviewBeta } from "@/components/FilePreview";

let props = withDefaults(defineProps<{ show: boolean; configData: UnKnownObject }>(), { show: () => false });

let emits = defineEmits(["update:show", "refresh"]);

let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

let changeModalShow = (show: boolean) => emits("update:show", show);

// 字典操作
let { getDictLibs, dictValueToLabel, dictValueToAll } = useDicts();

let setDictLibs = async () => {
    let dictName = ["project_status", "node_status", "project_type"];
    await getDictLibs(dictName);
};

watchEffect(async () => {
    if (props.show) await setDictLibs();
    if (props.show && props.configData.projectId) await getDetail();
});

// 表单数据
interface FormDataProps {
    [key: string]: any;
}

let formData = ref<FormDataProps>({});

// 获取详情
let getDetail = async () => {
    await GET_PROJECT_DETAIL_V2({ id: props.configData.projectId }).then((r) => (formData.value = r.data.data));
};

// beta预览
const previewBetaModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

const previewFileBeta = async () => {
    const fileRes: any = await GET_FILES_BY_ID({ ids: formData.value.tenderDocuments });
    if (fileRes.data.data[0]?.url) {
        const url = import.meta.env.VITE_PREVIEW_URL + fileRes.data.data[0]?.url;
        console.log(url);
        previewBetaModal.value = { show: true, configData: { url } };
    } else {
        window.$message.error("预览失败");
    }
};
</script>
