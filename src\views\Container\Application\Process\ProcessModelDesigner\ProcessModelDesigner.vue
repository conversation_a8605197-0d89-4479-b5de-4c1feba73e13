<script lang="ts" setup>
import { computed, nextTick, ref, watchEffect } from "vue";
import { ProcessDesigner } from "@/components/ProcessDesigner";
import { cloneDeep } from "lodash-es";
import dayjs from "dayjs";
import { ProcessBasicInfo, ProcessFormEditor, ProcessNodeEditor, ProcessOtherInfo } from "./components";
import { useStoreFormGenerator } from "@/store";
import type { FormGeneratorProps } from "@/components/FormGenerator";
import { ADD_OA_MODEL, GET_OA_MODEL_DETAIL, UPDATE_OA_MODEL } from "@/api/application/oa";

let storeFormGenerator = useStoreFormGenerator();

let props = withDefaults(defineProps<{ show: boolean; configData?: Record<string, any> }>(), { show: () => false });
let emits = defineEmits(["update:show", "refresh"]);
let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
let changeModalShow = (show: boolean) => emits("update:show", show);

let onManualClose = () => {
    window.$dialog.warning({
        title: "提示",
        content: "尚未保存的数据将会丢失，确定关闭吗？",
        positiveText: "确认关闭",
        negativeText: "取消",
        onPositiveClick: () => changeModalShow(false)
    });
};

let tabActive = ref(1);

let toPrevStep = () => (tabActive.value = tabActive.value - 1);

let toNextStep = async (toStep: number) => (tabActive.value = toStep);

/**
 * step1
 * 基础信息
 */

let initBasicFormData: Record<string, any> = {
    key: null,
    name: null,
    category: null,
    desc: null,
    initiatorScope: "1",
    initiatorDeptIds: null,
    isSystem: "0",
    modelIcon: null
};

let basicFormData = ref<Record<string, any>>(cloneDeep(initBasicFormData));

let clearBasicFrom = () => (basicFormData.value = cloneDeep(initBasicFormData));

let initFormKey = () => {
    const timestamp = dayjs().valueOf();
    const random = Math.floor(Math.random() * 10000); // 0-9999的随机数
    basicFormData.value.key = `ydflow_${timestamp}_${random}`;
};

/**
 * step2
 * 表单设计
 */

// 组件列表
let componentList = ref<FormGeneratorProps[]>([]);

// 组件编辑器ref
let formEditorRef = ref<any>(null);

/**
 * step3
 * 流程设计
 */

// 流程编辑
let processXml = ref("");
let processModeler = ref<any>(null);

let clearProcess = () => {
    processXml.value = "";
    processModeler.value = null;
    processNodeList.value = [];
};

// 自定义节点
let processNodeList = ref<any[]>([]);

// 流程节点编辑器ref
let processNodeEditorRef = ref<any>(null);

/**
 * step4
 * 其它选项
 */

let initOtherFormData: Record<string, any> = { ccPersons: null };

let otherFormData = ref<Record<string, any>>(cloneDeep(initOtherFormData));

let clearOtherForm = () => (otherFormData.value = cloneDeep(initOtherFormData));

// 获取详情
let wcfId = ref("");

let getDetail = async () => {
    await GET_OA_MODEL_DETAIL({ id: props.configData?.id }).then((res) => {
        basicFormData.value = {
            ...res.data.data,
            initiatorScope: String(res.data.data.initiatorScope),
            initiatorDeptIds: res.data.data.initiatorDeptIds?.split(","),
            isSystem: String(res.data.data.isSystem)
        };
        componentList.value = res.data.data.customFormDTO.elements.map((item: FormGeneratorProps) => {
            return { ...item, modelValue: item.modelValue ? JSON.parse(item.modelValue) : null };
        });
        storeFormGenerator.setFormConfigs(res.data.data.customFormDTO.wcfOptions);
        wcfId.value = res.data.data.customFormDTO.wcfId;
        processXml.value = res.data.data.processDTO.jsonXml;
        processXml.value && processModeler.value.importXML(processXml.value);

        processNodeList.value = res.data.data.processDTO.processNodeList.map((item: any) => {
            return {
                nodeId: item.nodeId,
                nodeName: item.nodeName ?? "",
                nodeType: item.nodeType,
                approveChooseWay: item.approveChooseWay,
                approveChooseRange: item.approveChooseRange,
                approveChooseContent: item.approveChooseContent,
                multiApproveType: item.multiApproveType,
                defaultChooseAssignee: item.defaultChooseAssignee,
                formParamId: item.formParamId,
                multiple: item.multiple,
                multipleColumn: item.multipleColumn,
                // 条件回显暂时删除
                // conditionExpression: item.conditionExpression,
                processFormNodeConfigList: item.processFormNodeConfigList ?? [],
                approveFormId: item.approveFormId,
                /*
                 * 2024年5月28日13:53:01
                 * 新增节点抄送人相关逻辑
                 */
                nodeCopyConditionList: item.nodeCopyConditionList || []
            };
        });

        console.log("获取详情时的所有节点", processNodeList.value);

        otherFormData.value.ccPersons = res.data.data.processDTO.ccPersons;
    });
};

watchEffect(async () => {
    if (props.show) {
        !props.configData ? initFormKey() : await getDetail();
    } else {
        tabActive.value = 1;
        clearBasicFrom();
        formEditorRef.value?.onDeleteComponent();
        clearProcess();
        clearOtherForm();
    }

    if (processModeler.value) {
        let modeling = processModeler.value.get("modeling");
        let elementRegistry = processModeler.value.get("elementRegistry");
        let elementKeys = Object.keys(elementRegistry._elements);
        if (!props.configData) {
            // 更改流程key
            modeling.updateProperties(elementRegistry.get(elementKeys[0]), { id: basicFormData.value.key });
        }
    }
});

// 提交
let onSubmit = async () => {
    // 在提交前确保所有节点都有最新的组件配置（双重保险）
    if (processNodeEditorRef.value) {
        processNodeEditorRef.value.ensureAllNodesHaveLatestComponents();
    }
    
    // 获取所有节点
    let elementRegistry = processModeler.value.get("elementRegistry");
    // 获取最新的节点属性
    let latestNodeAttrs = Object.values(elementRegistry._elements)
        .filter((item: any) => item.element.type !== "bpmn:Process")
        .map((item: any) => ({
            nodeId: item.element.id,
            ...item.element.businessObject.$attrs
        }));
    
    // 合并原有节点数据和最新属性
    processNodeList.value = processNodeList.value.map((existingNode: any) => {
        const latestAttrs = latestNodeAttrs.find(attr => attr.nodeId === existingNode.nodeId);
        // 只更新在 latestAttrs 中明确存在且不为 undefined 的字段
        const mergedNode = { ...existingNode };
        if (latestAttrs) {
            Object.keys(latestAttrs).forEach(key => {
                if (latestAttrs[key] !== undefined) {
                    mergedNode[key] = latestAttrs[key];
                }
            });
        }
        
        // 确保关键字段不被覆盖
        if (existingNode.nodeCopyConditionList && !mergedNode.nodeCopyConditionList) {
            mergedNode.nodeCopyConditionList = existingNode.nodeCopyConditionList;
        }
        if (existingNode.processFormNodeConfigList && !mergedNode.processFormNodeConfigList) {
            mergedNode.processFormNodeConfigList = existingNode.processFormNodeConfigList;
        }
        
        return mergedNode;
    });
    
    // 如果有新增的节点（不在原有列表中）
    latestNodeAttrs.forEach((attrs: any) => {
        if (!processNodeList.value.find((node: any) => node.nodeId === attrs.nodeId)) {
            processNodeList.value.push({
                nodeId: attrs.nodeId,
                nodeName: attrs.nodeName ?? "",
                nodeType: attrs.nodeType,
                approveChooseWay: attrs.approveChooseWay,
                approveChooseRange: attrs.approveChooseRange,
                approveChooseContent: attrs.approveChooseContent,
                multiApproveType: attrs.multiApproveType,
                defaultChooseAssignee: attrs.defaultChooseAssignee,
                formParamId: attrs.formParamId,
                multiple: attrs.multiple,
                multipleColumn: attrs.multipleColumn,
                processFormNodeConfigList: attrs.processFormNodeConfigList ?? [],
                approveFormId: attrs.approveFormId,
                nodeCopyConditionList: attrs.nodeCopyConditionList || [],
                ...attrs
            });
        }
    });
    
    console.log("提交时的所有节点", processNodeList.value);
    
    // 调试：检查 nodeCopyConditionList 的状态
    processNodeList.value.forEach((node: any) => {
        console.log(`节点 ${node.nodeId} (${node.nodeName}) 的 nodeCopyConditionList:`, node.nodeCopyConditionList);
    });
    
    // 检查发起人节点的组件配置
    const starterNode = processNodeList.value.find(node => node.nodeType === 0);
    if (starterNode) {
        console.log('发起人节点的组件配置数量:', starterNode.processFormNodeConfigList?.length || 0);
    }
    let componentArray = componentList.value.map((item: FormGeneratorProps) => {
        return {
            ...item,
            dynamicTableDataSource: item.dynamicTableDataSource?.map((citem) => ({ ...citem, defaultOptions: [] })),
            options: item.optionsSource === "static" ? item.options : [],
            modelValue: JSON.stringify(item.modelValue)
        };
    });
    let customFormDTO = {
        wcfId: wcfId.value ?? null,
        wcfCode: storeFormGenerator.getFormConfigs.wcfCode,
        wcfName: storeFormGenerator.getFormConfigs.wcfName,
        wcfType: storeFormGenerator.getFormConfigs.wcfType,
        wcfRemark: storeFormGenerator.getFormConfigs.wcfRemark,
        wcfOptions: storeFormGenerator.getFormConfigs,
        elements: componentArray
    };
    let publicParams = {
        ...basicFormData.value,
        initiatorDeptIds: basicFormData.value.initiatorDeptIds ? basicFormData.value.initiatorDeptIds.join(",") : null,
        customFormDTO,
        processDTO: {
            jsonXml: processXml.value,
            ccPersons: otherFormData.value.ccPersons,
            processNodeList: processNodeList.value
        }
    };
    // 提交数据
    let apiFunction = props.configData ? UPDATE_OA_MODEL : ADD_OA_MODEL;
    let apiParams = props.configData ? { id: props.configData.id, ...publicParams } : { ...publicParams };
    let res = await apiFunction(apiParams);
    if (res.data.code === 0) {
        window.$message.success(props.configData ? "编辑成功" : "新增成功");
        emits("refresh");
        changeModalShow(false);
    }
};

/*
 * 导入导出配置
 * 方便内部使用
 * 2024年3月4日14:39:19
 */

// 导出配置
let onExport = () => {
    // 在导出前确保所有节点都有最新的组件配置
    if (processNodeEditorRef.value) {
        processNodeEditorRef.value.ensureAllNodesHaveLatestComponents();
    }
    
    // 获取所有节点
    let elementRegistry = processModeler.value.get("elementRegistry");
    // 获取最新的节点属性
    let latestNodeAttrs = Object.values(elementRegistry._elements)
        .filter((item: any) => item.element.type !== "bpmn:Process")
        .map((item: any) => ({
            nodeId: item.element.id,
            ...item.element.businessObject.$attrs
        }));
    
    // 合并原有节点数据和最新属性
    processNodeList.value = processNodeList.value.map((existingNode: any) => {
        const latestAttrs = latestNodeAttrs.find(attr => attr.nodeId === existingNode.nodeId);
        // 只更新在 latestAttrs 中明确存在且不为 undefined 的字段
        const mergedNode = { ...existingNode };
        if (latestAttrs) {
            Object.keys(latestAttrs).forEach(key => {
                if (latestAttrs[key] !== undefined) {
                    mergedNode[key] = latestAttrs[key];
                }
            });
        }
        
        // 确保关键字段不被覆盖
        if (existingNode.nodeCopyConditionList && !mergedNode.nodeCopyConditionList) {
            mergedNode.nodeCopyConditionList = existingNode.nodeCopyConditionList;
        }
        if (existingNode.processFormNodeConfigList && !mergedNode.processFormNodeConfigList) {
            mergedNode.processFormNodeConfigList = existingNode.processFormNodeConfigList;
        }
        
        return mergedNode;
    });
    
    // 如果有新增的节点（不在原有列表中）
    latestNodeAttrs.forEach((attrs: any) => {
        if (!processNodeList.value.find((node: any) => node.nodeId === attrs.nodeId)) {
            processNodeList.value.push({
                nodeId: attrs.nodeId,
                nodeName: attrs.nodeName ?? "",
                nodeType: attrs.nodeType,
                approveChooseWay: attrs.approveChooseWay,
                approveChooseRange: attrs.approveChooseRange,
                approveChooseContent: attrs.approveChooseContent,
                multiApproveType: attrs.multiApproveType,
                defaultChooseAssignee: attrs.defaultChooseAssignee,
                formParamId: attrs.formParamId,
                multiple: attrs.multiple,
                multipleColumn: attrs.multipleColumn,
                processFormNodeConfigList: attrs.processFormNodeConfigList ?? [],
                approveFormId: attrs.approveFormId,
                nodeCopyConditionList: attrs.nodeCopyConditionList || [],
                ...attrs
            });
        }
    });
    
    let componentArray = componentList.value.map((item: FormGeneratorProps) => {
        return {
            ...item,
            dynamicTableDataSource: item.dynamicTableDataSource?.map((citem) => ({ ...citem, defaultOptions: [] })),
            options: item.optionsSource === "static" ? item.options : [],
            modelValue: JSON.stringify(item.modelValue)
        };
    });
    // 导出表单配置时，排除表单编码
    let exportFormConfigs = { ...storeFormGenerator.getFormConfigs };
    delete exportFormConfigs.wcfCode; // 删除表单编码
    
    let customFormDTO = {
        wcfId: null, // 导出时不包含wcfId，避免冲突
        wcfCode: null, // 导出时不包含wcfCode，客户要求排除
        wcfName: storeFormGenerator.getFormConfigs.wcfName,
        wcfType: storeFormGenerator.getFormConfigs.wcfType,
        wcfRemark: storeFormGenerator.getFormConfigs.wcfRemark,
        wcfOptions: exportFormConfigs, // 使用清理后的配置
        elements: componentArray
    };
    // 导出时只包含必要的字段，排除id、创建时间、更新时间、流程标识、表单编码等字段
    let publicParams = {
        // key: basicFormData.value.key, // 客户要求排除流程标识
        name: basicFormData.value.name,
        category: basicFormData.value.category,
        desc: basicFormData.value.desc,
        initiatorScope: basicFormData.value.initiatorScope,
        initiatorDeptIds: basicFormData.value.initiatorDeptIds ? basicFormData.value.initiatorDeptIds.join(",") : null,
        isSystem: basicFormData.value.isSystem,
        modelIcon: basicFormData.value.modelIcon,
        customFormDTO,
        processDTO: {
            jsonXml: processXml.value,
            ccPersons: otherFormData.value.ccPersons,
            processNodeList: processNodeList.value
        }
    };
    // Convert the data to a JSON string
    let dataStr = JSON.stringify(publicParams);

    // Create a Blob object from the string
    let dataBlob = new Blob([dataStr], { type: "application/json" });

    // Create a URL for the Blob object
    let url = URL.createObjectURL(dataBlob);

    // Create a hidden anchor element
    let downloadAnchorNode = document.createElement("a");
    downloadAnchorNode.setAttribute("href", url);
    downloadAnchorNode.setAttribute("download", `${customFormDTO.wcfName}.json`);

    // Append the anchor to the body
    document.body.appendChild(downloadAnchorNode);

    // Trigger the download
    downloadAnchorNode.click();

    // Clean up by removing the anchor from the body and revoking the Blob URL
    document.body.removeChild(downloadAnchorNode);
    URL.revokeObjectURL(url);
};

// 导入配置
let onImport = () => {
    let input = document.createElement("input");
    input.type = "file";
    input.accept = ".json";
    input.onchange = (e: any) => {
        let file = e.target.files[0];
        let reader = new FileReader();
        reader.onload = (e: any) => {
            let data = JSON.parse(e.target.result);
            
            // 保存当前的key、wcfCode和wcfId（编辑模式下需要保持不变）
            const currentKey = basicFormData.value.key;
            const currentWcfCode = storeFormGenerator.getFormConfigs.wcfCode;
            const currentWcfId = wcfId.value;
            const isEditMode = !!props.configData; // 判断是否为编辑模式
            
            // 导入时只保留必要的字段，排除敏感字段（如id、创建时间等）
            basicFormData.value = {
                key: isEditMode ? currentKey : undefined, // 编辑模式保持原有key，新增模式不设置
                name: data.name,
                category: data.category,
                desc: data.desc,
                initiatorScope: String(data.initiatorScope),
                initiatorDeptIds: data.initiatorDeptIds?.split(","),
                isSystem: String(data.isSystem),
                modelIcon: data.modelIcon
            };
            
            /*
             * 2024年12月27日16:30:00
             * 修复导入时字段处理逻辑
             * 编辑模式：保持原有的流程标识和表单编码不变
             * 新增模式：排除流程标识和表单编码，由系统重新生成
             */
            componentList.value = data.customFormDTO.elements.map((item: FormGeneratorProps) => {
                return { ...item, modelValue: item.modelValue ? JSON.parse(item.modelValue) : null };
            });
            
            // 处理表单配置
            let formConfigs = { ...data.customFormDTO.wcfOptions };
            if (isEditMode) {
                // 编辑模式：保持原有的表单编码
                formConfigs.wcfCode = currentWcfCode;
            } else {
                // 新增模式：删除表单编码，由系统重新生成
                delete formConfigs.wcfCode;
            }
            storeFormGenerator.setFormConfigs(formConfigs);
            
            // 处理wcfId
            if (isEditMode) {
                // 编辑模式：保持原有的wcfId
                wcfId.value = currentWcfId;
            } else {
                // 新增模式：清空wcfId
                wcfId.value = "";
            }

            if (!isEditMode) {
                initFormKey(); // 新增模式下生成新的流程标识
            }

            // 先设置processNodeList，确保节点数据在XML导入前就准备好
            processNodeList.value = data.processDTO.processNodeList.map((item: any) => {
                return {
                    nodeId: item.nodeId,
                    nodeName: item.nodeName ?? "",
                    nodeType: item.nodeType,
                    approveChooseWay: item.approveChooseWay,
                    approveChooseRange: item.approveChooseRange,
                    approveChooseContent: item.approveChooseContent,
                    multiApproveType: item.multiApproveType,
                    defaultChooseAssignee: item.defaultChooseAssignee,
                    formParamId: item.formParamId,
                    multiple: item.multiple,
                    multipleColumn: item.multipleColumn,
                    // 条件回显暂时删除
                    // conditionExpression: item.conditionExpression,
                    processFormNodeConfigList: item.processFormNodeConfigList ?? [],
                    approveFormId: item.approveFormId,
                    /*
                     * 2024年6月29日14:33:50
                     * 修复导入流程配置节点抄送人丢失的问题
                     */
                    nodeCopyConditionList: item.nodeCopyConditionList || []
                };
            });

            // 导入XML并等待完成
            processXml.value = data.processDTO.jsonXml;
            if (processXml.value && processModeler.value) {
                processModeler.value.importXML(processXml.value).then(() => {
                    // XML导入完成后，需要将processNodeList中的数据同步到流程图节点
                    nextTick(() => {
                        try {
                            let modeling = processModeler.value.get("modeling");
                            let elementRegistry = processModeler.value.get("elementRegistry");
                            
                            // 将processNodeList中的数据应用到对应的流程图节点
                            processNodeList.value.forEach((nodeData) => {
                                let element = elementRegistry.get(nodeData.nodeId);
                                if (element) {
                                    modeling.updateProperties(element, nodeData);
                                    console.log(`导入后同步节点数据: ${nodeData.nodeId} (${nodeData.nodeName})`);
                                }
                            });
                            
                            // 确保ProcessNodeEditor有最新的组件配置
                            if (processNodeEditorRef.value) {
                                processNodeEditorRef.value.ensureAllNodesHaveLatestComponents();
                            }
                        } catch (error) {
                            console.error('导入后同步节点数据失败:', error);
                        }
                    });
                });
            }

            otherFormData.value.ccPersons = data.processDTO.ccPersons;
        };
        reader.readAsText(file);
    };
    input.click();
};

/*
 * 2024年3月13日09:58:10
 * 流程编辑器初始化流程
 * 方便内部使用
 */
let processDesignerRef = ref<any>(null);

let onInitProcess = () => processDesignerRef.value?.onInit();
</script>

<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card
                :segmented="{ content: true }"
                class="w-95vw h-95vh"
                content-style="padding:0"
                header-style="padding:12px"
            >
                <template #header>
                    <div class="flex-center pl-124px">
                        <n-tabs
                            v-model:value="tabActive"
                            class="w-300px"
                            pane-style="padding-top:0"
                            size="small"
                            type="segment"
                        >
                            <n-tab-pane :name="1" tab="基础信息" />
                            <n-tab-pane :name="2" tab="表单设计" />
                            <n-tab-pane :name="3" tab="流程设计" />
                            <n-tab-pane :name="4" tab="其他信息" />
                        </n-tabs>
                    </div>
                </template>
                <template #header-extra>
                    <n-space class="ml-a">
                        <n-button secondary type="success" @click="onImport()">导入配置</n-button>
                        <n-button secondary type="warning" @click="onExport()">导出配置</n-button>
                        <n-button secondary type="error" @click="onInitProcess()">初始化流程</n-button>
                        <n-button v-if="tabActive > 1" type="success" @click="toPrevStep">上一步</n-button>
                        <n-button v-if="tabActive === 3" type="primary" @click="toNextStep(4)">下一步</n-button>
                        <n-button v-if="tabActive === 4" type="primary" @click="onSubmit">提交</n-button>
                        <n-button type="error" @click="onManualClose">关闭</n-button>
                    </n-space>
                </template>
                <div v-show="tabActive === 1" class="w-500px py-20px mx-a">
                    <ProcessBasicInfo v-model:value="basicFormData" :configData="configData" @submit="toNextStep(2)" />
                </div>
                <div v-show="tabActive === 2" class="relative wh-100%">
                    <ProcessFormEditor ref="formEditorRef" v-model:value="componentList" @submit="toNextStep(3)" />
                </div>
                <div v-show="tabActive === 3" class="relative wh-100%">
                    <ProcessDesigner
                        ref="processDesignerRef"
                        v-model:modeler="processModeler"
                        v-model:xml="processXml"
                        :components="componentList"
                    />
                    <n-card
                        class="w-350px absolute top-15px bottom-15px right-10px z-999"
                        content-style="padding:0;overflow-y:auto;"
                        hoverable
                        title="流程配置"
                    >
                        <div class="px-20px pb-20px">
                            <ProcessNodeEditor
                                ref="processNodeEditorRef"
                                :components="componentList"
                                :modeler="processModeler"
                                :nodes="processNodeList"
                                :configData="configData"
                            />
                        </div>
                    </n-card>
                </div>
                <div v-show="tabActive === 4" class="relative wh-100%">
                    <ProcessOtherInfo v-model:value="otherFormData" />
                </div>
            </n-card>
        </n-modal>
    </div>
</template>
