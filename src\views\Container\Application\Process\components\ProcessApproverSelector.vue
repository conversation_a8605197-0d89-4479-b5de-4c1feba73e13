<template>
    <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
        <n-card class="w-380px" closable title="选择审批人" @close="changeModalShow(false)">
            <div v-for="(item, index) in nextNodes">
                <template v-if="item.approveChooseWay === 2">
                    <n-divider>{{ `${item.nodeName}节点审批人` }}</n-divider>
                    <UserSelector
                        :multiple="item.multiple !== 0"
                        v-model:value="item.userSelectedAssignees"
                        :data-config="item"
                        :placeholder="`请选择${item.nodeName}节点审批人`"
                        class="w-100%"
                        key-name="username"
                    />
                </template>
            </div>
            <div class="mt-30px flex-center">
                <n-button block class="flex-1" type="primary" @click="onSubmit">提交</n-button>
                <n-button block class="flex-1 ml" type="error" @click="changeModalShow(false)">取消</n-button>
            </div>
        </n-card>
    </n-modal>
</template>

<script lang="ts" setup>
import { computed } from "vue";
import { UserSelector } from "@/components/UserSelector";
import { useDebounceFn } from "@vueuse/core";

let props = withDefaults(defineProps<{ show: boolean; configData: UnKnownObject }>(), { show: () => false });

let emits = defineEmits(["update:show", "submit"]);

let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

let changeModalShow = (show: boolean) => emits("update:show", show);

// 筛选节点
let nextNodes = computed({
    get: () => props.configData.nextNodes as any[],
    set: (val) => {}
});

/*
 * 提交
 * OA按钮防抖
 */
let onSubmit = useDebounceFn(async () => {
    // 判断approveChooseWay === 2，但是userSelectedAssignees为空的情况
    let flag = false;
    nextNodes.value.forEach((item) => {
        if (item.approveChooseWay === 2 && !item.userSelectedAssignees) flag = true;
    });
    if (flag) return window.$message.error("请选择审批人");

    if (props.configData.type === "approval") {
        emits("submit", {
            taskId: props.configData.taskId,
            status: props.configData.status,
            nextNodes: nextNodes.value,
            processNodeConfig: props.configData.processNodeConfig
        });
    } else {
        emits("submit", {
            taskId: props.configData.taskId,
            nextNodes: nextNodes.value
        });
    }

    changeModalShow(false);
}, 1000);
</script>
