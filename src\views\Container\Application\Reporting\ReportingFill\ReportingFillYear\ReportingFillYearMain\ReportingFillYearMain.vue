<template>
    <div>
        <n-card content-style="padding-top:8px;padding-bottom:4px" hoverable>
            <n-tabs v-model:value="planYear" animated size="small" type="bar">
                <n-tab-pane v-for="item in planYearList" :name="item.name" :tab="item.tab" />
            </n-tabs>
        </n-card>
        <div class="flex mt">
            <n-card class="flex-fixed-200" content-style="padding:0" hoverable>
                <n-menu
                    v-model:value="tabActive"
                    :indent="20"
                    :options="tabOptions"
                    class="flex-fixed-150 border-r-1px border-[#E5E5E5]"
                    mode="vertical"
                    @update:value="changeTabActive"
                />
            </n-card>
            <n-card class="flex-1 ml" hoverable>
                <ReportingFillYearMainAmount v-if="tabActive === 'amount'" :planYear="planYear" />
                <ReportingFillYearMainCost v-if="tabActive === 'cost'" :planYear="planYear" />
            </n-card>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import dayjs from "dayjs";
import ReportingFillYearMainAmount from "./ReportingFillYearMainAmount/ReportingFillYearMainAmount.vue";
import ReportingFillYearMainCost from "./ReportingFillYearMainCost.vue";

let tabActive = ref("amount");

let changeTabActive = (key: string) => {};

let tabOptions = ref<any[]>([
    { label: "产量计划", key: "amount" },
    { label: "计划人力资金", key: "cost" }
]);

// 当前月份
let planYear = ref("");

let planYearList = ref<any[]>([]);

let setPlanYearList = () => {
    let currentYear = dayjs().year();
    let nextYear = dayjs().add(1, "year").year();
    planYearList.value = [
        { tab: "当年", name: String(currentYear) },
        { tab: "次年", name: String(nextYear) }
    ];
    planYear.value = planYearList.value[0].name;
};

onMounted(() => {
    setPlanYearList();
});
</script>

<style lang="scss" scoped>
::v-deep(.n-menu) {
    .n-menu-item {
        &:first-child {
            margin-top: 0;
        }

        .n-menu-item-content {
            &:before {
                left: 0;
                right: 0;
            }
        }

        .n-menu-item-content--selected {
            &:before {
                border-right: 2px solid var(--n-item-text-color-active);
            }
        }
    }
}
</style>
