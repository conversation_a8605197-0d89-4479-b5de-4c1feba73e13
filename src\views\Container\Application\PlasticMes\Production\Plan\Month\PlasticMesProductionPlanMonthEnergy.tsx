import { computed, defineComponent, onMounted, ref } from "vue";
import dayjs from "dayjs";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { GET_USAGE_MONTH_PLAN_BY_MONTH, POST_USAGE_MONTH_PLAN } from "@/api/application/plasticMes";

export default defineComponent({
    name: "PlasticMesProductionPlanMonthEnergy",
    setup() {
        // 年月搜索
        const searchFormRef = ref<FormInst | null>(null);
        const searchFormData = ref<UnKnownObject>({
            planYear: null,
            planMonth: null,
            planMonthYear: null
        });
        const planMonthOptions = ref<UnKnownObject[]>([]);

        const getSearchFormOptions = async () => {
            const options: UnKnownObject[] = [];
            const currentYear = dayjs().year();
            const nextYear = currentYear + 1;

            // 生成当前年份和下一年的月份选项
            [currentYear, nextYear].forEach((year) => {
                for (let month = 1; month <= 12; month++) {
                    options.push({
                        label: `${year}年${month}月`,
                        value: `${year}${month.toString().padStart(2, "0")}`,
                        year: year,
                        month: month
                    });
                }
            });

            planMonthOptions.value = options;
        };

        const searchFormRules = computed(() => ({
            planMonth: [{ required: true, message: "请选择计划年月", trigger: ["blur", "change"], type: "string" }]
        }));

        const onSearch = async () => {
            const validateError = await searchFormRef.value?.validate((errors) => !!errors);
            if (validateError) return false;

            GET_USAGE_MONTH_PLAN_BY_MONTH({
                planYear: searchFormData.value.planYear,
                planMonth: searchFormData.value.planMonth,
                planMonthYear: searchFormData.value.planMonthYear
            }).then((res) => {
                const resData = res.data.data ?? null;
                formData.value = {
                    id: resData?.id ?? null,
                    riverWaterUsageCost: resData?.riverWaterUsageCost ?? null,
                    riverWaterUsageTonnage: resData?.riverWaterUsageTonnage ?? null,
                    tapWaterUsageCost: resData?.tapWaterUsageCost ?? null,
                    tapWaterUsageTonnage: resData?.tapWaterUsageTonnage ?? null,
                    electricityCost: resData?.electricityCost ?? null,
                    electricityUsageKwh: resData?.electricityUsageKwh ?? null
                };
            });
        };

        const handleMonthChange = (value: string) => {
            const selectedOption = planMonthOptions.value.find((opt) => opt.value === value);
            if (selectedOption) {
                searchFormData.value.planYear = selectedOption.year;
                searchFormData.value.planMonth = value;
                searchFormData.value.planMonthYear = selectedOption.month;
            }
        };

        // 左侧菜单切换
        const tabActive = ref<string>("water");

        const changeTabActive = async (key: string) => {
            await onSearch();
        };

        const tabOptions = ref<any[]>([
            { label: "水", key: "water" },
            { label: "电", key: "power" }
        ]);

        // 表单数据
        interface FormDataProps {
            [key: string]: any;
        }

        const formRef = ref<FormInst | null>(null);

        const getFormOptions = async () => {};

        const initFormData: FormDataProps = {
            id: null,
            // 水相关字段
            riverWaterUsageCost: null,
            riverWaterUsageTonnage: null,
            tapWaterUsageCost: null,
            tapWaterUsageTonnage: null,
            // 电相关字段
            electricityCost: null,
            electricityUsageKwh: null
        };

        const formRules = computed(() => ({
            riverWaterUsageCost: [
                { required: true, message: "请输入河道取水预计消耗成本", trigger: ["input", "blur"] }
            ],
            riverWaterUsageTonnage: [
                { required: true, message: "请输入河道取水预计使用吨数", trigger: ["input", "blur"] }
            ],
            tapWaterUsageCost: [{ required: true, message: "请输入自来水预计消耗成本", trigger: ["input", "blur"] }],
            tapWaterUsageTonnage: [{ required: true, message: "请输入自来水预计使用吨数", trigger: ["input", "blur"] }],
            electricityCost: [{ required: true, message: "请输入电预计耗费成本", trigger: ["input", "blur"] }],
            electricityUsageKwh: [{ required: true, message: "请输入电预计使用度数", trigger: ["input", "blur"] }]
        }));

        const formData = ref(cloneDeep(initFormData));

        const clearForm = () => {
            formData.value = cloneDeep(initFormData);
        };

        const onSubmit = async () => {
            const searchValidateError = await searchFormRef.value?.validate((errors) => !!errors);
            const validateError = await formRef.value?.validate((errors) => !!errors);
            if (!!validateError || !!searchValidateError) return false;

            const params = {
                id: formData.value.id,
                planYear: searchFormData.value.planYear,
                planMonth: searchFormData.value.planMonth,
                planMonthYear: searchFormData.value.planMonthYear,
                usageType: tabActive.value === "water" ? 4 : 5,
                ...formData.value
            };

            try {
                const res = await POST_USAGE_MONTH_PLAN(params);
                if (res.data.code === 0) {
                    window.$message.success("保存成功");
                    onSearch();
                }
            } catch (error) {
                window.$message.error("保存失败");
            }
        };

        onMounted(async () => {
            await getSearchFormOptions(); // 初始化年月选项
            await getFormOptions();
        });

        return () => (
            <div class="plastic-mes-production-plan-month mt-2">
                <n-form
                    inline
                    ref={searchFormRef}
                    model={searchFormData.value}
                    rules={searchFormRules.value}
                    label-placement="left"
                    label-width="auto"
                >
                    <n-form-item label="计划年月" path="planMonth">
                        <n-select
                            class="w-300px"
                            v-model:value={searchFormData.value.planMonth}
                            options={planMonthOptions.value}
                            clearable
                            filterable
                            placeholder="请选择计划年月"
                            onUpdate:value={handleMonthChange}
                        />
                    </n-form-item>
                    <n-form-item>
                        <n-button type="primary" onClick={() => onSearch()}>
                            查询
                        </n-button>
                    </n-form-item>
                </n-form>
                <div class="flex mt">
                    <n-menu
                        v-model:value={tabActive.value}
                        indent={20}
                        options={tabOptions.value}
                        class={"common-left-menu flex-fixed-180 border-r-1px border-[#E5E5E5]"}
                        mode="vertical"
                        onUpdate:value={changeTabActive}
                    />
                    <div class="flex-fixed-800 ml">
                        <n-space>
                            <n-button type="primary" onClick={onSubmit}>
                                保存配置
                            </n-button>
                            <n-button type="error" onClick={clearForm}>
                                清空内容
                            </n-button>
                        </n-space>
                        <n-form
                            class="mt"
                            ref={formRef}
                            model={formData.value}
                            rules={formRules.value}
                            label-placement="left"
                        >
                            <n-grid cols={12} x-gap={16}>
                                {tabActive.value === "water" && (
                                    <>
                                        <n-form-item-gi span={12}>
                                            <div class="text-20px">自来水</div>
                                        </n-form-item-gi>
                                        <n-form-item-gi span={6} label="预计使用吨数（吨）" path="tapWaterUsageTonnage">
                                            <n-input
                                                v-model:value={formData.value.tapWaterUsageTonnage}
                                                class="w-100%"
                                                clearable
                                                placeholder="请输入预计使用吨数"
                                            />
                                        </n-form-item-gi>
                                        <n-form-item-gi span={6} label="预计耗费成本（万元）" path="tapWaterUsageCost">
                                            <n-input
                                                v-model:value={formData.value.tapWaterUsageCost}
                                                class="w-100%"
                                                clearable
                                                placeholder="请输入预计消耗成本"
                                            />
                                        </n-form-item-gi>
                                        <n-form-item-gi span={12}>
                                            <div class="text-20px">河道取水</div>
                                        </n-form-item-gi>
                                        <n-form-item-gi
                                            span={6}
                                            label="预计使用吨数（吨）"
                                            path="riverWaterUsageTonnage"
                                        >
                                            <n-input
                                                v-model:value={formData.value.riverWaterUsageTonnage}
                                                class="w-100%"
                                                clearable
                                                placeholder="请输入预计使用吨数"
                                            />
                                        </n-form-item-gi>
                                        <n-form-item-gi
                                            span={6}
                                            label="预计耗费成本（万元）"
                                            path="riverWaterUsageCost"
                                        >
                                            <n-input
                                                v-model:value={formData.value.riverWaterUsageCost}
                                                class="w-100%"
                                                clearable
                                                placeholder="请输入预计消耗成本"
                                            />
                                        </n-form-item-gi>
                                    </>
                                )}
                                {tabActive.value === "power" && (
                                    <>
                                        <n-form-item-gi span={6} label="预计耗费成本（万元）" path="electricityCost">
                                            <n-input
                                                v-model:value={formData.value.electricityCost}
                                                class="w-100%"
                                                clearable
                                                placeholder="请输入预计耗费成本"
                                            />
                                        </n-form-item-gi>
                                        <n-form-item-gi span={6} label="预计使用度数（度）" path="electricityUsageKwh">
                                            <n-input
                                                v-model:value={formData.value.electricityUsageKwh}
                                                class="w-100%"
                                                clearable
                                                placeholder="请输入预计使用度数"
                                            />
                                        </n-form-item-gi>
                                    </>
                                )}
                            </n-grid>
                        </n-form>
                    </div>
                </div>
            </div>
        );
    }
});
