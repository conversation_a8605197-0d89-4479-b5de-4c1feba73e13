import { defineComponent, h, onMounted, reactive, ref } from "vue";
import {
    TableSearchbar,
    TableSearchbarConfig,
    TableSearchbarData,
    TableSearchbarOptions
} from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import { TableActions } from "@/components/TableActions";
import { DELETE_EQUIPMENT, GET_EQUIPMENT_PAGE_LIST } from "@/api/application/TowerScan";
import TowerScanEquipmentEdit from "./TowerScanEquipmentEdit";

export default defineComponent({
    name: "TowerScanEquipmentList",
    setup(props) {
        // 搜索项
        const searchConfig = ref<TableSearchbarConfig>([]);

        const searchOptions = ref<TableSearchbarOptions>({});

        const getSearchOptions = async () => {};

        const searchForm = ref<TableSearchbarData>({});

        const onSearch = () => {
            tablePagination.page = 1;
            getTableData();
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("equipmentId");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            { title: "设备名称", key: "equipmentName", align: "center" },
            {
                title: "设备编号",
                key: "equipmentNumber",
                align: "center",
                render: (row) => {
                    return <n-text type="info">{row.equipmentNumber}</n-text>;
                }
            },
            {
                title: "设备工序",
                key: "techniqueNames",
                align: "center",
                render: (row) => {
                    return <n-text type="info">{row.techniqueNames}</n-text>;
                }
            },
            { title: "设备操作工", key: "operatorNames", align: "center", render: (row) => row.operatorNames || "/" },
            { title: "备注", key: "remark", align: "center", render: (row) => row.remark || "/" },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 180,
                render: (row) => {
                    return (
                        <TableActions
                            type="button"
                            buttonActions={[
                                {
                                    label: "编辑",
                                    tertiary: true,
                                    type: "primary",
                                    onClick: () => openEditModal(row)
                                },
                                {
                                    label: "删除设备",
                                    tertiary: true,
                                    type: "error",
                                    onClick: () => {
                                        window.$dialog.warning({
                                            title: "确认删除",
                                            content: "确定要删除该条数据吗？",
                                            positiveText: "确定",
                                            negativeText: "取消",
                                            onPositiveClick: async () => {
                                                try {
                                                    const res = await DELETE_EQUIPMENT({
                                                        equipmentIds: row.equipmentId
                                                    });
                                                    if (res.data.code === 0) {
                                                        window.$message.success("删除成功");
                                                        getTableData();
                                                    } else {
                                                        window.$message.error(res.data.message || "删除失败");
                                                    }
                                                } catch (error) {
                                                    window.$message.error("删除失败");
                                                }
                                            }
                                        });
                                    }
                                }
                            ]}
                        />
                    );
                }
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_EQUIPMENT_PAGE_LIST({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                ...searchForm.value
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        // 新增编辑弹窗
        const editModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const openEditModal = (row?: RowProps) => {
            editModal.value = { show: true, configData: row ?? {} };
        };

        onMounted(async () => {
            await getSearchOptions();
            getTableData();
        });

        return () => (
            <div class="tower-scan-equipment-list">
                <n-card>
                    <TableSearchbar
                        form={searchForm.value}
                        config={searchConfig.value}
                        options={searchOptions.value}
                        onSearch={onSearch}
                    />
                </n-card>
                <n-card class="mt">
                    <n-space class="mb">
                        <n-button type="primary" onClick={() => openEditModal()}>
                            新增设备
                        </n-button>
                    </n-space>
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        pagination={tablePagination}
                        row-key={tableRowKey}
                        single-line={false}
                        bordered
                        remote
                        striped
                        onUpdate:checked-row-keys={changeTableSelection}
                    />
                </n-card>
                <TowerScanEquipmentEdit
                    v-model:show={editModal.value.show}
                    config-data={editModal.value.configData}
                    onRefresh={getTableData}
                />
            </div>
        );
    }
});
