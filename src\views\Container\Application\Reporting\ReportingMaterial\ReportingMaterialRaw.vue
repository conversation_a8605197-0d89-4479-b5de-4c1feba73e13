<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-space class="mb">
                <n-button secondary type="primary" @click="addTableItem">新增</n-button>
                <n-button secondary type="warning" @click="onBatchSave()">批量修改</n-button>
                <n-button secondary type="error" @click="onDelete()">批量删除</n-button>
                <n-button secondary type="success" @click="openImportModal()">导入调价表</n-button>
                <n-button secondary type="default" @click="openExportModal()">导出调价表</n-button>
            </n-space>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                v-model:checked-row-keys="tableSelection"
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <RawMaterialSpecifications
            v-model:show="specificationsModal.show"
            :config-data="specificationsModal.configData"
            @refresh="getTableData"
        />
        <!--导入导出-->
        <MaterialRawImport
            v-model:show="importModal.show"
            :config-data="importModal.configData"
            @refresh="getTableData"
        />
        <MaterialRawExport
            v-model:show="exportModal.show"
            :config-data="exportModal.configData"
            @refresh="getTableData"
        />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from "vue";
import type { DataTableColumns } from "naive-ui";
import { NButton } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import { cloneDeep } from "lodash-es";
import { TableActions } from "@/components/TableActions";
import {
    ADD_MATERIAL,
    BATCH_DELETE_RAW_MATERIALS,
    BATCH_UPDATE_MATERIAL_LIST,
    GET_CONFIG_COMPANY_LIST,
    GET_MATERIAL_CATEGORY_TREE_BY_GENRE,
    GET_MATERIAL_PAGE_LIST,
    UPDATE_MATERIAL
} from "@/api/application/reporting";
import { RawMaterialSpecifications, MaterialRawImport, MaterialRawExport } from "./components";
import { DynamicTableEditor } from "@/components/Dynamic";

interface RowProps {
    [key: string]: any;
}

onMounted(async () => {
    await getCategoryIdOptions();
    await getCompanyIdOptions();
    getSearchOptions();
    getTableData();
});

// 获取公司选项-填报专属修改2023年8月9日
let companyIdOptions = ref<any[]>([]);

let getCompanyIdOptions = async () => {
    await GET_CONFIG_COMPANY_LIST({ needFill: 1 }).then((res) => {
        companyIdOptions.value = res.data.data || [];
    });
};

// 获取物料分类选项
let categoryIdOptions = ref([]);

let getCategoryIdOptions = async () => {
    await GET_MATERIAL_CATEGORY_TREE_BY_GENRE({
        productGenre: 1
    }).then((res) => (categoryIdOptions.value = res.data.data));
};

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { prop: "materialName", type: "input", label: "物料名称" },
    { prop: "companyId", type: "select", label: "所属公司" }
]);

let searchOptions = ref<TableSearchbarOptions>({
    companyId: []
});

let searchForm = ref<TableSearchbarData>({
    materialName: null,
    companyId: null
});

let getSearchOptions = () => {
    searchOptions.value.companyId = companyIdOptions.value.map((item) => {
        return { label: item.companyName, value: item.id };
    });
};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "物料名称",
        align: "center",
        key: "materialName",
        render: (row) => {
            return h(DynamicTableEditor, {
                type: "input",
                value: row.materialName,
                onUpdateValue: (v: unknown) => (row.materialName = v)
            });
        }
    },
    {
        title: "物料分类",
        align: "center",
        key: "categoryId",
        render: (row) => {
            return h(DynamicTableEditor, {
                type: "treeSelect",
                value: row.categoryId,
                readonlyValue: row.categoryName,
                onUpdateValue: (v: unknown) => (row.categoryId = v),
                onUpdateReadonlyValue: (o: any) => (row.categoryName = o.categoryName),
                componentConfig: {
                    options: categoryIdOptions.value,
                    clearable: true,
                    filterable: true,
                    keyField: "id",
                    labelField: "categoryName",
                    placeholder: "请选择物料分类",
                    childrenField: "childrenList"
                }
            });
        }
    },
    {
        title: "规格",
        align: "center",
        key: "hasSpec",
        render: (row) => {
            if (row.hasSpec === 1 && row.id) {
                return h(
                    NButton,
                    { type: "primary", text: true, onClick: () => openSpecificationsModal(row) },
                    () => "点击查看"
                );
            } else {
                return "暂无规格";
            }
        }
    },
    {
        title: "所属公司",
        align: "center",
        key: "companyId",
        render: (row) => {
            return h(DynamicTableEditor, {
                type: "treeSelect",
                value: row.companyId,
                readonlyValue: row.companyName,
                onUpdateValue: (v: unknown) => (row.companyId = v),
                onUpdateReadonlyValue: (o: any) => (row.companyName = o.companyName),
                componentConfig: {
                    options: companyIdOptions.value,
                    clearable: true,
                    filterable: true,
                    keyField: "id",
                    labelField: "companyName",
                    placeholder: "请选择所属公司"
                }
            });
        }
    },
    {
        title: "备注",
        align: "center",
        key: "remark",
        render: (row) => {
            return h(DynamicTableEditor, {
                type: "input",
                value: row.remark,
                onUpdateValue: (v: unknown) => (row.remark = v)
            });
        }
    },
    {
        title: "操作",
        key: "action",
        align: "center",
        width: 180,
        render(row) {
            if (row.id) {
                return h(TableActions, {
                    type: "button",
                    buttonActions: [
                        {
                            label: "修改物料",
                            tertiary: true,
                            type: "primary",
                            onClick: () => updateTableItem(row)
                        }
                    ]
                });
            } else {
                return h(TableActions, {
                    type: "button",
                    buttonActions: [
                        {
                            label: "新增物料",
                            tertiary: true,
                            type: "primary",
                            onClick: () => updateTableItem(row)
                        },
                        {
                            label: "删除",
                            tertiary: true,
                            type: "error",
                            onClick: () => deleteTableItem(row)
                        }
                    ]
                });
            }
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    GET_MATERIAL_PAGE_LIST({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        ...searchForm.value
    }).then((res) => {
        tableData.value = res.data.data.records;
        tablePagination.itemCount = res.data.data.total;
        tableLoading.value = false;
    });
};

// 可编辑表单配置
let tableItem: RowProps = {
    id: "",
    materialName: "",
    categoryId: null,
    hasSpec: 1,
    companyId: null,
    remark: ""
};

let addTableItem = () => {
    // 插入底部
    // tableData.value.push(cloneDeep(tableItem));
    // 插入头部
    tableData.value.unshift(cloneDeep(tableItem));
};

let updateTableItem = async (row: RowProps) => {
    if (!row.materialName) {
        window.$message.error("请填写完整信息");
        return false;
    }
    let addParams = {
        materialName: row.materialName,
        categoryId: row.categoryId,
        hasSpec: row.hasSpec,
        companyId: row.companyId,
        remark: row.remark
    };
    let res = !row.id ? await ADD_MATERIAL(addParams) : await UPDATE_MATERIAL({ ...row });
    if (res.data.code === 0) {
        window.$message.success(!row.id ? "新增成功" : "修改成功");
        onSearch();
    } else {
        window.$message.error(res.data.msg);
    }
};

let deleteTableItem = (row: RowProps) => {
    window.$dialog.warning({
        title: "提示",
        content: "确定要删除吗？",
        positiveText: "确定",
        negativeText: "取消",
        onPositiveClick: () => {
            tableData.value.splice(tableData.value.indexOf(row), 1);
        }
    });
};

// 搜索
let onSearch = () => {
    // 2023年10月9日20:10:04填报关闭分页
    // tablePagination.page = 1;
    // tablePagination.pageSize = 10;
    getTableData();
};

// 查看规格
let specificationsModal = ref<{ show: boolean; configData: Record<string, any> }>({
    show: false,
    configData: {}
});

let openSpecificationsModal = (row: RowProps) => {
    specificationsModal.value.show = true;
    specificationsModal.value.configData = row;
};

// 批量修改
let onBatchSave = () => {
    window.$dialog.warning({
        title: "提示",
        content: "确定要保存所有修改吗？",
        positiveText: "确定",
        negativeText: "取消",
        onPositiveClick: () => {
            BATCH_UPDATE_MATERIAL_LIST(tableData.value).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("保存成功");
                    onSearch();
                } else window.$message.error(res.data.msg);
            });
        }
    });
};

// 批量删除
let onDelete = (id?: string | number) => {
    if (!id && tableSelection.value.length < 1) {
        window.$message.error("请选择要删除的数据");
        return false;
    }
    window.$dialog.warning({
        title: "警告",
        content: `确定删除${id ? "该" : "选中"}原材料吗？`,
        positiveText: "删除",
        negativeText: "取消",
        onPositiveClick: () => {
            let ids: (string | number)[];
            id ? (ids = [id]) : (ids = tableSelection.value);
            BATCH_DELETE_RAW_MATERIALS({ ids: ids.join(",") }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("删除成功");
                    onSearch();
                } else window.$message.error(res.data.msg);
            });
        }
    });
};

// 导入导出
let importModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

let openImportModal = () => {
    importModal.value.show = true;
};

let exportModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

let openExportModal = () => {
    exportModal.value.show = true;
};
</script>
