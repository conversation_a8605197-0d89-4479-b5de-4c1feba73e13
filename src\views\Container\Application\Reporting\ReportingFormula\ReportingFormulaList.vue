<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <div class="flex mt">
            <n-card class="flex-fixed-250" hoverable>
                <div class="flex-y-center mb">
                    <n-input v-model:value="treePattern" placeholder="搜索" />
                </div>
                <n-tree
                    v-model:selected-keys="treeSelectKeys"
                    :cancelable="false"
                    :data="treeData"
                    :pattern="treePattern"
                    :show-irrelevant-nodes="false"
                    block-line
                    children-field="childrenList"
                    default-expand-all
                    key-field="id"
                    label-field="categoryName"
                    selectable
                    @update:selected-keys="selectTreeNode"
                />
            </n-card>
            <n-card class="flex-1 ml" hoverable>
                <n-space class="mb">
                    <n-button secondary type="primary" @click="openEditModal()">新增配方</n-button>
                    <n-button secondary type="error" @click="onDelete()">批量删除</n-button>
                </n-space>
                <n-data-table
                    :columns="tableColumns as any"
                    :data="tableData"
                    :loading="tableLoading"
                    :pagination="tablePagination as any"
                    :row-key="tableRowKey"
                    :single-line="false"
                    bordered
                    remote
                    striped
                    @update:checked-row-keys="changeTableSelection"
                />
            </n-card>
            <FormulaEditModal
                v-model:show="editModal.show"
                :config-data="editModal.configData"
                @refresh="getTableData"
            />
            <FormulaHistoryModal v-model:show="historyModal.show" :config-data="historyModal.configData" />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from "vue";
import {
    BATCH_DELETE_FORMULA,
    GET_CONFIG_COMPANY_LIST,
    GET_FORMULA_CATEGORY_TREE_LIST,
    GET_FORMULA_PAGE_LIST,
    GET_MANUFACTURE_PAGE_LIST,
    GET_SEMI_MANUFACTURE_PAGE_LIST
} from "@/api/application/reporting";
import type { DataTableColumns } from "naive-ui";
import { NButton, NCollapse, NCollapseItem, NDataTable, NInput, NText } from "naive-ui";
import { useCommonTable } from "@/hooks";
import { FormulaEditModal, FormulaHistoryModal } from "./components";
import { TableActions } from "@/components/TableActions";
import { TableSearchbar } from "@/components/TableSearchbar";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";

onMounted(async () => {
    await getSearchOptions();
    await getTreeData();
    await getProductList();
    getTableData();
});

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { prop: "formulaName", type: "input", label: "配方名称" },
    { prop: "productModel", type: "input", label: "规格" },
    { prop: "companyId", type: "select", label: "所属公司" }
]);

let searchOptions = ref<TableSearchbarOptions>({
    companyId: []
});

let searchForm = ref<TableSearchbarData>({
    formulaName: null,
    productModel: null,
    companyId: null
});

let getSearchOptions = async () => {
    await GET_CONFIG_COMPANY_LIST({ needFill: 1 }).then((res) => {
        searchOptions.value.companyId = (res.data.data || []).map((item: any) => ({
            label: item.companyName,
            value: item.id
        }));
    });
};

let onSearch = () => {
    // 2023年10月9日20:10:04填报关闭分页
    // tablePagination.page = 1;
    // tablePagination.pageSize = 10;
    getTableData();
};

// 获取产成品/办成品列表
let productList = ref<any[]>([]);

let getProductList = async () => {
    let infinitePage = { current: 1, size: 9999 };
    let productArray: any[] = [];
    let semiProductArray: any[] = [];
    await GET_MANUFACTURE_PAGE_LIST(infinitePage).then((res) => (productArray = res.data.data.records));
    await GET_SEMI_MANUFACTURE_PAGE_LIST(infinitePage).then((res) => (semiProductArray = res.data.data.records));
    productList.value = [...productArray, ...semiProductArray];
};

// 树
let treeData = ref<any[]>([]);
let treePattern = ref("");
let treeSelectKeys = ref<(string | number)[]>([]);

let treeAddDisabled = (array: any[]): any[] => {
    return array.map((item) => {
        let newItem = { ...item };
        newItem.disabled = !!(newItem.childrenList && newItem.childrenList.length > 0);
        if (newItem.childrenList) {
            newItem.childrenList = treeAddDisabled(newItem.childrenList);
        }
        return newItem;
    });
};

let findLastLevel = (array: any[]): any => {
    let lastLevel = array.find((item) => item.childrenList && item.childrenList.length > 0);
    if (lastLevel) {
        return findLastLevel(lastLevel.childrenList);
    } else {
        return array;
    }
};

let getTreeData = async () => {
    await GET_FORMULA_CATEGORY_TREE_LIST({}).then((res) => {
        if (res.data.code === 0) {
            // treeData.value = treeAddDisabled(res.data.data ?? []);
            // treeSelectKeys.value = [findLastLevel(treeData.value)[0].id];
            treeData.value = [{ id: null, categoryName: "全部配方" }, ...res.data.data];
            treeSelectKeys.value = [treeData.value[0].id];
        }
    });
};

let selectTreeNode = (keys?: (string | number)[]) => {
    treeSelectKeys.value = keys ?? [];
    onSearch();
};

// 数据列表
interface RowProps {
    [key: string]: any;
}

let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    { type: "selection" },
    {
        type: "expand",
        expandable: (row) => row.formulaItemList && row.formulaItemList.length > 0,
        renderExpand: (row) => {
            return h(NCollapse, { defaultExpandedNames: ["1", "2"] }, () => [
                h(NCollapseItem, { title: "原材料列表", name: "1" }, () =>
                    h(NDataTable, {
                        bordered: true,
                        striped: true,
                        singleLine: true,
                        data: row.formulaItemList ?? [],
                        columns: [
                            {
                                title: "原材料",
                                key: "materialName",
                                align: "center",
                                render: (row) => `【${row.materialName}】${row.spec}`
                            },
                            { title: "原材料占比", key: "materialProportion", align: "center" },
                            { title: "原材料价格", key: "unitPrice", align: "center" },
                            { title: "原材料成本", key: "materialCost", align: "center" },
                            {
                                title: "状态",
                                align: "center",
                                key: "specDelFlag",
                                width: "100",
                                render: (row) => {
                                    if (row.specDelFlag === 1) {
                                        return h(NText, { type: "error" }, () => "已删除");
                                    } else {
                                        return h(NText, { type: "primary" }, () => "正常");
                                    }
                                }
                            }
                        ]
                    })
                ),
                h(NCollapseItem, { title: "半成品列表", name: "2" }, () =>
                    h(NDataTable, {
                        bordered: true,
                        striped: true,
                        singleLine: true,
                        data: row.formulaSemiItemList ?? [],
                        columns: [
                            { title: "半成品", key: "semiProductModel", align: "center" },
                            { title: "半成品占比", key: "semiManufactureProportion", align: "center" },
                            {
                                title: "状态",
                                align: "center",
                                key: "specDelFlag",
                                width: "100",
                                render: (row) => {
                                    if (row.specDelFlag === 1) {
                                        return h(NText, { type: "error" }, () => "已删除");
                                    } else {
                                        return h(NText, { type: "primary" }, () => "正常");
                                    }
                                }
                            }
                        ]
                    })
                )
            ]);
        }
    },
    {
        title: "配方名称",
        key: "formulaName",
        align: "center"
    },
    {
        title: "绑定的规格型号",
        key: "bindContentId",
        align: "center",
        render: (row) => {
            if (row.isBind === 1) {
                let stringArray = productList.value
                    .filter((item) => (row.bindContentId ?? []).includes(item.id))
                    .map((item) => item.productModel);
                if (stringArray && stringArray.length > 0) {
                    return stringArray.join("、");
                } else return "暂无";
            } else return "暂无";
        }
    },
    {
        title: "配方分类",
        key: "categoryName",
        align: "center",
        render: (row) => {
            return row.categoryName ?? "暂未分类";
        }
    },
    {
        title: "所属公司",
        align: "center",
        key: "companyName",
        render: (row) => {
            return row.companyName ?? "暂无";
        }
    },
    // {
    //     title: "原材料累计占比",
    //     key: "totalAmount",
    //     align: "center"
    // },
    {
        title: "原材料累计成本（元）",
        key: "totalCost",
        align: "center"
    },
    {
        title: "备注",
        key: "remark",
        align: "center"
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: 260,
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "编辑信息",
                        tertiary: true,
                        type: "primary",
                        onClick: () => openEditModal(row)
                    },
                    {
                        label: "历史版本",
                        tertiary: true,
                        type: "warning",
                        onClick: () => openHistoryModal(row)
                    },
                    {
                        label: "删除",
                        tertiary: true,
                        type: "error",
                        onClick: () => onDelete(row.id)
                    }
                ]
            });
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    GET_FORMULA_PAGE_LIST({
        ...searchForm.value,
        current: tablePagination.page,
        size: tablePagination.pageSize,
        categoryId: treeSelectKeys.value[0] ?? null
    }).then((res) => {
        tableData.value = res.data.data.records;
        tablePagination.itemCount = res.data.data.total;
        tableLoading.value = false;
    });
};

// 新增编辑
let editModal = ref<{ show: boolean; configData: Record<string, any> }>({
    show: false,
    configData: {}
});

let openEditModal = (row?: RowProps) => {
    editModal.value.show = true;
    editModal.value.configData = { ...row, categoryId: treeSelectKeys.value[0] } ?? {
        categoryId: treeSelectKeys.value[0]
    };
};

// 历史记录
let historyModal = ref<{ show: boolean; configData: Record<string, any> }>({
    show: false,
    configData: {}
});

let openHistoryModal = (row: RowProps) => {
    historyModal.value.show = true;
    historyModal.value.configData = row;
};

// 批量删除
let onDelete = (id?: string | number) => {
    if (!id && tableSelection.value.length < 1) {
        window.$message.error("请选择要删除的数据");
        return false;
    }
    window.$dialog.warning({
        title: "警告",
        content: `确定删除${id ? "该" : "选中"}配方吗？`,
        positiveText: "删除",
        negativeText: "取消",
        onPositiveClick: () => {
            let ids: (string | number)[];
            id ? (ids = [id]) : (ids = tableSelection.value);
            BATCH_DELETE_FORMULA({ ids: ids.join(",") }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("删除成功");
                    onSearch();
                    if (!id) {
                        location.reload();
                    }
                } else window.$message.error(res.data.msg);
            });
        }
    });
};
</script>
