export interface RosterProps<T = string | number | null> {
    archivesId?: T; // 花名册id
    userId?: T; // 用户id
    avatar?: T; // 头像
    trueName?: string | null; // 姓名
    nickname?: T; // 昵称
    postGroupValue?: T; // 职位组
    companyId?: T; // 公司id
    company?: T; // 公司名称
    deptId?: T; // 部门
    timeOfEntry?: T; // 入职时间
    phone?: T; // 联系电话
    remark?: T; // 备注
    showOrder?: T; // 排序
    sex?: T; // 性别
    dateOfBirth?: T; // 出生日期
    age?: T; // 年龄
    idNumber?: T; // 身份证号
    email?: T; // 邮箱
    individualResume?: T; // 个人简介
    nativePlace?: T; // 籍贯
    politicsStatus?: T; // 政治面貌
    maritalStatus?: T; // 婚姻状况
    typeOfRegisteredPermanentResidence?: T; // 户口类型
    physicalCondition?: T; // 健康状况
    timeForPhysicalExamination?: T; // 体检时间
    healthInsuranceStatus?: T; // 参保状态
    insuredEntity?: T; // 参保单位
    whetherThereIsProvidentFund?: T; // 有无公积金
    providentFundParticipatingUnits?: T; // 公积金参保单位
    vehicle?: T; // 交通工具
    licenseNumber?: T; // 车牌号码
    theTitleOfATechnicalPost?: T; // 职称
    professionalQualification?: T; // 职业资格
    workingYears?: T; // 工龄
    previousEmployerAndPosition?: T; // 原任职单位及岗位
    howToApply?: T; // 应聘方式
    educationBackground?: T; // 学历
    degree?: T; // 学位
    graduateInstitutions?: T; // 毕业院校
    majorOfStudy?: T; // 所学专业
    foreignLanguageLevel?: T; // 外语水平
    computerSkill?: T; // 计算机水平
    username?: T; // 工号
    codeOfFile?: T; // 档案编码
    statusOfEmployees?: T; // 员工状态
    contractNo?: T; // 合同编号
    currentSalaryScale?: T; // 当前薪资标准
    jobCategory?: T; // 职类
    typeOfJob?: T; // 岗位类型
    nameOfThePost?: T; // 岗位名称
    post?: T; // 职级
    rank?: T; // 薪资级别
    contactAddress?: T; // 家庭住址
    familyBackground?: T; // 家庭情况
    emergencyContact?: T; // 紧急联系人
    contactRelation?: T; // 联系人关系
    phoneNumberOfTheContact?: T; // 联系人电话
    typeOfEmployee?: T; // 员工类型
    lockFlag?: T; // 锁定标志
    /*
     * 以下为展示字段
     */
    deptName?: T; // 部门名称
    postName?: T; // 职级名称
    rankPostName?: T; // 薪资级别名称
    jobCategoryPostName?: T; // 职类名称
    typeOfJobPostName?: T; // 岗位类型名称
}
