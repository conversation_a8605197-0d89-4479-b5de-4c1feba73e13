import { defineComponent, h, ref, watchEffect } from "vue";
import { GET_DRAWBENCH_SCHEDULE_DETAIL_LIST, POST_DRAWBENCH_SCHEDULE_FILL_FINISH } from "@/api/application/plasticMes";
import type { DataTableColumns } from "naive-ui";
import { TableActions } from "@/components/TableActions";
import { LineDetail } from "../components";

export default defineComponent({
    name: "PlasticMesDrawbenchProductionScheduleLineList",
    props: {
        scheduleId: { type: String }
    },
    setup(props, { emit }) {
        const lineList = ref<any[]>([]);

        const getLineList = () => {
            GET_DRAWBENCH_SCHEDULE_DETAIL_LIST({ id: props.scheduleId }).then((res) => {
                lineList.value = res.data.data ?? [];
            });
        };

        const tableColumns = ref<DataTableColumns<any>>([
            { title: "ID", key: "id", align: "center" },
            {
                title: "拉丝类型",
                key: "ptTypeName",
                align: "center",
                render: (row) => <n-text type="info">{row.ptTypeName || "/"}</n-text>
            },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 220,
                render: (row) => {
                    return h(TableActions, {
                        type: "button",
                        buttonActions: [
                            {
                                label: "查看详情",
                                tertiary: true,
                                onClick: () => openDetailModal(row)
                            },
                            {
                                label: "完成拉丝",
                                disabled: () => row.fillState === 2,
                                tertiary: true,
                                type: "success",
                                onClick: () => {
                                    window.$dialog.warning({
                                        title: "提示",
                                        content: "是否确认完成拉丝？",
                                        positiveText: "确定",
                                        negativeText: "取消",
                                        onPositiveClick: () => {
                                            POST_DRAWBENCH_SCHEDULE_FILL_FINISH({
                                                id: row.id
                                            }).then((res) => {
                                                if (res.data.code === 0) {
                                                    getLineList();
                                                } else {
                                                    window.$message.error(res.data.msg ?? "操作失败");
                                                }
                                            });
                                        }
                                    });
                                }
                            }
                        ]
                    });
                }
            }
        ]);

        const detailModal = ref<{ show: boolean; configData: any }>({ show: false, configData: {} });

        const openDetailModal = (row: any) => {
            detailModal.value.configData = row;
            detailModal.value.show = true;
        };

        watchEffect(async () => {
            if (props.scheduleId) getLineList();
        });

        return () => (
            <div>
                <n-data-table
                    columns={tableColumns.value}
                    data={lineList.value ?? []}
                    single-line={false}
                    bordered
                    remote
                    striped
                    max-height={250}
                />
                <LineDetail v-model:show={detailModal.value.show} config-data={detailModal.value.configData} />
            </div>
        );
    }
}); 