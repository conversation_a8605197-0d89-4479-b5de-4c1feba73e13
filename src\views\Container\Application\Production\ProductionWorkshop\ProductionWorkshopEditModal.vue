<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card
                :title="configData.id ? '编辑车间' : '新增车间'"
                class="w-600px"
                closable
                @close="changeModalShow(false)"
            >
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-grid :cols="12" x-gap="16">
                        <n-form-item-gi :span="12" label="业务类型" path="businessType">
                            <n-radio-group v-model:value="formData.businessType">
                                <n-space>
                                    <n-radio :value="1" label="电力" />
                                    <n-radio :value="2" label="塑业" />
                                </n-space>
                            </n-radio-group>
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="所属公司" path="companyId">
                            <n-cascader
                                :disabled="!!configData.id"
                                v-model:value="formData.companyId"
                                :options="companyIdOptions"
                                class="w-100%"
                                clearable
                                filterable
                                label-field="company"
                                placeholder="请选择所属公司"
                                value-field="id"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="车间名称" path="workshopName">
                            <n-input
                                v-model:value="formData.workshopName"
                                class="w-100%"
                                clearable
                                placeholder="请输入车间名称"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="车间主任" path="workshopDirector">
                            <UserSelector
                                v-model:value="formData.workshopDirector"
                                class="w-100%"
                                clearable
                                :multiple="false"
                                key-name="username"
                                placeholder="请选择车间主任"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12">
                            <n-space>
                                <n-button type="primary" @click="onSubmit">提交</n-button>
                                <n-button @click="changeModalShow(false)">取消</n-button>
                            </n-space>
                        </n-form-item-gi>
                    </n-grid>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref, watchEffect } from "vue";
import type { FormInst } from "naive-ui";
import { NInput } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { GET_YD_COMPANY_LIST } from "@/api/permission";
import { ADD_WORK_SHOP, UPDATE_WORK_SHOP } from "@/api/application/production";
import { UserSelector } from "@/components/UserSelector";

let props = withDefaults(defineProps<{ show: boolean; configData: UnKnownObject }>(), { show: () => false });

let emits = defineEmits(["update:show", "refresh"]);

let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

let changeModalShow = (show: boolean) => {
    emits("update:show", show);
    clearFrom();
};

onMounted(async () => {
    await getCompanyIdOptions();
});

// 获取公司选项
let companyIdOptions = ref<any[]>([]);

let getCompanyIdOptions = async () => {
    await GET_YD_COMPANY_LIST({}).then((res) => {
        companyIdOptions.value = res.data.data || [];
    });
};

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {
    businessType: { required: true, message: "请选择业务类型", trigger: ["blur", "change"], type: "number" },
    companyId: { required: true, message: "请选择所属公司", trigger: ["blur", "change"] },
    workshopName: { required: true, message: "请输入车间名称", trigger: ["input", "blur"] },
    workshopDirector: { required: false, message: "请输入车间主任", trigger: ["input", "blur"] }
};

// 表单数据
interface FormDataProps {
    [key: string]: any;
}

let initFormData: FormDataProps = {
    businessType: 1,
    companyId: null,
    workshopName: "",
    workshopDirector: ""
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

let getDetail = () => {
    formData.value = {
        id: props.configData.id,
        businessType: props.configData.businessType,
        companyId: props.configData.companyId,
        workshopName: props.configData.workshopName,
        workshopDirector: props.configData.workshopDirector
    };
};

watchEffect(() => {
    if (props.show && props.configData.id) getDetail();
});

// 提交
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;

    if (props.configData.id) {
        UPDATE_WORK_SHOP({ id: props.configData.id, ...formData.value }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("修改成功");
                changeModalShow(false);
                emits("refresh");
            } else window.$message.error(res.data.message);
        });
    } else {
        ADD_WORK_SHOP({ ...formData.value }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("新增成功");
                changeModalShow(false);
                emits("refresh");
            } else window.$message.error(res.data.message);
        });
    }
};
</script>
