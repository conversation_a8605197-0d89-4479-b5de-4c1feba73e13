<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card :title="id ? '编辑组织架构' : '新增组织架构'" class="w-600px" closable @close="closeModal">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-form-item label="上级组织架构" path="parentId">
                        <n-tree-select
                            v-model:value="formData.parentId"
                            :options="parentIdOptions"
                            clearable
                            filterable
                            key-field="id"
                            label-field="name"
                            placeholder="请选择上级组织架构"
                        />
                    </n-form-item>
                    <n-form-item label="组织架构名称" path="name">
                        <n-input
                            v-model:value="formData.name"
                            class="w-100%"
                            clearable
                            placeholder="请输入组织架构名称"
                        />
                    </n-form-item>
                    <n-form-item label="组织架构类型" path="type">
                        <n-select
                            v-model:value="formData.type"
                            :options="typeOptions"
                            placeholder="请选择组织架构类型"
                        />
                    </n-form-item>
                    <n-form-item label="组织架构排序" path="sortOrder">
                        <n-input-number v-model:value="formData.sortOrder" class="w-100%" />
                    </n-form-item>
                    <n-form-item label="负责人" path="responsiblePerson">
                        <UserSelector
                            v-model:value="formData.responsiblePerson"
                            :multiple="false"
                            class="w-100%"
                            clearable
                            key-name="username"
                            placeholder="请选择负责人"
                        />
                    </n-form-item>
                    <n-form-item label="联系方式" path="contact">
                        <n-input
                            v-model:value="formData.contact"
                            class="w-100%"
                            clearable
                            placeholder="请输入联系方式"
                        />
                    </n-form-item>
                    <n-form-item label="限定权限范围" path="dataPermissions">
                        <n-switch v-model:value="formData.dataPermissions" checked-value="0" unchecked-value="1" />
                    </n-form-item>
                    <n-form-item label="是否隐藏" path="status">
                        <n-switch v-model:value="formData.status" checked-value="0" unchecked-value="1" />
                    </n-form-item>
                    <n-form-item>
                        <n-space>
                            <n-button type="primary" @click="onSubmit">提交</n-button>
                            <n-button @click="closeModal">取消</n-button>
                        </n-space>
                    </n-form-item>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import type { FormInst } from "naive-ui";
import { ADD_DEPT, GET_DEPT_BY_ID, GET_DEPT_TREE, UPDATE_DEPT } from "@/api/permission";
import { cloneDeep } from "lodash-es";
import { useDicts } from "@/hooks";
import { UserSelector } from "@/components/UserSelector";

let { getDict } = useDicts();

let props = defineProps({
    show: { type: Boolean, default: false },
    id: { type: [String, Number] as PropType<any> }
});

let emits = defineEmits(["update:show", "refresh"]);

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {
    parentId: { required: true, message: "请选择上级组织架构", trigger: ["blur", "change"] },
    name: {
        required: true,
        message: "请输入组织架构名称",
        trigger: ["input", "blur"]
    },
    type: { required: true, message: "请选择组织架构类型", trigger: ["blur", "change"] }
};

// 表单数据
interface FormDataProps<T = string | null> {
    parentId: T;
    name: T;
    type: T;
    sortOrder: number;
    responsiblePerson: T;
    contact: T;
    dataPermissions: T;
    status: T;
}

let initFormData: FormDataProps = {
    parentId: null,
    name: null,
    type: null,
    sortOrder: 0,
    responsiblePerson: null,
    contact: null,
    dataPermissions: "1",
    status: "1"
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 编辑时获取详情
watch(
    () => ({ id: props.id, show: props.show }),
    (newVal) => {
        if (newVal.show) getOptions();
        if (newVal.show && newVal.id) getDetail();
    },
    { deep: true }
);

// 获取详情
let getDetail = () => {
    GET_DEPT_BY_ID({ id: props.id }).then((res) => {
        let rowItem: FormDataProps = res.data.data;
        formData.value = {
            parentId: rowItem.parentId,
            name: rowItem.name,
            type: rowItem.type,
            sortOrder: rowItem.sortOrder,
            responsiblePerson: rowItem.responsiblePerson,
            contact: rowItem.contact,
            dataPermissions: rowItem.dataPermissions || "1",
            status: rowItem.status || "1"
        };
    });
};

// 表单选项
interface ParentIdOptionsProps {
    name: string;
    id: string | number;
    children?: ParentIdOptionsProps[];
}

let parentIdOptions = ref<ParentIdOptionsProps[]>([]);
let typeOptions = ref([]);

let getOptions = async () => {
    typeOptions.value = await getDict("dept_type");
    GET_DEPT_TREE({ deptName: "" }).then((res) => {
        // parentIdOptions.value = [{ name: "浙江永达电力实业股份有限公司", id: "0", children: res.data.data || [] }];
        parentIdOptions.value = res.data.data || [];
    });
};

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    emits("update:show", false);
};

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    if (!props.id) {
        ADD_DEPT({
            ...formData.value
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("新增成功");
                closeModal();
                emits("refresh");
            }
        });
    } else {
        UPDATE_DEPT({
            deptId: props.id,
            ...formData.value
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("编辑成功");
                closeModal();
                emits("refresh");
            }
        });
    }
};
</script>
