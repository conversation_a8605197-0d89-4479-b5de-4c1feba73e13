<template>
    <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
        <n-card class="w-800px" closable title="标书制作完成确认" @close="changeModalShow(false)">
            <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                <n-grid :cols="24" x-gap="16">
                    <n-form-item-gi :span="12" label="项目编号">
                        <div>{{ props.configData.projectNumber }}</div>
                    </n-form-item-gi>
                    <n-form-item-gi :span="12" label="实际完成时间" path="produceFinishTime">
                        <n-date-picker
                            v-model:formatted-value="formData.produceFinishTime"
                            class="w-100%"
                            clearable
                            placeholder="请选择实际完成时间"
                            type="datetime"
                            value-format="yyyy-MM-dd HH:mm:ss"
                        />
                    </n-form-item-gi>
                    <n-form-item-gi :span="12" label="制作人" path="producer">
                        <UserSelector
                            v-model:value="formData.producer"
                            :multiple="false"
                            class="w-100%"
                            clearable
                            key-name="username"
                            placeholder="请选择制作人"
                        />
                    </n-form-item-gi>
                    <n-form-item-gi :span="24" label="附件" path="producer">
                        <FileValueUploader v-model:value="formData.fileList" value-key="fileId" />
                    </n-form-item-gi>
                    <n-form-item-gi :span="24">
                        <n-space>
                            <n-button type="primary" @click="onSubmit">提交</n-button>
                            <n-button @click="changeModalShow(false)">取消</n-button>
                        </n-space>
                    </n-form-item-gi>
                </n-grid>
            </n-form>
        </n-card>
    </n-modal>
</template>

<script lang="ts" setup>
import { computed, ref } from "vue";
import { cloneDeep } from "lodash-es";
import type { FormInst } from "naive-ui";
import { UserSelector } from "@/components/UserSelector";
import { FileValueUploader } from "@/components/Uploader";
import { POST_BID_COMPLETE_CONFIRM } from "@/api/application/power";

let props = withDefaults(defineProps<{ show: boolean; configData: UnKnownObject }>(), { show: () => false });

let emits = defineEmits(["update:show", "refresh"]);

let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

let changeModalShow = (show: boolean) => emits("update:show", show);

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {
    produceFinishTime: [{ required: true, message: "请选择实际完成时间", trigger: ["blur", "change"] }],
    producer: [{ required: true, message: "请选择制作人", trigger: ["blur", "change"] }]
};

// 表单数据
interface FormDataProps {
    [key: string]: any;
}

let initFormData: FormDataProps = {
    produceFinishTime: null,
    producer: null,
    fileList: null
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    POST_BID_COMPLETE_CONFIRM({
        projectId: props.configData.projectId,
        nodeKey: props.configData.nextNodeKey,
        ...formData.value
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("提交成功");
            changeModalShow(false);
            emits("refresh");
        }
    });
};
</script>
