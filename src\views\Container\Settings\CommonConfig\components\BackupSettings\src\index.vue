<template>
    <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
        <n-card class="w-600px" closable title="新增任务" @close="closeModal">
            <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                <n-grid cols="12" x-gap="16">
                    <n-form-item-gi :span="12" label="任务名称" path="name">
                        <n-input v-model:value="formData.name" class="w-100%" clearable placeholder="请输入任务名称" />
                    </n-form-item-gi>
                    <n-form-item-gi :span="12" label="执行日期" path="week">
                        <n-select
                            v-model:value="formData.week"
                            :options="[
                                { label: '星期一', value: 1 },
                                { label: '星期二', value: 2 },
                                { label: '星期三', value: 3 },
                                { label: '星期四', value: 4 },
                                { label: '星期五', value: 5 },
                                { label: '星期六', value: 6 },
                                { label: '星期日', value: 7 }
                            ]"
                            clearable
                            filterable
                            multiple
                            placeholder="请选择执行日期"
                        />
                    </n-form-item-gi>
                    <n-form-item-gi :span="12" label="执行时间" path="time">
                        <n-time-picker
                            v-model:formatted-value="formData.time"
                            class="w-100%"
                            clearable
                            placeholder="请选择执行时间"
                            value-format="HH:mm:ss"
                        />
                    </n-form-item-gi>
                    <n-form-item-gi :span="12" label="备注" path="remark">
                        <n-input
                            v-model:value="formData.remark"
                            :autosize="{ minRows: 3 }"
                            class="w-100%"
                            clearable
                            placeholder="请输入备注"
                            type="textarea"
                        />
                    </n-form-item-gi>
                    <n-form-item-gi :span="12">
                        <n-space>
                            <n-button type="primary" @click="onSubmit">提交</n-button>
                            <n-button @click="closeModal">取消</n-button>
                        </n-space>
                    </n-form-item-gi>
                </n-grid>
            </n-form>
        </n-card>
    </n-modal>
</template>

<script lang="ts" setup>
import { computed, ref, watchEffect } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";

const props = withDefaults(defineProps<{ show: boolean; configData: UnKnownObject }>(), { show: () => false });

const emits = defineEmits(["update:show", "refresh"]);

const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

const changeModalShow = (show: boolean) => emits("update:show", show);

const closeModal = () => {
    changeModalShow(false);
    clearFrom();
};

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
const formRules = {
    name: { required: true, message: "请输入任务名称", trigger: ["input", "blur"] },
    week: { required: true, message: "请选择执行日期", trigger: ["blur", "change"], type: "array" },
    time: { required: true, message: "请选择执行时间", trigger: ["blur", "change"] }
};

// 表单数据
interface FormDataProps {
    [key: string]: any;
}

const initFormData: FormDataProps = {
    name: "", // 任务名称
    week: [], // 执行日期
    time: null, // 执行时间
    remark: "" // 备注
};

const formData = ref(cloneDeep(initFormData));

const clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

const getFormOptions = async () => {};

const onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;

    window.$message.success("操作成功");
    closeModal();
    emits("refresh");
};

watchEffect(async () => {
    if (show.value) {
        await getFormOptions();
    }
});
</script>
