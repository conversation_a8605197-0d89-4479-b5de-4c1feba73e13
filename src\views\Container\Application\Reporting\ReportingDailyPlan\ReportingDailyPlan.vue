<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <div class="flex mt">
            <n-card class="flex-1 ml" hoverable>
                <n-data-table
                    :columns="tableColumns as any"
                    :data="tableData"
                    :loading="tableLoading"
                    :pagination="tablePagination as any"
                    :row-key="tableRowKey"
                    :single-line="false"
                    bordered
                    remote
                    striped
                    @update:checked-row-keys="changeTableSelection"
                />
            </n-card>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { Ref, h, onMounted, reactive, ref, watch } from "vue";
import {
    GET_CONFIG_COMPANY_LIST,
    GET_CONFIG_WORK_GROUP_LIST,
    GET_DAILY_PLAN_LIST
} from "@/api/application/reporting";
import type { DataTableColumns } from "naive-ui";
import { NButton, NCollapse, NCollapseItem, NDataTable, NInput, NText } from "naive-ui";
import { useCommonTable } from "@/hooks";
import { TableActions } from "@/components/TableActions";
import { TableSearchbar } from "@/components/TableSearchbar";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";

onMounted(async () => {
    await getSearchOptions();
    getTableData();
});

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { 
        prop: "companyId", 
        type: "select", 
        label: "所属公司"
    },
    { 
        prop: "workGroupId", 
        type: "select", 
        label: "班组" ,
        span: 2
    },
    {
        prop: "planBeginDate",
        type: "date",
        label: "开始日期",
        dateFormat: "yyyy-MM-dd"
    },
    {
        prop: "planEndDate",
        type: "date",
        label: "结束日期",
        dateFormat: "yyyy-MM-dd"
    }
]);

let searchOptions = ref<TableSearchbarOptions>({
    companyId: []
});

let searchForm = ref<TableSearchbarData>({
    companyId: null
});

let getSearchOptions = async () => {
    await GET_CONFIG_COMPANY_LIST({ needFill: 1 }).then((res) => {
        searchOptions.value.companyId = (res.data.data || []).map((item: any) => ({
            label: item.companyName,
            value: item.id
        }));
    });

    await GET_CONFIG_WORK_GROUP_LIST({
        companyId: searchOptions.value.companyId
    }).then((res) => {
        searchOptions.value.workGroupId = (res.data.data || []).map((item: any) => ({
            label: item.companyName+"-"+item.workshopName+"-"+item.groupName,
            value: item.id
        }));
    });
};


let onSearch = () => {
    tablePagination.page = 1;
    tablePagination.pageSize = 10;
    getTableData();
};

// 数据列表
interface RowProps {
    [key: string]: any;
}

let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    { type: "selection" },
    {
        type: "expand",
        expandable: (row) => row.dailyAmountList && row.dailyAmountList.length > 0,
        renderExpand: (row) => {
            return h(NCollapse, { defaultExpandedNames: ["1"] }, () => [
                h(NCollapseItem, { title: "计划项", name: "1" }, () =>
                    h(NDataTable, {
                        bordered: true,
                        striped: true,
                        singleLine: true,
                        data: row.dailyAmountList ?? [],
                        columns: [
                            { 
                                title: "类型", 
                                key: "contentType", 
                                align: "center",
                                render: (row) => {
                                    if(row.contentType==1){
                                        return "产成品";
                                    }else if(row.contentType==2){
                                        return "半成品";
                                    }
                                }
                            },
                            { title: "名称", key: "poleName", align: "center" },
                            { title: "规格", key: "productModel", align: "center" },
                            { title: "计划量", key: "planAmount", align: "center" },
                            { title: "产量", key: "productAmount", align: "center" },
                            { title: "废品量", key: "wasteAmount", align: "center" },
                            { title: "废品产生时间", key: "wasteDate", align: "center" }
                        ]
                    })
                )
            ]);
        }
    },
    {
        title: "所属公司",
        key: "companyName",
        align: "center"
    },
    {
        title: "所属班组",
        key: "workGroupName",
        align: "center",
        render: (row) => {
            return row.workshopName+"-"+row.workGroupName;
        }
    },
    {
        title: "日期",
        key: "planDate",
        align: "center"
    },
    {
        title: "计划量",
        key: "totalPlanAmount",
        align: "center"
    },
    {
        title: "产量",
        key: "totalProductAmount",
        align: "center"
    },
    {
        title: "废品量",
        key: "totalWasteAmount",
        align: "center"
    },
    {
        title: "混凝土用量",
        key: "concreteTrueAmount",
        align: "center"
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    GET_DAILY_PLAN_LIST({
        ...searchForm.value,
        current: tablePagination.page,
        size: tablePagination.pageSize
    }).then((res) => {
        tableData.value = res.data.data.records;
        tablePagination.itemCount = res.data.data.total;
        tableLoading.value = false;
    });
};
</script>
