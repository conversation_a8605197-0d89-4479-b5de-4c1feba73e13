<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-95vw" closable content-style="padding:0" title="订单详情" @close="changeModalShow(false)">
                <div class="h-80vh">
                    <n-scrollbar trigger="hover">
                        <div class="p-20px pt-0">
                            <n-card class="pb-20px" hoverable>
                                <n-breadcrumb>
                                    <n-breadcrumb-item v-for="item in $route.meta.breadcrumb">
                                        {{ item }}
                                    </n-breadcrumb-item>
                                    <n-breadcrumb-item>项目详情</n-breadcrumb-item>
                                    <n-breadcrumb-item>订单详情</n-breadcrumb-item>
                                </n-breadcrumb>
                                <div class="flex-y-center mt-20px">
                                    <div class="flex-1">
                                        <n-grid :cols="24" :x-gap="16" :y-gap="16" class="mt-20px">
                                            <n-grid-item :span="6" class="text-14px">
                                                订单编号：{{ formData.contractNumber }}
                                            </n-grid-item>
                                            <n-grid-item :span="6" class="text-14px">
                                                订单状态：
                                                <span
                                                    :style="`color:${
                                                        dictValueToAll(formData.nodeStatus, 'node_status').color || ''
                                                    }`"
                                                >
                                                    {{ dictValueToLabel(formData.nodeStatus, "node_status") }}
                                                </span>
                                            </n-grid-item>
                                            <n-grid-item :span="6" class="text-14px">
                                                创建时间：{{ formData.createTime }}
                                            </n-grid-item>
                                            <!--<n-grid-item :span="6" class="text-14px">-->
                                            <!--    订单金额：-->
                                            <!--    <n-text type="info">0（万元）</n-text>-->
                                            <!--</n-grid-item>-->
                                            <n-grid-item :span="6" class="text-14px">
                                                已开票金额：
                                                <n-text type="info">{{ formData.totalInvoicing || 0 }}（万元）</n-text>
                                            </n-grid-item>
                                            <n-grid-item :span="6" class="text-14px">
                                                订单金额：
                                                <n-text type="info">{{ formData.contractAmount || 0 }}（万元）</n-text>
                                            </n-grid-item>
                                            <n-grid-item :span="6" class="text-14px">
                                                已回款金额：
                                                <n-text type="info">{{ formData.totalReturn || 0 }}（万元）</n-text>
                                            </n-grid-item>
                                            <n-grid-item :span="6" class="text-14px">
                                                待回款金额：
                                                <n-text type="error">{{ formData.needReturn || 0 }}（万元）</n-text>
                                            </n-grid-item>
                                        </n-grid>
                                    </div>
                                </div>
                            </n-card>
                            <n-card class="w-100% mt-15px" hoverable>
                                <n-tabs pane-class="pt!" type="line">
                                    <n-tab-pane name="开票记录">
                                        <ProjectOrderInvoicingRecord :formData="formData" @refresh="onRefresh" />
                                    </n-tab-pane>
                                    <n-tab-pane name="回款记录">
                                        <ProjectOrderPaymentRecord :formData="formData" @refresh="onRefresh" />
                                    </n-tab-pane>
                                    <n-tab-pane name="规格列表">
                                        <ProjectOrderSpecRecord :formData="formData" @refresh="onRefresh" />
                                    </n-tab-pane>
                                </n-tabs>
                            </n-card>
                        </div>
                    </n-scrollbar>
                </div>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref, watchEffect } from "vue";
import { GET_CHILD_PROJECT_DETAIL_V2 } from "@/api/application/power";
import { useDicts } from "@/hooks";
import {
    ProjectOrderInvoicingRecord,
    ProjectOrderPaymentRecord,
    ProjectOrderSpecRecord
} from "../ProjectOrderDetailModal";

let props = withDefaults(defineProps<{ show: boolean; configData: UnKnownObject }>(), { show: () => false });

let emits = defineEmits(["update:show", "refresh"]);

let onRefresh = () => emits("refresh");

let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

let changeModalShow = (show: boolean) => emits("update:show", show);

// 字典操作
let { getDictLibs, dictValueToLabel, dictValueToAll } = useDicts();

let setDictLibs = async () => {
    let dictName = ["project_status", "node_status", "project_type"];
    await getDictLibs(dictName);
};

watchEffect(async () => {
    if (props.show) await setDictLibs();
    if (props.show && props.configData.projectId) await getDetail();
});

// 表单数据
interface FormDataProps {
    [key: string]: any;
}

let formData = ref<FormDataProps>({});

// 获取详情
let getDetail = async () => {
    await GET_CHILD_PROJECT_DETAIL_V2({ id: props.configData.childProjectId }).then(
        (r) => (formData.value = r.data.data)
    );
};
</script>
