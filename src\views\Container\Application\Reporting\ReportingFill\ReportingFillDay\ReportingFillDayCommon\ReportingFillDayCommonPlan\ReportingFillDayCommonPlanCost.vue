<template>
    <n-spin :show="loadingShow">
        <template #description>正在处理中，请耐心等候</template>
        <div class="flex">
            <n-card class="flex-fixed-200">
                <div class="flex-y-center mb">
                    <n-input v-model:value="treePattern" clearable placeholder="搜索" />
                </div>
                <n-tree
                    v-model:selected-keys="treeSelectKeys"
                    :cancelable="false"
                    :data="treeData"
                    :pattern="treePattern"
                    :show-irrelevant-nodes="false"
                    block-line
                    children-field="childrenList"
                    default-expand-all
                    key-field="id"
                    label-field="categoryName"
                    selectable
                    @update:selected-keys="selectTreeNode"
                />
            </n-card>
            <div class="flex-1 ml">
                <n-card>
                    <table-searchbar
                        auto-search
                        v-model:form="searchForm"
                        :config="searchConfig"
                        :options="searchOptions"
                        @search="onSearch"
                        @componentClick="onComponentClick"
                    />
                </n-card>
                <n-card class="mt">
                    <div class="flex-y-center mb">
                        <n-space>
                            <n-button secondary type="success" @click="saveTableData">保存填写</n-button>
                        </n-space>
                        <div class="h-100% flex-y-center ml-a" v-if="wageType === 1">
                            <div>
                                参考工资全部合计：<n-text type="primary">{{ wagesTotal.reference }}元</n-text>
                            </div>
                            <!--<div class="ml">-->
                            <!--    实际工资合计：<n-text type="primary">{{ wagesTotal.actual }}元</n-text>-->
                            <!--</div>-->
                        </div>
                    </div>
                    <n-data-table
                        v-if="wageType === 1"
                        :columns="tableColumnsQuota"
                        :data="tableData"
                        :loading="tableLoading"
                        :row-key="tableRowKey"
                        :single-line="false"
                        bordered
                        remote
                        striped
                        @update:checked-row-keys="changeTableSelection"
                    />
                    <n-data-table
                        v-else-if="wageType === 2"
                        :columns="tableColumnsNonQuota"
                        :data="tableData"
                        :loading="tableLoading"
                        :row-key="tableRowKey"
                        :single-line="false"
                        bordered
                        remote
                        striped
                        @update:checked-row-keys="changeTableSelection"
                    />
                </n-card>
            </div>
        </div>
    </n-spin>
</template>

<script lang="ts" setup>
import { h, onMounted, ref } from "vue";
import {
    GET_CONFIG_WORK_GROUP_LIST,
    GET_PLAN_NONQUOTA_COST_LIST,
    GET_PLAN_QUOTA_COST_LIST,
    GET_WAGE_CATEGORY_TREE_LIST,
    SAVE_PLAN_NONQUOTA_COST_LIST,
    SAVE_PLAN_QUOTA_COST_LIST
} from "@/api/application/reporting";
import type { DataTableColumns } from "naive-ui";
import { NButton, NInput, NInputGroup, NInputGroupLabel, NTooltip } from "naive-ui";
import { useCommonTable, useReportingTree } from "@/hooks";
import {
    TableSearchbar,
    TableSearchbarConfig,
    TableSearchbarData,
    TableSearchbarOptions
} from "@/components/TableSearchbar";
import dayjs from "dayjs";
import { useStoreReportingSearch } from "@/store";
import { useThrottleFn } from "@vueuse/core";

let storeReportingSearch = useStoreReportingSearch();

interface RowProps {
    [key: string]: any;
}

let loadingShow = ref(false);

onMounted(async () => {
    await getTreeData();
    await getWorkGroupIdOptions();
    getTableData();
});

// 工资类型（1：定额，2：非定额）
let wageType = ref<Nullable<number>>(null);

// 树相关操作
let { treeData, treePattern, treeSelectKeys, treeAddDisabled, findLastLevel } = useReportingTree();

let getTreeData = async () => {
    await GET_WAGE_CATEGORY_TREE_LIST({}).then((res) => {
        if (res.data.code === 0) {
            treeData.value = treeAddDisabled(res.data.data ?? []);
            treeSelectKeys.value = [findLastLevel(treeData.value)[0].id];
            wageType.value = findLastLevel(treeData.value)[0].wageType;
        }
    });
};

let selectTreeNode = (keys?: (string | number)[], option?: any[]) => {
    treeSelectKeys.value = keys ?? [];
    wageType.value = option?.[0].wageType;
    onSearch();
};

// 搜索相关操作
let getWorkGroupIdOptions = async () => {
    await GET_CONFIG_WORK_GROUP_LIST({}).then((res) => {
        searchOptions.value.workGroupId = (res.data.data ?? []).map((item: any) => ({
            label: item.companyName + "-" + item.workshopName + "-" + item.groupName,
            value: item.id
        }));
    });
    searchForm.value.workGroupId = searchOptions.value.workGroupId[0].value;
    searchForm.value.planDate = dayjs().format("YYYY-MM-DD");
    if (storeReportingSearch.getSearchForm.workGroupId) {
        searchForm.value.workGroupId = storeReportingSearch.getSearchForm.workGroupId;
    }
    if (storeReportingSearch.getSearchForm.planDate) {
        searchForm.value.planDate = storeReportingSearch.getSearchForm.planDate;
    }
};

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { label: "班组", type: "select", prop: "workGroupId", span: 2 },
    { label: "日期筛选", type: "date", dateFormat: "yyyy-MM-dd", prop: "planDate" }
]);

let searchOptions = ref<TableSearchbarOptions>({ workGroupId: [] });

let searchForm = ref<TableSearchbarData>({ workGroupId: null, planDate: null });

let onSearch = () => {
    if (!searchForm.value.workGroupId) return window.$message.error("请先选择班组！");
    if (!searchForm.value.planDate) return window.$message.error("请先选择日期！");
    storeReportingSearch.setSearchForm({
        workGroupId: searchForm.value.workGroupId ?? null,
        planDate: searchForm.value.planDate ?? null
    });
    getTableData();
};

// 搜索栏自动保存逻辑
let autoSave = useThrottleFn(async () => {
    if (wageType.value === 1) {
        SAVE_PLAN_QUOTA_COST_LIST({
            ...searchForm.value,
            dailyQuotaCostList: tableData.value
        }).then((res) => {
            if (res.data.code === 0) window.$message.success("自动保存成功");
        });
    } else if (wageType.value === 2) {
        SAVE_PLAN_NONQUOTA_COST_LIST({
            ...searchForm.value,
            dailyNonQuotaCostList: tableData.value
        }).then((res) => {
            if (res.data.code === 0) window.$message.success("自动保存成功");
        });
    }
}, 1000);

let onComponentClick = async (val: TableSearchbarData) => {
    // await autoSave();
};

// 数据列表
let { tableRowKey, tableData, tableLoading, changeTableSelection } = useCommonTable<RowProps>("id");

let tableColumnsQuota = ref<DataTableColumns<RowProps>>([
    { type: "selection" },
    { title: "名称", align: "center", key: "poleName" },
    { title: "规格型号", align: "center", key: "productModel" },
    { title: "定额单价(元)", align: "center", key: "quotaWage" },
    { title: "计划产量合计工资(元)", align: "center", key: "planQuotaCost" },
    {
        title: "实际单价(元)",
        align: "center",
        key: "planCost",
        render: (row) => {
            return h(
                NTooltip,
                {},
                {
                    trigger: () => {
                        return h(NInput, {
                            value: row.planCost,
                            onUpdateValue: (v) => (row.planCost = v),
                            onFocus: () => {
                                if (row.planCost === "0") row.planCost = "";
                            },
                            onBlur: () => {
                                if (!row.planCost) row.planCost = "0";
                            }
                        });
                    },
                    default: () => {
                        return row.planCost;
                    }
                }
            );
        }
    }
]);

let tableColumnsNonQuota = ref<DataTableColumns<RowProps>>([
    { type: "selection" },
    { title: "每日定额（元）", align: "center", key: "quotaWage" },
    {
        title: "计划工资（元）",
        align: "center",
        key: "planCost",
        render: (row) => {
            return h(
                NTooltip,
                {},
                {
                    trigger: () => {
                        return h(NInput, {
                            value: row.planCost,
                            onUpdateValue: (v) => (row.planCost = v),
                            onFocus: () => {
                                if (row.planCost === "0") row.planCost = "";
                            },
                            onBlur: () => {
                                if (!row.planCost) row.planCost = "0";
                            }
                        });
                    },
                    default: () => {
                        return row.planCost;
                    }
                }
            );
        }
    }
]);

// 参考工资合计、实际工资合计
let wagesTotal = ref<Record<"reference" | "actual", number>>({
    reference: 0,
    actual: 0
});

let getTableData = () => {
    tableLoading.value = true;
    if (wageType.value === 1) {
        GET_PLAN_QUOTA_COST_LIST({
            ...searchForm.value,
            wageCategoryId: treeSelectKeys.value[0]
        }).then((res) => {
            tableData.value = res.data.data.map((item: any) => {
                wagesTotal.value = { reference: 0, actual: 0 };
                wagesTotal.value.reference += Number(item.planQuotaCost) ?? 0;
                wagesTotal.value.actual += Number(item.quotaCost) ?? 0;
                return item;
            });
            // 2023年9月14日修复计算数字精度问题
            wagesTotal.value = {
                reference: Number(wagesTotal.value.reference.toFixed(2)),
                actual: Number(wagesTotal.value.actual.toFixed(2))
            };
            tableLoading.value = false;
        });
    } else {
        GET_PLAN_NONQUOTA_COST_LIST({
            ...searchForm.value,
            wageCategoryId: treeSelectKeys.value[0]
        }).then((res) => {
            tableData.value = res.data.data;
            tableLoading.value = false;
        });
    }
};

// 保存全部
let saveTableData = () => {
    loadingShow.value = true;
    if (wageType.value === 1) {
        SAVE_PLAN_QUOTA_COST_LIST({
            ...searchForm.value,
            dailyQuotaCostList: tableData.value
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("保存成功");
                onSearch();
            }
            loadingShow.value = false;
        });
    } else if (wageType.value === 2) {
        SAVE_PLAN_NONQUOTA_COST_LIST({
            ...searchForm.value,
            dailyNonQuotaCostList: tableData.value
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("保存成功");
                onSearch();
            }
            loadingShow.value = false;
        });
    }
};
</script>
