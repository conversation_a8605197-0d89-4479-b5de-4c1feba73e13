<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card
                :title="`导出《${configData.processFormVO.wcfName}》流程`"
                class="w-90vw"
                closable
                content-style="padding: 0"
                @close="closeModal"
            >
                <template #header-extra>
                    <n-space>
                        <n-button v-print="onPrint" type="primary">打印本单据</n-button>
                        <n-button
                            type="error"
                            @click="generatePdf('exportContainer', configData.processFormVO.wcfName)"
                        >
                            导出PDF
                        </n-button>
                    </n-space>
                </template>
                <div class="h-80vh">
                    <n-scrollbar trigger="hover">
                        <div id="exportContainer" class="p-20px">
                            <div>
                                <div class="mb">审批单号：{{ configData.businessNumber }}</div>
                                <n-form ref="formRef" label-placement="left">
                                    <n-grid
                                        :class="configData.processFormVO.wcfOptions.showBorder && 'b-1px b-[#E5E5E5]'"
                                        :x-gap="configData.processFormVO.wcfOptions.showBorder ? 0 : 16"
                                        cols="12"
                                    >
                                        <template v-for="(element, index) in configData.processFormVO.elements">
                                            <n-form-item-gi
                                                v-if="element.isHide !== 1"
                                                :class="
                                                    configData.processFormVO.wcfOptions.showBorder &&
                                                    'p-24px pb-0 b-1px b-[#E5E5E5]'
                                                "
                                                :label="element.label + '：'"
                                                :label-width="element.labelWidth as string"
                                                :show-label="element.showLabel"
                                                :span="element.row as number"
                                            >
                                                <!--单文本-->
                                                <component
                                                    :is="element.textType"
                                                    v-if="element.type === 'text'"
                                                    :class="`w-100% mt-0 mb-0 text-${element.textAlign} ${
                                                        element.textBold && 'font-bold'
                                                    }`"
                                                >
                                                    {{ element.textContent }}
                                                </component>
                                                <!--按钮-->
                                                <n-button
                                                    v-if="element.type === 'button'"
                                                    :type="element.buttonType"
                                                    block
                                                >
                                                    {{ element.buttonText }}
                                                </n-button>
                                                <!--输入框-->
                                                <n-text v-if="element.type === 'input'">
                                                    <template v-if="element.modelValue">
                                                        {{ element.inputPrefix }}
                                                        {{ element.modelValue }}
                                                        {{ element.inputSuffix }}
                                                    </template>
                                                    <template v-else>/</template>
                                                </n-text>
                                                <!--下拉/树形选择器-->
                                                <n-text
                                                    v-if="element.type === 'treeSelect' || element.type === 'select'"
                                                >
                                                    {{ element.modelLabel }}
                                                </n-text>
                                                <!--单/多选-->
                                                <n-text v-if="element.type === 'radio'">
                                                    {{
                                                        (element.options ?? []).find(
                                                            (i: any) => i.value === element.modelValue
                                                        )?.label
                                                    }}
                                                </n-text>
                                                <n-text v-if="element.type === 'checkbox'">
                                                    {{
                                                        (
                                                            (element.modelValue ?? []).map(
                                                                (i: any) =>
                                                                    (element.options ?? []).find(
                                                                        (ci: any) => ci.value === i
                                                                    )?.label
                                                            ) ?? []
                                                        ).join("，")
                                                    }}
                                                </n-text>
                                                <!--时间选择器-->
                                                <n-text v-if="element.type === 'datePicker'">
                                                    <template v-if="element.modelValue">
                                                        {{
                                                            typeof element.modelValue !== "string"
                                                                ? element.modelValue.join(" - ")
                                                                : element.modelValue
                                                        }}
                                                    </template>
                                                </n-text>
                                                <!--用户选择器-->
                                                <UserSelector
                                                    v-if="element.type === 'userSelector'"
                                                    v-model:value="element.modelValue"
                                                    :disabled="element.isRead === 0"
                                                    :placeholder="`请选择${element.label}`"
                                                    :show-input="false"
                                                    class="w-100%"
                                                    key-name="username"
                                                >
                                                    <template #input="scope">{{ scope.data }}</template>
                                                </UserSelector>
                                                <!--文件预览-->
                                                <FileViewer
                                                    v-if="element.type === 'fileUploader'"
                                                    :file-ids="element.modelValue"
                                                    is-export
                                                />
                                                <!--动态表格-->
                                                <DynamicTable
                                                    v-if="element.type === 'dynamicTable'"
                                                    v-model:header="element.dynamicTableHeader"
                                                    v-model:value="element.modelValue"
                                                    :addable="false"
                                                    :data-source="element.dynamicTableDataSource"
                                                    :debug="element.debugFlag"
                                                    :deletable="false"
                                                    :header-configurable="false"
                                                    class="w-100%"
                                                    textMode
                                                />
                                                <!--富文本-->
                                                <div
                                                    v-if="element.type === 'richTextEditor'"
                                                    class="w-100% rich-text-container"
                                                    v-html="element.modelValue"
                                                />
                                                <!--其他业务控件-->
                                                <PeGasPipeExtrusionParams
                                                    v-if="element.type === 'PeGasPipeExtrusionParams'"
                                                    v-model:value="element.modelValue"
                                                />
                                                <MeltMassFlowRateSample
                                                    v-if="element.type === 'MeltMassFlowRateSample'"
                                                    v-model:value="element.modelValue"
                                                />
                                                <ChecklistForTheStartUpPhase
                                                    v-if="element.type === 'ChecklistForTheStartUpPhase'"
                                                    v-model:value="element.modelValue"
                                                />
                                                <AppearanceGeoDimInspectionRecords
                                                    v-if="element.type === 'AppearanceGeoDimInspectionRecords'"
                                                    v-model:value="element.modelValue"
                                                />
                                                <OxidationInductionTimeTestingRecords
                                                    v-if="element.type === 'OxidationInductionTimeTestingRecords'"
                                                    v-model:value="element.modelValue"
                                                />
                                                <OriginalRecordOfHydrostaticStrengthTest
                                                    v-if="element.type === 'OriginalRecordOfHydrostaticStrengthTest'"
                                                    v-model:value="element.modelValue"
                                                />
                                                <OriginalRecordOfFractureElongationDetection
                                                    v-if="
                                                        element.type === 'OriginalRecordOfFractureElongationDetection'
                                                    "
                                                    v-model:value="element.modelValue"
                                                />
                                                <PEGasPipeFinishedProductInspectionForm
                                                    v-if="element.type === 'PEGasPipeFinishedProductInspectionForm'"
                                                    v-model:value="element.modelValue"
                                                />
                                                <PEGasPipeInspectionReport
                                                    v-if="element.type === 'PEGasPipeInspectionReport'"
                                                    v-model:value="element.modelValue"
                                                />
                                            </n-form-item-gi>
                                        </template>
                                        <n-grid-item
                                            :class="
                                                configData.processFormVO.wcfOptions.showBorder && 'b-1px b-[#E5E5E5]'
                                            "
                                            :span="2"
                                        >
                                            <div class="flex-center wh-100% text-center">审批流程</div>
                                        </n-grid-item>
                                        <n-grid-item
                                            :class="
                                                configData.processFormVO.wcfOptions.showBorder && 'b-1px b-[#E5E5E5]'
                                            "
                                            :span="10"
                                        >
                                            <div>
                                                <div
                                                    v-for="(item, index) in configData.processHistoryVoList"
                                                    :class="
                                                        configData.processFormVO.wcfOptions.showBorder &&
                                                        'flex-y-center b-t-2px b-t-[#E5E5E5] first:b-t-0 p-4'
                                                    "
                                                >
                                                    <div>
                                                        <template v-if="index === 0"
                                                            >{{ item.starter ?? item.assignee }}发起</template
                                                        >
                                                        <template v-else>
                                                            {{
                                                                `${item.assignee}${item.approvalType}， 意见：${item.comment}`
                                                            }}
                                                        </template>
                                                    </div>
                                                    <div class="ml-auto">{{ item.endTime }}</div>
                                                </div>
                                                <div
                                                    v-if="configData.status === 5"
                                                    :class="
                                                        configData.processFormVO.wcfOptions.showBorder &&
                                                        'flex-y-center b-t-2px b-t-[#E5E5E5] first:b-t-0 p-4'
                                                    "
                                                >
                                                    <div>发起人已撤回</div>
                                                    <div class="ml-auto">{{ configData.updateTime }}</div>
                                                </div>
                                                <div
                                                    v-if="configData.ccPersons"
                                                    :class="
                                                        configData.processFormVO.wcfOptions.showBorder &&
                                                        'b-t-2px b-t-[#E5E5E5] first:b-t-0 p-4'
                                                    "
                                                >
                                                    抄送{{ configData.ccPersonsNames }}
                                                </div>
                                            </div>
                                        </n-grid-item>
                                    </n-grid>
                                </n-form>
                            </div>
                        </div>
                    </n-scrollbar>
                </div>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { useStoreUser } from "@/store";
import { usePublic } from "@/hooks";
import { ref } from "vue";
import { generatePdf } from "@/utils/generatePdf";
import {
    AppearanceGeoDimInspectionRecords,
    ChecklistForTheStartUpPhase,
    MeltMassFlowRateSample,
    OriginalRecordOfFractureElongationDetection,
    OriginalRecordOfHydrostaticStrengthTest,
    OxidationInductionTimeTestingRecords,
    PeGasPipeExtrusionParams,
    PEGasPipeFinishedProductInspectionForm,
    PEGasPipeInspectionReport
} from "@/components/BusinessForm";
import { FileViewer } from "@/components/Business";
import { DynamicTable } from "@/components/Dynamic";
import { UserSelector } from "@/components/UserSelector";

let storeUser = useStoreUser();
let { $router } = usePublic();

let props = defineProps({
    show: { type: Boolean, default: false },
    configData: { type: Object as PropType<any> }
});

let emits = defineEmits(["update:show", "refresh"]);

// 关闭弹窗
let closeModal = () => {
    emits("update:show", false);
};

// 打印相关操作
let onPrint = ref({
    id: "exportContainer" //这里的id就是上面我们的打印区域id，实现指哪打哪
});
</script>
