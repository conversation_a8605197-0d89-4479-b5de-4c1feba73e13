<template>
    <n-card content-style="padding-top:10px">
        <n-tabs animated type="bar">
            <n-tab-pane name="定额工资配置">
                <n-tabs animated type="bar">
                    <n-tab-pane name="半成品定额">
                        <ReportingWagesFixedSemiProduct class="pt-1" />
                    </n-tab-pane>
                    <n-tab-pane name="产成品定额">
                        <ReportingWagesFixedProduct class="pt-1" />
                    </n-tab-pane>
                </n-tabs>
            </n-tab-pane>
            <n-tab-pane name="非定额工资配置">
                <ReportingWagesFloat class="pt-1" />
            </n-tab-pane>
        </n-tabs>
    </n-card>
</template>

<script lang="ts" setup>
import ReportingWagesFixedProduct from "./ReportingWagesFixedProduct.vue";
import ReportingWagesFixedSemiProduct from "./ReportingWagesFixedSemiProduct.vue";
import ReportingWagesFloat from "./ReportingWagesFloat.vue";
</script>
