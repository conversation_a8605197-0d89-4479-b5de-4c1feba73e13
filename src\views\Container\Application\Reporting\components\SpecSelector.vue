<template>
    <div>
        <n-tooltip v-if="showInput" trigger="hover">
            <template #trigger>
                <n-input
                    :disabled="disabled"
                    :placeholder="placeholder"
                    :value="selectNames"
                    clearable
                    readonly
                    @click="changeModalShow(true)"
                >
                    <template #clear-icon>
                        <dynamic-icon icon="CloseCircleOutlined" @click.stop="onClear()" />
                    </template>
                </n-input>
            </template>
            {{ selectNames ?? "暂无" }}
        </n-tooltip>
        <div v-else class="flex-y-center" @click="changeModalShow(true)">
            <slot name="input" />
        </div>
        <n-modal v-model:show="modalShow" :close-on-esc="false" :mask-closable="false">
            <n-card
                class="w-1000px"
                closable
                content-style="padding:0"
                title="规格选择"
                @close="changeModalShow(false)"
            >
                <div class="h-80vh">
                    <n-scrollbar trigger="hover">
                        <div class="p-20px pt-0">
                            <n-card>
                                <table-searchbar
                                    v-model:form="searchForm"
                                    :config="searchConfig"
                                    :options="searchOptions"
                                    @search="onSearch"
                                >
                                    <template #buttons>
                                        <n-button type="success" @click="onSubmit()">提交选择</n-button>
                                    </template>
                                </table-searchbar>
                            </n-card>
                            <div class="flex mt">
                                <n-card class="flex-fixed-300 mr">
                                    <div class="flex-y-center mb">
                                        <n-input v-model:value="treePattern" placeholder="搜索" />
                                    </div>
                                    <n-tree
                                        v-model:selected-keys="treeSelectKeys"
                                        :cancelable="false"
                                        :data="treeData"
                                        :default-expand-all="false"
                                        :pattern="treePattern"
                                        :show-irrelevant-nodes="false"
                                        block-line
                                        children-field="childrenList"
                                        key-field="id"
                                        label-field="categoryName"
                                        selectable
                                        @update:selected-keys="selectTreeNode"
                                    />
                                </n-card>
                                <n-card class="flex-1">
                                    <n-data-table
                                        v-model:checked-row-keys="tableSelection"
                                        :columns="productGenre !== 1 ? productTableColumns : materialTableColumns"
                                        :data="tableData"
                                        :loading="tableLoading"
                                        :pagination="tablePagination"
                                        :row-key="tableRowKey"
                                        :single-line="false"
                                        bordered
                                        remote
                                        striped
                                        @update:checked-row-keys="changeTableSelection"
                                    />
                                </n-card>
                            </div>
                        </div>
                    </n-scrollbar>
                </div>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, watchEffect } from "vue";
import type { DataTableColumns } from "naive-ui";
import { NInput } from "naive-ui";
import {
    TableSearchbar,
    TableSearchbarConfig,
    TableSearchbarData,
    TableSearchbarOptions
} from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import {
    GET_CONFIG_COMPANY_LIST,
    GET_MANUFACTURE_PAGE_LIST,
    GET_MATERIAL_CATEGORY_TREE_BY_GENRE,
    GET_MATERIAL_CATEGORY_TREE_LIST,
    GET_MATERIAL_SPEC_PAGE_LIST,
    GET_SEMI_MANUFACTURE_PAGE_LIST
} from "@/api/application/reporting";
import { DynamicIcon } from "@/components/DynamicIcon";
import { useStoreUser } from "@/store";

let storeUser = useStoreUser();

let props = withDefaults(
    defineProps<{
        value?: any;
        arrayValue?: any[];
        productGenre?: Nullable<number>;
        placeholder?: any;
        keyName?: string;
        dataConfig?: any;
        disabled?: boolean;
        multiple?: boolean;
        showInput?: boolean;
    }>(),
    {
        productGenre: null,
        placeholder: "请选择",
        keyName: "id",
        disabled: false,
        multiple: false,
        showInput: true
    }
);

let emits = defineEmits(["update:value", "update:arrayValue", "submit"]);

onMounted(async () => {
    await getSearchOptions();
    await getTreeData();
});

// 当前产品类型
let productGenre = ref<Nullable<number>>(null);

watchEffect(() => {
    if (props.productGenre) {
        console.log(props.productGenre);
        productGenre.value = props.productGenre;
    }
});

// 分类树
let treeData = ref<any[]>([]);
let treePattern = ref("");
let treeSelectKeys = ref<(string | number)[]>([]);

let getTreeData = async () => {
    // 是否需要根据产品类型获取分类树
    let apiRequest = productGenre.value ? GET_MATERIAL_CATEGORY_TREE_BY_GENRE : GET_MATERIAL_CATEGORY_TREE_LIST;
    let params = productGenre.value ? { productGenre: productGenre.value } : {};
    await apiRequest(params).then((res) => {
        if (res.data.code === 0) {
            treeData.value =
                productGenre.value !== 1
                    ? (res.data.data ?? []).filter((item: any) => item.productGenre !== 1)
                    : res.data.data ?? [];
            treeSelectKeys.value = [treeData.value[0].id];
            productGenre.value = treeData.value[0].productGenre;
        }
    });
};

let selectTreeNode = (keys: (string | number)[], array: any[]) => {
    treeSelectKeys.value = keys;
    productGenre.value = array?.[0]?.productGenre ?? productGenre.value;
    onSearch();
};

// 选中的展示名称
let selectNames = ref("");

let setSelectNames = async () => {
    let ids = props.value ?? (props.arrayValue ?? []).join(",");
    if (ids) {
        if (productGenre.value !== 1 || !props.productGenre) {
            let [semiManufacture, manufacture] = await Promise.all([
                GET_SEMI_MANUFACTURE_PAGE_LIST({ current: 1, size: 9999, ids }),
                GET_MANUFACTURE_PAGE_LIST({ current: 1, size: 9999, ids })
            ]);
            let selectArray = [...semiManufacture.data.data.records, ...manufacture.data.data.records];
            selectNames.value = selectArray.map((item: any) => item.productModel).join(",");
        } else {
            GET_MATERIAL_SPEC_PAGE_LIST({ current: 1, size: 9999, ids }).then((res) => {
                let selectArray = res.data.data.records;
                selectNames.value = selectArray.map((item: any) => `${item.materialName} - ${item.spec}`).join(",");
            });
        }
    }
};

// 选择弹窗
let modalShow = ref(false);

let changeModalShow = (val: boolean) => {
    if (val) {
        if (props.disabled) return false;
        modalShow.value = true;
        onSearch();
    } else modalShow.value = false;
};

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { prop: "productModel", type: "input", label: "规格" },
    { prop: "companyId", type: "select", label: "所属公司" }
]);
let searchOptions = ref<TableSearchbarOptions>({
    companyId: []
});

let searchForm = ref<TableSearchbarData>({
    productModel: null,
    companyId: null
});

let getSearchOptions = async () => {
    await GET_CONFIG_COMPANY_LIST({ needFill: 1 }).then((res) => {
        searchOptions.value.companyId = (res.data.data || []).map((item: any) => ({
            label: item.companyName,
            value: item.id
        }));
    });
    let userCompanyId = storeUser.getUserData.sysUser?.companyId ?? null;
    let companyIds = searchOptions.value.companyId.map((item) => item.value);
    if (companyIds.includes(userCompanyId)) {
        searchForm.value.companyId = userCompanyId;
    }
};

let onSearch = () => {
    tablePagination.page = 1;
    tablePagination.pageSize = 10;
    getTableData();
};

// 清除当前选中
let onClear = () => {
    tableSelection.value = [];
    emits("update:value", "");
    emits("update:arrayValue", []);
    emits("submit", "");
    selectNames.value = "";
};

// 数据列表
type RowProps = { [propName: string]: any };

let { tableRowKey, tableData, tableLoading, tableSelection, changeTableSelection, tablePaginationPreset } =
    useCommonTable<RowProps>(props.keyName);

let productTableColumns = ref<DataTableColumns<RowProps>>([
    { type: "selection", multiple: props.multiple },
    { title: "产成品名称", align: "center", key: "poleName" },
    { title: "规格", align: "center", key: "productModel" },
    { title: "配筋", align: "center", key: "reinforceBar" },
    { title: "分类名称", align: "center", key: "categoryName" },
    { title: "备注", align: "center", key: "remark", render: (row) => row.remark ?? "暂无" }
]);

let materialTableColumns = ref<DataTableColumns<RowProps>>([
    { type: "selection", multiple: props.multiple },
    { title: "原材料名称", align: "center", key: "materialName", render: (row) => `${row.materialName} - ${row.spec}` },
    { title: "备注", align: "center", key: "remark", render: (row) => row.remark ?? "暂无" }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = async () => {
    tableLoading.value = true;
    let params = {
        current: tablePagination.page,
        size: tablePagination.pageSize,
        categoryId: treeSelectKeys.value[0],
        ...searchForm.value
    };
    if (productGenre.value === 1) {
        GET_MATERIAL_SPEC_PAGE_LIST(params).then((res) => {
            tableData.value = res.data.data.records || [];
            tablePagination.itemCount = res.data.data.total;
        });
    } else if (productGenre.value === 2) {
        await GET_MANUFACTURE_PAGE_LIST(params).then((res) => {
            tableData.value = res.data.data.records || [];
            tablePagination.itemCount = res.data.data.total;
        });
    } else if (productGenre.value === 3) {
        await GET_SEMI_MANUFACTURE_PAGE_LIST(params).then((res) => {
            tableData.value = res.data.data.records || [];
            tablePagination.itemCount = res.data.data.total;
        });
    }
    tableLoading.value = false;
};

let onSubmit = () => {
    let selectedList = tableData.value.filter((item: any) => tableSelection.value.includes(item[props.keyName]));
    emits("update:value", tableSelection.value.join(","));
    emits("update:arrayValue", tableSelection.value ?? []);
    emits("submit", tableSelection.value.join(","), selectedList ?? []);
    changeModalShow(false);
};

watchEffect(() => {
    if (props.value) {
        tableSelection.value = props.value.split(",");
        setSelectNames();
    } else if (props.arrayValue) {
        tableSelection.value = props.arrayValue;
        setSelectNames();
    }
});
</script>
