<template>
    <div class="system-config">
        <n-card hoverable>
            <div class="flex h-100%">
                <n-menu
                    v-model:value="menuActive"
                    :indent="20"
                    :options="menuOptions"
                    class="flex-fixed-220 border-r-1px border-[#E5E5E5]"
                    mode="vertical"
                    @update:value="handleUpdateMenu"
                />
                <SystemConfig class="pl-20px" v-if="menuActive === 'CommonConfig'" />
                <UserDictList class="pl-20px" v-else-if="menuActive === 'UserDictList'" />
                <BackupRecords class="pl-20px" v-else-if="menuActive === 'BackupRecords'" />
                <BackupTasks class="pl-20px" v-else-if="menuActive === 'BackupTasks'" />
                <n-result
                    v-else
                    class="w-100% pt-100px pb-100px"
                    description="该功能暂未开放"
                    size="large"
                    status="403"
                    title="非常抱歉"
                />
            </div>
        </n-card>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { SystemConfig, BackupRecords, BackupTasks } from "./components";
import UserDictList from "../UserDictList/UserDictList.vue";

// 当前选中
let menuActive = ref("CommonConfig");

// 菜单点击跳转
let handleUpdateMenu = (key: string) => {};

// 菜单内容
let menuOptions = ref<any[]>([
    { label: "系统配置", key: "CommonConfig" },
    { label: "功能配置", key: "UserDictList" },
    { label: "提醒设置", key: "RemindConfig" },
    { label: "流程应用绑定配置", key: "ProcessBindConfig" },
    { label: "第三方配置", key: "ThirdPartyConfig" },
    { label: "备份任务", key: "BackupTasks" },
    { label: "备份记录", key: "BackupRecords" }
]);
</script>

<style lang="scss" scoped>
.system-config {
    ::v-deep(.n-card) {
        .n-card__content {
            .n-menu {
                .n-menu-item {
                    &:first-child {
                        margin-top: 0;
                    }

                    .n-menu-item-content {
                        &:before {
                            left: 0;
                            right: 0;
                        }
                    }

                    .n-menu-item-content--selected {
                        &:before {
                            border-right: 2px solid var(--n-item-text-color-active);
                        }
                    }
                }
            }
        }
    }
}
</style>
