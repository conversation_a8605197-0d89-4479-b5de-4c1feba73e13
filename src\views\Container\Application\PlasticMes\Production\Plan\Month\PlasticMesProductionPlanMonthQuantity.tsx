import { computed, defineComponent, onMounted, reactive, ref, watch } from "vue";
import dayjs from "dayjs";
import {
    DELETE_OUTPUT_MONTH_PLAN,
    GET_OUTPUT_MONTH_PLAN_PAGE_LIST,
    POST_OUTPUT_MONTH_PLAN,
    PUT_OUTPUT_MONTH_PLAN
} from "@/api/application/plasticMes";
import type { DataTableColumns, FormInst } from "naive-ui";
import { useCommonTable, useDicts } from "@/hooks";
import { TableActions } from "@/components/TableActions";
import { PlasticProductTypeSelector } from "@/components/PlasticProductTypeSelector";
import { GET_PRODUCT_TYPE_LIST } from "@/api/application/plastic";

export default defineComponent({
    name: "PlasticMesProductionPlanMonthQuantity",
    setup() {
        // 字典操作
        const { dictLibs, getDictLibs } = useDicts();

        const setDictLibs = async () => {
            const dictName = ["plastic_product_type"];
            await getDictLibs(dictName);
        };

        // 表单数据
        const formRef = ref<FormInst | null>(null);
        const searchFormData = ref<UnKnownObject>({
            planYear: null,
            planMonth: null,
            planMonthYear: null,
            type: null,
            productClassify: 1
        });

        const planMonthOptions = ref<UnKnownObject[]>([]);
        const typeOptions = ref<any[]>([
            // { label: "PE给水管", value: 1 },
            // { label: "PE污水管", value: 2 },
            // { label: "MPP", value: 3 },
            // { label: "PE燃气管", value: 4 },
            // { label: "通信管", value: 5 },
            // { label: "注塑产品", value: 6 },
            // { label: "波纹管", value: 7 },
            // { label: "排水管", value: 8 }
        ]);

        const getFormOptions = async () => {
            const options: UnKnownObject[] = [];
            const currentYear = dayjs().year();
            const nextYear = currentYear + 1;

            [currentYear, nextYear].forEach((year) => {
                for (let month = 1; month <= 12; month++) {
                    options.push({
                        label: `${year}年${month}月`,
                        value: `${year}${month.toString().padStart(2, "0")}`,
                        year: year,
                        month: month
                    });
                }
            });

            planMonthOptions.value = options;
            // 根据productClassify过滤typeOptions
            const allTypes = dictLibs["plastic_product_type"] ?? [];
            typeOptions.value = allTypes.filter((type) => {
                if (searchFormData.value.productClassify === 1) {
                    return type.label !== "注塑产品";
                } else {
                    return type.label === "注塑产品";
                }
            });
        };

        // 添加watch以监听productClassify变化
        watch(
            () => searchFormData.value.productClassify,
            () => {
                const allTypes = dictLibs["plastic_product_type"] ?? [];
                typeOptions.value = allTypes.filter((type) => {
                    if (searchFormData.value.productClassify === 1) {
                        return type.label !== "注塑产品";
                    } else {
                        return type.label === "注塑产品";
                    }
                });
                // 清空已选择的type
                searchFormData.value.type = null;
            }
        );

        const handleMonthChange = (value: string) => {
            const selectedOption = planMonthOptions.value.find((opt) => opt.value === value);
            if (selectedOption) {
                searchFormData.value.planYear = selectedOption.year;
                searchFormData.value.planMonth = value;
                searchFormData.value.planMonthYear = selectedOption.month;
            }
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
            useCommonTable<RowProps>("id");

        // 新增空行数据
        const handleAdd = () => {
            const tempId = -(tableData.value.length + 1);
            tableData.value.unshift({
                id: tempId,
                ptId: null,
                planMeter: "",
                planQuantity: ""
            });
        };

        // 保存行数据
        const handleSave = async (row: RowProps) => {
            if (!row.ptId) return window.$message.warning("请选择规格型号");
            if (!searchFormData.value.planMonth) return window.$message.warning("请选择计划年月");

            const params = [
                {
                    id: row.id > 0 ? row.id : undefined,
                    ptId: row.ptId,
                    planMeter: row.planMeter,
                    planQuantity: row.planQuantity,
                    planYear: searchFormData.value.planYear,
                    planMonth: searchFormData.value.planMonth,
                    planMonthYear: searchFormData.value.planMonthYear,
                    productClassify: searchFormData.value.productClassify
                }
            ];

            try {
                if (row.id > 0) {
                    await PUT_OUTPUT_MONTH_PLAN(params);
                } else {
                    await POST_OUTPUT_MONTH_PLAN(params);
                }
                window.$message.success("保存成功");
                getTableData();
            } catch (error) {
                window.$message.error("保存失败");
            }
        };

        // 删除行
        const handleDelete = async (row: RowProps) => {
            window.$dialog.warning({
                title: "确认删除",
                content: "确定要删除该条数据吗？",
                positiveText: "确定",
                negativeText: "取消",
                onPositiveClick: async () => {
                    if (!row.id) {
                        tableData.value = tableData.value.filter((item) => item !== row);
                        return false;
                    }
                    try {
                        const res = await DELETE_OUTPUT_MONTH_PLAN({
                            ids: String(row.id)
                        });
                        if (res.data.code === 0) {
                            window.$message.success("删除成功");
                            getTableData();
                        } else {
                            window.$message.error(res.data.message || "删除失败");
                        }
                    } catch (error) {
                        window.$message.error("删除失败");
                    }
                }
            });
        };

        // 添加批量删除方法
        const handleBatchDelete = async () => {
            if (!tableSelection.value.length) {
                return window.$message.warning("请选择要删除的数据");
            }

            window.$dialog.warning({
                title: "确认删除",
                content: "确定要删除选中的数据吗？",
                positiveText: "确定",
                negativeText: "取消",
                onPositiveClick: async () => {
                    try {
                        const res = await DELETE_OUTPUT_MONTH_PLAN({
                            ids: tableSelection.value.join(",")
                        });
                        if (res.data.code === 0) {
                            window.$message.success("删除成功");
                            getTableData();
                        } else {
                            window.$message.error(res.data.message || "删除失败");
                        }
                    } catch (error) {
                        window.$message.error("删除失败");
                    }
                }
            });
        };

        // 添加批量保存方法
        const handleBatchSave = async () => {
            if (!searchFormData.value.planMonth) {
                return window.$message.warning("请选择计划年月");
            }

            const invalidRows = tableData.value.filter((row) => !row.ptId);
            if (invalidRows.length > 0) {
                return window.$message.warning("存在未选择规格型号的行，请检查");
            }

            const params = tableData.value.map((row) => ({
                id: row.id > 0 ? row.id : undefined,
                ptId: row.ptId,
                planMeter: row.planMeter,
                planQuantity: row.planQuantity,
                planYear: searchFormData.value.planYear,
                planMonth: searchFormData.value.planMonth,
                planMonthYear: searchFormData.value.planMonthYear,
                productClassify: searchFormData.value.productClassify
            }));

            try {
                const hasExistingData = params.some((item) => item.id);
                if (hasExistingData) {
                    await PUT_OUTPUT_MONTH_PLAN(params);
                } else {
                    await POST_OUTPUT_MONTH_PLAN(params);
                }
                window.$message.success("批量保存成功");
                getTableData();
            } catch (error) {
                window.$message.error("批量保存失败");
            }
        };

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            {
                title: "规格型号",
                key: "ptId",
                align: "center",
                render: (row: RowProps) => {
                    return (
                        <PlasticProductTypeSelector
                            key-name="id"
                            value={row.ptId}
                            onSubmit={async (v: string) => {
                                row.ptId = v;
                                // 在选择规格型号时获取newMeterWeight
                                if (v) {
                                    try {
                                        const res = await GET_PRODUCT_TYPE_LIST({ ids: v });
                                        row.newMeterWeight = res.data.data.records[0]?.newMeterWeight;
                                    } catch (error) {
                                        console.error("Failed to fetch meter weight:", error);
                                    }
                                }
                            }}
                        />
                    );
                }
            },
            {
                title: "计划生产米数（米）",
                key: "planMeter",
                align: "center",
                width: 200,
                render: (row: RowProps) => {
                    return <n-input v-model:value={row.planMeter} clearable />;
                }
            },
            {
                title: "计划生产吨数（吨）",
                key: "planQuantity",
                align: "center",
                width: 200,
                render: (row: RowProps) => {
                    // 直接使用已缓存的newMeterWeight计算
                    if (row.newMeterWeight && row.planMeter) {
                        row.planQuantity = (Number(row.newMeterWeight) * Number(row.planMeter)).toFixed(2);
                    } else {
                        row.planQuantity = "0.00";
                    }
                    return row.planQuantity;
                }
            },
            {
                title: "操作",
                key: "actions",
                align: "center",
                width: 160,
                render: (row) => (
                    <TableActions
                        type="button"
                        buttonActions={[
                            {
                                label: "保存",
                                tertiary: true,
                                onClick: () => handleSave(row)
                            },
                            {
                                label: "删除",
                                tertiary: true,
                                type: "error",
                                onClick: () => handleDelete(row)
                            }
                        ]}
                    />
                )
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        // const totalQuantity = ref<any>(null);
        // const totalMeter = ref<any>(null);

        const getTableData = async () => {
            tableLoading.value = true;
            try {
                const res = await GET_OUTPUT_MONTH_PLAN_PAGE_LIST({
                    current: tablePagination.page,
                    size: tablePagination.pageSize,
                    type: searchFormData.value.type,
                    planYear: searchFormData.value.planYear,
                    planMonth: searchFormData.value.planMonth,
                    planMonthYear: searchFormData.value.planMonthYear,
                    productClassify: searchFormData.value.productClassify
                });

                if (res.data.code === 0) {
                    const records = res.data.data?.records ?? [];

                    // 获取所有记录的ptId
                    const ptIds = records.filter((record: any) => record.ptId).map((record: any) => record.ptId);

                    if (ptIds.length > 0) {
                        // 批量获取规格型号信息
                        const typeRes = await GET_PRODUCT_TYPE_LIST({ ids: ptIds.join(",") });
                        const typeMap = new Map(
                            typeRes.data.data.records.map((item: any) => [item.id, item.newMeterWeight])
                        );

                        // 将newMeterWeight添加到每条记录中
                        records.forEach((record: any) => {
                            if (record.ptId) {
                                record.newMeterWeight = typeMap.get(record.ptId);
                            }
                        });
                    }

                    tableData.value = records;
                    tablePagination.itemCount = res.data.data?.total;
                }
            } catch (error) {
                console.error("Failed to fetch table data:", error);
            } finally {
                tableLoading.value = false;
            }
        };

        onMounted(async () => {
            await setDictLibs();
            await getFormOptions();
        });

        return () => (
            <div class="plastic-mes-production-plan-month mt-2">
                <n-form show-feedback={false} ref={formRef} model={searchFormData.value} label-placement="left">
                    <n-grid cols={24} x-gap={16}>
                        <n-form-item-gi span={7} label="计划年月：">
                            <n-select
                                class="w-100%"
                                v-model:value={searchFormData.value.planMonth}
                                options={planMonthOptions.value}
                                clearable
                                filterable
                                placeholder="请选择计划年月"
                                onUpdate:value={handleMonthChange}
                            />
                        </n-form-item-gi>
                        <n-form-item-gi span={7} label="品类：">
                            <n-select
                                class="w-100%"
                                v-model:value={searchFormData.value.type}
                                options={typeOptions.value}
                                clearable
                                filterable
                                placeholder="请选择品类"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi span={7} label="产品类型：">
                            <n-radio-group v-model:value={searchFormData.value.productClassify}>
                                <n-space>
                                    <n-radio value={1} label="管线产品" />
                                    <n-radio value={2} label="注塑产品" />
                                </n-space>
                            </n-radio-group>
                        </n-form-item-gi>
                        <n-form-item-gi>
                            <n-button type="primary" onClick={getTableData}>
                                查询
                            </n-button>
                        </n-form-item-gi>
                    </n-grid>
                </n-form>
                <n-space class="mt">
                    <n-button type="success" onClick={handleAdd}>
                        新增规格计划
                    </n-button>
                    <n-button type="primary" onClick={handleBatchSave}>
                        批量保存计划
                    </n-button>
                    <n-button type="error" onClick={handleBatchDelete}>
                        批量删除计划
                    </n-button>
                </n-space>
                {/* <n-space class="mt">
                    <n-space size="small">
                        <div>合计数量：</div>
                        <n-element tag="div" class="color-[var(--primary-color)]">
                            {totalMeter.value ?? "0"}米
                        </n-element>
                    </n-space>
                    <n-space size="small">
                        <div>合计重量：</div>
                        <n-element tag="div" class="color-[var(--primary-color)]">
                            {totalQuantity.value ?? "0"}吨
                        </n-element>
                    </n-space>
                </n-space> */}
                <div class="mt">
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        pagination={tablePagination}
                        row-key={tableRowKey}
                        single-line={false}
                        bordered
                        remote
                        striped
                        onUpdate:checked-row-keys={changeTableSelection}
                    />
                </div>
            </div>
        );
    }
});
