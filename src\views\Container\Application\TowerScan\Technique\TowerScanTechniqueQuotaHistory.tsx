import { computed, defineComponent, reactive, ref, watchEffect } from "vue";
import type { DataTableColumns, PaginationProps } from "naive-ui";
import { NText } from "naive-ui";
import { GET_IRON_TECHNIQUE_HISTORY_PAGE_LIST } from "@/api/application/TowerScan";
import { useCommonTable, useDicts } from "@/hooks";
import dayjs from "dayjs";

export default defineComponent({
    name: "TowerScanTechniqueQuotaHistory",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        // 字典操作
        const { dictLibs, getDictLibs } = useDicts();

        const setDictLibs = async () => {
            const dictName = ["common_units"];
            await getDictLibs(dictName);
        };

        // 数据列表
        interface HistoryRowProps {
            createBy: string;
            createTime: string;
            oldCreateTime: string;
            oldTechniqueAmount: string;
            oldTechniqueUnit: string;
            techniqueAmount: string;
            techniqueUnit: string;
            towerTechniqueId: string;
            updateBy: string;
            updateTime: string;
        }

        const { tableRowKey, tableData, tableLoading } = useCommonTable<HistoryRowProps>("id");

        const tableColumns = ref<DataTableColumns<HistoryRowProps>>([
            {
                title: "操作时间",
                key: "updateTime",
                align: "center",
                width: 160,
                render: (row) => dayjs(row.updateTime).format("YYYY-MM-DD HH:mm:ss")
            },
            {
                title: "操作人",
                key: "updateBy",
                align: "center",
                width: 100
            },
            {
                title: "原定价金额（元）",
                key: "oldTechniqueAmount",
                align: "center",
                width: 120,
                render: (row) => row.oldTechniqueAmount || "/"
            },
            {
                title: "原定价单位",
                key: "oldTechniqueUnit",
                align: "center",
                width: 100,
                render: (row) => {
                    if (row.oldTechniqueUnit) {
                        const unit = dictLibs["common_units"]?.find((item: any) => item.value === row.oldTechniqueUnit);
                        return unit ? unit.label : row.oldTechniqueUnit;
                    }
                    return "/";
                }
            },
            {
                title: "新定价金额（元）",
                key: "techniqueAmount",
                align: "center",
                width: 120,
                render: (row) => <NText type="success">{row.techniqueAmount || "/"}</NText>
            },
            {
                title: "新定价单位",
                key: "techniqueUnit",
                align: "center",
                width: 100,
                render: (row) => {
                    if (row.techniqueUnit) {
                        const unit = dictLibs["common_units"]?.find((item: any) => item.value === row.techniqueUnit);
                        return <NText type="success">{unit ? unit.label : row.techniqueUnit}</NText>;
                    }
                    return <NText type="info">/</NText>;
                }
            },
            {
                title: "原创建时间",
                key: "oldCreateTime",
                align: "center",
                width: 160,
                render: (row) => (row.oldCreateTime ? dayjs(row.oldCreateTime).format("YYYY-MM-DD HH:mm:ss") : "/")
            }
        ]);

        const tablePagination = reactive<PaginationProps>({
            page: 1,
            pageSize: 10,
            itemCount: 0,
            pageSizes: [10, 20, 50],
            showSizePicker: true,
            showQuickJumper: true,
            displayOrder: ["size-picker", "pages", "quick-jumper"],
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            if (!props.configData.ironTechniqueId) return;

            tableLoading.value = true;
            GET_IRON_TECHNIQUE_HISTORY_PAGE_LIST({
                current: String(tablePagination.page),
                size: String(tablePagination.pageSize),
                ironTechniqueId: String(props.configData.ironTechniqueId)
            })
                .then((res) => {
                    if (res.data.code === 0) {
                        tableData.value = res.data.data.records || [];
                        tablePagination.itemCount = res.data.data.total;
                        tableLoading.value = false;
                    } else {
                        tableLoading.value = false;
                    }
                })
                .catch(() => {
                    tableLoading.value = false;
                });
        };

        watchEffect(async () => {
            if (show.value) {
                await setDictLibs();
                tablePagination.page = 1;
                getTableData();
            }
        });

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card
                    title={`修改历史记录 - ${props.configData.techniqueName || ""}`}
                    class="w-1200px"
                    closable
                    onClose={() => changeModalShow(false)}
                >
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        pagination={tablePagination}
                        row-key={tableRowKey}
                        single-line={false}
                        bordered
                        remote
                        striped
                        size="small"
                    />
                </n-card>
            </n-modal>
        );
    }
});
