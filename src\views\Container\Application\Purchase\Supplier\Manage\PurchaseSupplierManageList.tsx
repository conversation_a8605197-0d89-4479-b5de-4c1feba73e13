import { defineComponent, onMounted, reactive, ref } from "vue";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import PurchaseSupplierManageEdit from "./PurchaseSupplierManageEdit";
import PurchaseSupplierManageDetail from "./PurchaseSupplierManageDetail";
import {
    DELETE_SUPPLIERS_MANAGEMENT,
    GET_SUPPLIERS_MANAGEMENT_LIST,
    PULL_INTO_THE_BLACKLIST,
    SUSPENSION_OF_COOPERATION
} from "@/api/application/purchase";
import { TableActions } from "@/components/TableActions";
import PurchaseSupplierManageApprove from "@/views/Container/Application/Purchase/Supplier/Manage/PurchaseSupplierManageApprove.vue";

export default defineComponent({
    name: "PurchaseSupplierCategoryList",
    setup(props) {
        // 搜索项
        const searchConfig = ref<TableSearchbarConfig>([
            { prop: "supplierName", type: "input", label: "供应商名称", labelWidth: "100" },
            { prop: "supplierType", type: "select", label: "供应商类型", labelWidth: "100" },
            { prop: "supplierStatus", type: "select", label: "供应商状态", labelWidth: "100" }
        ]);
        const searchOptions = ref<TableSearchbarOptions>({
            supplierType: [
                { label: "企业", value: 1 },
                { label: "个人", value: 2 }
            ],
            supplierStatus: [
                { label: "待审批", value: 0 },
                { label: "正常", value: 1 },
                { label: "暂停合作", value: 2 },
                { label: "终止合作", value: 3 },
                { label: "已拉黑", value: 4 },
                { label: "评审中", value: 5 }
            ]
        });
        const getSearchOptions = async () => {};
        const searchForm = ref<TableSearchbarData>({
            supplierName: null,
            supplierType: null,
            supplierStatus: null
        });
        const onSearch = () => {
            tablePagination.page = 1;
            tablePagination.pageSize = 10;
            getTableData();
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            { title: "供应商名称", key: "supplierName", align: "center", render: (row) => row.supplierName ?? "/" },
            {
                title: "供应商类型",
                key: "supplierType",
                align: "center",
                render: (row) => {
                    switch (row.supplierType) {
                        case 1:
                            return <n-text type="info">企业</n-text>;
                        case 2:
                            return <n-text type="info">个人</n-text>;
                        default:
                            return <n-text type="info">/</n-text>;
                    }
                }
            },
            {
                title: "供应商状态",
                key: "supplierStatus",
                align: "center",
                render: (row) => {
                    switch (row.supplierStatus) {
                        case 0:
                            return <n-text type="warning">待审批</n-text>;
                        case 1:
                            return <n-text type="success">正常</n-text>;
                        case 2:
                            return <n-text type="warning">暂停合作</n-text>;
                        case 3:
                            return <n-text type="error">终止合作</n-text>;
                        case 4:
                            return <n-text type="error">已拉黑</n-text>;
                        case 5:
                            return <n-text type="info">评审中</n-text>;
                        default:
                            return <n-text type="info">/</n-text>;
                    }
                }
            },
            { title: "备注", key: "remark", align: "center", render: (row) => row.remark ?? "/" },
            {
                title: "操作",
                key: "actions",
                align: "center",
                width: 350,
                render: (row) => {
                    const actions: {
                        label: string;
                        type: "default" | "warning" | "primary" | "success" | "error" | "tertiary" | "info";
                        tertiary: boolean;
                        onClick: () => void;
                    }[] = [
                        {
                            label: "编辑",
                            type: "warning",
                            tertiary: true,
                            onClick: () => openEditModal(row)
                        },
                        {
                            label: "查看详情",
                            type: "primary",
                            tertiary: true,
                            onClick: () => openDetailModal(row)
                        }
                    ];

                    // 待审批状态
                    if (row.supplierStatus === 0) {
                        actions.push(
                            {
                                label: "发起评审",
                                type: "success",
                                tertiary: true,
                                onClick: () => openReviewProcessModal(row)
                            },
                            {
                                label: "删除供应商",
                                tertiary: true,
                                type: "error",
                                onClick: () => onDelete(row)
                            }
                        );
                    }
                    // 正常状态
                    else if (row.supplierStatus === 1) {
                        actions.push(
                            {
                                label: "暂停合作",
                                tertiary: true,
                                type: "error",
                                onClick: () => onPause(row)
                            },
                            {
                                label: "拉入黑名单",
                                tertiary: true,
                                type: "default",
                                onClick: () => onBlacklist(row)
                            }
                        );
                    }
                    // 暂停合作状态
                    else if (row.supplierStatus === 2) {
                        actions.push({
                            label: "拉入黑名单",
                            tertiary: true,
                            type: "default",
                            onClick: () => onBlacklist(row)
                        });
                    }
                    // 终止合作状态
                    else if (row.supplierStatus === 3) {
                        actions.push({
                            label: "拉入黑名单",
                            tertiary: true,
                            type: "default",
                            onClick: () => onBlacklist(row)
                        });
                    }
                    // 已拉黑状态 - 不显示额外操作按钮
                    // 评审中状态 - 不显示额外操作按钮

                    return <TableActions type="button" buttonActions={actions} />;
                }
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_SUPPLIERS_MANAGEMENT_LIST({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                techState: 0,
                ...searchForm.value
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        // 新增编辑弹窗
        const editModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const openEditModal = (row?: RowProps) => {
            editModal.value.show = true;
            editModal.value.configData = row ? { id: row.id } : {};
        };

        // 详情弹窗
        const detailModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const openDetailModal = (row: RowProps) => {
            detailModal.value = { show: true, configData: { id: row.id } };
        };

        // 删除
        const onDelete = (row: RowProps) => {
            window.$dialog.warning({
                title: "警告",
                content: "确认删除该条数据？该操作不可逆",
                positiveText: "确认删除",
                negativeText: "我再想想",
                onPositiveClick: () => {
                    DELETE_SUPPLIERS_MANAGEMENT({ id: row.id }).then((res) => {
                        if (res.data.code === 0) {
                            window.$message.success("删除成功");
                            onSearch();
                        }
                    });
                }
            });
        };

        // 暂停合作
        const onPause = (row: RowProps) => {
            window.$dialog.warning({
                title: "警告",
                content: "确认暂停合作？",
                positiveText: "确认暂停",
                negativeText: "我再想想",
                onPositiveClick: () => {
                    SUSPENSION_OF_COOPERATION({ id: row.id }).then((res) => {
                        if (res.data.code === 0) {
                            window.$message.success("暂停成功");
                            onSearch();
                        } else {
                            window.$message.error(res.data.msg);
                        }
                    });
                }
            });
        };

        // 拉入黑名单
        const onBlacklist = (row: RowProps) => {
            window.$dialog.warning({
                title: "警告",
                content: "确认拉入黑名单？",
                positiveText: "确认拉入",
                negativeText: "我再想想",
                onPositiveClick: () => {
                    PULL_INTO_THE_BLACKLIST({ id: row.id }).then((res) => {
                        if (res.data.code === 0) {
                            window.$message.success("拉入成功");
                            onSearch();
                        } else {
                            window.$message.error(res.data.msg);
                        }
                    });
                }
            });
        };

        // 发起评审流程弹窗
        const reviewProcessModal = ref<{ show: boolean; configData: Record<string, any> }>({
            show: false,
            configData: {}
        });

        const openReviewProcessModal = (row: RowProps) => {
            reviewProcessModal.value.show = true;
            reviewProcessModal.value.configData = row;
        };

        onMounted(async () => {
            await getSearchOptions();
            getTableData();
        });

        return () => (
            <div class="plastic-mes-repair-inside-list">
                <n-card>
                    <TableSearchbar
                        form={searchForm.value}
                        config={searchConfig.value}
                        options={searchOptions.value}
                        onSearch={onSearch}
                    />
                </n-card>
                <n-card class="mt">
                    <n-space class="mb">
                        <n-button type="primary" onClick={() => openEditModal()}>
                            新增供应商
                        </n-button>
                    </n-space>
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        pagination={tablePagination}
                        row-key={tableRowKey}
                        single-line={false}
                        bordered
                        remote
                        striped
                        onUpdate:checked-row-keys={changeTableSelection}
                    />
                </n-card>
                <PurchaseSupplierManageEdit
                    v-model:show={editModal.value.show}
                    configData={editModal.value.configData}
                    onRefresh={getTableData}
                />
                <PurchaseSupplierManageDetail
                    v-model:show={detailModal.value.show}
                    configData={detailModal.value.configData}
                />
                <PurchaseSupplierManageApprove
                    v-model:show={reviewProcessModal.value.show}
                    configData={reviewProcessModal.value.configData}
                    onRefresh={getTableData}
                />
            </div>
        );
    }
});
