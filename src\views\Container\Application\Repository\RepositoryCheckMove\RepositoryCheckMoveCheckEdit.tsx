import { computed, defineComponent, ref, watchEffect } from "vue";
import { type FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { ADD_WORK_PLAN_RULE, GET_WORK_PLAN_RULE_BY_ID, UPDATE_WORK_PLAN_RULE } from "@/api/application/plasticMes";
import {
    ADD_REPOSITORY_INVENTORY_TASK,
    GET_REPOSITORY_INVENTORY_TASK_DETAIL,
    GET_REPOSITORY_POINT_LIST,
    GET_REPOSITORY_STOREROOM_LIST,
    UPDATE_REPOSITORY_INVENTORY_TASK
} from "@/api/application/repository";
import { UserSelector } from "@/components/UserSelector";

export default defineComponent({
    name: "RepositoryCheckMoveCheckEdit",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        // 表单数据
        interface FormDataProps {
            [key: string]: any;
        }

        const formRef = ref<FormInst | null>(null);

        const storeroomIdsOptions = ref<any[]>([]);

        const getFormOptions = async () => {
            GET_REPOSITORY_STOREROOM_LIST({ current: 1, size: 9999 }).then((res) => {
                if (res.data.code === 0) {
                    storeroomIdsOptions.value = res.data.data.records.map((i: any) => ({
                        label: i.storeroomName,
                        value: i.id
                    }));
                }
            });
        };

        const initFormData: FormDataProps = {
            planTime: null,
            inventoryBy: null,
            storeroomIds: null,
            remark: null
        };

        const formRules = computed(() => ({
            planTime: [{ required: true, message: "请选择计划盘盘点", trigger: ["blur", "change"] }],
            inventoryBy: [{ required: true, message: "请选择执行人", trigger: ["blur", "change"] }],
            storeroomIds: [{ required: true, message: "请选择盘点仓库", trigger: ["blur", "change"], type: "array" }],
            remark: [{ required: false, message: "请输入备注", trigger: ["input", "blur"] }]
        }));

        const formData = ref(cloneDeep(initFormData));

        const clearForm = () => {
            formData.value = cloneDeep(initFormData);
        };

        // 获取详情
        const getDetail = () => {
            GET_REPOSITORY_INVENTORY_TASK_DETAIL({ id: props.configData.id }).then((res) => {
                if (res.data.code === 0) {
                    formData.value = {
                        planTime: res.data.data.planTime,
                        inventoryBy: res.data.data.inventoryBy,
                        storeroomIds: res.data.data.storeroomIds.split(","),
                        remark: res.data.data.remark
                    };
                }
            });
        };

        watchEffect(async () => {
            if (show.value) {
                await getFormOptions();
                if (props.configData.id) getDetail();
            }
        });

        const onClose = () => {
            clearForm();
            changeModalShow(false);
            emit("refresh");
        };

        const onSubmit = async () => {
            let validateError = await formRef.value?.validate((errors) => !!errors);
            if (validateError) return false;

            if (props.configData.id) {
                await UPDATE_REPOSITORY_INVENTORY_TASK({
                    id: props.configData.id,
                    ...formData.value,
                    storeroomIds: formData.value.storeroomIds.join(",")
                }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("编辑成功");
                        onClose();
                    }
                });
            } else {
                await ADD_REPOSITORY_INVENTORY_TASK({
                    ...formData.value,
                    storeroomIds: formData.value.storeroomIds.join(",")
                }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("新增成功");
                        onClose();
                    }
                });
            }
        };

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card
                    title={props.configData.id ? "编辑盘点任务" : "新增盘点任务"}
                    class="w-800px"
                    closable
                    onClose={onClose}
                >
                    <n-form
                        ref={formRef}
                        model={formData.value}
                        rules={formRules.value}
                        label-placement="left"
                        label-width="auto"
                    >
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi span={6} label="计划盘点时间" path="planTime">
                                <n-date-picker
                                    v-model:formatted-value={formData.value.planTime}
                                    class="w-100%"
                                    clearable
                                    placeholder="请选择计划盘点时间"
                                    type="date"
                                    value-format="yyyy-MM-dd"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="执行人" path="inventoryBy">
                                <UserSelector
                                    v-model:value={formData.value.inventoryBy}
                                    class="w-100%"
                                    key-name="username"
                                    placeholder="请选择执行人"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="盘点仓库" path="storeroomIds">
                                <n-select
                                    class="w-100%"
                                    v-model:value={formData.value.storeroomIds}
                                    options={storeroomIdsOptions.value}
                                    multiple
                                    clearable
                                    filterable
                                    placeholder="请选择盘点仓库"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="备注" path="remark">
                                <n-input
                                    v-model:value={formData.value.remark}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入备注"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={12}>
                                <n-space>
                                    <n-button type="primary" onClick={onSubmit}>
                                        提交
                                    </n-button>
                                    <n-button onClick={onClose}>取消</n-button>
                                </n-space>
                            </n-form-item-gi>
                        </n-grid>
                    </n-form>
                </n-card>
            </n-modal>
        );
    }
});
