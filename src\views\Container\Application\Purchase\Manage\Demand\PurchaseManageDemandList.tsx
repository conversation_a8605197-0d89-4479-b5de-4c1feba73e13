import { defineComponent, onMounted, reactive, ref, watch, watchEffect } from "vue";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";

import { GET_PURCHASING_DEMAND_MANAGEMENT_LIST } from "@/api/application/purchase";
import PurchaseManageDemandContent from "./PurchaseManageDemandContent";

export default defineComponent({
    name: "PurchaseManageDemandList",
    components: {
        PurchaseManageDemandContent
    },
    setup(props) {
        // 搜索项
        const searchConfig = ref<TableSearchbarConfig>([]);
        const searchOptions = ref<TableSearchbarOptions>({});
        const getSearchOptions = async () => {};
        const searchForm = ref<TableSearchbarData>({});
        const onSearch = () => {
            tablePagination.page = 1;
            tablePagination.pageSize = 10;
            getTableData();
        };

        const tabActive = ref(1);

        // 弹窗控制
        const listModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const openListModal = (row?: RowProps) => {
            listModal.value.show = true;
            listModal.value.configData = row ? { id: row.id } : {};
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            { title: "需求单号", key: "no", align: "center", render: (row) => row.no ?? "/" },
            { title: "申请人", key: "applicant", align: "center", render: (row) => row.applicant ?? "/" },
            { title: "需求人所属公司", key: "company", align: "center", render: (row) => row.company ?? "/" },
            {
                title: "需求内容",
                key: "no",
                align: "center",
                render: (row) => (
                    <n-button
                        type="primary"
                        text
                        onClick={() => openListModal(row)}
                    >
                        点击查看
                    </n-button>
                )
            },
            {
                title: "需求类型",
                key: "type",
                align: "center",
                render: (row) => {
                    switch (row.type) {
                        case 1:
                            return "零星采购";
                        case 2:
                            return "产品采购";
                        case 3:
                            return "原材料";
                        case 4:
                            return "辅料";
                        default:
                            return "/";
                    }
                }
            },
            {
                title: "需求状态",
                key: "remark",
                align: "center",
                render: (row) => <n-text type="info">{row.demandStatusName}</n-text>
            },
            {
                title: "单据审批状态",
                key: "approvalStatus",
                align: "center",
                render: (row) => {
                    switch (row.approvalStatus) {
                        case "0":
                            return <n-text>暂无</n-text>;
                        case "1":
                            return <n-text type="warning">审批中</n-text>;
                        case "2":
                            return <n-text type="error">审批未同意</n-text>;

                        default:
                            return "/";
                    }
                }
            },
            { title: "备注", key: "remark", align: "center", render: (row) => row.remark ?? "/" }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_PURCHASING_DEMAND_MANAGEMENT_LIST({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                states: tabActive.value,
                ...searchForm.value
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        onMounted(async () => {
            await getSearchOptions();
        });

        watchEffect(() => {
            if (tabActive.value) {
                getTableData();
            }
        });

        return () => (
            <div class="plastic-mes-repair-inside-list">
                <n-card>
                    <TableSearchbar
                        form={searchForm.value}
                        config={searchConfig.value}
                        options={searchOptions.value}
                        onSearch={onSearch}
                    />
                </n-card>
                <n-card class="mt">
                    <n-space class="mb">
                        <n-tabs v-model:value={tabActive.value} animated type="bar">
                            <n-tab-pane name={1} tab="我发起的" />
                            <n-tab-pane name={2} tab="待完成的" />
                            <n-tab-pane name={3} tab="已完成的" />
                            <n-tab-pane name={4} tab="已关闭的" />
                        </n-tabs>
                    </n-space>
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        pagination={tablePagination}
                        row-key={tableRowKey}
                        single-line={false}
                        bordered
                        remote
                        striped
                        onUpdate:checked-row-keys={changeTableSelection}
                    />
                </n-card>
                <PurchaseManageDemandContent
                    v-model:show={listModal.value.show}
                    configData={listModal.value.configData}
                />
            </div>
        );
    }
});
