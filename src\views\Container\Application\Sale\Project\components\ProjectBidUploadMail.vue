<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-600px" closable title="上传标书/邮寄确认" @close="closeModal">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-form-item label="确认类型" path="bidUploadType">
                        <n-radio-group v-model:value="formData.bidUploadType">
                            <n-space>
                                <n-radio :value="1" label="线下邮寄" />
                                <n-radio :value="2" label="在线上传" />
                            </n-space>
                        </n-radio-group>
                    </n-form-item>
                    <n-form-item label="上传凭证">
                        <FileUploader v-model:file-list="bidList" />
                    </n-form-item>
                    <n-form-item>
                        <n-space>
                            <n-button type="primary" @click="onSubmit">提交</n-button>
                            <n-button @click="closeModal">取消</n-button>
                        </n-space>
                    </n-form-item>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";

import { FileUploader } from "@/components/Uploader";
import { POST_PROJECT_BID_FORM } from "@/api/application/sale";

let props = defineProps({
    show: { type: Boolean, default: false },
    configData: { type: Object as PropType<any> }
});

let emits = defineEmits(["update:show", "refresh"]);

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {};

// 表单数据
interface FormDataProps<T = string | null> {
    bidUploadType: number;
}

let initFormData: FormDataProps = {
    bidUploadType: 1
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    emits("update:show", false);
};

// 标书文件列表
let bidList = ref<any[]>([]);

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    POST_PROJECT_BID_FORM({
        projectId: props.configData.projectId,
        nodeKey: props.configData.nextNodeKey,
        bidUploadType: formData.value.bidUploadType,
        fileList: bidList.value.map((item) => item.fileId).join(",")
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("标书信息已提交");
            closeModal();
            emits("refresh");
        }
    });
};
</script>
