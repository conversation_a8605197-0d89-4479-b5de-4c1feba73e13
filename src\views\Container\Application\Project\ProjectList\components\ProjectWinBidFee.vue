<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-600px" closable title="中标相关费用填写" @close="closeModal">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-form-item path="bidCosts">
                        <n-dynamic-input
                            v-model:value="formData.bidCosts"
                            key-placeholder="费用名称"
                            preset="pair"
                            value-placeholder="费用金额"
                        >
                            <template #create-button-default>新增费用</template>
                        </n-dynamic-input>
                    </n-form-item>
                    <n-form-item>
                        <n-space>
                            <n-button type="primary" @click="onSubmit">提交</n-button>
                            <n-button @click="closeModal">取消</n-button>
                        </n-space>
                    </n-form-item>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { SUBMIT_PROJECT_BID_COST_FORM } from "@/api/application/project";

let props = defineProps({
    show: { type: Boolean, default: false },
    configData: { type: Object as PropType<any> }
});

let emits = defineEmits(["update:show", "refresh"]);

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {};

// 表单数据
interface FormDataProps<T = string | null> {
    bidCosts: any[];
}

let initFormData: FormDataProps = {
    bidCosts: []
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    emits("update:show", false);
};

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    if (formData.value.bidCosts.length <= 0) {
        window.$message.error("请输入中标相关费用！");
        return false;
    }
    let newArr = formData.value.bidCosts.map((obj: any) => {
        let newObj: any = {};
        newObj[obj.value] = obj.key;
        return newObj;
    });
    console.log(newArr);
    SUBMIT_PROJECT_BID_COST_FORM({
        projectId: props.configData.projectId,
        nodeKey: props.configData.nextNodeKey,
        bidCosts: JSON.stringify(newArr)
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("提交成功");
            closeModal();
            emits("refresh");
        }
    });
};
</script>
