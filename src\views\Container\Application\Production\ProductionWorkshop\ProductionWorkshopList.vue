<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-space class="mb">
                <n-button secondary type="primary" @click="openEditModal()">新增</n-button>
                <!--<n-button secondary type="error" @click="onDelete()">批量删除</n-button>-->
            </n-space>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <!--新增编辑-->
        <ProductionWorkshopEditModal
            v-model:show="editModal.show"
            :configData="editModal.configData"
            @refresh="getTableData()"
        />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from "vue";
import type { DataTableColumns } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { TableActions } from "@/components/TableActions";
import { useCommonTable } from "@/hooks";
import { DELETE_WORK_SHOP, GET_WORK_SHOP_PAGE_LIST } from "@/api/application/production";
import ProductionWorkshopEditModal from "./ProductionWorkshopEditModal.vue";
import { GET_YD_COMPANY_LIST } from "@/api/permission";

interface RowProps {
    [key: string]: any;
}

onMounted(async () => {
    await getSearchOptions();
    getTableData();
});

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    {
        prop: "workshopName",
        type: "input",
        label: "车间名称"
    },
    {
        prop: "companyId",
        type: "select",
        label: "所属公司"
    }
]);

let searchOptions = ref<TableSearchbarOptions>({
    companyId: []
});

let searchForm = ref<TableSearchbarData>({
    workshopName: null,
    companyId: null
});

let getSearchOptions = async () => {
    await GET_YD_COMPANY_LIST({}).then((res) => {
        searchOptions.value.companyId = (res.data.data || []).map((item: any) => ({
            label: item.company,
            value: item.id
        }));
    });
};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "车间名称",
        key: "workshopName",
        align: "center"
    },
    {
        title: "车间主任",
        key: "workshopDirectorName",
        align: "center"
    },
    {
        title: "所属公司",
        key: "companyName",
        align: "center"
    },
    {
        title: "业务类型",
        key: "businessType",
        align: "center",
        render: (row) => {
            if (row.businessType === 1) {
                return "电力";
            } else if (row.businessType === 2) {
                return "塑业";
            } else {
                return "/";
            }
        }
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: "150",
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "编辑",
                        tertiary: true,
                        onClick: () => {
                            openEditModal(row);
                        }
                    },
                    {
                        label: "删除",
                        type: "error",
                        tertiary: true,
                        onClick: () => {
                            onDelete(row);
                        }
                    }
                ]
            });
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_WORK_SHOP_PAGE_LIST({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        ...searchForm.value
    }).then((res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data.records;
            tablePagination.itemCount = res.data.data.total;
            tableLoading.value = false;
        }
    });
};

// 搜索
let onSearch = () => {
    tablePagination.page = 1;
    tablePagination.pageSize = 10;
    getTableData();
};

// 新增编辑
let editModal = ref<{ show: boolean; configData: UnKnownObject }>({
    show: false,
    configData: {}
});

let openEditModal = (row?: RowProps) => {
    editModal.value = {
        show: true,
        configData: row ?? {}
    };
};

// 删除
let onDelete = (row: RowProps) => {
    window.$dialog.warning({
        title: "警告",
        content: "确定删除该车间吗？",
        positiveText: "删除",
        negativeText: "取消",
        onPositiveClick: () => {
            DELETE_WORK_SHOP({ id: row.id }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("删除成功");
                    onSearch();
                }
            });
        }
    });
};
</script>
