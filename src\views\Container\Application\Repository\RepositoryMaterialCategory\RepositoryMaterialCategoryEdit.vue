<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card
                :title="configData.id ? '编辑物资品类' : '新增物资品类'"
                class="w-1000px"
                closable
                @close="closeModal"
            >
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-grid :cols="12" :x-gap="16">
                        <n-form-item-gi :span="6" label="上级品类" path="parentId">
                            <n-tree-select
                                v-model:value="formData.parentId"
                                :options="parentIdOptions"
                                clearable
                                filterable
                                key-field="id"
                                label-field="categoryName"
                                placeholder="请选择上级品类"
                            />
                        </n-form-item-gi>
                        <!--<n-form-item-gi :span="6" label="所属库房" path="storeroomId">-->
                        <!--    <n-select-->
                        <!--        v-model:value="formData.storeroomId"-->
                        <!--        :options="storeroomIdOptions"-->
                        <!--        clearable-->
                        <!--        placeholder="请选择所属库房"-->
                        <!--    />-->
                        <!--</n-form-item-gi>-->
                        <n-form-item-gi :span="6" label="品类名称" path="categoryName">
                            <n-input
                                v-model:value="formData.categoryName"
                                class="w-100%"
                                clearable
                                placeholder="请输入品类名称"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="品类编号" path="categoryCode">
                            <n-input
                                v-model:value="formData.categoryCode"
                                class="w-100%"
                                clearable
                                placeholder="请输入品类编号"
                            />
                        </n-form-item-gi>
                        <!--<n-form-item-gi :span="6" label="品类类型" path="categoryType">-->
                        <!--    <n-select-->
                        <!--        v-model:value="formData.categoryType"-->
                        <!--        :options="categoryTypeOptions"-->
                        <!--        clearable-->
                        <!--        placeholder="请选择品类类型"-->
                        <!--    />-->
                        <!--</n-form-item-gi>-->
                        <n-form-item-gi :span="6" label="排序" path="showOrder">
                            <n-input-number
                                v-model:value="formData.showOrder"
                                class="w-100%"
                                clearable
                                placeholder="请输入排序"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="是否隐藏" path="hideFlag">
                            <n-radio-group v-model:value="formData.hideFlag">
                                <n-space>
                                    <n-radio :value="0">显示</n-radio>
                                    <n-radio :value="1">隐藏</n-radio>
                                </n-space>
                            </n-radio-group>
                        </n-form-item-gi>
                        <n-form-item-gi :span="12">
                            <n-space>
                                <n-button type="primary" @click="onSubmit">提交</n-button>
                                <n-button @click="closeModal">取消</n-button>
                            </n-space>
                        </n-form-item-gi>
                    </n-grid>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref, watchEffect } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import {
    ADD_REPOSITORY_CATEGORY,
    GET_REPOSITORY_CATEGORY_DETAIL,
    GET_REPOSITORY_CATEGORY_LIST,
    UPDATE_REPOSITORY_CATEGORY
} from "@/api/application/repository";

let props = withDefaults(
    defineProps<{
        show: boolean;
        configData: UnKnownObject;
    }>(),
    {
        show: () => false
    }
);

let emits = defineEmits(["update:show", "refresh"]);

watchEffect(() => {
    if (props.show) {
        getOptions();
    }
});

// 获取选项
let parentIdOptions = ref<any[]>([]);

// let storeroomIdOptions = ref<any[]>([]);
//
// // 1：原材料；2：产成品； 3：废料；4：零星物品
// let categoryTypeOptions = ref<any[]>([
//     { label: "原材料", value: 1 },
//     { label: "产成品", value: 2 },
//     { label: "废料", value: 3 },
//     { label: "零星物品", value: 4 }
// ]);

let getOptions = async () => {
    await GET_REPOSITORY_CATEGORY_LIST({}).then((res) => {
        parentIdOptions.value = res.data.data || [];
    });
    // await GET_REPOSITORY_STOREROOM_LIST({
    //     current: 1,
    //     size: 9999
    // }).then((res) => {
    //     storeroomIdOptions.value = (res.data.data.records || []).map((item: any) => ({
    //         label: item.storeroomName,
    //         value: item.id
    //     }));
    // });
};

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = computed(() => {
    return {
        parentId: [{ required: false, message: "请选择父级分类", trigger: "blur" }],
        // storeroomId: [{ required: true, message: "请选择所属库房", trigger: "blur" }],
        categoryName: [{ required: true, message: "请输入分类名称", trigger: "blur" }],
        categoryCode: [{ required: true, message: "请输入分类编号", trigger: "blur" }],
        // categoryType: [{ required: true, message: "请选择分类类型", trigger: "blur", type: "number" }],
        showOrder: [{ required: true, message: "请输入显示顺序", trigger: "blur", type: "number" }],
        hideFlag: [{ required: true, message: "请选择是否隐藏", trigger: ["blur", "change"], type: "number" }]
    };
});

// 表单数据
interface FormDataProps {
    /**
     * 父级id
     */
    parentId: Nullable<string>;
    /**
     * 所属库房
     */
    // storeroomId: Nullable<string>;
    /**
     * 分类名称
     */
    categoryName: string;
    /**
     * 分类编号
     */
    categoryCode: string;
    /**
     * 分类类型
     */
    // categoryType: Nullable<string | number>;
    /**
     * 显示顺序
     */
    showOrder: Nullable<string | number>;
    /**
     * 是否隐藏
     */
    hideFlag: Nullable<string | number>;
}

let initFormData: FormDataProps = {
    parentId: null,
    // storeroomId: null,
    categoryName: "",
    categoryCode: "",
    // categoryType: null,
    showOrder: 0,
    hideFlag: 0
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 获取详情
let getDetail = () => {
    GET_REPOSITORY_CATEGORY_DETAIL({ id: props.configData.id }).then((res) => {
        if (res.data.code === 0) {
            formData.value = {
                parentId: res.data.data.parentId,
                // storeroomId: res.data.data.storeroomId,
                categoryName: res.data.data.categoryName,
                categoryCode: res.data.data.categoryCode,
                // categoryType: res.data.data.categoryType,
                showOrder: res.data.data.showOrder,
                hideFlag: Number(res.data.data.hideFlag)
            };
        }
    });
};

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    emits("update:show", false);
};

// 监听
watchEffect(() => {
    if (props.show && props.configData.id) {
        getDetail();
    }
});

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;

    if (props.configData.id) {
        await UPDATE_REPOSITORY_CATEGORY({
            ...formData.value,
            parentId: formData.value.parentId || null,
            id: props.configData.id
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("编辑成功");
                closeModal();
                emits("refresh");
            }
        });
    } else {
        await ADD_REPOSITORY_CATEGORY({
            ...formData.value,
            parentId: formData.value.parentId || null
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("新增成功");
                closeModal();
                emits("refresh");
            }
        });
    }
};
</script>
