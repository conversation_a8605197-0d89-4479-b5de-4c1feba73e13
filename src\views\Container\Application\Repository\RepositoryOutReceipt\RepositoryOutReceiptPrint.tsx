import { computed, defineComponent, ref, watchEffect } from "vue";
import { GET_REPOSITORY_OUT_RECEIPT_DETAIL } from "@/api/application/repository";
import type { DataTableColumns } from "naive-ui";
import { useStoreUser } from "@/store";

export default defineComponent({
    name: "RepositoryOutReceiptPrint",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        const storeUser = useStoreUser();

        // 获取详情
        const detailData = ref<any>(null);

        const getDetail = async () => {
            await GET_REPOSITORY_OUT_RECEIPT_DETAIL({ id: props.configData.id }).then((res) => {
                if (res.data.code === 0) detailData.value = res.data.data;
            });
        };

        const hiddenCells = ref<Record<string, boolean>>({});

        const toggleCellVisibility = (key: string, rowIndex: number) => {
            const cellKey = `${key}-${rowIndex}`;
            hiddenCells.value[cellKey] = !hiddenCells.value[cellKey];
        };

        const restoreAllCells = () => {
            hiddenCells.value = {};
        };

        const tableColumns = ref<DataTableColumns<any>>([
            {
                title: "物料名称",
                key: "goodsName",
                align: "center",
                render: (row, rowIndex) => (
                    <div
                        onClick={() => toggleCellVisibility("goodsName", rowIndex)}
                        class="cursor-pointer min-h-30px flex-y-center w-100%"
                    >
                        <div class="w-100%">{hiddenCells.value[`goodsName-${rowIndex}`] ? "" : row.goodsName}</div>
                    </div>
                )
            },
            {
                title: "物料规格",
                key: "goodsSpec",
                align: "center",
                render: (row, rowIndex) => (
                    <div
                        onClick={() => toggleCellVisibility("goodsSpec", rowIndex)}
                        class="cursor-pointer min-h-30px flex-y-center w-100%"
                    >
                        <div class="w-100%">{hiddenCells.value[`goodsSpec-${rowIndex}`] ? "" : row.goodsSpec}</div>
                    </div>
                )
            },
            {
                title: "物料代码",
                key: "goodsCode",
                align: "center",
                render: (row, rowIndex) => (
                    <div
                        onClick={() => toggleCellVisibility("goodsCode", rowIndex)}
                        class="cursor-pointer min-h-30px flex-y-center w-100%"
                    >
                        <div class="w-100%">{hiddenCells.value[`goodsCode-${rowIndex}`] ? "" : row.goodsCode}</div>
                    </div>
                )
            },
            {
                title: "应出库数",
                key: "applyQuality",
                align: "center",
                render: (row, rowIndex) => (
                    <div
                        onClick={() => toggleCellVisibility("applyQuality", rowIndex)}
                        class="cursor-pointer min-h-30px flex-y-center w-100%"
                    >
                        <div class="w-100%">
                            {hiddenCells.value[`applyQuality-${rowIndex}`] ? "" : row.applyQuality}
                        </div>
                    </div>
                )
            },
            {
                title: "实出库数",
                key: "actualQuality",
                align: "center",
                render: (row, rowIndex) => (
                    <div
                        onClick={() => toggleCellVisibility("actualQuality", rowIndex)}
                        class="cursor-pointer min-h-30px flex-y-center w-100%"
                    >
                        <div class="w-100%">
                            {hiddenCells.value[`actualQuality-${rowIndex}`] ? "" : row.actualQuality}
                        </div>
                    </div>
                )
            },
            {
                title: "物料基本单位",
                key: "goodsUnitName",
                align: "center",
                render: (row, rowIndex) => (
                    <div
                        onClick={() => toggleCellVisibility("goodsUnitName", rowIndex)}
                        class="cursor-pointer min-h-30px flex-y-center w-100%"
                    >
                        <div class="w-100%">
                            {hiddenCells.value[`goodsUnitName-${rowIndex}`] ? "" : row.goodsUnitName}
                        </div>
                    </div>
                )
            },
            {
                title: "备注",
                key: "goodsRemark",
                align: "center",
                render: (row, rowIndex) => (
                    <div
                        onClick={() => toggleCellVisibility("goodsRemark", rowIndex)}
                        class="cursor-pointer min-h-30px flex-y-center w-100%"
                    >
                        <div class="w-100%">{hiddenCells.value[`goodsRemark-${rowIndex}`] ? "" : row.goodsRemark}</div>
                    </div>
                )
            }
        ]);

        const onClose = () => {
            changeModalShow(false);
            emit("refresh");
        };

        watchEffect(() => {
            if (props.configData.id) getDetail();
        });

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card title="打印预览" class="w-800px" closable onClose={onClose}>
                    <div class="flex-center">
                        <div id="printBox" class="w-100% mx-a box-border flex-y-center">
                            <div class="flex-1">
                                <div class="text-20px text-center">
                                    浙江永达电力实业股份有限公司 -{" "}
                                    {detailData.value?.outType === 2 ? "发货单" : "出库单"}
                                </div>
                                <n-form
                                    class="mt"
                                    label-placement="left"
                                    label-width="auto"
                                    size="small"
                                    show-feedback={false}
                                >
                                    <n-grid cols={12} x-gap={16} y-gap={8}>
                                        <n-form-item-gi span={6} label="申请人">
                                            {detailData.value?.applyByName ?? "/"}
                                        </n-form-item-gi>
                                        <n-form-item-gi span={6} label="所属部门">
                                            {detailData.value?.applyDepName ?? "/"}
                                        </n-form-item-gi>
                                        <n-form-item-gi span={6} label="出库类型">
                                            {detailData.value?.outType === 1 && "手动出库"}
                                            {detailData.value?.outType === 2 && "发货出库"}
                                            {detailData.value?.outType === 3 && "领料出库"}
                                        </n-form-item-gi>
                                        <n-form-item-gi span={6} label="出库状态">
                                            {detailData.value?.outState === 1 && "待出库"}
                                            {detailData.value?.outState === 2 && "已确认"}
                                            {detailData.value?.outState === 3 && "已拒绝"}
                                        </n-form-item-gi>
                                        <n-form-item-gi span={6} label="用途类型">
                                            {detailData.value?.purposeType === 1 && "原材料"}
                                            {detailData.value?.purposeType === 2 && "维修配件"}
                                            {detailData.value?.purposeType === 3 && "辅料"}
                                            {![1, 2, 3].includes(detailData.value?.purposeType) && "/"}
                                        </n-form-item-gi>
                                        {detailData.value?.outState === 3 && (
                                            <n-form-item-gi span={6} label="拒绝原因">
                                                {detailData.value?.rejectReason ?? "/"}
                                            </n-form-item-gi>
                                        )}
                                        {detailData.value?.outType === 2 && (
                                            <>
                                                <n-form-item-gi span={6} label="收货人姓名">
                                                    {detailData.value?.receiveUser ?? "/"}
                                                </n-form-item-gi>
                                                <n-form-item-gi span={6} label="收货人电话">
                                                    {detailData.value?.contactPhone ?? "/"}
                                                </n-form-item-gi>
                                                <n-form-item-gi span={6} label="收货地址">
                                                    {detailData.value?.receiveAddress ?? "/"}
                                                </n-form-item-gi>
                                                <n-form-item-gi span={6} label="发货方式">
                                                    {detailData.value?.deliveryWay === 1 ? "配送" : "物流"}
                                                </n-form-item-gi>
                                                {detailData.value?.deliveryWay === 1 && (
                                                    <>
                                                        <n-form-item-gi span={6} label="司机姓名">
                                                            {detailData.value?.driverName ?? "/"}
                                                        </n-form-item-gi>
                                                        <n-form-item-gi span={6} label="车牌号">
                                                            {detailData.value?.driverNo ?? "/"}
                                                        </n-form-item-gi>
                                                        <n-form-item-gi span={6} label="司机电话">
                                                            {detailData.value?.driverPhone ?? "/"}
                                                        </n-form-item-gi>
                                                    </>
                                                )}
                                                {detailData.value?.deliveryWay === 2 && (
                                                    <>
                                                        <n-form-item-gi span={6} label="物流公司">
                                                            {detailData.value?.logisticsCompany ?? "/"}
                                                        </n-form-item-gi>
                                                        <n-form-item-gi span={6} label="物流单号">
                                                            {detailData.value?.logisticsNo ?? "/"}
                                                        </n-form-item-gi>
                                                    </>
                                                )}
                                            </>
                                        )}
                                        <n-form-item-gi span={6} label="备注">
                                            {detailData.value?.remark ?? "/"}
                                        </n-form-item-gi>
                                        <n-form-item-gi class="mt-2" span={12}>
                                            <n-data-table
                                                class="print-data-table"
                                                size="small"
                                                columns={tableColumns.value}
                                                data={detailData.value?.recordItemList}
                                                single-line={false}
                                                bordered
                                                striped
                                            />
                                        </n-form-item-gi>
                                        <n-form-item-gi span={6} label="出库人">
                                            {storeUser.getUserData.sysUser?.trueName ?? "/"}
                                        </n-form-item-gi>
                                    </n-grid>
                                </n-form>
                            </div>
                            <div class="flex-fixed-80 ml-20px">①白联存更②红回单联③蓝记账联④绿客户联⑤黄运输联</div>
                        </div>
                    </div>
                    <div class="mt">
                        <n-alert title="温馨提示" type="warning">
                            <div>点击表格某一项可以展示/隐藏该项内容，底部“全部恢复按钮”可以恢复所有隐藏项。</div>
                        </n-alert>
                    </div>
                    <n-space class="mt">
                        <n-button v-print={{ id: "printBox" }} type="primary" size="large">
                            确认打印
                        </n-button>
                        <n-button type="error" size="large" onClick={() => onClose()}>
                            取消打印
                        </n-button>
                        <n-button type="default" size="large" onClick={restoreAllCells}>
                            全部恢复
                        </n-button>
                    </n-space>
                </n-card>
            </n-modal>
        );
    }
});
