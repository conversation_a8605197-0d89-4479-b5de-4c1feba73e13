<template>
    <div>
        <n-card content-style="padding-top:8px;padding-bottom:4px" hoverable>
            <div class="flex-y-center">
                <n-tabs v-model:value="tabActive" animated type="bar" @before-leave="onTabBeforeLeave">
                    <n-tab-pane :name="1" tab="产量/人工计划" />
                    <n-tab-pane :name="2" tab="外购/其他费用计划" />
                </n-tabs>
                <n-button type="primary" @click="changeActive(0)">返回选择</n-button>
            </div>
        </n-card>
        <div class="mt">
            <ReportingFillYearMain v-if="tabActive === 1" />
            <ReportingFillYearOther v-if="tabActive === 2" />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import ReportingFillYearMain from "./ReportingFillYearMain/ReportingFillYearMain.vue";
import ReportingFillYearOther from "./ReportingFillYearOther/ReportingFillYearOther.vue";
import { usePublic } from "@/hooks";

let { onTabBeforeLeave } = usePublic();

let props = withDefaults(defineProps<{ active: number }>(), {});

let emits = defineEmits(["update:active"]);

let changeActive = (item: number) => emits("update:active", item);

let tabActive = ref(1);
</script>
