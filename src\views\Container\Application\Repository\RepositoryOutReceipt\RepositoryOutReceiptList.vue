<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-space class="mb">
                <n-button secondary type="primary" @click="openEditModal()">新增出库单</n-button>
                <n-button secondary type="warning" @click="openDeliveryModal()">新增发货单</n-button>
                <!--<n-button secondary type="error" @click="openPrintModal()">测试功能，请勿使用</n-button>-->
            </n-space>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <!--新增编辑-->
        <RepositoryOutReceiptEdit
            v-model:show="editModal.show"
            :configData="editModal.configData"
            @refresh="getTableData()"
        />
        <!--新增发货单-->
        <RepositoryOutReceiptDelivery
            v-model:show="deliveryModal.show"
            :configData="deliveryModal.configData"
            @refresh="getTableData()"
        />
        <!--同意出库-->
        <RepositoryOutReceiptConfirm
            v-model:show="confirmModal.show"
            :configData="confirmModal.configData"
            @refresh="getTableData()"
        />
        <!--查看清单-->
        <RepositoryOutReceiptItemList v-model:show="itemListModal.show" :configData="itemListModal.configData" />
        <!--查看详情-->
        <RepositoryOutReceiptDetail v-model:show="detailModal.show" :configData="detailModal.configData" />
        <!--打印-->
        <RepositoryOutReceiptPrint v-model:show="printModal.show" :configData="printModal.configData" />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from "vue";
import { DataTableColumns, NButton, NInput, NText } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { TableActions } from "@/components/TableActions";
import { useCommonTable } from "@/hooks";
import { GET_REPOSITORY_OUT_RECEIPT_LIST, REJECT_REPOSITORY_OUT_RECEIPT } from "@/api/application/repository";
import RepositoryOutReceiptEdit from "./RepositoryOutReceiptEdit";
import RepositoryOutReceiptConfirm from "./RepositoryOutReceiptConfirm";
import RepositoryOutReceiptItemList from "@/views/Container/Application/Repository/RepositoryOutReceipt/RepositoryOutReceiptItemList";
import RepositoryOutReceiptDetail from "./RepositoryOutReceiptDetail";
import RepositoryOutReceiptDelivery from "@/views/Container/Application/Repository/RepositoryOutReceipt/RepositoryOutReceiptDelivery";
import RepositoryOutReceiptPrint from "@/views/Container/Application/Repository/RepositoryOutReceipt/RepositoryOutReceiptPrint";

interface RowProps {
    [key: string]: any;
}

onMounted(async () => {
    await getSearchOptions();
    getTableData();
});

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { label: "出库类型", prop: "outType", type: "select" },
    { label: "出库状态", prop: "outState", type: "select" }
]);

let searchOptions = ref<TableSearchbarOptions>({
    outType: [
        { label: "手动出库", value: 1 },
        { label: "发货出库", value: 2 },
        { label: "领料出库", value: 3 }
    ],
    outState: [
        { label: "待出库", value: 1 },
        { label: "已确认", value: 2 },
        { label: "已拒绝", value: 3 }
    ]
});

let searchForm = ref<TableSearchbarData>({
    outType: null,
    outState: null
});

let getSearchOptions = async () => {};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    { title: "出库单号", key: "id", align: "center" },
    {
        title: "出库类型",
        key: "outType",
        align: "center",
        render: (row) => {
            let outTypeArray = [
                { label: "手动出库", value: 1 },
                { label: "发货出库", value: 2 },
                { label: "领料出库", value: 3 }
            ];
            let outTypeObj = outTypeArray.find((item) => item.value === row.outType);
            return outTypeObj ? outTypeObj.label : "/";
        }
    },
    { title: "申请人", key: "applyByName", align: "center" },
    { title: "所属部门", key: "applyDepName", align: "center" },
    {
        title: "出库物品清单",
        key: "recordItemList",
        align: "center",
        width: 100,
        render: (row) => {
            return h(NButton, { type: "primary", onClick: () => openItemListModal(row) }, () => "点击查看");
        }
    },
    {
        title: "出库状态",
        key: "outState",
        align: "center",
        render: (row) => {
            if (row.outState === 1) {
                return h(NText, { type: "primary" }, () => "待出库");
            } else if (row.outState === 2) {
                return h(NText, { type: "success" }, () => "已确认");
            } else if (row.outState === 3) {
                return h(NText, { type: "error" }, () => "已拒绝");
            } else {
                return h(NText, { type: "default" }, () => "/");
            }
        }
    },
    {
        title: "用途类型",
        key: "purposeType",
        align: "center",
        render: (row) => {
            const purposeTypeList = [
                { label: "原材料", value: 1 },
                { label: "维修配件", value: 2 },
                { label: "辅料", value: 3 }
            ];
            const object = purposeTypeList.find((i: any) => i.value === row.purposeType);
            return object?.label ?? "/";
        }
    },
    { title: "拒绝原因", key: "rejectReason", align: "center", render: (row) => row.rejectReason || "/" },
    { title: "备注", key: "remark", align: "center" },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: "220",
        render(row) {
            if (row.outState === 1) {
                return h(TableActions, {
                    type: "button",
                    buttonActions: [
                        {
                            label: "同意出库",
                            type: "success",
                            tertiary: true,
                            disabled: () => row.outState !== 1,
                            onClick: () => openConfirmModal(row)
                        },
                        {
                            label: "拒绝出库",
                            type: "error",
                            tertiary: true,
                            disabled: () => row.outState !== 1,
                            onClick: () => onRefuseOut(row)
                        }
                    ]
                });
            } else {
                return h(TableActions, {
                    type: "button",
                    buttonActions: [
                        {
                            label: "查看详情",
                            tertiary: true,
                            onClick: () => openDetailModal(row)
                        }
                    ]
                });
            }
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_REPOSITORY_OUT_RECEIPT_LIST({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        ...searchForm.value
    }).then((res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data.records;
            tablePagination.itemCount = res.data.data.total;
            tableLoading.value = false;
        }
    });
};

// 搜索
let onSearch = () => {
    tablePagination.page = 1;
    tablePagination.pageSize = 10;
    getTableData();
};

// 新增编辑
let editModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

let openEditModal = (row?: RowProps) => {
    editModal.value = { show: true, configData: row ?? {} };
};

// 新增发货单
let deliveryModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

let openDeliveryModal = (row?: RowProps) => {
    deliveryModal.value = { show: true, configData: row ?? {} };
};

// 查看清单
let itemListModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

let openItemListModal = (row: RowProps) => {
    itemListModal.value = { show: true, configData: row };
};

// 确认拒绝
let confirmModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

let openConfirmModal = (row: RowProps) => {
    confirmModal.value = { show: true, configData: row };
};

let rejectReason = ref("");

let onRefuseOut = (row: RowProps) => {
    window.$dialog.warning({
        title: "提示",
        content: () => {
            return h("div", { class: "py-10px flex-y-center" }, [
                h(
                    "div",
                    {
                        class: "flex-fixed-120"
                    },
                    "请输入拒绝原因："
                ),
                h(NInput, {
                    value: rejectReason.value,
                    onUpdateValue: (v) => (rejectReason.value = v),
                    clearable: true,
                    placeholder: "请输入拒绝原因"
                })
            ]);
        },
        positiveText: "确认拒绝",
        negativeText: "我再想想",
        onPositiveClick: () => {
            if (!rejectReason.value) return window.$message.error("请输入拒绝原因");
            REJECT_REPOSITORY_OUT_RECEIPT({ id: row.id, rejectReason: rejectReason.value }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("操作成功");
                    getTableData();
                }
            });
        }
    });
};

// 详情弹窗
const detailModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

const openDetailModal = (row: RowProps) => {
    detailModal.value = { show: true, configData: row };
};

// 打印
const printModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

const openPrintModal = () => {
    printModal.value = { show: true, configData: {} };
};
</script>
