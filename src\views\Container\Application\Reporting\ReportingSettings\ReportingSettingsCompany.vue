<template>
    <div>
        <div class="flex">
            <n-card class="flex-fixed-300" hoverable>
                <n-checkbox-group v-model:value="needFillList" @update:value="changeNeedFillList">
                    <n-space size="large" vertical>
                        <n-checkbox v-for="item in companyList" :label="item.companyName" :value="item.id" />
                    </n-space>
                </n-checkbox-group>
            </n-card>
            <n-card class="flex-1 ml" hoverable>
                <n-h2>填报公司清单</n-h2>
                <div class="flex-y-center">
                    <n-button class="mr-2" type="success" @click="onSave()">确认保存</n-button>
                    <n-button class="mr-2" type="primary" @click="onClear()">清除选择</n-button>
                </div>
                <div class="mt">
                    <template v-for="item in companyList">
                        <div v-if="item.needFill === 1">
                            <div>{{ item.companyName }}</div>
                            <n-divider class="my!" />
                        </div>
                    </template>
                </div>
            </n-card>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { GET_REPORTING_COMPANY_LIST, SAVE_REPORTING_COMPANY_LIST } from "@/api/application/reporting";

onMounted(async () => {
    await getCompanyList();
});

// 树
let companyList = ref<any[]>([]);

let getCompanyList = async () => {
    await GET_REPORTING_COMPANY_LIST({ deptName: "" }).then((res) => {
        companyList.value = res.data.data || [];
        needFillList.value = companyList.value.filter((i) => i.needFill === 1).map((i) => i.id);
    });
};

let needFillList = ref<any[]>([]);

let changeNeedFillList = (val: any[]) => {
    companyList.value.forEach((i) => {
        if (val.includes(i.id)) {
            i.needFill = 1;
        } else {
            i.needFill = 0;
        }
    });
};
// 清除选择
let onClear = () => {
    companyList.value.forEach((i) => (i.needFill = 0));
    needFillList.value = [];
};

// 保存选择
let onSave = () => {
    let newArr = companyList.value.map((i) => ({ id: i.id, needFill: i.needFill }));
    SAVE_REPORTING_COMPANY_LIST(newArr).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("保存成功");
        } else {
            window.$message.error(res.data.msg);
        }
    });
};
</script>
