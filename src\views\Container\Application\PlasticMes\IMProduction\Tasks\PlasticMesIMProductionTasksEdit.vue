<template>
    <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
        <n-card
            :title="configData.id ? '编辑注塑生产任务单' : '新建注塑生产任务单'"
            class="w-1200px"
            closable
            @close="closeModal()"
        >
            <!-- 添加历史记录展示 -->
            <template v-if="storePlasticMes.imTaskHistory && storePlasticMes.imTaskHistory.length > 0">
                <div>
                    <div class="text-20px">历史记录</div>
                    <n-space>
                        <n-tag
                            type="primary"
                            class="mt cursor-pointer"
                            v-for="(item, index) in storePlasticMes.imTaskHistory ?? []"
                            :key="index"
                            @click="selectImTaskHistory(item)"
                            closable
                            @close="closeImTaskHistory(index)"
                        >
                            {{ item.orderCode }}
                        </n-tag>
                    </n-space>
                </div>
                <n-divider />
            </template>
            <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                <n-grid :cols="24" x-gap="16">
                    <n-form-item-gi :span="24" label="生产任务类型" path="productionType">
                        <n-radio-group v-model:value="formData.productionType">
                            <n-space>
                                <n-radio :value="1" label="销售订单" />
                                <n-radio :value="2" label="备货订单" />
                            </n-space>
                        </n-radio-group>
                    </n-form-item-gi>
                    <n-form-item-gi v-if="formData.productionType === 1" :span="12" label="订单编号" path="orderCode">
                        <n-input
                            v-model:value="formData.orderCode"
                            class="w-100%"
                            clearable
                            placeholder="请输入订单编号"
                        />
                    </n-form-item-gi>
                    <n-form-item-gi
                        v-if="formData.productionType === 1"
                        :span="12"
                        label="交货日期"
                        path="deliveryDate"
                    >
                        <n-date-picker
                            v-model:formatted-value="formData.deliveryDate"
                            class="w-100%"
                            clearable
                            placeholder="请选择交货日期"
                            type="date"
                            value-format="yyyy-MM-dd"
                        />
                    </n-form-item-gi>
                    <n-form-item-gi
                        v-if="formData.productionType === 1"
                        :span="12"
                        label="客户名称"
                        path="customerName"
                    >
                        <n-input
                            v-model:value="formData.customerName"
                            class="w-100%"
                            clearable
                            placeholder="请输入客户名称"
                        />
                    </n-form-item-gi>
                    <n-form-item-gi
                        v-if="formData.productionType === 1"
                        :span="12"
                        label="联系方式"
                        path="contactPhone"
                    >
                        <n-input-number
                            v-model:value="formData.contactPhone"
                            class="w-100%"
                            clearable
                            placeholder="请输入联系方式"
                        />
                    </n-form-item-gi>
                    <n-form-item-gi :span="12" label="备注" path="remark">
                        <n-input v-model:value="formData.remark" class="w-100%" clearable placeholder="请输入备注" />
                    </n-form-item-gi>
                </n-grid>
            </n-form>
            <SpecCountSelector v-model:value="itemList" @confirm="onSubmit" />
        </n-card>
    </n-modal>
</template>

<script lang="ts" setup>
import { computed, ref, watchEffect } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import {
    GET_IM_PRODUCTION_TASK_BY_ID,
    GET_IM_PRODUCTION_TASK_LIST,
    POST_IM_PRODUCTION_TASK,
    PUT_IM_PRODUCTION_TASK
} from "@/api/application/plasticMes";
import { SpecCountSelector } from "./components";
import dayjs from "dayjs";
import { useStorePlasticMes } from "@/store";

let props = withDefaults(defineProps<{ show: boolean; configData: UnKnownObject }>(), { show: () => false });

let emits = defineEmits(["update:show", "refresh"]);

let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

let changeModalShow = (show: boolean) => emits("update:show", show);

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {
    productionType: [{ required: true, message: "请选择生产任务类型", trigger: ["blur", "change"], type: "number" }],
    orderCode: [{ required: true, message: "请输入订单编号", trigger: ["input", "blur"] }],
    deliveryDate: [{ required: true, message: "请选择交货日期", trigger: ["blur", "change"] }],
    customerName: [{ required: true, message: "请输入客户名称", trigger: ["input", "blur"] }],
    contactPhone: [{ required: false, message: "请输入联系方式", trigger: ["input", "blur"], type: "number" }],
    remark: [{ required: false, message: "请输入备注", trigger: ["input", "blur"] }]
};

// 表单数据
interface FormDataProps {
    [key: string]: any;
}

let initFormData: FormDataProps = {
    productionType: 2, // 任务类型（1：销售订单，2：备货订单）
    orderCode: "", // 订单编号
    deliveryDate: null, // 交货日期
    customerName: "", // 客户名称
    contactPhone: null, // 联系方式
    remark: "" // 备注
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

let itemList = ref<any[]>([]);

let getDetail = () => {
    GET_IM_PRODUCTION_TASK_BY_ID({
        id: props.configData.id
    }).then((res) => {
        let resData = res.data.data;
        formData.value = {
            productionType: resData?.productionType,
            orderCode: resData?.orderCode,
            deliveryDate: resData?.deliveryDate ? dayjs(resData?.deliveryDate).format("YYYY-MM-DD") : null,
            customerName: resData?.customerName,
            contactPhone: Number(resData?.contactPhone) ?? null,
            remark: resData?.remark
        };
        itemList.value = resData?.itemList ?? [];
    });
};

watchEffect(() => {
    if (props.show && props.configData.id) {
        getDetail();
    }
});

// 表单操作按钮
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;

    if (props.configData.id) {
        PUT_IM_PRODUCTION_TASK({
            ...formData.value,
            id: props.configData.id,
            itemList: itemList.value ?? []
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("编辑成功");
                closeModal();
                emits("refresh");
            }
        });
    } else {
        POST_IM_PRODUCTION_TASK({
            ...formData.value,
            itemList: itemList.value ?? []
        }).then(async (res) => {
            if (res.data.code === 0) {
                // 从列表获取最新数据添加到历史记录
                await GET_IM_PRODUCTION_TASK_LIST({ current: 1, size: 1 }).then((res) => {
                    const resData = res.data.data.records[0];
                    if (resData) storePlasticMes.setImTaskHistory(resData);
                });
                window.$message.success("新增成功");
                closeModal();
                emits("refresh");
            }
        });
    }
};

let closeModal = () => {
    itemList.value = [];
    changeModalShow(false);
    clearFrom();
};

const storePlasticMes = useStorePlasticMes();

// 历史记录相关函数
const selectImTaskHistory = (item: any) => {
    if (formData.value.productionType === 1) {
        formData.value.orderCode = item.orderCode;
    }
};

const closeImTaskHistory = (index: number) => {
    window.$dialog.warning({
        title: "温馨提示",
        content: "确定要删除该条历史记录吗？该操作不可逆！",
        positiveText: "确定",
        negativeText: "取消",
        onPositiveClick: () => {
            storePlasticMes.deleteImTaskHistory(index);
        }
    });
};
</script>
