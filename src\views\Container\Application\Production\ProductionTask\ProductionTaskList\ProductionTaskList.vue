<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-tabs v-model:value="tabActive" animated class="mb" type="line">
                <n-tab-pane name="生产中">
                    <TaskInProduction :search-form="searchForm" />
                </n-tab-pane>
                <n-tab-pane name="检验中">
                    <TaskInTest :search-form="searchForm" />
                </n-tab-pane>
                <n-tab-pane name="未达标">
                    <TaskInFailed :search-form="searchForm" />
                </n-tab-pane>
                <n-tab-pane name="准入库">
                    <TaskInStorage :search-form="searchForm" />
                </n-tab-pane>
                <n-tab-pane name="完全入库">
                    <TaskInStorageEnd :search-form="searchForm" />
                </n-tab-pane>
            </n-tabs>
        </n-card>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { TaskInProduction, TaskInTest, TaskInFailed, TaskInStorage, TaskInStorageEnd } from "./Task";

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([]);

let searchOptions = ref<TableSearchbarOptions>({});

let searchForm = ref<TableSearchbarData>({});

let onSearch = () => {};

// tab切换
let tabActive = ref("生产中");
</script>
