<template>
    <div class="p-4">
        <!--<n-tabs type="line">-->
        <!--    <n-tab-pane name="待办" />-->
        <!--    <n-tab-pane name="已完成" />-->
        <!--</n-tabs>-->
        <n-data-table
            :columns="tableColumns"
            :data="tableData"
            :loading="tableLoading"
            :row-key="tableRowKey"
            :single-line="false"
            bordered
            remote
            striped
        />
    </div>
</template>

<script lang="ts" setup>
import { useCommonTable } from "@/hooks";
import { h, onMounted, ref } from "vue";
import { DataTableColumns, NText } from "naive-ui";
import { NDataTable } from "naive-ui";
import { GET_PROCESS_TASK_RUNNING } from "@/api/application/oa";
import { GET_PROJECT_LIST_PREHANDLE } from "@/api/application/sale";
import { GET_MESSAGE_LIST } from "@/api/application/message";

interface RowProps<T = string | null> {
    [propName: string]: any;
}

onMounted(() => {
    getTableData();
});

// 数据列表
let { tableRowKey, tableData, tableLoading } = useCommonTable<RowProps>("processInstanceId");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        title: "消息内容",
        key: "messageContent",
        align: "center"
    },
    {
        title: "创建时间",
        key: "createTime",
        align: "center"
    }
]);

let getTableData = () => {
    tableLoading.value = true;
    GET_MESSAGE_LIST({
        current: 1,
        size: 5
    }).then((res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data.records;
            tableLoading.value = false;
        }
    });
};
</script>
