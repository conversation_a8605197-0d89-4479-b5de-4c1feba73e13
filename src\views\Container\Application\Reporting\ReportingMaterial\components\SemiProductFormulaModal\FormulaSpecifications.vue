<template>
    <div>
        <n-data-table :columns="tableColumns" :data="tableData" :single-line="false" bordered striped />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, ref, watchEffect } from "vue";
import type { DataTableColumns } from "naive-ui";
import { GET_SEMI_MANUFACTURE_FORMULA_SPEC_LIST } from "@/api/application/reporting";
import { NText } from "naive-ui";
import { useDicts } from "@/hooks";
import { UnitSelector } from "@/views/Container/Application/Reporting/components";

let props = withDefaults(
    defineProps<{ configData: UnKnownObject; tabType: "manufactureSpecList" | "semiManufactureList" }>(),
    {}
);

let emits = defineEmits(["changeTab"]);

onMounted(async () => {
    await setDictLibs();
});

// 字典操作
let { dictLibs, getDictLibs } = useDicts();

let setDictLibs = async () => {
    let dictName = ["common_units"];
    await getDictLibs(dictName);
};

// 表单数据
interface RowProps {
    materialSpecId: Nullable<string>; // 原材料
    unitPrice: string; // 单价
    unit: string; // 单位
    amount: string; // 用量
    deviationRatio: string; // 偏差比例
    consumeCoefficient: string; // 用量系数

    [key: string]: any;
}

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        title: "原材料",
        key: "materialName",
        align: "center",
        render: (row) => {
            return `【${row.materialName}】${row.spec}`;
        }
    },
    {
        title: "单价",
        key: "unitPrice",
        align: "center"
    },
    {
        title: "价格计量单位",
        key: "unit",
        align: "center",
        render: (row) => {
            return h(UnitSelector, {
                type: "text",
                value: row.unit,
                options: dictLibs["common_units"] ?? []
            });
        }
    },
    {
        title: "用量",
        key: "amount",
        align: "center"
    },
    // 2023年9月8日单位变更需要对接数据-已处理
    {
        title: "用量计量单位",
        key: "amountUnit",
        align: "center",
        render: (row) => {
            return h(UnitSelector, {
                type: "text",
                value: row.amountUnit,
                options: dictLibs["common_units"] ?? []
            });
        }
    },
    {
        title: "用量系数",
        key: "consumeCoefficient",
        align: "center"
    },
    {
        title: "偏差比例",
        key: "deviationRatio",
        align: "center"
    },
    {
        title: "状态",
        align: "center",
        key: "specDelFlag",
        width: "100",
        render: (row) => {
            if (row.specDelFlag === 1) {
                return h(NText, { type: "error" }, () => "已删除");
            } else {
                return h(NText, { type: "primary" }, () => "正常");
            }
        }
    }
]);

// 可编辑表单配置
let tableItem: RowProps = {
    materialSpecId: null,
    unitPrice: "",
    unit: "",
    amount: "",
    consumeCoefficient: "",
    deviationRatio: ""
};

let addTableItem = () => {
    // tableData.value.push(cloneDeep(tableItem));
    emits("changeTab", "独立原材料");
};

let deleteItem = (index: number) => {
    tableData.value.splice(index, 1);
};

let tableData = ref<RowProps[]>([]);

let clearFrom = () => {
    tableData.value = [];
};

let getTableData = () => {
    GET_SEMI_MANUFACTURE_FORMULA_SPEC_LIST({ id: props.configData.id }).then((res) => {
        tableData.value = res.data.data;
    });
};

watchEffect(() => {
    if (props.configData.id) {
        getTableData();
    }
});

defineExpose({
    getTableData
});
</script>
