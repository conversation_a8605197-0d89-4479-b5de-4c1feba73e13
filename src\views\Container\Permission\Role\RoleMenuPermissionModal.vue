<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-600px" closable title="菜单权限" @close="closeModal">
                <n-spin :show="menuTreeLoading">
                    <n-tree
                        v-model:checked-keys="menuTreeChecked"
                        :data="menuTree"
                        block-line
                        checkable
                        key-field="id"
                        label-field="name"
                    />
                    <n-space class="mt">
                        <n-button type="primary" @click="onSubmit">提交</n-button>
                        <n-button @click="closeModal">取消</n-button>
                    </n-space>
                </n-spin>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import { GET_MENU_BY_ROLE_ID, GET_MENU_TREE, UPDATE_ROLE_MENU } from "@/api/permission";

let props = defineProps({
    show: { type: Boolean, default: false },
    id: { type: [String, Number] as PropType<any> }
});

let emits = defineEmits(["update:show", "refresh"]);

watch(
    () => ({ id: props.id, show: props.show }),
    (newVal) => {
        if (newVal.show) getMenuData();
    },
    { deep: true }
);

// 菜单树
let menuTree = ref<any[]>([]);

let menuTreeLoading = ref(false);

let getMenuData = async () => {
    menuTreeLoading.value = true;
    // 获取当前角色的菜单
    await GET_MENU_BY_ROLE_ID({ roleId: props.id }).then((res) => {
        menuTreeChecked.value = res.data.data || [];
    });
    // 获取菜单列表
    await GET_MENU_TREE({}).then((res) => {
        menuTree.value = res.data.data || [];
    });
    menuTreeLoading.value = false;
};

// 菜单权限选中
let menuTreeChecked = ref([]);

// 关闭弹窗
let closeModal = () => {
    menuTreeChecked.value = [];
    emits("update:show", false);
};

// 提交表单
let onSubmit = () => {
    console.log(props.id, menuTreeChecked.value);
    UPDATE_ROLE_MENU({
        roleId: props.id,
        menuIds: menuTreeChecked.value.join(",")
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("提交成功");
            closeModal();
            emits("refresh");
        }
    });
};
</script>
