<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-800px" closable @close="closeModal">
                <n-data-table
                    :columns="tableColumns"
                    :data="tableData"
                    :loading="tableLoading"
                    :row-key="tableRowKey"
                    :single-line="false"
                    bordered
                    remote
                    striped
                    @update:checked-row-keys="changeTableSelection"
                />
            </n-card>
        </n-modal>
        <project-approval
            v-model:show="projectApprovalModal.show"
            :config-data="projectApprovalModal.configData"
            type="child"
            @refresh="getTableData"
        />
        <!--绑定订单合同（匹配新订单）-->
        <ProjectBindOrderContract
            v-model:show="bindOrderContractModal.show"
            :config-data="bindOrderContractModal.configData"
            node-key="MatchContractOrder"
            @refresh="getTableData"
        />
    </div>
</template>

<script lang="ts" setup>
import { h, ref, watch } from "vue";
import type { DataTableColumns } from "naive-ui";
import { NButton } from "naive-ui";
import {
    CHILD_PROJECT_ADD_FINISH,
    GET_PROJECT_CHILD_LIST,
    SUBMIT_FINAL_PAYMENTTRACK_CHILD_FORM
} from "@/api/application/project";
import { useCommonTable, useDicts } from "@/hooks";
import { TableActions } from "@/components/TableActions";
import ProjectApproval from "../ProjectApproval/ProjectApproval.vue";
import { ProjectBindOrderContract } from "./components";

let props = defineProps({
    show: { type: Boolean, default: false },
    type: { type: String, default: "add" },
    id: { type: [String, Number] as PropType<any> },
    configData: { type: Object as PropType<any>, default: {} }
});

let emits = defineEmits(["update:show", "refresh"]);

// 数据列表
interface RowProps<T = string | null> {
    [propName: string]: any;
}

let { tableRowKey, tableData, tableLoading, tableSelection, changeTableSelection } =
    useCommonTable<RowProps>("projectId");

let tableColumns = ref<DataTableColumns<any>>([
    {
        title: "订单编号",
        key: "projectOrderManagement.pomNumber",
        align: "center"
    },
    {
        title: "框架合同号",
        key: "projectOrderManagement.pfcNumber",
        align: "center"
    },
    {
        title: "订单进度",
        key: "childProjectStatus",
        align: "center",
        render: (row: RowProps) => {
            return dictValueToLabel(row.childProjectStatus, "project_status") || "未知";
        }
    },
    {
        title: "当前流程",
        key: "nodeName",
        align: "center",
        render: (row: RowProps) => row.nodeName || "暂无"
    },
    {
        title: "下一步流程",
        key: "nextNodeName",
        align: "center",
        render: (row: RowProps) => {
            if (row.nextNodeName) {
                return h(
                    NButton,
                    {
                        text: true,
                        type: "primary",
                        size: "small",
                        onClick: () => {
                            if (row.projectStatus === 4) {
                                window.$message.error("项目已关闭，无法进行操作");
                                return false;
                            } else if (row.nextNodeKey === "FinalPaymentTrack") {
                                window.$dialog.warning({
                                    title: "确认信息",
                                    content: "是否需要发起尾款追踪流程？",
                                    positiveText: "需要",
                                    negativeText: "不需要",
                                    onPositiveClick: () => {
                                        finalPay(row.projectId, row.childProjectId, row.nextNodeKey);
                                    }
                                });
                            } else openProjectApprovalModal(row);
                        }
                    },
                    () => row.nextNodeName
                );
            } else return "暂无";
        }
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: 250,
        render(row: any) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "匹配新订单",
                        tertiary: true,
                        type: "warning",
                        disabled: () => String(projectInfo.value.isComplete) === "1",
                        onClick: () => {
                            openBindOrderContract(row);
                        }
                    },
                    {
                        label: "无后续订单",
                        tertiary: true,
                        disabled: () => String(projectInfo.value.isComplete) === "1",
                        type: "error",
                        onClick: () => {
                            window.$dialog.warning({
                                title: "确认信息",
                                content: "确认该项目无后续订单？",
                                positiveText: "确认",
                                negativeText: "再想想",
                                onPositiveClick: () => {
                                    CHILD_PROJECT_ADD_FINISH({ projectId: row.projectId }).then((res) => {
                                        if (res.data.code === 0) {
                                            window.$message.success("操作成功");
                                            getTableData();
                                        }
                                    });
                                }
                            });
                        }
                    }
                ]
            });
        }
    }
]);

// 子项目尾款提交
let finalPay = (id: string | number | null, cid: string | number | null, nodeKey: string | number | null) => {
    SUBMIT_FINAL_PAYMENTTRACK_CHILD_FORM({
        projectId: id,
        childProjectId: cid,
        nodeKey: nodeKey
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("尾款信息已提交");
            getTableData();
        }
    });
};

// 下一步流程
let projectApprovalModal = ref<any>({
    show: false,
    configData: {}
});

let openProjectApprovalModal = (row: any) => {
    projectApprovalModal.value.show = true;
    projectApprovalModal.value.configData = row;
};

// 获取详情
watch(
    () => ({ id: props.id, show: props.show }),
    async (newVal) => {
        await setDictLibs();
        if (newVal.show && newVal.id) getTableData();
    },
    { deep: true }
);

let { dictLibs, getDictLibs, dictValueToLabel } = useDicts();

let setDictLibs = async () => {
    let dictName = ["project_status"];
    await getDictLibs(dictName);
};

// 数据列表
let projectInfo = ref<any>({});

// 获取详情
let getTableData = () => {
    tableLoading.value = true;
    GET_PROJECT_CHILD_LIST({
        id: props.id
    }).then((res) => {
        projectInfo.value = res.data.data.project;
        tableData.value = res.data.data.childList || [];
        tableLoading.value = false;
    });
};

// 关闭弹窗
let closeModal = () => {
    emits("update:show", false);
};

// 绑定订单合同（匹配新项目）
let bindOrderContractModal = ref<any>({
    show: false,
    configData: {}
});

let openBindOrderContract = (row: any) => {
    bindOrderContractModal.value.show = true;
    bindOrderContractModal.value.configData = {
        ...row,
        contractId: props.configData?.contractId || null
    };
};
</script>
