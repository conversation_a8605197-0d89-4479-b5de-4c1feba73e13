<template>
    <div class="flex">
        <n-card class="flex-fixed-200" content-style="padding:0" hoverable>
            <n-menu
                v-model:value="tabActive"
                :indent="20"
                :options="tabOptions"
                class="flex-fixed-150 border-r-1px border-[#E5E5E5]"
                mode="vertical"
                @update:value="changeTabActive"
            />
        </n-card>
        <n-card class="flex-1 ml" content-style="padding-top:10px" hoverable>
            <n-tabs v-if="tabActive === 'receive'" animated type="bar">
                <n-tab-pane name="待确认领料单">
                    <ReceiveUnConfirmed />
                </n-tab-pane>
                <n-tab-pane name="已确认领料单">
                    <ReceiveConfirmed />
                </n-tab-pane>
                <n-tab-pane name="已拒绝领料单">
                    <ReceiveRejected />
                </n-tab-pane>
            </n-tabs>
            <n-tabs v-if="tabActive === 'entry'" animated type="bar">
                <n-tab-pane name="待确认入库单">
                    <InUnConfirmed />
                </n-tab-pane>
                <n-tab-pane name="已确认入库单">
                    <InConfirmed />
                </n-tab-pane>
                <n-tab-pane name="已拒绝入库单">
                    <InRejected />
                </n-tab-pane>
            </n-tabs>
            <n-tabs v-if="tabActive === 'exit'" animated type="bar">
                <n-tab-pane name="待处理发货申请">
                    <OutUnDelivery />
                </n-tab-pane>
                <n-tab-pane name="非生产领用出库">
                    <OutNonProduction />
                </n-tab-pane>
                <n-tab-pane name="未完结出库单">
                    <OutUnfinished />
                </n-tab-pane>
                <n-tab-pane name="生产虚拟出库">
                    <OutFictitious />
                </n-tab-pane>
            </n-tabs>
        </n-card>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import ReceiveUnConfirmed from "./WarehouseEntryExitReceive/WarehouseEntryExitReceiveUnConfirmed.vue";
import ReceiveConfirmed from "./WarehouseEntryExitReceive/WarehouseEntryExitReceiveConfirmed.vue";
import ReceiveRejected from "./WarehouseEntryExitReceive/WarehouseEntryExitReceiveRejected.vue";
import InUnConfirmed from "./WarehouseEntryExitIn/WarehouseEntryExitInUnConfirmed.vue";
import InConfirmed from "./WarehouseEntryExitIn/WarehouseEntryExitInConfirmed.vue";
import InRejected from "./WarehouseEntryExitIn/WarehouseEntryExitInRejected.vue";
import OutUnDelivery from "./WarehouseEntryExitOut/WarehouseEntryExitOutUnDelivery.vue";
import OutNonProduction from "./WarehouseEntryExitOut/WarehouseEntryExitOutNonProduction.vue";
import OutUnfinished from "./WarehouseEntryExitOut/WarehouseEntryExitOutUnfinished.vue";
import OutFictitious from "./WarehouseEntryExitOut/WarehouseEntryExitOutFictitious.vue";

let tabActive = ref("receive");

let changeTabActive = (key: string) => {};

let tabOptions = ref<any[]>([
    { label: "领料管理", key: "receive" },
    { label: "入库管理", key: "entry" },
    { label: "出库管理", key: "exit" }
    // { label: "出库数量确认", key: "outCountConfirm" }
]);
</script>

<style lang="scss" scoped>
::v-deep(.n-menu) {
    .n-menu-item {
        &:first-child {
            margin-top: 0;
        }

        .n-menu-item-content {
            &:before {
                left: 0;
                right: 0;
            }
        }

        .n-menu-item-content--selected {
            &:before {
                border-right: 2px solid var(--n-item-text-color-active);
            }
        }
    }
}
</style>
