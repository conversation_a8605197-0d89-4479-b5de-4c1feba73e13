<template>
    <div>
        <n-card hoverable>
            <n-tabs v-model:value="tabActive" animated type="bar">
                <n-tab-pane :name="1" tab="物资列表" />
            </n-tabs>
        </n-card>
        <div class="mt">
            <WarehouseMaterialList v-if="tabActive === 1" />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import WarehouseMaterialList from "./WarehouseMaterialList.vue";

let tabActive = ref(1);
</script>
