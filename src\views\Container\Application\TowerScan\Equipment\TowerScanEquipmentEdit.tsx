import { computed, defineComponent, h, ref, watchEffect } from "vue";
import { type DataTableColumns, type FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { useStoreUser } from "@/store";
import {
    ADD_EQUIPMENT,
    GET_EQUIPMENT_DETAIL,
    UPDATE_EQUIPMENT,
    GET_IRON_TECHNIQUE_TREE_LIST
} from "@/api/application/TowerScan";
import { UserSelector } from "@/components/UserSelector";
import { TableActions } from "@/components/TableActions";
import { useDicts } from "@/hooks";
import dayjs from "dayjs";

interface OperatorList {
    delFlag?: number;
    operator?: string;
    [property: string]: any;
}

export default defineComponent({
    name: "EquipmentEdit",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        const storeUser = useStoreUser();

        // 字典操作
        const { dictLibs, getDictLibs } = useDicts();

        const setDictLibs = async () => {
            const dictName = ["TechniqueProcessType"];
            await getDictLibs(dictName);
            dictLibs["TechniqueProcessType"] =
                dictLibs["TechniqueProcessType"]?.filter((item) => item.value !== "0" && item.value !== "1") || [];
        };

        // 获取详情
        const getDetail = () => {
            GET_EQUIPMENT_DETAIL({ id: props.configData.equipmentId }).then((res) => {
                console.log(res.data.code);
                if (res.data.code === 0) {
                    formData.value = {
                        equipmentName: res.data.data.equipmentName,
                        equipmentNumber: res.data.data.equipmentNumber,
                        techniqueId: res.data.data.techniqueId,
                        remark: res.data.data.remark,
                        operBy: res.data.data.operBy,
                        operTime: res.data.data.operTime
                    };
                    operatorList.value = res.data.data.operatorList || [];
                }
            });
        };

        // 表单数据
        interface FormDataProps {
            [key: string]: any;
        }

        const formRef = ref<FormInst | null>(null);

        // 工艺树数据
        const techniqueTreeData = ref<any[]>([]);
        const processTypeOptions = ref<any[]>([]);

        // 递归构建树节点
        const buildTreeNode = (item: any): any => {
            const node = {
                label: item.techniqueName,
                key: item.id,
                ...item
            };

            if (item.childrenList && item.childrenList.length > 0) {
                node.children = item.childrenList.map((child: any) => buildTreeNode(child));
            } else {
                node.isLeaf = true;
            }

            return node;
        };

        // 获取表单选项
        const getFormOptions = async () => {
            await setDictLibs();
            processTypeOptions.value = dictLibs["TechniqueProcessType"] || [];

            // 获取所有工艺分类的工艺数据
            const allTechniqueData: any[] = [];
            for (const processType of processTypeOptions.value) {
                const res = await GET_IRON_TECHNIQUE_TREE_LIST({
                    processType: processType.value
                });
                if (res.data.code === 0) {
                    const categoryData = {
                        label: processType.label,
                        key: `category_${processType.value}`,
                        children: (res.data.data || []).map((item: any) => buildTreeNode(item))
                    };
                    allTechniqueData.push(categoryData);
                }
            }
            techniqueTreeData.value = allTechniqueData;
        };

        const initFormData: FormDataProps = {
            equipmentName: null,
            equipmentNumber: null,
            techniqueId: null,
            remark: null,
            operBy: storeUser.getUserData.sysUser?.username,
            operTime: dayjs().format("YYYY-MM-DD HH:mm:ss")
        };

        const formRules = computed(() => ({
            equipmentName: [{ required: true, message: "请输入设备名称", trigger: ["input", "blur"] }],
            equipmentNumber: [{ required: true, message: "请输入设备编号", trigger: ["input", "blur"] }],
            techniqueId: [{ required: true, message: "请选择工序", trigger: ["blur", "change"] }]
        }));

        const formData = ref(cloneDeep(initFormData));

        const clearForm = () => {
            formData.value = cloneDeep(initFormData);
            operatorList.value = [];
        };

        // 操作工管理 - 改为表格模式
        const operatorList = ref<OperatorList[]>([]);

        const operatorTableColumns = ref<DataTableColumns<OperatorList>>([
            {
                title: "操作工",
                key: "operator",
                align: "center",
                render: (row) => {
                    return (
                        <UserSelector
                            v-model:value={row.operator}
                            class="w-100%"
                            key-name="username"
                            placeholder="请选择操作工"
                            multiple={false}
                        />
                    );
                }
            },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 100,
                render: (row, index) => {
                    return (
                        <TableActions
                            type="button"
                            buttonActions={[
                                {
                                    label: "删除",
                                    tertiary: true,
                                    type: "error",
                                    onClick: () => removeOperator(row, index)
                                }
                            ]}
                        />
                    );
                }
            }
        ]);

        const addOperator = () => {
            operatorList.value.push({
                operator: "",
                delFlag: 0
            });
        };

        const removeOperator = (row: OperatorList, index: number) => {
            if (row.id) {
                const operatorIndex = operatorList.value.findIndex((item) => item.id === row.id);
                if (operatorIndex !== -1) {
                    operatorList.value[operatorIndex].delFlag = 1;
                    operatorList.value.splice(operatorIndex, 1);
                }
            } else {
                operatorList.value.splice(index, 1);
            }
        };

        const onClose = () => {
            clearForm();
            changeModalShow(false);
            emit("refresh");
        };

        const onSubmit = async () => {
            let validateError = await formRef.value?.validate((errors) => !!errors);
            if (validateError) return false;

            const submitData = {
                ...formData.value,
                operatorList: operatorList.value
                    .filter((item) => item.operator)
                    .map((item) => ({
                        operator: item.operator,
                        delFlag: item.delFlag || 0,
                        ...(item.id && { id: item.id })
                    }))
            };

            if (props.configData.equipmentId) {
                await UPDATE_EQUIPMENT({
                    equipmentId: props.configData.equipmentId,
                    ...submitData
                }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("编辑成功");
                        onClose();
                    } else {
                        window.$message.error(res.data.msg ?? "编辑失败");
                    }
                });
            } else {
                await ADD_EQUIPMENT({
                    ...submitData
                }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("新增成功");
                        onClose();
                    } else {
                        window.$message.error(res.data.msg ?? "新增失败");
                    }
                });
            }
        };

        watchEffect(async () => {
            if (show.value) {
                await getFormOptions();
                if (props.configData.equipmentId) getDetail();
            }
        });

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card
                    title={props.configData.equipmentId ? "编辑设备" : "新增设备"}
                    class="w-1200px"
                    closable
                    onClose={onClose}
                >
                    <n-form
                        ref={formRef}
                        model={formData.value}
                        rules={formRules.value}
                        label-placement="left"
                        label-width="auto"
                    >
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi span={6} label="操作人" path="operBy" required>
                                <UserSelector
                                    value={formData.value.operBy}
                                    class="w-100%"
                                    disabled
                                    key-name="username"
                                    placeholder="请选择操作人"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="操作时间" path="operTime">
                                <n-date-picker
                                    v-model:formatted-value={formData.value.operTime}
                                    class="w-100%"
                                    clearable
                                    placeholder="请选择操作时间"
                                    type="datetime"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="设备编号" path="equipmentNumber">
                                <n-input
                                    v-model:value={formData.value.equipmentNumber}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入设备编号"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="设备名称" path="equipmentName">
                                <n-input
                                    v-model:value={formData.value.equipmentName}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入设备名称"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="工序" path="techniqueId">
                                <n-tree-select
                                    v-model:value={formData.value.techniqueId}
                                    class="w-100%"
                                    options={techniqueTreeData.value}
                                    clearable
                                    filterable
                                    placeholder="请选择工序"
                                    check-strategy="all"
                                    cascade
                                    onUpdate:value={(value: any) => {
                                        // 检查是否选择了第一级（工艺分类），如果是则不允许选择
                                        if (value && typeof value === "string" && value.startsWith("category_")) {
                                            formData.value.techniqueId = null;
                                            return window.$message.warning("请选择具体的工序，不能只选择工艺分类");
                                        }
                                        formData.value.techniqueId = value;
                                    }}
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="备注" path="remark">
                                <n-input
                                    v-model:value={formData.value.remark}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入备注"
                                />
                            </n-form-item-gi>

                            <n-form-item-gi span={12}>
                                <div class="w-100%">
                                    <n-space class="mb">
                                        <n-button type="success" onClick={addOperator}>
                                            + 添加操作工
                                        </n-button>
                                    </n-space>
                                    <n-data-table
                                        columns={operatorTableColumns.value}
                                        data={operatorList.value}
                                        single-line={false}
                                        bordered
                                        striped
                                    />
                                </div>
                            </n-form-item-gi>
                            <n-form-item-gi span={12}>
                                <n-space>
                                    <n-button type="primary" onClick={onSubmit}>
                                        提交
                                    </n-button>
                                    <n-button onClick={onClose}>取消</n-button>
                                </n-space>
                            </n-form-item-gi>
                        </n-grid>
                    </n-form>
                </n-card>
            </n-modal>
        );
    }
});
