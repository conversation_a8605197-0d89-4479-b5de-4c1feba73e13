<template>
    <div class="flex">
        <n-card class="flex-fixed-300">
            <div class="flex-y-center mb">
                <n-input v-model:value="treePattern" clearable placeholder="搜索" />
                <n-button class="ml-2" type="default" @click="selectTreeNode()">查看全部</n-button>
            </div>
            <n-tree
                v-model:selected-keys="treeSelectKeys"
                :cancelable="false"
                :data="treeData"
                :pattern="treePattern"
                :show-irrelevant-nodes="false"
                block-line
                children-field="childrenList"
                default-expand-all
                key-field="id"
                label-field="categoryName"
                selectable
                @update:selected-keys="selectTreeNode"
            />
        </n-card>
        <div class="flex-1 ml">
            <n-card hoverable>
                <table-searchbar
                    v-model:form="searchForm"
                    :config="searchConfig"
                    :options="searchOptions"
                    @search="onSearch"
                />
            </n-card>
            <n-card class="mt" hoverable>
                <n-space class="mb">
                    <n-button secondary type="primary" @click="openEditModal()">新增</n-button>
                </n-space>
                <n-data-table
                    :columns="tableColumns"
                    :data="tableData"
                    :loading="tableLoading"
                    :pagination="tablePagination"
                    :row-key="tableRowKey"
                    :single-line="false"
                    bordered
                    remote
                    striped
                    @update:checked-row-keys="changeTableSelection"
                />
            </n-card>
        </div>
        <!--新增编辑-->
        <RepositoryMaterialEdit
            v-model:show="editModal.show"
            :configData="editModal.configData"
            @refresh="getTableData()"
        />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from "vue";
import { DataTableColumns, NInput } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { TableActions } from "@/components/TableActions";
import { useCommonTable, useDicts } from "@/hooks";
import {
    BIND_GOODS_PRODUCT_TYPE,
    GET_REPOSITORY_CATEGORY_LIST,
    GET_REPOSITORY_GOODS_LIST,
    GET_REPOSITORY_STOREROOM_LIST
} from "@/api/application/repository";
import RepositoryMaterialEdit from "./RepositoryMaterialEdit.vue";
import { PlasticProductTypeSelector } from "@/components/PlasticProductTypeSelector";

interface RowProps {
    [key: string]: any;
}

// 字典操作
let { dictLibs, getDictLibs } = useDicts();

onMounted(async () => {
    await setDictLibs();
    await getSearchOptions();
    await getTreeData();
    getTableData();
});

let setDictLibs = async () => {
    let dictName = ["common_units"];
    await getDictLibs(dictName);
};

// 树形查询
let treeData = ref<any[]>([]);

let treePattern = ref("");

let treeSelectKeys = ref<(string | number)[]>([]);

let getTreeData = async () => {
    await GET_REPOSITORY_CATEGORY_LIST({
        current: 1,
        size: 9999
    }).then((res) => {
        if (res.data.code === 0) {
            treeData.value = res.data.data ?? [];
        }
    });
};
let selectTreeNode = (keys?: (string | number)[], option?: any[]) => {
    treeSelectKeys.value = keys ?? [];
    onSearch();
};

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { label: "物资名称", prop: "goodsName", type: "input" },
    { label: "所属仓库", prop: "storeroomIds", type: "select" },
    { label: "库存情况", prop: "hasCapacity", type: "radio" }
]);

let searchOptions = ref<TableSearchbarOptions>({
    storeroomIds: [],
    hasCapacity: [
        { label: "有库存", value: 1 },
        { label: "无库存", value: 0 }
    ]
});

let searchForm = ref<TableSearchbarData>({
    goodsName: null,
    storeroomIds: null,
    hasCapacity: null
});

let getSearchOptions = async () => {
    GET_REPOSITORY_STOREROOM_LIST({ current: 1, size: 9999, lockFlag: 0 }).then((res) => {
        searchOptions.value.storeroomIds = (res.data.data.records || []).map((i: any) => ({
            label: i.storeroomName,
            value: i.id
        }));
    });
};

// 产成品规格ID
let fromId = ref<any>(null);

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    { title: "物资编码", key: "goodsCode", align: "center" },
    { title: "物资名称", key: "goodsName", align: "center" },
    { title: "物资规格", key: "goodsSpec", align: "center" },
    { title: "所属品类", key: "categoryName", align: "center" },
    { title: "物料单位", key: "unit", align: "center", render: (row) => getUnitText(row.unit) },
    { title: "辅助单位", key: "auxiliaryUnit", align: "center", render: (row) => getUnitText(row.auxiliaryUnit) },
    {
        title: "绑定产成品",
        key: "categoryName",
        align: "center",
        render: (row) => {
            if (row.fromId) {
                return row.degree + "-" + row.series + "-" + row.specification;
            } else {
                return "未绑定";
            }
        }
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: "220",
        render: (row) => {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "编辑信息",
                        tertiary: true,
                        onClick: () => {
                            openEditModal(row);
                        }
                    },
                    {
                        label: "绑定产成品",
                        tertiary: true,
                        type: "warning",
                        onClick: () => {
                            if (row.fromId) {
                                fromId.value = row.fromId;
                            } else {
                                fromId.value = null;
                            }
                            window.$dialog.warning({
                                title: "绑定产成品",
                                content: () => {
                                    return h("div", { class: "py" }, [
                                        h(PlasticProductTypeSelector, {
                                            value: fromId.value,
                                            placeholder: "请选择产成品",
                                            keyName: "id",
                                            onSubmit: (v: any) => {
                                                fromId.value = v;
                                            }
                                        })
                                    ]);
                                },
                                positiveText: "提交",
                                negativeText: "取消",
                                onPositiveClick: () => {
                                    BIND_GOODS_PRODUCT_TYPE({
                                        goodsId: row.id,
                                        goodsFrom: 1,
                                        fromId: fromId.value
                                    }).then((res) => {
                                        if (res.data.code === 0) {
                                            window.$message.success("绑定成功");
                                            getTableData();
                                        } else {
                                            window.$message.error(res.data.msg ?? "绑定失败");
                                        }
                                        fromId.value = null;
                                    });
                                },
                                onNegativeClick: () => {
                                    fromId.value = null;
                                }
                            });
                        }
                    }
                ]
            });
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_REPOSITORY_GOODS_LIST({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        categoryId: treeSelectKeys.value[0] ?? null,
        ...searchForm.value
    }).then((res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data.records;
            tablePagination.itemCount = res.data.data.total;
            tableLoading.value = false;
        }
    });
};

// 搜索
let onSearch = () => {
    tablePagination.page = 1;
    tablePagination.pageSize = 10;
    getTableData();
};

// 新增编辑
let editModal = ref<{ show: boolean; configData: UnKnownObject }>({
    show: false,
    configData: {}
});

let openEditModal = (row?: RowProps) => {
    editModal.value = {
        show: true,
        configData: row ?? {}
    };
};

let getUnitText = (value: Nullable<string>): string => {
    let object = (dictLibs["common_units"] || []).find((item: any) => item.value === String(value));
    return object?.label ?? "未知";
};
</script>
