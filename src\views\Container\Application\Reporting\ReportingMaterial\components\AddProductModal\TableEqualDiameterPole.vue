<template>
    <div class="w-100%">
        <n-data-table :columns="tableColumns" :data="tableData" :single-line="false" bordered striped />
        <div class="flex-x-center mt" v-if="showButtons">
            <n-space>
                <n-button type="primary" @click="addTableItem">新增一行</n-button>
                <n-button type="success" @click="onSubmit">保存全部</n-button>
            </n-space>
        </div>
    </div>
</template>

<script lang="ts" setup>
import type { VNode } from "vue";
import { computed, h, onMounted, ref } from "vue";
import type { DataTableColumns, SelectOption } from "naive-ui";
import { NInput, NSelect, NTooltip } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { useDicts } from "@/hooks";

let props = withDefaults(defineProps<{ value: RowProps[]; showButtons?: boolean }>(), {
    value: () => [],
    showButtons: true
});

let emits = defineEmits(["confirm", "update:value"]);

let tableData = computed({ get: () => props.value, set: (val) => emits("update:value", val) });

onMounted(async () => {
    await setDictLibs();
});

// 获取字典
let { dictLibs, getDictLibs } = useDicts();

let setDictLibs = async () => {
    let dictName = ["pole_type"];
    await getDictLibs(dictName);
};

// 表单数据
interface RowProps {
    poleCode: string; // 名称代号
    reinforceBar: string; // 配筋
    nameSuffix: string; // 后缀
    standardNumber: string; // 标准编号
    wallThickness: string; // 壁厚(mm)
    outsideDiameter: string; // 外径(mm)
    poleType: Nullable<string>; // 类型
    extent: string; // 长度(m)
    concreteAmount: string; // 扣除钢筋混凝土用量(m³)

    [key: string]: any;
}

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        title: "名称代号",
        key: "poleCode",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.poleCode,
                onUpdateValue: (v) => (row.poleCode = v)
            });
        }
    },
    {
        title: "配筋",
        key: "reinforceBar",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.reinforceBar,
                onUpdateValue: (v) => (row.reinforceBar = v)
            });
        }
    },
    {
        title: "后缀",
        key: "nameSuffix",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.nameSuffix,
                onUpdateValue: (v) => (row.nameSuffix = v)
            });
        }
    },
    {
        title: "标准编号",
        key: "standardNumber",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.standardNumber,
                onUpdateValue: (v) => (row.standardNumber = v)
            });
        }
    },
    {
        title: "壁厚(mm)",
        key: "wallThickness",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.wallThickness,
                onUpdateValue: (v) => (row.wallThickness = v)
            });
        }
    },
    {
        title: "外径(mm)",
        key: "outsideDiameter",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.outsideDiameter,
                onUpdateValue: (v) => (row.outsideDiameter = v)
            });
        }
    },
    {
        title: "类型",
        key: "poleType",
        align: "center",
        render: (row) => {
            return h(NSelect, {
                options: dictLibs["pole_type"] as any[],
                clearable: true,
                filterable: true,
                placeholder: "请选择类型",
                value: row.poleType,
                onUpdateValue: (v) => (row.poleType = v),
                renderOption: ({ node, option }: { node: VNode; option: SelectOption }) => {
                    return h(
                        NTooltip,
                        {},
                        {
                            trigger: () => node,
                            default: () => option.label
                        }
                    );
                }
            });
        }
    },
    {
        title: "长度(m)",
        key: "extent",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.extent,
                onUpdateValue: (v) => (row.extent = v)
            });
        }
    },
    {
        title: "扣除钢筋混凝土用量(m³)",
        key: "concreteAmount",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.concreteAmount,
                onUpdateValue: (v) => (row.concreteAmount = v)
            });
        }
    }
]);

// 可编辑表单配置
let tableItem: RowProps = {
    poleCode: "",
    reinforceBar: "",
    nameSuffix: "",
    standardNumber: "",
    wallThickness: "",
    outsideDiameter: "",
    poleType: null,
    extent: "",
    concreteAmount: ""
};

let addTableItem = () => {
    tableData.value.push(cloneDeep(tableItem));
};

// let tableData = ref<RowProps[]>([]);

// 提交
let onSubmit = () => {
    emits("confirm", tableData.value);
};
</script>
