<template>
    <div v-if="formData">
        <n-card class="w-100% mt-15px" hoverable title="项目进度展示">
            <n-tabs type="line">
                <n-tab-pane name="项目总进度">
                    <n-scrollbar x-scrollable>
                        <div class="pt-20px flex-y-center">
                            <div v-for="(item, index) in formData.projectNodeConfigVoList" class="step-box">
                                <div class="flex-y-center">
                                    <div class="round">{{ index + 1 }}</div>
                                    <div v-if="isShowLine(formData.projectNodeConfigVoList, index)" class="line" />
                                </div>
                                <div class="name">{{ item.nodeName }}</div>
                                <div>
                                    状态：
                                    <template v-if="item.nodeKey === 'BidUpload'">
                                        <n-text v-if="formData.bidUploadType === 1" type="success"> 线下邮寄</n-text>
                                        <n-text v-else-if="formData.bidUploadType === 2" type="success">
                                            线上上传
                                        </n-text>
                                        <n-text v-else>未知</n-text>
                                    </template>
                                    <template v-else-if="item.nodeKey === 'IsWinBid'">
                                        <n-text v-if="formData.isWinBid === 0" type="error"> 未中标</n-text>
                                        <n-text v-else-if="formData.isWinBid === 1" type="success"> 已中标</n-text>
                                        <n-text v-else>未知</n-text>
                                    </template>
                                    <template v-else>
                                        <span
                                            :style="`color:${
                                                dictValueToAll(item.nodeStatus, 'node_status').color || ''
                                            }`"
                                        >
                                            {{ dictValueToAll(item.nodeStatus, "node_status").label || "" }}
                                        </span>
                                    </template>
                                </div>
                                <div>
                                    <template v-if="item.processInstanceId">
                                        审批单：
                                        <span
                                            v-if="item.formKey"
                                            class="name cursor-pointer"
                                            @click="openProcessDetailModal(item)"
                                        >
                                            查看详情
                                        </span>
                                        <span v-else>暂无</span>
                                    </template>
                                    <div v-else class="nodeTime op0">占位符</div>
                                </div>
                                <div class="nodeTime">
                                    {{ item.createTime }}
                                </div>
                            </div>
                            <!--<div v-if="formData.projectNodeConfigVoList?.length === 1" class="step-box">-->
                            <!--    <div class="flex-y-center">-->
                            <!--        <div class="round roundready">2</div>-->
                            <!--    </div>-->
                            <!--    <div class="name">暂无反馈</div>-->
                            <!--    <div>状态：<span class="ready">待反馈</span></div>-->
                            <!--    <div>审批单：暂无</div>-->
                            <!--    <div class="nodeTime op-0">占位符</div>-->
                            <!--                            </div>-->
                        </div>
                    </n-scrollbar>
                </n-tab-pane>
                <n-tab-pane v-if="(formData.childProjectInfoVoList || [])?.length > 0" name="订单进度">
                    <div class="pt-20px">
                        <n-collapse>
                            <n-collapse-item
                                v-for="(item, index) in formData.childProjectInfoVoList || []"
                                :name="index"
                                :title="`订单编号：${item.projectOrderManagement
.pomNumber}`"
                            >
                                <div class="flex-y-center">
                                    <div v-for="(citem, cindex) in item.childProjectNodeConfigList" class="step-box">
                                        <div class="flex-y-center">
                                            <div class="round">{{ cindex + 1 }}</div>
                                            <div
                                                v-if="isShowLine(item.childProjectNodeConfigList, cindex)"
                                                class="line"
                                            />
                                        </div>
                                        <div class="name">{{ citem.nodeName }}</div>
                                        <div>
                                            状态：
                                            <span
                                                :style="`color:${
                                                    dictValueToAll(citem.nodeStatus, 'node_status').color || ''
                                                }`"
                                            >
                                                {{ dictValueToAll(citem.nodeStatus, "node_status").label || "" }}
                                            </span>
                                        </div>
                                        <div>
                                            <template v-if="citem.processInstanceId">
                                                审批单：
                                                <span
                                                    v-if="citem.formKey"
                                                    class="name cursor-pointer"
                                                    @click="openProcessDetailModal(citem)"
                                                >
                                                    查看详情
                                                </span>
                                                <span v-else>暂无</span>
                                            </template>
                                            <div v-else class="nodeTime op0">占位符</div>
                                        </div>
                                        <div class="nodeTime">
                                            {{ citem.createTime }}
                                        </div>
                                    </div>
                                </div>
                            </n-collapse-item>
                        </n-collapse>
                    </div>
                </n-tab-pane>
            </n-tabs>
        </n-card>
        <!--审批单详情-->
        <ProcessDetail v-model:show="processDetailModal.show" :config-data="processDetailModal.configData" />
    </div>
</template>

<script lang="ts" setup>
import { ref, watchEffect } from "vue";
import { useDicts } from "@/hooks";
import { ProcessDetail } from "@/views/Container/Application/Process/components";

interface FormDataProps {
    [key: string]: any;
}

let props = withDefaults(defineProps<{ formData: FormDataProps }>(), {});

// 字典操作
let { dictLibs, getDictLibs, dictValueToLabel, dictValueToAll } = useDicts();

let setDictLibs = async () => {
    let dictName = ["project_status", "node_status", "project_type"];
    await getDictLibs(dictName);
};

watchEffect(async () => {
    if (props.formData) await setDictLibs();
});

// 是否显示线
let isShowLine = (list: any[], index: number) => {
    return index + 1 !== list.length;
    // return index + 1 !== list.length || list?.length === 1;
};

// 单据详情
let processDetailModal = ref<{ show: boolean; configData: FormDataProps }>({ show: false, configData: {} });

let openProcessDetailModal = (row: FormDataProps) => {
    processDetailModal.value.show = true;
    processDetailModal.value.configData = row;
};
</script>

<style lang="scss" scoped>
.ready {
    color: orange;
}

.step-box {
    width: 18%;
    min-width: 180px;
    margin-right: 5px;

    .line {
        width: 80%;
        height: 1px;
        border-bottom: 1px solid #cecece;
        margin-left: 10px;
    }

    .round {
        width: 20px;
        height: 20px;
        text-align: center;
        line-height: 20px;
        background: green;
        border-radius: 50%;
        color: #fff;
    }

    .roundready {
        background: orange;
    }

    .name {
        color: #7faadf;
        margin-top: 10px;
    }

    .nodeTime {
        color: #cecece;
    }
}
</style>
