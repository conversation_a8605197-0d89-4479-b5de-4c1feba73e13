import { computed, defineComponent, ref, watchEffect } from "vue";
import type { UploadFileInfo, FormInst } from "naive-ui";
import { DynamicIcon } from "@/components/DynamicIcon";
import { UserSelector } from "@/components/UserSelector";
import { useStoreUser } from "@/store";
import { PROCESS_IMPORT_FROM_EXCEL, GET_IRON_PROJECT_TYPE_DETAIL } from "@/api/application/TowerScan";

export default defineComponent({
    name: "TowerScanEngineeringProcessesImport",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        const storeUser = useStoreUser();
        const formRef = ref<FormInst | null>(null);

        // 塔型详情
        const typeDetail = ref<any>({});
        
        const getTypeDetail = async () => {
            if (props.configData.id) {
                const res = await GET_IRON_PROJECT_TYPE_DETAIL({ typeId: props.configData.id });
                if (res.data.code === 0) {
                    typeDetail.value = res.data.data;
                }
            }
        };

        // 表单数据
        const formData = ref({
            operator: storeUser.getUserData.sysUser?.username || ""
        });

        // 文件列表
        const fileList = ref<UploadFileInfo[]>([]);

        // 提交loading状态
        const submitLoading = ref(false);

        const onBeforeUpload = async (fileData: { file: UploadFileInfo; fileList: UploadFileInfo[] }) => {
            // 限制文件类型
            const allowedTypes = [
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-excel'
            ];
            
            if (!allowedTypes.includes(fileData.file.file?.type || '')) {
                window.$message.error('请上传Excel文件（.xlsx或.xls格式）');
                return false;
            }
            
            return true;
        };

        const onDownloadTemplate = () => {
            // 这里需要根据实际的模板下载地址进行调整
            window.location.href = import.meta.env.VITE_API_URL + "/admin/sys-file/local/IronProcess.xls";
        };

        const onClose = () => {
            changeModalShow(false);
            formData.value.operator = storeUser.getUserData.sysUser?.username || "";
            fileList.value = [];
        };

        const onSubmit = async () => {
            let validateError = await formRef.value?.validate((errors) => !!errors);
            if (validateError) return false;

            if (!formData.value.operator) {
                window.$message.error("请选择操作人");
                return;
            }
            
            if (!fileList.value.length) {
                window.$message.error("请上传文件");
                return;
            }

            submitLoading.value = true;

            const submitFormData = new FormData();
            submitFormData.append("file", fileList.value[0].file as File);
            submitFormData.append("typeId", String(props.configData.id));
            submitFormData.append("operator", formData.value.operator);

            try {
                const res = await PROCESS_IMPORT_FROM_EXCEL(submitFormData as any);
                if (res.data.code === 0) {
                    window.$message.success("导入成功");
                    onClose();
                    emit("refresh");
                } else {
                    window.$message.error(res.data.msg || "导入失败");
                }
            } catch (error) {
                window.$message.error("导入失败");
            } finally {
                submitLoading.value = false;
            }
        };

        watchEffect(() => {
            if (show.value) {
                getTypeDetail();
            }
        });

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card class="w-600px" title="导入工序表" closable onClose={onClose}>
                    <n-form ref={formRef} model={formData.value} label-placement="left" label-width="auto">
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi span={6} label="操作人" required>
                                <UserSelector
                                    v-model:value={formData.value.operator}
                                    class="w-100%"
                                    key-name="username"
                                    placeholder="请选择操作人"
                                    multiple={false}
                                    disabled
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="塔型信息">
                                <n-text type="info">
                                    {typeDetail.value?.typeName ? `${typeDetail.value.typeName}` : "未选择塔型"}
                                </n-text>
                            </n-form-item-gi>
                        </n-grid>

                        <n-form-item label="上传文件" required>
                            <div class="w-100%">
                                <n-upload 
                                    v-model:file-list={fileList.value} 
                                    directory-dnd 
                                    onBeforeUpload={onBeforeUpload}
                                    max={1}
                                    accept=".xlsx,.xls"
                                >
                                    <n-upload-dragger>
                                        <div class="mb-4">
                                            <DynamicIcon icon="UploadOutlined" />
                                        </div>
                                        <n-p class="text-16px">点击或拖动文件到该区域上传</n-p>
                                        <n-text depth="3" class="text-12px">
                                            支持Excel文件格式（.xlsx、.xls）
                                        </n-text>
                                    </n-upload-dragger>
                                </n-upload>
                                <n-button class="mt-10px" text type="primary" onClick={onDownloadTemplate}>
                                    点击此处下载批量导入模板
                                </n-button>
                            </div>
                        </n-form-item>

                        <n-form-item>
                            <n-space>
                                <n-button type="primary" loading={submitLoading.value} onClick={onSubmit}>
                                    提交
                                </n-button>
                                <n-button onClick={onClose} disabled={submitLoading.value}>
                                    取消
                                </n-button>
                            </n-space>
                        </n-form-item>
                    </n-form>
                </n-card>
            </n-modal>
        );
    }
}); 