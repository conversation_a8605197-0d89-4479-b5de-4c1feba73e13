<template>
    <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
        <n-card class="w-800px" closable title="确认领料出库" @close="changeModalShow(false)"></n-card>
    </n-modal>
</template>

<script lang="ts" setup>
import { computed, ref } from "vue";
import { cloneDeep } from "lodash-es";
import type { FormInst } from "naive-ui";
import { UserSelector } from "@/components/UserSelector";
import { FileValueUploader } from "@/components/Uploader";
import { POST_BID_WIN_NOTIFICATION } from "@/api/application/sale";

let props = withDefaults(defineProps<{ show: boolean; configData: UnKnownObject }>(), { show: () => false });

let emits = defineEmits(["update:show", "refresh"]);

let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

let changeModalShow = (show: boolean) => emits("update:show", show);
</script>
