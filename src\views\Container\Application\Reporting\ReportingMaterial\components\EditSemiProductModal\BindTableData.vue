<template>
    <div class="w-100%">
        <n-data-table :columns="tableColumns" :data="tableData" :single-line="false" bordered striped />
        <div class="flex-x-center mt">
            <n-space>
                <n-button v-if="showButtons" type="primary" @click="addTableItem">新增一行</n-button>
                <n-button type="success" @click="onSubmit">保存全部</n-button>
            </n-space>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed, h, onMounted, ref, watchEffect } from "vue";
import type { DataTableColumns } from "naive-ui";
import { NInput, NSelect } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { GET_MANUFACTURE_PAGE_LIST } from "@/api/application/reporting";
import { TableActions } from "@/components/TableActions";

let props = withDefaults(defineProps<{ value: RowProps[]; showButtons?: boolean }>(), {
    value: () => [],
    showButtons: true
});

let emits = defineEmits(["confirm", "update:value"]);

let tableData = computed({ get: () => props.value, set: (val) => emits("update:value", val) });

onMounted(async () => {
    await getBindMIdOptions();
});

// 获取产成品列表
let bindMIdOptions = ref([]);

let getBindMIdOptions = async () => {
    await GET_MANUFACTURE_PAGE_LIST({ current: 1, size: 9999 }).then((res) => {
        if (res.data.code === 0) bindMIdOptions.value = res.data.data.records;
    });
};

// 表单数据
interface RowProps {
    bindMId: Nullable<string>;
    concreteAmount: string;

    [key: string]: any;
}

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        title: "选择规格",
        key: "bindMId",
        align: "center",
        render: (row) => {
            return h(NSelect, {
                options: bindMIdOptions.value,
                clearable: true,
                filterable: true,
                placeholder: "请选择规格",
                labelField: "productModel",
                valueField: "id",
                value: row.bindMId,
                onUpdateValue: (v) => (row.bindMId = v)
            });
        }
    },
    {
        title: "操作",
        key: "action",
        align: "center",
        width: 80,
        render(row, index) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "删除",
                        tertiary: true,
                        type: "error",
                        disabled: () => !props.showButtons,
                        onClick: () => deleteItem(index)
                    }
                ]
            });
        }
    }
    // {
    //     title: "扣除钢筋混凝土用量(m³)",
    //     key: "concreteAmount",
    //     align: "center",
    //     render: (row) => {
    //         return h(NInput, {
    //             value: row.concreteAmount,
    //             onUpdateValue: (v) => (row.concreteAmount = v)
    //         });
    //     }
    // }
]);

// 可编辑表单配置
let tableItem: RowProps = {
    bindMId: null,
    concreteAmount: ""
};

let addTableItem = () => {
    tableData.value.push(cloneDeep(tableItem));
};

let deleteItem = (index: number) => {
    tableData.value.splice(index, 1);
};

// 提交
let onSubmit = () => {
    console.log(tableData.value);
    emits("confirm", tableData.value);
};
</script>
