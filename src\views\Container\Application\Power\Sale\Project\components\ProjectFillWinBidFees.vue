<template>
    <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
        <n-card
            :title="`填写中标费用【${configData.projectNumber}】`"
            class="w-800px"
            closable
            @close="changeModalShow(false)"
        >
            <n-data-table :columns="tableColumns" :data="tableData" :single-line="false" bordered remote striped />
            <div class="flex-center mt">
                <n-space>
                    <n-button type="primary" @click="onSubmit">确认提交</n-button>
                    <n-button type="success" @click="addRow">新增一行</n-button>
                    <n-button type="error" @click="resetRow">重置信息</n-button>
                    <n-button @click="changeModalShow(false)">取消</n-button>
                </n-space>
            </div>
        </n-card>
    </n-modal>
</template>

<script lang="ts" setup>
import { computed, h, onMounted, ref } from "vue";
import type { DataTableColumns } from "naive-ui";
import { NInput, NSelect } from "naive-ui";
import { useDicts } from "@/hooks";
import { cloneDeep } from "lodash-es";
import { POST_BID_COST_FORM_SUCCESS } from "@/api/application/power";

let props = withDefaults(defineProps<{ show: boolean; configData: UnKnownObject }>(), { show: () => false });

let emits = defineEmits(["update:show", "refresh"]);

let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

let changeModalShow = (show: boolean) => emits("update:show", show);

onMounted(async () => {
    await setDictLibs();
    addRow();
});

// 字典
let { dictLibs, getDictLibs } = useDicts();

let setDictLibs = async () => {
    let dictName = ["project_win_bid_cost_type"];
    await getDictLibs(dictName);
};

// 数据列表
interface RowProps {
    [key: string]: any;
}

let tableData = ref<RowProps[]>([]);

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        title: "费用类型",
        key: "type",
        width: 200,
        render: (row) => {
            return h(NSelect, {
                value: row.type,
                options: (dictLibs["project_win_bid_cost_type"] || []) as any[],
                onUpdateValue: (v, o: { label: string }) => {
                    row.type = v;
                    if (o.label !== "其他费用") {
                        row.name = o.label;
                    } else {
                        row.name = "";
                    }
                }
            });
        }
    },
    // {
    //     title: "费用KEY",
    //     key: "keyword",
    //     align: "center",
    //     render: (row) => {
    //         return h(NInput, { value: row.keyword, onUpdateValue: (v) => (row.keyword = v) });
    //     }
    // },
    {
        title: "费用名称",
        key: "name",
        align: "center",
        render: (row) => {
            return h(NInput, {
                value: row.name,
                disabled: row.type !== "otherBidCost" && row.type !== "otherWinBidCost",
                onUpdateValue: (v) => (row.name = v)
            });
        }
    },
    {
        title: "费用金额（元）",
        key: "value",
        align: "center",
        render: (row) => {
            return h(NInput, { value: row.value, onUpdateValue: (v) => (row.value = v) });
        }
    }
]);

let initRow = {
    type: null,
    // keyword: null,
    name: null,
    value: null
};

let addRow = () => {
    tableData.value.push(cloneDeep(initRow));
};

let resetRow = () => {
    tableData.value = [cloneDeep(initRow)];
};

// 提交
let onSubmit = () => {
    let isPass = tableData.value.every((i) => Object.values(i).every((val) => val));
    if (!isPass) return window.$message.error("请填写完整信息");
    POST_BID_COST_FORM_SUCCESS({
        projectId: props.configData.projectId,
        nodeKey: props.configData.nextNodeKey,
        bidCosts: tableData.value || []
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("提交成功");
            changeModalShow(false);
            emits("refresh");
        }
    });
};
</script>
