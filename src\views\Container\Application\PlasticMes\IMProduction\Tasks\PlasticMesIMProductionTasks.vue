<template>
    <div>
        <n-card>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-space>
                <n-button type="primary" @click="openEditModal()">
                    <template #icon>
                        <DynamicIcon icon="PlusCircleOutlined" />
                    </template>
                    <span>新增生产任务</span>
                </n-button>
                <n-button type="error" @click="onCloseTasks()">
                    <template #icon>
                        <DynamicIcon icon="PoweroffOutlined" />
                    </template>
                    <span>批量关闭任务</span>
                </n-button>
            </n-space>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                class="mt"
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <!--新增编辑-->
        <PlasticMesIMProductionTasksEdit
            v-model:show="editModal.show"
            :config-data="editModal.configData"
            @refresh="getTableData"
        />
        <!--查看详情-->
        <PlasticMesIMProductionTasksDetail v-model:show="detailModal.show" :config-data="detailModal.configData" />
        <!--排产-->
        <PlasticMesIMProductionScheduleEdit
            v-model:show="scheduleModal.show"
            :config-data="scheduleModal.configData"
            @refresh="getTableData"
        />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, ref } from "vue";
import type { DataTableColumns, PaginationProps } from "naive-ui";
import { NText, NButton } from "naive-ui";
import { useCommonTable } from "@/hooks";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import {
    DELETE_IM_PRODUCTION_TASK,
    GET_IM_PRODUCTION_TASK_LIST,
    POST_IM_PRODUCTION_TASK_CLOSE,
    POST_IM_PRODUCTION_TASK_FINISH
} from "@/api/application/plasticMes";
import { DynamicIcon } from "@/components/DynamicIcon";
import { TableActions } from "@/components/TableActions";
import PlasticMesIMProductionTasksEdit from "./PlasticMesIMProductionTasksEdit.vue";
import PlasticMesIMProductionTasksDetail from "./PlasticMesIMProductionTasksDetail.vue";
import PlasticMesIMProductionScheduleEdit from "../Schedule/PlasticMesIMProductionScheduleEdit";
import { useStoreUser } from "@/store";

const storeUser = useStoreUser();

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([{ label: "任务状态", prop: "productionStatus", type: "select" }]);

let searchOptions = ref<TableSearchbarOptions>({
    productionStatus: [
        { label: "未排产", value: 1 },
        { label: "已排产", value: 2 },
        { label: "已完成", value: 3 },
        { label: "已关闭", value: 4 },
        { label: "待完成", value: 5 }
    ]
});

let searchForm = ref<TableSearchbarData>({
    productionStatus: null
});

let getSearchOptions = async () => {};

// 数据列表
interface RowProps {
    [key: string]: any;
}

let { tableRowKey, tableData, tableLoading, tableSelection, changeTableSelection } = useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    { type: "selection" },
    { title: "订单号", key: "orderCode", align: "center", render: (row) => row.orderCode || "/" },
    { title: "生产任务单", key: "id", align: "center", render: (row) => row.id || "/" },
    {
        title: "类型",
        key: "productionType",
        align: "center",
        render: (row) => {
            if (String(row.productionType) === "1") {
                return h(NText, { type: "info" }, { default: () => "销售订单" });
            } else if (String(row.productionType) === "2") {
                return h(NText, { type: "info" }, { default: () => "备货订单" });
            } else {
                return "/";
            }
        }
    },
    {
        title: "状态",
        key: "productionStatus",
        align: "center",
        render: (row) => {
            if (String(row.productionStatus) === "1") {
                return h(NText, { type: "default" }, { default: () => "未排产" });
            } else if (String(row.productionStatus) === "2") {
                return h(NText, { type: "info" }, { default: () => "已排产" });
            } else if (String(row.productionStatus) === "3") {
                return h(NText, { type: "success" }, { default: () => "已完成" });
            } else if (String(row.productionStatus) === "4") {
                const currentUser = storeUser.getUserData.sysUser?.username;
                if (currentUser === "admin") {
                    return h(
                        NButton,
                        {
                            type: "error",
                            text: true,
                            onClick: () => {
                                onDelete(row);
                            }
                        },
                        { default: () => "已关闭，点击删除" }
                    );
                } else {
                    return h(NText, { type: "error" }, { default: () => "已关闭" });
                }
            } else if (String(row.productionStatus) === "5") {
                return h(NText, { type: "warning" }, { default: () => "待完成" });
            } else {
                return "/";
            }
        }
    },
    {
        title: "是否满足需求",
        key: "meetFlag",
        align: "center",
        render: (row) => {
            if (String(row.meetFlag) === "1") {
                return h(NText, { type: "success" }, { default: () => "满足" });
            } else if (String(row.meetFlag) === "0") {
                return h(NText, { type: "error" }, { default: () => "不满足" });
            } else {
                return "/";
            }
        }
    },
    { title: "备注", key: "remark", align: "center", render: (row) => row.remark || "/" },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: 380,
        fixed: "right",
        render: (row) => {
            if (row.productionStatus === 3 && String(row.meetFlag) === "1") {
                return h(TableActions, {
                    type: "button",
                    buttonActions: [
                        {
                            label: "点击查看详情",
                            tertiary: true,
                            onClick: () => openDetailModal(row)
                        }
                    ]
                });
            } else {
                return h(TableActions, {
                    type: "button",
                    buttonActions: [
                        {
                            label: "新建排产",
                            // 2024年11月3日修改，待定内容
                            // disabled: () => row.productionStatus === 3 || row.productionStatus === 4,
                            type: "success",
                            tertiary: true,
                            onClick: () => openScheduleModal(row)
                        },
                        {
                            label: "查看详情",
                            tertiary: true,
                            onClick: () => openDetailModal(row)
                        },
                        {
                            label: "编辑信息",
                            disabled: () => row.productionStatus === 3 || row.productionStatus === 4,
                            type: "warning",
                            tertiary: true,
                            onClick: () => openEditModal(row)
                        },
                        {
                            label: row.productionStatus === 5 ? "完成任务" : "关闭任务",
                            disabled: () => row.productionStatus === 3 || row.productionStatus === 4,
                            type: row.productionStatus === 5 ? "success" : "error",
                            tertiary: true,
                            onClick: () => {
                                if (row.productionStatus === 5) {
                                    onCompleteTasks(row.id);
                                } else {
                                    onCloseTasks(row.id);
                                }
                            }
                        }
                    ]
                });
            }
        }
    }
]);

let tablePagination = ref<PaginationProps>({
    page: 1,
    pageSize: 10,
    itemCount: 0,
    pageSizes: [10, 50, 100],
    showSizePicker: true,
    showQuickJumper: true,
    displayOrder: ["size-picker", "pages", "quick-jumper"],
    onChange: (page: number) => {
        tablePagination.value.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.value.pageSize = pageSize;
        tablePagination.value.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_IM_PRODUCTION_TASK_LIST({
        current: tablePagination.value.page,
        size: tablePagination.value.pageSize,
        ...searchForm.value
    }).then((res) => {
        tableData.value = res.data.data.records || [];
        tablePagination.value.itemCount = res.data.data.total;
        tableLoading.value = false;
    });
};

let onSearch = () => {
    getTableData();
};

onMounted(async () => {
    await getSearchOptions();
    getTableData();
});

// 新增编辑
let editModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

let openEditModal = (row?: RowProps) => {
    editModal.value = { show: true, configData: row ?? {} };
};

// 查看详情
let detailModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

let openDetailModal = (row: RowProps) => {
    detailModal.value = { show: true, configData: row };
};

// 关闭任务
let onCloseTasks = (id?: string | number) => {
    if (!id && tableSelection.value.length < 1) return window.$message.error("请选择要关闭的任务");
    window.$dialog.warning({
        title: "温馨提示",
        content: "关闭后，该生产任务下将不可新建新的排产单，已下发的排产单不受影响。",
        positiveText: "确定关闭",
        negativeText: "取消",
        onPositiveClick: () => {
            let ids: (string | number)[];
            id ? (ids = [id]) : (ids = tableSelection.value);
            POST_IM_PRODUCTION_TASK_CLOSE({ ids: ids.join(",") }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("关闭成功");
                    onSearch();
                }
            });
        }
    });
};

// 完成任务
let onCompleteTasks = (id: string | number) => {
    window.$dialog.warning({
        title: "温馨提示",
        content: "完成后，该生产任务下将不可新建新的排产单，已下发的排产单不受影响。",
        positiveText: "确定完成",
        negativeText: "取消",
        onPositiveClick: () => {
            POST_IM_PRODUCTION_TASK_FINISH({ id }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("完成成功");
                    onSearch();
                }
            });
        }
    });
};

// 排产
let scheduleModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

let openScheduleModal = (row: RowProps) => {
    console.log(row);
    scheduleModal.value = {
        show: true,
        configData: {
            taskId: row.id
        }
    };
};

// 删除
const onDelete = (row: RowProps) => {
    window.$dialog.warning({
        title: "警告",
        content: "确认删除该条数据？该操作不可逆",
        positiveText: "确认删除",
        negativeText: "我再想想",
        onPositiveClick: () => {
            DELETE_IM_PRODUCTION_TASK({ ids: row.id }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("删除成功");
                    onSearch();
                }
            });
        }
    });
};
</script>
