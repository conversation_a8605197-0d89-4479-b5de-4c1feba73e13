import type { ConfigEnv, UserConfigExport } from "vite";
import { loadEnv } from "vite";
import vue from "@vitejs/plugin-vue";
import vueJsx from "@vitejs/plugin-vue-jsx";
import autoprefixer from "autoprefixer";
import unocss from "unocss/vite";
import { resolve } from "path";

export default ({ command, mode }: ConfigEnv): UserConfigExport => {
    // vite环境变量
    const viteEnv = loadEnv(mode, process.cwd());

    return {
        plugins: [
            vue(),
            vueJsx(),
            unocss({
                exclude: ["node_modules", ".git", ".idea", "dist", "public", "build"],
                rules: [
                    [/^flex-fixed-(\d+)$/, ([, d]) => ({ flex: `0 0 ${d}px` })],
                    [/^wh-(.+)$/, ([, d]) => ({ width: `${d}`, height: `${d}` })],
                    [/^ptb-(.+)$/, ([, d]) => ({ "padding-top": `${d}`, "padding-bottom": `${d}` })],
                    [/^plr-(.+)$/, ([, d]) => ({ "padding-left": `${d}`, "padding-right": `${d}` })],
                    [/^mtb-(.+)$/, ([, d]) => ({ "margin-top": `${d}`, "margin-bottom": `${d}` })],
                    [/^mlr-(.+)$/, ([, d]) => ({ "margin-left": `${d}`, "margin-right": `${d}` })]
                ],
                shortcuts: {
                    "flex-center": "flex justify-center items-center",
                    "flex-x-center": "flex justify-center",
                    "flex-y-center": "flex items-center",
                    "ellipsis-text": "nowrap-hidden overflow-ellipsis"
                }
            })
        ],
        base: viteEnv.VITE_BASE_PATH, // 开发或生产环境服务的公共基础路径。
        publicDir: "public", // 作为静态资源服务的文件夹。
        resolve: {
            // 设置别名
            alias: {
                "@/": `${resolve(__dirname, "src")}/`,
                "#/": `${resolve(__dirname, "types")}/`
            }
        },
        build: {
            outDir: viteEnv.VITE_OUTPUT_DIR, // 指定输出路径（相对于项目根目录)。
            assetsDir: "static", // 指定生成静态资源的存放路径（相对于 build.outDir）。
            target: "es2015", // 设置最终构建的浏览器兼容目标。
            cssTarget: "chrome80" // 此选项允许用户为 CSS 的压缩设置一个不同的浏览器 target，此处的 target 并非是用于 JavaScript 转写目标。
        },
        css: {
            postcss: {
                plugins: [
                    autoprefixer({
                        overrideBrowserslist: ["Android >= 4.0", "iOS >= 7"]
                    })
                ]
            }
            // preprocessorOptions: {
            //     // 如果'modern-compiler'不管用，可换成"modern"
            //     scss: {
            //         api: "modern" // or "modern"
            //     }
            // }
        },
        server: {
            host: false, // 指定服务器应该监听哪个 IP 地址。 如果将此设置为 0.0.0.0 或者 true 将监听所有地址，包括局域网和公网地址。
            port: 23333, // 指定开发服务器端口。
            open: true, // 是否自动打开浏览器。
            cors: false, // 为开发服务器配置 CORS。
            proxy: {
                "/api": {
                    target: "http://*************:19999",
                    changeOrigin: true,
                    rewrite: (path) => path.replace(/^\/api/, "")
                },
                "/workflow": {
                    target: "http://*************:19999",
                    changeOrigin: true
                }
            }
        }
    };
};
