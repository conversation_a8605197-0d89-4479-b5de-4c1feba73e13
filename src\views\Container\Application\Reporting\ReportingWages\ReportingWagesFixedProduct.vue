<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <div class="flex mt">
            <n-card class="flex-fixed-250" hoverable>
                <div class="flex-y-center mb">
                    <n-input v-model:value="treePattern" placeholder="搜索" />
                </div>
                <n-tree
                    v-model:selected-keys="treeSelectKeys"
                    :cancelable="false"
                    :data="treeData"
                    :pattern="treePattern"
                    :show-irrelevant-nodes="false"
                    block-line
                    children-field="childrenList"
                    default-expand-all
                    key-field="id"
                    label-field="categoryName"
                    selectable
                    @update:selected-keys="selectTreeNode"
                />
            </n-card>
            <n-card class="flex-1 ml" hoverable>
                <n-space class="mb">
                    <n-button secondary type="warning" @click="onBatchSave()">批量保存修改</n-button>
                </n-space>
                <n-data-table
                    :columns="tableColumns"
                    :data="tableData"
                    :loading="tableLoading"
                    :pagination="tablePagination"
                    :row-key="tableRowKey"
                    :single-line="false"
                    bordered
                    remote
                    striped
                    @update:checked-row-keys="changeTableSelection"
                />
            </n-card>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from "vue";
import {
    BATCH_UPDATE_QUOTA_WAGES_LIST,
    GET_CONFIG_COMPANY_LIST,
    GET_MATERIAL_CATEGORY_TREE_LIST,
    GET_QUOTA_WAGES_CATEGORY_TREE,
    GET_QUOTA_WAGES_PAGE_LIST,
    UPDATE_QUOTA_WAGES
} from "@/api/application/reporting";
import { DataTableColumns, NButton, NInput } from "naive-ui";
import { useCommonTable } from "@/hooks";
import { TableActions } from "@/components/TableActions";
import { DynamicTableEditor } from "@/components/Dynamic";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";

onMounted(async () => {
    await getCategoryIdOptions();
    await getCompanyIdOptions();
    await getTreeData();
    getTableData();
});

// 获取公司选项
let getCompanyIdOptions = async () => {
    await GET_CONFIG_COMPANY_LIST({ needFill: 1 }).then((res) => {
        searchOptions.value.companyId = (res.data.data || []).map((item: any) => ({
            label: item.companyName,
            value: item.id
        }));
        searchForm.value.companyId = searchOptions.value.companyId[0]?.value;
    });
};

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { prop: "companyId", type: "select", label: "所属公司" },
    { prop: "productModel", type: "input", label: "规格型号" }
]);

let searchOptions = ref<TableSearchbarOptions>({ companyId: [] });

let searchForm = ref<TableSearchbarData>({
    companyId: null,
    productModel: null
});

// 获取物料分类选项
let categoryIdOptions = ref([]);

let getCategoryIdOptions = async () => {
    await GET_MATERIAL_CATEGORY_TREE_LIST({}).then((res) => (categoryIdOptions.value = res.data.data));
};

// 树
let treeData = ref<any[]>([]);
let treePattern = ref("");
let treeSelectKeys = ref<(string | number)[]>([]);

let treeAddDisabled = (array: any[]): any[] => {
    return array.map((item) => {
        let newItem = { ...item };
        newItem.disabled = !!(newItem.childrenList && newItem.childrenList.length > 0);
        if (newItem.childrenList) {
            newItem.childrenList = treeAddDisabled(newItem.childrenList);
        }
        return newItem;
    });
};

let findLastLevel = (array: any[]): any => {
    let lastLevel = array.find((item) => item.childrenList && item.childrenList.length > 0);
    if (lastLevel) {
        return findLastLevel(lastLevel.childrenList);
    } else {
        return array;
    }
};

let getTreeData = async () => {
    await GET_QUOTA_WAGES_CATEGORY_TREE({}).then((res) => {
        if (res.data.code === 0) {
            treeData.value = treeAddDisabled(res.data.data ?? []);
            treeSelectKeys.value = [findLastLevel(treeData.value)[0].id];
        }
    });
};

let selectTreeNode = (keys?: (string | number)[]) => {
    treeSelectKeys.value = keys ?? [];
    onSearch();
};

// 数据列表
interface RowProps {
    [key: string]: any;
}

let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "物料名称",
        align: "center",
        key: "poleName",
        render: (row) => {
            return row.poleName ?? "暂无";
        }
    },
    {
        title: "规格型号",
        key: "productModel",
        align: "center"
    },
    {
        title: "物料分类",
        align: "center",
        key: "manufactureCategoryName"
    },
    {
        title: "最新工资定额（元）",
        key: "wage",
        align: "center",
        render: (row) => {
            return h(DynamicTableEditor, {
                type: "input",
                value: row.wage,
                onUpdateValue: (v: unknown) => (row.wage = v)
            });
        }
    },
    // {
    //     title: "系数",
    //     key: "coefficient",
    //     align: "center",
    //     render: (row) => {
    //         return h(DynamicTableEditor, {
    //             type: "input",
    //             value: row.coefficient,
    //             onUpdateValue: (v: unknown) => (row.coefficient = v)
    //         });
    //     }
    // },
    // {
    //     title: "修正系数",
    //     key: "correctCoefficient",
    //     align: "center",
    //     render: (row) => {
    //         return h(DynamicTableEditor, {
    //             type: "input",
    //             value: row.correctCoefficient,
    //             onUpdateValue: (v: unknown) => (row.correctCoefficient = v)
    //         });
    //     }
    // },
    {
        title: "备注",
        key: "remark",
        align: "center",
        render: (row) => {
            return h(DynamicTableEditor, {
                type: "input",
                value: row.remark,
                onUpdateValue: (v: unknown) => (row.remark = v)
            });
        }
    },
    {
        title: "操作",
        key: "action",
        align: "center",
        width: 120,
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "保存修改",
                        tertiary: true,
                        type: "primary",
                        onClick: () => updateTableItem(row)
                    }
                ]
            });
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    GET_QUOTA_WAGES_PAGE_LIST({
        ...searchForm.value,
        current: tablePagination.page,
        size: tablePagination.pageSize,
        wageCategoryId: treeSelectKeys.value[0] ?? null
    }).then((res) => {
        tableData.value = res.data.data.records;
        tablePagination.itemCount = res.data.data.total;
        tableLoading.value = false;
    });
};

// 搜索
let onSearch = () => {
    // 2023年10月9日20:10:04填报关闭分页
    // tablePagination.page = 1;
    // tablePagination.pageSize = 10;
    getTableData();
};

// 更新
let updateTableItem = async (row: RowProps) => {
    let res = await UPDATE_QUOTA_WAGES({ ...row });
    if (res.data.code === 0) {
        window.$message.success("保存成功");
        onSearch();
    } else {
        window.$message.error(res.data.msg);
    }
};

// 批量修改
let onBatchSave = () => {
    window.$dialog.warning({
        title: "提示",
        content: "确定要保存所有修改吗？",
        positiveText: "确定",
        negativeText: "取消",
        onPositiveClick: () => {
            BATCH_UPDATE_QUOTA_WAGES_LIST(tableData.value).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("保存成功");
                    onSearch();
                } else window.$message.error(res.data.msg);
            });
        }
    });
};
</script>
