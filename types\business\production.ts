import { DynamicTableHeaderProps, DynamicTableRowProps } from "@/components/Dynamic";

declare global {
    namespace Production {
        interface OrderDetailProps {
            poId?: string | number;
            pomNumber?: string | number; // 订单编号
            projectName?: string; // 项目名称
            deliveryDate?: string; // 交货日期
            salesPerson?: string; // 销售联系人
            salesPersonName?: string; // 销售联系人名称
            customerRemarks?: string; // 客户备注
            underWayProdCount?: string | number; // 进行中的生产数量
            finishedProdCount?: string | number; // 完成的生产数量
            returnOfMaterialCount?: string | number; // 退料数量
            materialCount?: string | number; // 累计领料数量
            orderProductState?: string | number; // 订单生产状态
            overviewList?: {
                potId?: string | number;
                specification?: string;
                needCount?: string | number;
                inProdCount?: string | number;
                noProdCount?: string | number;
                alreadyProdCount?: string | number;
                deliveredProdCount?: string | number;
                gunkProdCount?: string | number;
                concessionsReceivedCount?: string | number;
            }[]; // 订单概览列表
        }

        interface SpecificationProps {
            specification: Nullable<string>;
            stockCount: Nullable<number>;
            materialIsEnough: Nullable<number>;
            formulaName: Nullable<string>;
            formulaCheckBy: Nullable<string>;
            formulaCheckTime: Nullable<string>;
            formulaDetail: {
                header: DynamicTableHeaderProps[];
                value: DynamicTableRowProps[];
            };
        }

        interface ProductionRecordProps {
            productReqDetailList: {
                id: string;
                prodScheDate: string;
                prodCount: number;
                prodLineName: string;
            }[];

            [key: string]: any;
        }
    }
}
