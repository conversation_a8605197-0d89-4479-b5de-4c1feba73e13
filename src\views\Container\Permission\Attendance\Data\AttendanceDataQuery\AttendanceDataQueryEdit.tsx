import { computed, defineComponent, ref, watchEffect } from "vue";
import { type FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { useDicts } from "@/hooks";
import { UserSelector } from "@/components/UserSelector";
import { ADD_ATTENDANCE_QUERY, GET_DEPT_TREE } from "@/api/permission";
import dayjs from "dayjs";
import { useStoreDesign, useStoreUser } from "@/store";

export default defineComponent({
    name: "AttendanceDataQueryEdit",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "update:configData", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        const storeUser = useStoreUser();
        const storeDesign = useStoreDesign();

        // 表单数据
        interface FormDataProps {
            [key: string]: any;
        }

        // 字典操作
        const { dictLibs, getDictLibs } = useDicts();

        const setDictLibs = async () => {
            const dictName = ["common_units"];
            await getDictLibs(dictName);
        };

        // 表单实例
        const formRef = ref<FormInst | null>(null);

        // 获取表单选项
        const corpIdOptions = ref<any[]>([]);

        const getFormOptions = async () => {
            await GET_DEPT_TREE({ deptName: "" }).then((res) => {
                corpIdOptions.value = res.data.data ?? [];
            });
        };

        const initFormData: FormDataProps = {
            operator: null,
            operationTime: null,
            queryType: 1,
            startDate: null,
            endDate: null
        };

        const formRules = computed(() => ({
            operator: [{ required: true, message: "请选择操作人", trigger: ["blur", "change"] }],
            operationTime: [{ required: true, message: "请选择操作时间", trigger: ["blur", "change"] }],
            queryType: [{ required: true, message: "请选择查询类型", trigger: ["blur", "change"], type: "number" }],
            startDate: [
                { required: true, message: "请选择开始日期", trigger: ["blur", "change"] },
                {
                    validator: (rule: any, value: string) => {
                        if (formData.value.queryType === 4 && value && formData.value.endDate) {
                            if (dayjs(value).isAfter(dayjs(formData.value.endDate))) {
                                return new Error("开始日期不能晚于结束日期");
                            }
                        }
                        return true;
                    },
                    trigger: ["blur", "change"]
                }
            ],
            endDate: [
                { required: true, message: "请选择结束日期", trigger: ["blur", "change"] },
                {
                    validator: (rule: any, value: string) => {
                        if (formData.value.queryType === 4 && value && formData.value.startDate) {
                            if (dayjs(value).isBefore(dayjs(formData.value.startDate))) {
                                return new Error("结束日期不能早于开始日期");
                            }
                        }
                        return true;
                    },
                    trigger: ["blur", "change"]
                }
            ]
        }));

        const formData = ref(cloneDeep(initFormData));

        const clearForm = () => {
            formData.value = cloneDeep(initFormData);
        };

        // 日期同步处理函数
        const handleDateSync = (date: string | null) => {
            if (!date) return;

            const selectedDate = dayjs(date);

            switch (formData.value.queryType) {
                case 1: // 每日报表
                    formData.value.startDate = date;
                    formData.value.endDate = date;
                    break;
                case 2: // 每周报表
                    // 获取当前日期是周几（1-7，1代表周一）
                    const dayOfWeek = selectedDate.day() || 7;
                    // 计算本周一的日期
                    const monday = selectedDate.subtract(dayOfWeek - 1, "day");
                    // 计算本周日的日期
                    const sunday = monday.add(6, "day");
                    formData.value.startDate = monday.format("YYYY-MM-DD");
                    formData.value.endDate = sunday.format("YYYY-MM-DD");
                    break;
                case 3: // 每月报表
                    formData.value.startDate = selectedDate.startOf("month").format("YYYY-MM-DD");
                    formData.value.endDate = selectedDate.endOf("month").format("YYYY-MM-DD");
                    break;
                case 4: // 自定义时间段
                    // 不做特殊处理，允许自由选择
                    break;
            }
        };

        // 监听查询类型变化
        watchEffect(() => {
            if (formData.value.queryType !== 4 && formData.value.startDate) {
                handleDateSync(formData.value.startDate);
            }
        });

        // 关闭
        const onClose = () => {
            clearForm();
            changeModalShow(false);
            emit("refresh");
        };

        // 提交
        const onSubmit = async () => {
            const validateError = await formRef.value?.validate((errors) => !!errors);
            if (validateError) return false;

            ADD_ATTENDANCE_QUERY({
                ...formData.value,
                corpId: props.configData.corpId
            }).then(async (res) => {
                if (res.data.code === 0) {
                    window.$message.success("操作成功");
                    onClose();
                }
            });
        };

        watchEffect(async () => {
            if (show.value) {
                await getFormOptions();
                formData.value.operator = storeUser.getUserData.sysUser?.username;
                formData.value.operationTime = dayjs().format("YYYY-MM-DD HH:mm:ss");
            }
        });

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card title="新增考勤查询" class={["w-800px"]} closable onClose={onClose}>
                    <n-form
                        ref={formRef}
                        model={formData.value}
                        rules={formRules.value}
                        label-placement="left"
                        label-width={120}
                    >
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi span={6} label="操作人" path="operator">
                                <UserSelector
                                    v-model:value={formData.value.operator}
                                    class="w-100%"
                                    multiple={false}
                                    key-name="username"
                                    placeholder="请选择操作人"
                                    disabled
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="操作时间" path="operationTime">
                                <n-date-picker
                                    v-model:formatted-value={formData.value.operationTime}
                                    class="w-100%"
                                    clearable
                                    placeholder="请选择操作时间"
                                    type="datetime"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    disabled
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={12} label="查询类型" path="queryType">
                                <n-radio-group v-model:value={formData.value.queryType}>
                                    <n-space>
                                        <n-radio label="每日报表" value={1} />
                                        <n-radio label="每周报表" value={2} />
                                        <n-radio label="每月报表" value={3} />
                                        <n-radio label="自定义时间段" value={4} />
                                    </n-space>
                                </n-radio-group>
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="开始日期" path="startDate">
                                <n-date-picker
                                    v-model:formatted-value={formData.value.startDate}
                                    class="w-100%"
                                    clearable
                                    type="date"
                                    value-format="yyyy-MM-dd"
                                    onUpdate:formatted-value={(val: string | null) => handleDateSync(val)}
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="结束日期" path="endDate">
                                <n-date-picker
                                    v-model:formatted-value={formData.value.endDate}
                                    class="w-100%"
                                    clearable
                                    type="date"
                                    value-format="yyyy-MM-dd"
                                    disabled={formData.value.queryType !== 4}
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={12}>
                                <n-space>
                                    <n-button onClick={() => onClose()}>取消操作</n-button>
                                    <n-button type="primary" onClick={() => onSubmit()}>
                                        查询
                                    </n-button>
                                </n-space>
                            </n-form-item-gi>
                        </n-grid>
                    </n-form>
                </n-card>
            </n-modal>
        );
    }
});
