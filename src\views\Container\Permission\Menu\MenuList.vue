<template>
    <div class="menu-list">
        <n-card hoverable>
            <n-space class="mb">
                <n-button secondary type="primary" @click="openEditModal()">新增</n-button>
                <n-button secondary type="error" @click="onDelete()">批量删除</n-button>
            </n-space>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <menu-edit-modal v-model:id="editModal.id" v-model:show="editModal.show" @refresh="getTableData" />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, ref } from "vue";
import type { DataTableColumns, PaginationProps } from "naive-ui";
import { NButton } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableActions } from "@/components/TableActions";
import { DynamicIcon } from "@/components/DynamicIcon";
import { useCommonTable } from "@/hooks";
import { DELETE_MENU, DELETE_MENUS, GET_MENU_TREE } from "@/api/permission";
import MenuEditModal from "./MenuEditModal.vue";

interface RowProps {
    id: Nullable<string | number>;
    parentId: Nullable<string | number>;
    name: Nullable<string>;
    path: Nullable<string>;
    type: Nullable<string>;
    permission: Nullable<string>;
    sortOrder: number;
    menuType: Nullable<string>;
    menuColor: Nullable<string>;
    icon: Nullable<string>;
}

onMounted(() => {
    getSearchOptions();
    getTableData();
});

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([]);

let searchOptions = ref<TableSearchbarOptions>({});

let searchForm = ref<TableSearchbarData>({});

let getSearchOptions = () => {};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, changeTableSelection } = useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "菜单名称",
        key: "name"
    },
    {
        title: "菜单图标",
        key: "icon",
        align: "center",
        width: 100,
        render: (row) => {
            if (row.icon) {
                return h("div", { class: "flex-center" }, [h(DynamicIcon, { icon: row.icon, size: 25 })]);
            } else {
                return "无";
            }
        }
    },
    {
        title: "菜单颜色",
        key: "menuColor",
        align: "center",
        width: 100,
        render: (row) => {
            // 2024年3月8日10:39:39删除条件row.menuType !== "0"
            if (row.menuColor) {
                return h("div", { class: "flex-center" }, [
                    h(NButton, {
                        class: "w-30px h-30px",
                        color: row.menuColor
                    })
                ]);
            } else return "无";
        }
    },
    {
        title: "类型",
        key: "type",
        align: "center",
        render(row: RowProps) {
            if (row.type === "0") {
                return "目录";
            } else if (row.type === "1") {
                return "菜单";
            } else if (row.type === "2") {
                return "按钮";
            } else {
                return "未知";
            }
        }
    },
    {
        title: "菜单路由",
        key: "path",
        align: "center",
        render: (row: RowProps) => row.path || "无"
    },
    {
        title: "权限标识",
        key: "permission",
        align: "center",
        render: (row: RowProps) => row.permission || "无"
    },
    {
        title: "排序",
        key: "sortOrder",
        align: "center"
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: 150,
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "编辑",
                        tertiary: true,
                        onClick: () => {
                            openEditModal(row.id);
                        }
                    },
                    {
                        label: "删除",
                        type: "error",
                        tertiary: true,
                        onClick: () => {
                            if (row.id) onDelete(row.id);
                        }
                    }
                ]
            });
        }
    }
]);

let tablePagination = ref<PaginationProps>({
    page: 1,
    pageSize: 10,
    itemCount: 0,
    pageSizes: [10, 50, 100],
    showSizePicker: true,
    showQuickJumper: true,
    displayOrder: ["size-picker", "pages", "quick-jumper"],
    onChange: (page: number) => {
        tablePagination.value.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.value.pageSize = pageSize;
        tablePagination.value.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_MENU_TREE({
        ...searchForm.value
    }).then((res) => {
        console.log("菜单树", res.data.data);
        tableData.value = res.data.data || [];
        tablePagination.value.itemCount = res.data.total;
        tableLoading.value = false;
    });
};

let onSearch = () => {
    getTableData();
};

// 新增编辑
let editModal = ref<{ show: boolean; id: string | number | null }>({
    show: false,
    id: null
});

let openEditModal = (id?: string | number | null) => {
    editModal.value.show = true;
    editModal.value.id = id || null;
};

// 删除
let onDelete = (id?: string | number) => {
    if (!id && tableSelection.value.length < 1) {
        window.$message.error("请选择要删除的数据");
        return false;
    }
    window.$dialog.warning({
        title: "警告",
        content: `确定删除${id ? "该" : "选中"}菜单吗？`,
        positiveText: "删除",
        negativeText: "取消",
        onPositiveClick: () => {
            if (id) {
                DELETE_MENU({ id: id }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("删除成功");
                        onSearch();
                    }
                });
            } else {
                DELETE_MENUS({ ids: tableSelection.value.join(",") }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("删除成功");
                        onSearch();
                    }
                });
            }
        }
    });
};
</script>
