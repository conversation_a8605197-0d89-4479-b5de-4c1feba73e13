<script lang="ts" setup>
import { computed, nextTick, onMounted, ref, watch, watchEffect } from "vue";
import { useDebounceFn } from "@vueuse/core";
import { cloneDeep } from "lodash-es";
import { UserSelector } from "@/components/UserSelector";
import { GET_DEPT_TREE, GET_POST_ALL_LIST } from "@/api/permission";
import { useDicts } from "@/hooks";

let props = withDefaults(
    defineProps<{
        modeler: any;
        components: any[];
        nodes: any[];
        configData?: Record<string, any>;
    }>(),
    {
        modeler: null,
        components: () => [],
        nodes: () => []
    }
);

let tabActive = ref<string>("基础属性");

// 根据类型获取组件列表
let getUserSelector = computed(() => props.components.filter((item) => item.type === "userSelector"));
let getConditionOptions = computed(() =>
    props.components.filter((item) => ["input", "select", "radio", "checkbox"].includes(item.type))
);

// 获取流程线条件类型
let { dictLibs, getDictLibs } = useDicts();

let setDictLibs = async () => {
    let dictName = ["contain_type"];
    await getDictLibs(dictName);
};

// 条件相关配置
let conditionList = ref<any[]>([
    { conditionCate: null, conditionParam: null, conditionType: null, conditionText: null }
]);

let updateCondition = () => {
    if (!props.modeler || !formData.value?.nodeId) {
        console.warn('modeler 或 nodeId 不存在，跳过条件更新');
        return;
    }
    
    try {
        let modeling = props.modeler.get("modeling");
        let element = props.modeler.get("elementRegistry").get(formData.value.nodeId);
        let moddle = props.modeler.get("moddle");
        
        if (!element) {
            console.warn(`找不到节点 ${formData.value.nodeId}`);
            return;
        }
        
        conditionList.value.map((item: any) => {
            let conditionExpression: any = null;
            if (item.conditionCate && item.conditionParam && item.conditionType && item.conditionText) {
                conditionExpression = moddle.create("bpmn:FormalExpression", { ...item });
            }
            modeling.updateProperties(element, { conditionExpression });
            modeling.setColor(element, { stroke: conditionExpression ? "blue" : "black" });
        });
    } catch (error) {
        console.error('更新条件时出错:', error);
    }
};

let initNodeConfig = (e: any) => {
    tabActive.value = "基础属性";
    let node = props.nodes.find((item: any) => item.nodeId === e.element.id);
    if (node) {
        let element = cloneDeep(e.element.businessObject.$attrs);
        formData.value = {
            ...element,
            nodeType: Number(element.nodeType),
            // 确保 processFormNodeConfigList 存在
            processFormNodeConfigList: element.processFormNodeConfigList ?? []
        };
        // 初始化时更新组件列表
        updateComponents();
    } else {
        let addElement = e.element;
        nextTick(() => {
            let nodeData = addElement.businessObject.$attrs;
            formData.value = {
                nodeId: nodeData.nodeId,
                nodeName: nodeData.nodeName ?? "",
                nodeType: nodeData.nodeType,
                approveChooseWay: nodeData.approveChooseWay || 2,
                approveChooseRange: nodeData.approveChooseRange,
                approveChooseContent: nodeData.approveChooseContent,
                multiApproveType: nodeData.multiApproveType,
                defaultChooseAssignee: nodeData.defaultChooseAssignee,
                formParamId: nodeData.formParamId,
                multiple: nodeData.multiple,
                multipleColumn: nodeData.multipleColumn,
                processFormNodeConfigList: nodeData.processFormNodeConfigList ?? [],
                approveFormId: nodeData.approveFormId,
                nodeCopyConditionList: nodeData.nodeCopyConditionList || []
            };
            // 新增节点时更新组件列表
            updateComponents();
        });
    }
};

watchEffect(async () => {
    if (props.modeler) {
        props.modeler.on("element.click", (e: any) => {
            let element = e.element;
            let modeling = props.modeler.get("modeling");
            if (element.type === "bpmn:SequenceFlow") {
                let conditionExpression = element.businessObject.conditionExpression;
                if (conditionExpression) {
                    conditionList.value = [
                        {
                            conditionCate: conditionExpression.$attrs.conditionCate ?? null,
                            conditionParam: conditionExpression.$attrs.conditionParam ?? null,
                            conditionType: conditionExpression.$attrs.conditionType ?? null,
                            conditionText: conditionExpression.$attrs.conditionText ?? null
                        }
                    ];
                } else {
                    conditionList.value = [
                        { conditionCate: null, conditionParam: null, conditionType: null, conditionText: null }
                    ];
                }
            } else {
                modeling.updateProperties(element, { conditionExpression: null });
            }
            initNodeConfig(e);
        });
        props.modeler.on("shape.added", initNodeConfig);
        props.modeler.on("shape.removed", () => nextTick(() => (formData.value = null)));

        await new Promise((resolve) => props.modeler.on("import.done", resolve));
        /*
         * 2024年3月11日14:27:40
         * 优化流程导入导出功能
         * - 删除条件props.configData &&
         * - 新增element &&
         */
        if ((props.nodes ?? []).length > 0) {
            let modeling = props.modeler.get("modeling");
            props.nodes.forEach((item) => {
                let element = props.modeler.get("elementRegistry").get(item.nodeId);
                element && modeling.updateProperties(element, item);
            });
        }
    }
});

// 表单实例
let formRef = ref<any>(null);

// 表单校验
let formRules = {};

// 表单数据
let formData = ref<any>({});

// 更改审批人来源操作
let changeApproveChooseWay = () => {
    // 除发起人自身，其他情况下默认设置为多人
    formData.value.multiple = 1;
    // 发起人自身
    if (formData.value.approveChooseWay === 8) {
        formData.value.multiple = 0;
    }
};

// 实时更新节点数据
let updateNode = useDebounceFn(async () => {
    if (!props.modeler || !formData.value?.nodeId) {
        return;
    }
    
    try {
        let modeling = props.modeler.get("modeling");
        let element = props.modeler.get("elementRegistry").get(formData.value.nodeId);
        
        if (element) {
            modeling.updateProperties(element, { ...formData.value, name: formData.value.nodeName });
            console.log(`节点 ${formData.value.nodeId} 数据已更新`);
        }
    } catch (error) {
        console.error('更新节点数据时出错:', error);
    }
}, 100);

watch(
    () => formData.value,
    () => updateNode(),
    { deep: true }
);

// 监听组件列表变化
watch(
    () => props.components,
    useDebounceFn((newComponents: any[], oldComponents: any[]) => {
        // 简单的变化检测
        if (!newComponents || newComponents.length === 0) {
            return;
        }
        
        if (oldComponents && newComponents.length === oldComponents.length) {
            const hasChanges = newComponents.some((comp: any, index: number) => {
                const oldComp = oldComponents[index];
                return !oldComp || 
                       comp.id !== oldComp.id || 
                       comp.label !== oldComp.label ||
                       comp.type !== oldComp.type;
            });
            if (!hasChanges) {
                return;
            }
        }
        
        console.log('ProcessNodeEditor: 组件列表发生变化，更新当前选中节点...');
        
        // 如果当前有选中的节点，更新当前表单的组件配置
        if (formData.value?.nodeId) {
            updateComponents();
        }
        
        // ProcessDesigner 已经自动更新所有节点，这里不需要重复操作
    }, 200),
    { deep: true }
);

// 更新组件
let updateComponents = () => {
    if (!formData.value?.processFormNodeConfigList) {
        formData.value.processFormNodeConfigList = [];
    }
    // 确保 processFormNodeConfigList 是数组
    if (!Array.isArray(formData.value.processFormNodeConfigList)) {
        formData.value.processFormNodeConfigList = [];
    }
    // 保留原有的权限设置
    const existingConfigs = formData.value.processFormNodeConfigList;
    
    // 创建新的配置列表，确保所有组件都有配置
    const newConfigList = props.components.map(component => {
        const existingConfig = existingConfigs.find((config: { id: string }) => config.id === component.id);
        const newConfig = {
            ...component,
            // 确保新组件有明确的默认值，并且是响应式的
            isRead: existingConfig?.isRead !== undefined ? existingConfig.isRead : 0,
            isHide: existingConfig?.isHide !== undefined ? existingConfig.isHide : 0
        };
        return newConfig;
    });
    
    // 使用 Vue.set 或直接赋值来确保响应式
    formData.value.processFormNodeConfigList = newConfigList;
    
    // 立即触发一次更新，确保新组件的配置被保存
    nextTick(() => {
        updateNode();
    });
    
    // 调试信息：打印当前配置列表
    console.log(`ProcessNodeEditor: 节点 ${formData.value.nodeId} 组件配置已更新，共 ${formData.value.processFormNodeConfigList.length} 个组件`);
};

// 确保所有节点都有最新的组件配置
let ensureAllNodesHaveLatestComponents = () => {
    if (!props.modeler || !props.components || props.components.length === 0) {
        console.log('没有 modeler 或组件数据，跳过更新');
        return;
    }
    
    console.log('开始确保所有节点都有最新的组件配置...');
    
    try {
        let elementRegistry = props.modeler.get("elementRegistry");
        let modeling = props.modeler.get("modeling");
        
        // 获取所有节点元素
        let allElements = Object.values(elementRegistry._elements)
            .filter((item: any) => item.element.type !== "bpmn:Process")
            .map((item: any) => item.element);
        
        allElements.forEach((element: any) => {
            const nodeData = element.businessObject.$attrs;
            const nodeType = Number(nodeData.nodeType);
            
            // 只为发起人和用户任务节点更新组件配置
            if (nodeType === 0 || nodeType === 1) {
                const existingConfigs = nodeData.processFormNodeConfigList || [];
                
                const newConfigList = props.components.map(component => {
                    const existingConfig = existingConfigs.find((config: { id: string }) => config.id === component.id);
                    return {
                        ...component,
                        isRead: existingConfig?.isRead !== undefined ? existingConfig.isRead : 0,
                        isHide: existingConfig?.isHide !== undefined ? existingConfig.isHide : 0
                    };
                });
                
                // 直接更新 elementRegistry 中的节点数据
                modeling.updateProperties(element, {
                    processFormNodeConfigList: newConfigList
                });
                
                console.log(`ProcessNodeEditor: 节点 ${nodeData.nodeId} (${nodeData.nodeName}) 的组件配置已更新，共 ${newConfigList.length} 个组件`);
            }
        });
        
        // 同时更新 props.nodes（如果存在）
        if (props.nodes && props.nodes.length > 0) {
            props.nodes.forEach((node) => {
                if (node.nodeType === 0 || node.nodeType === 1) {
                    const existingConfigs = node.processFormNodeConfigList || [];
                    const newConfigList = props.components.map(component => {
                        const existingConfig = existingConfigs.find((config: { id: string }) => config.id === component.id);
                        return {
                            ...component,
                            isRead: existingConfig?.isRead !== undefined ? existingConfig.isRead : 0,
                            isHide: existingConfig?.isHide !== undefined ? existingConfig.isHide : 0
                        };
                    });
                    node.processFormNodeConfigList = newConfigList;
                }
            });
        }
        
        console.log('所有节点的组件配置更新完成');
    } catch (error) {
        console.error('更新节点组件配置时出错:', error);
    }
};

let postOptions = ref<any[]>([]);
let deptIdOptions = ref<any[]>([]);

let getOptions = async () => {
    await GET_POST_ALL_LIST({ postGroup: "post" }).then((res) => (postOptions.value = res.data.data ?? []));
    await GET_DEPT_TREE({ deptName: "" }).then((res) => (deptIdOptions.value = res.data.data || []));
};

/*
 * 2024年5月28日13:53:01
 * 新增节点抄送人相关逻辑
 */
let addNodeCopyCondition = () => {
    formData.value.nodeCopyConditionList.push({
        nodeId: formData.value.nodeId,
        conditionCate: "compare",
        conditionParam: null,
        conditionType: null,
        conditionText: null,
        resultText: null
    });
};

let deleteNodeCopyCondition = (index: number) => {
    formData.value.nodeCopyConditionList.splice(index, 1);
};

onMounted(async () => {
    await getOptions();
    await setDictLibs();
});

// 暴露方法给父组件使用
defineExpose({
    ensureAllNodesHaveLatestComponents,
    updateComponents
});
</script>

<template>
    <div v-if="formData && formData.nodeId">
        <n-form ref="formRef" :model="formData" :rules="formRules">
            <n-tabs v-model:value="tabActive" animated type="line">
                <n-tab-pane display-directive="if" name="基础属性">
                    <n-form-item label="节点ID">
                        <n-input v-model:value="formData.nodeId" disabled />
                    </n-form-item>
                    <n-form-item v-if="formData.nodeType === 0 || formData.nodeType === 1" label="节点名称">
                        <n-input
                            v-model:value="formData.nodeName"
                            :disabled="formData.nodeName === '发起人' && formData.nodeType === -1"
                        />
                    </n-form-item>
                    <n-form-item label="节点类型">
                        <!--2024年3月9日12:45:48删除条件disabled-->
                        <n-radio-group v-model:value="formData.nodeType">
                            <n-space>
                                <n-radio :value="-1" label="开始节点" size="large" />
                                <n-radio :value="0" label="发起人" size="large" />
                                <n-radio :value="1" label="用户任务节点" size="large" />
                                <n-radio :value="2" label="排他网关" size="large" />
                                <n-radio :value="3" label="并行网关" size="large" />
                                <n-radio :value="4" label="包含网关" size="large" />
                                <n-radio :value="5" label="流程线" size="large" />
                                <n-radio :value="6" label="结束节点" size="large" />
                            </n-space>
                        </n-radio-group>
                    </n-form-item>
                    <n-form-item v-if="formData.nodeType === 0 || formData.nodeType === 1" label="选择流程表单组件">
                        <n-select
                            v-model:value="formData.approveFormId"
                            :options="props.components"
                            clearable
                            label-field="label"
                            placeholder="请选择流程表单组件"
                            value-field="id"
                        />
                    </n-form-item>
                </n-tab-pane>
                <!--发起人/用户任务节点-->
                <template v-if="formData.nodeType === 0 || formData.nodeType === 1">
                    <n-tab-pane display-directive="if" name="审批人来源">
                        <template v-if="formData.nodeType === 1">
                            <div>
                                <n-element>
                                    <n-h3 class="mb-0! color-[var(--primary-color)]">审批人来源</n-h3>
                                </n-element>
                                <n-form-item>
                                    <n-radio-group
                                        v-model:value="formData.approveChooseWay"
                                        @update:value="changeApproveChooseWay"
                                    >
                                        <n-space>
                                            <n-radio :value="1" label="指定成员" size="large" />
                                            <n-radio :value="2" label="发起人自选" size="large" />
                                            <n-radio :value="3" label="指定职级" size="large" />
                                            <n-radio :value="4" label="部门主管" size="large" />
                                            <n-radio :value="5" label="单位负责人" size="large" />
                                            <n-radio :value="6" disabled label="管理组负责人" size="large" />
                                            <n-radio :value="7" label="表单获取" size="large" />
                                            <n-radio :value="8" label="发起人自身" size="large" />
                                        </n-space>
                                    </n-radio-group>
                                </n-form-item>
                            </div>
                            <!--指定成员-->
                            <div v-if="formData.approveChooseWay === 1">
                                <n-element>
                                    <n-h3 class="color-[var(--primary-color)]">指定成员设置</n-h3>
                                </n-element>
                                <n-form-item label="选择成员">
                                    <UserSelector
                                        v-model:value="formData.approveChooseContent"
                                        class="w-100%"
                                        clearable
                                        key-name="username"
                                        placeholder="请选择成员"
                                    />
                                </n-form-item>
                                <!--2023年12月19日15:32:10-->
                                <n-form-item label="审批类型">
                                    <n-radio-group v-model:value="formData.multiApproveType">
                                        <n-space>
                                            <n-radio :value="2" label="会签" size="large" />
                                            <n-radio :value="3" label="或签" size="large" />
                                        </n-space>
                                    </n-radio-group>
                                </n-form-item>
                            </div>
                            <!--发起人自选-->
                            <div v-if="formData.approveChooseWay === 2">
                                <n-element>
                                    <n-h3 class="color-[var(--primary-color)]">发起人自选设置</n-h3>
                                </n-element>
                                <n-form-item label="是否多人/职级">
                                    <n-radio-group v-model:value="formData.multiple">
                                        <n-space>
                                            <n-radio :value="0" label="否" size="large" />
                                            <n-radio :value="1" label="是" size="large" />
                                        </n-space>
                                    </n-radio-group>
                                </n-form-item>
                                <n-form-item label="审批选择范围">
                                    <n-radio-group v-model:value="formData.approveChooseRange">
                                        <n-space>
                                            <n-radio :value="1" label="全公司" size="large" />
                                            <n-radio :value="2" label="指定成员" size="large" />
                                            <n-radio :value="3" label="指定职级" size="large" />
                                        </n-space>
                                    </n-radio-group>
                                </n-form-item>
                                <n-form-item v-if="formData.approveChooseRange === 2" label="选择成员">
                                    <UserSelector
                                        v-if="formData.multiple === 1"
                                        v-model:value="formData.approveChooseContent"
                                        class="w-100%"
                                        clearable
                                        key-name="username"
                                        placeholder="请选择成员"
                                    />
                                    <UserSelector
                                        v-else
                                        v-model:value="formData.approveChooseContent"
                                        :multiple="false"
                                        class="w-100%"
                                        clearable
                                        key-name="username"
                                        placeholder="请选择成员"
                                    />
                                </n-form-item>
                                <n-form-item v-if="formData.approveChooseRange === 3" label="选择职级">
                                    <n-select
                                        v-model:value="formData.approveChooseContent"
                                        :options="postOptions"
                                        label-field="postName"
                                        placeholder="请选择职级"
                                        value-field="postId"
                                    />
                                </n-form-item>
                                <n-form-item v-if="formData.multiple === 1" label="审批类型">
                                    <n-radio-group v-model:value="formData.multiApproveType">
                                        <n-space>
                                            <n-radio :value="2" label="会签" size="large" />
                                            <n-radio :value="3" label="或签" size="large" />
                                        </n-space>
                                    </n-radio-group>
                                </n-form-item>
                            </div>
                            <!--职级设置-->
                            <div v-if="formData.approveChooseWay === 3">
                                <n-element>
                                    <n-h3 class="color-[var(--primary-color)]">职级设置</n-h3>
                                </n-element>
                                <n-form-item label="选择职级">
                                    <n-select
                                        v-model:value="formData.approveChooseContent"
                                        :options="postOptions"
                                        label-field="postName"
                                        placeholder="请选择职级"
                                        value-field="postId"
                                    />
                                </n-form-item>
                                <n-form-item label="审批类型">
                                    <n-radio-group v-model:value="formData.multiApproveType">
                                        <n-space>
                                            <n-radio :value="2" label="会签" size="large" />
                                            <n-radio :value="3" label="或签" size="large" />
                                        </n-space>
                                    </n-radio-group>
                                </n-form-item>
                                <n-form-item label="职级无人时">
                                    <n-radio-group v-model:value="formData.defaultChooseAssignee">
                                        <n-space>
                                            <n-radio :value="1" label="单位负责人" size="large" />
                                            <n-radio :value="2" disabled label="管理组负责人" size="large" />
                                            <n-radio :value="3" label="指定人" size="large" />
                                            <n-radio :value="4" label="跳过节点" size="large" />
                                        </n-space>
                                    </n-radio-group>
                                </n-form-item>
                            </div>
                            <!--部门主管-->
                            <div v-if="formData.approveChooseWay === 4">
                                <n-element>
                                    <n-h3 class="color-[var(--primary-color)]">部门主管获取设置</n-h3>
                                </n-element>
                                <n-form-item label="审批类型">
                                    <n-radio-group v-model:value="formData.multiApproveType">
                                        <n-space>
                                            <n-radio :value="2" label="会签" size="large" />
                                            <n-radio :value="3" label="或签" size="large" />
                                        </n-space>
                                    </n-radio-group>
                                </n-form-item>
                                <n-form-item label="部门主管为空时">
                                    <n-radio-group v-model:value="formData.defaultChooseAssignee">
                                        <n-space>
                                            <n-radio :value="1" label="单位负责人" size="large" />
                                            <n-radio :value="2" disabled label="管理组负责人" size="large" />
                                            <n-radio :value="3" label="指定人" size="large" />
                                            <n-radio :value="4" label="跳过节点" size="large" />
                                        </n-space>
                                    </n-radio-group>
                                </n-form-item>
                            </div>
                            <!--单位负责人-->
                            <div v-if="formData.approveChooseWay === 5">
                                <n-element>
                                    <n-h3 class="color-[var(--primary-color)]">单位负责人获取设置</n-h3>
                                </n-element>
                                <n-form-item label="审批类型">
                                    <n-radio-group v-model:value="formData.multiApproveType">
                                        <n-space>
                                            <n-radio :value="2" label="会签" size="large" />
                                            <n-radio :value="3" label="或签" size="large" />
                                        </n-space>
                                    </n-radio-group>
                                </n-form-item>
                                <n-form-item label="单位负责人为空时">
                                    <n-radio-group v-model:value="formData.defaultChooseAssignee">
                                        <n-space>
                                            <n-radio :value="2" disabled label="管理组负责人" size="large" />
                                            <n-radio :value="3" label="指定人" size="large" />
                                            <n-radio :value="4" label="跳过节点" size="large" />
                                        </n-space>
                                    </n-radio-group>
                                </n-form-item>
                            </div>
                            <!--表单获取-->
                            <div v-if="formData.approveChooseWay === 7">
                                <n-element>
                                    <n-h3 class="color-[var(--primary-color)]">表单获取设置</n-h3>
                                </n-element>
                                <n-form-item label="选择获取控件">
                                    <n-select
                                        v-model:value="formData.formParamId"
                                        :options="getUserSelector"
                                        label-field="label"
                                        placeholder="请选择获取控件"
                                        value-field="id"
                                    />
                                </n-form-item>
                                <n-form-item label="审批类型">
                                    <n-radio-group v-model:value="formData.multiApproveType">
                                        <n-space>
                                            <n-radio :value="2" label="会签" size="large" />
                                            <n-radio :value="3" label="或签" size="large" />
                                        </n-space>
                                    </n-radio-group>
                                </n-form-item>
                                <n-form-item label="表单填写为空时">
                                    <n-radio-group v-model:value="formData.defaultChooseAssignee">
                                        <n-space>
                                            <n-radio :value="1" label="单位负责人" size="large" />
                                            <n-radio :value="2" disabled label="管理组负责人" size="large" />
                                            <n-radio :value="3" label="指定人" size="large" />
                                            <n-radio :value="4" label="跳过节点" size="large" />
                                        </n-space>
                                    </n-radio-group>
                                </n-form-item>
                            </div>
                            <!--指定人-->
                            <div
                                v-if="
                                    formData.defaultChooseAssignee === 3 &&
                                    [3, 4, 5, 6, 7].includes(formData.approveChooseWay)
                                "
                            >
                                <n-form-item label="选择指定人">
                                    <UserSelector
                                        v-model:value="formData.defaultDesignPerson"
                                        class="w-100%"
                                        clearable
                                        key-name="username"
                                        placeholder="请选择指定人"
                                    />
                                </n-form-item>
                            </div>
                        </template>
                        <n-result
                            v-else
                            class="pt-100px"
                            description="该节点无需配置审批人来源"
                            status="404"
                            title="无需配置"
                        />
                    </n-tab-pane>
                    <n-tab-pane display-directive="if" name="表单权限">
                        <template v-if="formData.nodeType === 0 || formData.nodeType === 1">
                            <n-button block class="mb" type="primary" @click="updateComponents()">刷新组件</n-button>
                            <n-table :single-line="false" class="text-center">
                                <thead>
                                    <tr>
                                        <th>组件名称</th>
                                        <th>是否可用</th>
                                        <th>是否隐藏</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="item in formData.processFormNodeConfigList" :key="item.id">
                                        <td>{{ item.label ?? item.labelName }}</td>
                                        <td>
                                            <n-switch
                                                v-model:value="item.isRead"
                                                :checked-value="1"
                                                :unchecked-value="0"
                                                @update:value="() => updateNode()"
                                            />
                                        </td>
                                        <td>
                                            <n-switch
                                                v-model:value="item.isHide"
                                                :checked-value="1"
                                                :unchecked-value="0"
                                                @update:value="() => updateNode()"
                                            />
                                        </td>
                                    </tr>
                                </tbody>
                            </n-table>
                        </template>
                        <n-result
                            v-else
                            class="pt-100px"
                            description="该节点无需配置表单权限"
                            status="404"
                            title="无需配置"
                        />
                    </n-tab-pane>
                    <n-tab-pane display-directive="if" name="抄送人配置">
                        <n-card
                            class="mb-4!"
                            v-for="(item, index) in formData.nodeCopyConditionList || []"
                            :key="index"
                            closable
                            @close="deleteNodeCopyCondition(index)"
                        >
                            <n-form-item label="条件类型">
                                <n-radio-group
                                    v-model:value="item.conditionCate"
                                    @update:value="
                                        () => {
                                            item.conditionParam = null;
                                            item.conditionType = null;
                                            item.conditionText = null;
                                            item.resultText = null;
                                        }
                                    "
                                >
                                    <n-space>
                                        <n-radio size="large" value="compare">常规条件</n-radio>
                                        <n-radio size="large" value="contain">包含条件</n-radio>
                                    </n-space>
                                </n-radio-group>
                            </n-form-item>
                            <n-form-item label="条件组件">
                                <n-select
                                    v-model:value="item.conditionParam"
                                    :options="getConditionOptions"
                                    clearable
                                    placeholder="请选择控件"
                                    value-field="id"
                                />
                            </n-form-item>
                            <n-form-item label="条件判断">
                                <n-select
                                    v-model:value="item.conditionType"
                                    :options="
                                        item.conditionCate === 'compare'
                                            ? dictLibs['contain_type'].filter((i) => !i.label?.includes('包含'))
                                            : dictLibs['contain_type'].filter((i) => i.label?.includes('包含'))
                                    "
                                    clearable
                                    placeholder="请选择判断"
                                />
                            </n-form-item>
                            <n-form-item label="条件内容">
                                <n-input v-model:value="item.conditionText" clearable placeholder="请输入值" />
                            </n-form-item>
                            <n-form-item label="抄送人">
                                <UserSelector
                                    v-model:value="item.resultText"
                                    class="w-100%"
                                    clearable
                                    key-name="username"
                                    placeholder="请选择抄送人"
                                />
                            </n-form-item>
                        </n-card>
                        <n-button block type="primary" @click="addNodeCopyCondition">新增抄送配置</n-button>
                    </n-tab-pane>
                </template>
                <!--流程线-->
                <template v-if="formData.nodeType === 5">
                    <n-tab-pane display-directive="if" name="流程线条件">
                        <div>
                            <div v-for="item in conditionList">
                                <n-divider>判断条件</n-divider>
                                <n-form-item label="条件类型">
                                    <n-radio-group
                                        v-model:value="item.conditionCate"
                                        @update:value="
                                            () => {
                                                item.conditionParam = null;
                                                item.conditionType = null;
                                                item.conditionText = null;
                                                updateCondition();
                                            }
                                        "
                                    >
                                        <n-space>
                                            <n-radio size="large" value="compare">常规条件</n-radio>
                                            <n-radio size="large" value="contain">包含条件</n-radio>
                                        </n-space>
                                    </n-radio-group>
                                </n-form-item>
                                <n-form-item label="条件组件">
                                    <n-select
                                        v-if="item.conditionCate === 'compare'"
                                        v-model:value="item.conditionParam"
                                        :options="getConditionOptions"
                                        clearable
                                        placeholder="请选择控件"
                                        value-field="id"
                                        @update:value="updateCondition"
                                    />
                                    <n-select
                                        v-else-if="item.conditionCate === 'contain'"
                                        v-model:value="item.conditionParam"
                                        :options="[{ label: '发起人', value: 'starter' }]"
                                        clearable
                                        placeholder="请选择控件"
                                        @update:value="updateCondition"
                                    />
                                </n-form-item>
                                <n-form-item label="条件判断">
                                    <n-select
                                        v-model:value="item.conditionType"
                                        :options="
                                            item.conditionCate === 'compare'
                                                ? dictLibs['contain_type'].filter((i) => !i.label?.includes('包含'))
                                                : dictLibs['contain_type'].filter((i) => i.label?.includes('包含'))
                                        "
                                        clearable
                                        placeholder="请选择判断"
                                        @update:value="updateCondition"
                                    />
                                </n-form-item>
                                <n-form-item label="条件内容">
                                    <n-input
                                        v-if="item.conditionCate === 'compare'"
                                        v-model:value="item.conditionText"
                                        clearable
                                        placeholder="请输入值"
                                        @update:value="updateCondition"
                                    />
                                    <n-tree-select
                                        v-else-if="item.conditionCate === 'contain'"
                                        v-model:value="item.conditionText"
                                        :options="deptIdOptions"
                                        clearable
                                        filterable
                                        key-field="id"
                                        label-field="name"
                                        placeholder="请选择所属组织架构"
                                        @update:value="updateCondition"
                                    />
                                </n-form-item>
                            </div>
                        </div>
                    </n-tab-pane>
                </template>
            </n-tabs>
        </n-form>
    </div>
</template>
