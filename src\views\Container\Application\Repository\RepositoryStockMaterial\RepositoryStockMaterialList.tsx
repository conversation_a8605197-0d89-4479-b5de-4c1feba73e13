import { defineComponent, onMounted, reactive, ref } from "vue";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable, useDicts } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import {
    GET_REPOSITORY_CATEGORY_LIST,
    GET_REPOSITORY_STORE_ROOM_GOODS_LIST,
    GET_REPOSITORY_STOREROOM_LIST
} from "@/api/application/repository";
import RepositoryStockMaterialBatch from "./RepositoryStockMaterialBatch";
import { useStoreUser } from "@/store";

export default defineComponent({
    name: "RepositoryStockMaterialList",
    setup(props) {
        const storeUser = useStoreUser();

        // 字典操作
        const { dictLibs, getDictLibs } = useDicts();

        const setDictLibs = async () => {
            let dictName = ["common_units"];
            await getDictLibs(dictName);
        };

        // 树形查询
        const treeData = ref<any[]>([]);

        const treePattern = ref("");

        const treeSelectKeys = ref<(string | number)[]>([]);

        const getTreeData = async () => {
            await GET_REPOSITORY_CATEGORY_LIST({
                current: 1,
                size: 9999
            }).then((res) => {
                if (res.data.code === 0) {
                    treeData.value = res.data.data ?? [];
                }
            });
        };

        const selectTreeNode = (keys?: (string | number)[], option?: any[]) => {
            treeSelectKeys.value = keys ?? [];
            onSearch();
        };

        // 搜索项
        const searchConfig = ref<TableSearchbarConfig>([
            { label: "物资名称", prop: "goodsName", type: "input" },
            { label: "所属仓库", prop: "storeroomIds", type: "select" }
        ]);

        const searchOptions = ref<TableSearchbarOptions>({
            storeroomIds: []
        });

        const getSearchOptions = async () => {
            GET_REPOSITORY_STOREROOM_LIST({ current: 1, size: 9999, lockFlag: 0 }).then((res) => {
                searchOptions.value.storeroomIds = (res.data.data.records || []).map((i: any) => ({
                    label: i.storeroomName,
                    value: i.id
                }));
            });
        };

        const searchForm = ref<TableSearchbarData>({
            goodsName: null,
            storeroomIds: null
        });

        const onSearch = () => {
            tablePagination.page = 1;
            tablePagination.pageSize = 10;
            getTableData();
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            // {
            //     title: "操作",
            //     key: "action",
            //     align: "center",
            //     width: 200,
            //     render(row) {
            //         return h(TableActions, {
            //             type: "button",
            //             buttonActions: [
            //                 {
            //                     label: "快速出库",
            //                     tertiary: true,
            //                     type: "success",
            //                     onClick: () => {}
            //                 },
            //                 {
            //                     label: "快捷发货",
            //                     tertiary: true,
            //                     type: "warning",
            //                     onClick: () => {}
            //                 }
            //             ]
            //         });
            //     }
            // }
        ]);

        const setTableColumns = () => {
            const roles = storeUser.getUserData?.roleList;
            const isShow = (roles ?? []).some((item) => {
                return item.roleCode === "productionByPlasticIndustryCompany" || item.roleCode === "ROLE_ADMIN";
            });

            if (isShow) {
                tableColumns.value = [
                    { type: "selection" },
                    { title: "物品名称", key: "goodsName", align: "center" },
                    { title: "物品编码", key: "goodsCode", align: "center", render: (row) => row.goodsCode || "/" },
                    { title: "规格型号", key: "goodsSpec", align: "center", render: (row) => row.goodsSpec || "/" },
                    { title: "在库数量", key: "storageCapacity", align: "center" },
                    {
                        title: "在库批次",
                        key: "id",
                        align: "center",
                        render: (row) => (
                            <n-button type="primary" onClick={() => openBatchListModal(row)}>
                                点击查看
                            </n-button>
                        )
                    },
                    {
                        title: "在库平均单价（元）",
                        key: "avgUnitPrice",
                        align: "center",
                        render: (row) => row.avgUnitPrice || "/"
                    },
                    { title: "常用单位", key: "unitName", align: "center" },
                    { title: "储存仓库", key: "storeroomName", align: "center" }
                ];
            } else {
                tableColumns.value = [
                    { type: "selection" },
                    { title: "物品名称", key: "goodsName", align: "center" },
                    { title: "物品编码", key: "goodsCode", align: "center", render: (row) => row.goodsCode || "/" },
                    { title: "规格型号", key: "goodsSpec", align: "center", render: (row) => row.goodsSpec || "/" },
                    { title: "在库数量", key: "storageCapacity", align: "center" },
                    { title: "常用单位", key: "unitName", align: "center" },
                    { title: "储存仓库", key: "storeroomName", align: "center" }
                ];
            }
        };

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_REPOSITORY_STORE_ROOM_GOODS_LIST({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                categoryId: treeSelectKeys.value[0] ?? null,
                ...searchForm.value
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        // 在库批次查看弹窗
        const batchListModal = ref<{ show: boolean; configData: RowProps }>({ show: false, configData: {} });

        const openBatchListModal = (row: RowProps) => {
            batchListModal.value = {
                show: true,
                configData: row
            };
        };

        onMounted(async () => {
            await getSearchOptions();
            await setDictLibs();
            await getTreeData();
            setTableColumns();
            getTableData();
        });

        return () => (
            <div class="repository-page flex">
                <n-card class="flex-fixed-300">
                    <div class="flex-y-center mb">
                        <n-input v-model:value={treePattern.value} clearable placeholder="搜索" />
                        <n-button class="ml-2" type="default" onClick={() => selectTreeNode()}>
                            查看全部
                        </n-button>
                    </div>
                    <n-tree
                        v-model:selected-keys={treeSelectKeys.value}
                        cancelable={false}
                        data={treeData.value}
                        pattern={treePattern.value}
                        show-irrelevant-nodes={false}
                        block-line
                        children-field="childrenList"
                        default-expand-all
                        key-field="id"
                        label-field="categoryName"
                        selectable
                        onUpdate:selected-keys={selectTreeNode}
                    />
                </n-card>
                <div class="flex-1 ml">
                    <n-card>
                        <TableSearchbar
                            form={searchForm.value}
                            config={searchConfig.value}
                            options={searchOptions.value}
                            onSearch={onSearch}
                        />
                    </n-card>
                    <n-card class="mt">
                        <n-data-table
                            columns={tableColumns.value}
                            data={tableData.value}
                            loading={tableLoading.value}
                            pagination={tablePagination}
                            row-key={tableRowKey}
                            single-line={false}
                            bordered
                            remote
                            striped
                            onUpdate:checked-row-keys={changeTableSelection}
                        />
                    </n-card>
                </div>
                <RepositoryStockMaterialBatch
                    v-model:show={batchListModal.value.show}
                    configData={batchListModal.value.configData}
                />
            </div>
        );
    }
});
