<template>
    <div>
        <div class="flex">
            <n-card class="flex-fixed-300">
                <div class="flex-y-center mb">
                    <n-input v-model:value="treePattern" clearable placeholder="搜索" />
                </div>
                <n-tree
                    v-model:selected-keys="treeSelectKeys"
                    :cancelable="false"
                    :data="treeData"
                    :pattern="treePattern"
                    :show-irrelevant-nodes="false"
                    block-line
                    children-field="childrenList"
                    default-expand-all
                    key-field="id"
                    label-field="companyName"
                    selectable
                    @update:selected-keys="selectTreeNode"
                />
            </n-card>
            <n-card class="flex-1 ml">
                <n-collapse :default-expanded-names="[0]">
                    <n-collapse-item :name="0" title="计划生产天数设置">
                        <ReportingSettingsOtherMonthDay :treeSelectKeys="treeSelectKeys" />
                    </n-collapse-item>
                </n-collapse>
            </n-card>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { NInput } from "naive-ui";
import { GET_CONFIG_COMPANY_LIST } from "@/api/application/reporting";
import ReportingSettingsOtherMonthDay from "./ReportingSettingsOtherMonthDay.vue";

onMounted(async () => {
    await getTreeData();
});

// 分类树
let treeData = ref<any[]>([]);
let treePattern = ref("");
let treeSelectKeys = ref<(string | number)[]>([]);

let getTreeData = async () => {
    await GET_CONFIG_COMPANY_LIST({ needFill: 1 }).then((res) => {
        if (res.data.code === 0) {
            treeData.value = (res.data.data ?? []).filter((item: any) => item.productGenre !== 1);
            treeSelectKeys.value = [treeData.value[0].id];
        }
    });
};

let selectTreeNode = (keys: (string | number)[], array: any[]) => {
    treeSelectKeys.value = keys;
};
</script>
