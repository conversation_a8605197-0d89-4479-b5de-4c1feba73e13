import { computed, defineComponent, ref, watchEffect } from "vue";
import type { DataTableColumns } from "naive-ui";
import { GET_PURCHASING_DEMAND_MANAGEMENT_REQUIREMENT_CONTENT } from "@/api/application/purchase";
import { useCommonTable } from "@/hooks";

export default defineComponent({
    name: "PurchaseManageDemandContent",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => emit("update:show", val) });

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading } = useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { title: "物品名称", key: "goodsName", align: "center", render: (row) => row.goodsName ?? "/" },
            { title: "规格型号", key: "goodsSpec", align: "center", render: (row) => row.goodsSpec ?? "/" },
            { title: "需求数量", key: "demandQuantity", align: "center", render: (row) => row.demandQuantity ?? "/" },
            {
                title: "已采买数量",
                key: "purchaseQuantity",
                align: "center",
                render: (row) => row.purchaseQuantity ?? "/"
            },
            { title: "物品单位", key: "unit", align: "center", render: (row) => row.unit || "/" },
            { title: "备注", key: "remark", align: "center", render: (row) => row.remark || "/" }
        ]);

        const tablePagination = ref({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.value.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.value.pageSize = pageSize;
                tablePagination.value.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_PURCHASING_DEMAND_MANAGEMENT_REQUIREMENT_CONTENT({
                id: props.configData.id
            })
                .then((res) => {
                    if (res.data.code === 0) {
                        tableData.value = res.data.data?.records ?? [];
                        tableLoading.value = false;
                    } else {
                        tableLoading.value = false;
                    }
                })
                .catch(() => {
                    tableLoading.value = false;
                });
        };

        watchEffect(() => {
            if (show.value) {
                getTableData();
            }
        });

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card title="需求采购清单" class="w-1200px" closable onClose={() => (show.value = false)}>
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        pagination={tablePagination.value}
                        row-key={tableRowKey}
                        single-line={false}
                        bordered
                        striped
                    />
                </n-card>
            </n-modal>
        );
    }
});
