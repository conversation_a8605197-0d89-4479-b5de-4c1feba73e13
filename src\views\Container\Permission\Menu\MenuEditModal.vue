<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card :title="id ? '编辑菜单' : '新增菜单'" class="w-600px" closable @close="closeModal">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-form-item label="父级菜单" path="parentId">
                        <n-tree-select
                            v-model:value="formData.parentId"
                            :options="parentIdOptions"
                            clearable
                            filterable
                            key-field="id"
                            placeholder="请选择父级菜单"
                        />
                    </n-form-item>
                    <n-form-item label="菜单名称" path="name">
                        <n-input v-model:value="formData.name" class="w-100%" clearable placeholder="请输入菜单名称" />
                    </n-form-item>
                    <n-form-item label="菜单路由">
                        <n-input
                            v-model:value="formData.path"
                            :disabled="formData.type !== '1'"
                            class="w-100%"
                            clearable
                            placeholder="请输入菜单路由"
                        />
                    </n-form-item>
                    <n-form-item label="菜单类型" path="type">
                        <n-select
                            v-model:value="formData.type"
                            :options="typeOptions"
                            placeholder="请选择菜单类型"
                            @update:value="changeMenuType"
                        />
                    </n-form-item>
                    <n-form-item label="权限标识">
                        <n-input
                            v-model:value="formData.permission"
                            class="w-100%"
                            clearable
                            placeholder="请输入权限标识"
                        />
                    </n-form-item>
                    <n-form-item label="排序" path="sortOrder">
                        <n-input-number v-model:value="formData.sortOrder" class="w-100%" />
                    </n-form-item>
                    <n-form-item label="菜单图标" path="icon">
                        <n-input
                            v-model:value="formData.icon"
                            class="w-100%"
                            placeholder="请选择菜单图标"
                            @click="openIconModal"
                        >
                            <template #suffix>
                                <dynamic-icon :icon="formData.icon" size="22" />
                            </template>
                        </n-input>
                    </n-form-item>
                    <n-form-item label="菜单颜色" path="menuColor">
                        <n-color-picker
                            v-model:value="formData.menuColor"
                            :show-alpha="false"
                            :swatches="themeColorPreset"
                        />
                    </n-form-item>
                    <n-form-item label="是否在主菜单展示" path="visible">
                        <n-switch v-model:value="formData.visible" checked-value="0" unchecked-value="1" />
                    </n-form-item>
                    <n-form-item label="是否在全部应用展示" path="isApplyDisplay">
                        <n-switch v-model:value="formData.isApplyDisplay" checked-value="0" unchecked-value="1" />
                    </n-form-item>
                    <n-form-item>
                        <n-space>
                            <n-button type="primary" @click="onSubmit">提交</n-button>
                            <n-button @click="closeModal">取消</n-button>
                        </n-space>
                    </n-form-item>
                </n-form>
            </n-card>
        </n-modal>
        <n-modal v-model:show="iconModalShow">
            <n-card class="w-1000px" closable title="选择图标" @close="closeIconModal">
                <div class="w-100% h-500px">
                    <n-scrollbar trigger="hover">
                        <n-grid cols="16" y-gap="20">
                            <n-grid-item
                                v-for="item in iconsList"
                                class="flex-center cursor-pointer"
                                @click="selectIcon(item)"
                            >
                                <dynamic-icon :icon="item" size="30" />
                            </n-grid-item>
                        </n-grid>
                    </n-scrollbar>
                </div>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from "vue";
import type { FormInst } from "naive-ui";
import { ADD_MENU, CLEAR_MENU_CACHE, GET_MENU_BY_ID, GET_MENU_TREE, UPDATE_MENU } from "@/api/permission";
import { DynamicIcon } from "@/components/DynamicIcon";
import { cloneDeep } from "lodash-es";
import { themeColorPreset } from "@/setttings/theme";
import * as icons from "@vicons/antd";

let props = defineProps({
    show: { type: Boolean, default: false },
    id: { type: [String, Number] as PropType<any> }
});

let emits = defineEmits(["update:show", "refresh"]);

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {
    parentId: { required: true, message: "请选择父级菜单", trigger: ["blur", "change"] },
    name: {
        required: true,
        message: "请输入菜单名称",
        trigger: ["input", "blur"]
    },
    type: { required: true, message: "请选择菜单类型", trigger: ["blur", "change"] },
    sortOrder: { required: true, type: "number", message: "请输入排序", trigger: ["input", "blur"] }
};

// 表单数据
interface FormDataProps<T = string | null> {
    parentId: T;
    name: T;
    path: T;
    type: T;
    permission: T;
    sortOrder: number;
    icon: T;
    menuColor: T;
    visible: T;
    isApplyDisplay: T;
}

let initFormData: FormDataProps = {
    parentId: null,
    name: null,
    path: null,
    type: "0",
    permission: null,
    sortOrder: 0,
    icon: "AppstoreOutlined",
    menuColor: "",
    visible: "0",
    isApplyDisplay: "0"
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 编辑时获取详情
watch(
    () => ({ id: props.id, show: props.show }),
    (newVal) => {
        if (newVal.show) getOptions();
        if (newVal.show && newVal.id) getDetail();
    },
    { deep: true }
);

// 获取详情
let getDetail = () => {
    GET_MENU_BY_ID({ id: props.id }).then((res) => {
        let rowItem: FormDataProps = res.data.data;
        formData.value = {
            parentId: rowItem.parentId,
            name: rowItem.name,
            path: rowItem.path,
            type: rowItem.type,
            permission: rowItem.permission,
            sortOrder: rowItem.sortOrder,
            icon: rowItem.icon,
            menuColor: rowItem.menuColor,
            visible: rowItem.visible || "1",
            isApplyDisplay: rowItem.isApplyDisplay || "1"
        };
    });
};

// 表单选项
let typeOptions = [
    { label: "目录", value: "0" },
    { label: "菜单", value: "1" },
    { label: "按钮", value: "2" }
];

interface ParentIdOptionsProps {
    label: string;
    id: string | number;
    children?: ParentIdOptionsProps[];
}

let parentIdOptions = ref<ParentIdOptionsProps[]>([]);

let getOptions = () => {
    GET_MENU_TREE({}).then((res) => {
        parentIdOptions.value = [{ label: "顶级菜单", id: "-1", children: res.data.data || [] }];
    });
};

// 更改菜单类型
let changeMenuType = (value: number) => {
    if (value !== 1) formData.value.path = null;
};

// 选择图标
let iconModalShow = ref(false);

let iconsList = computed(() => Object.keys(icons));

let openIconModal = () => (iconModalShow.value = true);
let closeIconModal = () => (iconModalShow.value = false);

let selectIcon = (icon: string) => {
    formData.value.icon = icon;
    closeIconModal();
};

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    clearMenuCache();
    emits("update:show", false);
};

// 清除菜单缓存
let clearMenuCache = () => {
    CLEAR_MENU_CACHE({});
};

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    if (!props.id) {
        ADD_MENU({
            ...formData.value
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("新增成功");
                closeModal();
                emits("refresh");
            }
        });
    } else {
        UPDATE_MENU({
            menuId: props.id,
            ...formData.value
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("编辑成功");
                closeModal();
                emits("refresh");
            }
        });
    }
};
</script>
