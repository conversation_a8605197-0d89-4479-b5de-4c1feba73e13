<script lang="ts" setup>
import { FormConfigs } from "@/components/FormGenerator/src/components";
import { computed, ref, watchEffect } from "vue";
import { GET_DEPT_TREE } from "@/api/permission";
import { useStoreFormGenerator, useStoreUser } from "@/store";
import { useDicts } from "@/hooks";
import { DynamicIcon } from "@/components/DynamicIcon";
import * as icons from "@vicons/antd";

let storeUser = useStoreUser();
let storeFormGenerator = useStoreFormGenerator();
let { getDict } = useDicts();

let props = withDefaults(
    defineProps<{
        value: Record<string, any>;
        configData?: UnKnownObject;
    }>(),
    {}
);

let emits = defineEmits(["update:value", "submit"]);

// 表单实例
let formRef = ref<any>(null);

// 表单校验
let formRules = {
    key: [{ required: true, message: "请输入流程标识", trigger: ["input", "blur"] }],
    name: [{ required: true, message: "请输入流程名称", trigger: ["input", "blur"] }],
    modelNumber: [{ required: true, message: "请输入流程编号", trigger: ["input", "blur"] }],
    category: [{ required: true, message: "请选择流程分类", trigger: ["blur", "change"] }],
    isSystem: [{ required: true, message: "请选择是否系统流程", trigger: ["blur", "change"] }],
    initiatorScope: [{ required: true, message: "请选择发起人范围", trigger: ["blur", "change"] }],
    initiatorDeptIds: [{ required: true, type: "array", message: "请选择特定部门", trigger: ["blur", "change"] }],
    modelIcon: [{ required: false, message: "请选择流程图标", trigger: ["blur", "change"] }]
};

// 表单数据
let formData = computed<Record<string, any>>({
    get: () => props.value,
    set: (val) => emits("update:value", val)
});

// 表单选项
let categoryOptions = ref<any[]>([]);
let initiatorDeptIdsOptions = ref([]);

let getOptions = async () => {
    categoryOptions.value = await getDict("flow_category");
    GET_DEPT_TREE({ deptName: "" }).then((res) => (initiatorDeptIdsOptions.value = res.data.data || []));
};

watchEffect(() => {
    getOptions();
});

let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors: any) => !!errors);
    if (validateError) return false;
    emits("submit");
};

let formConfigsRef = ref<any>(null);

let changeIsSystem = (val: any) => {
    formConfigsRef.value.changeIsBusiness(!!Number(val));
};

// 选择图标
let iconModalShow = ref(false);

let iconsList = computed(() => Object.keys(icons));

let openIconModal = () => (iconModalShow.value = true);
let closeIconModal = () => (iconModalShow.value = false);

let selectIcon = (icon: string) => {
    formData.value.modelIcon = icon;
    closeIconModal();
};
</script>

<template>
    <div>
        <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="140px">
            <n-grid cols="12" x-gap="16">
                <n-form-item-gi v-if="!!configData" :span="12" label="流程标识：" path="key">
                    <n-input
                        v-model:value="formData.key"
                        class="w-100%"
                        clearable
                        disabled
                        placeholder="请输入流程标识"
                    />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="流程名称：" path="name">
                    <n-input
                        v-model:value="formData.name"
                        class="w-100%"
                        clearable
                        placeholder="请输入流程名称"
                        @update:value="storeFormGenerator.formConfigs.wcfName = formData.name"
                    />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="流程分类：" path="category">
                    <n-select
                        v-model:value="formData.category"
                        :options="categoryOptions"
                        placeholder="请选择流程分类"
                    />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="描述：" path="desc">
                    <n-input
                        v-model:value="formData.desc"
                        class="w-100%"
                        clearable
                        placeholder="请输入模型描述"
                        @update:value="storeFormGenerator.formConfigs.wcfRemark = formData.desc"
                    />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="流程编号：" path="modelNumber">
                    <n-input
                        v-model:value="formData.modelNumber"
                        class="w-100%"
                        clearable
                        placeholder="请输入流程编号"
                        @update:value="storeFormGenerator.formConfigs.wcfRemark = formData.modelNumber"
                    />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="流程图标：" path="modelIcon">
                    <n-input
                        v-model:value="formData.modelIcon"
                        class="w-100%"
                        placeholder="请选择流程图标"
                        @click="openIconModal"
                    >
                        <template #suffix>
                            <dynamic-icon :icon="formData.icon" size="22" />
                        </template>
                    </n-input>
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="发起人范围：" path="initiatorScope">
                    <n-radio-group v-model:value="formData.initiatorScope" name="initiatorScope">
                        <n-radio label="全公司" value="1" />
                        <n-radio label="特定部门" value="2" />
                    </n-radio-group>
                </n-form-item-gi>
                <n-form-item-gi
                    v-if="formData.initiatorScope === '2'"
                    :span="12"
                    label="特定部门："
                    path="initiatorDeptIds"
                >
                    <n-tree-select
                        v-model:value="formData.initiatorDeptIds"
                        :options="initiatorDeptIdsOptions"
                        clearable
                        filterable
                        key-field="id"
                        label-field="name"
                        multiple
                        placeholder="请选择特定部门"
                    />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="是否系统流程：" path="isSystem">
                    <n-radio-group
                        v-model:value="formData.isSystem"
                        :disabled="!!configData && storeUser.getUserData?.sysUser?.username !== 'admin'"
                        name="isSystem"
                        @update:value="changeIsSystem"
                    >
                        <n-radio label="是" value="1" />
                        <n-radio label="否" value="0" />
                    </n-radio-group>
                </n-form-item-gi>
            </n-grid>
        </n-form>
        <!--
            测试暂时放开，后续需要删除
            2023年12月23日22:59:14
            v-show="formData.isSystem === '1'"
        -->
        <n-form label-placement="left" label-width="120px">
            <form-configs ref="formConfigsRef" v-model="storeFormGenerator.formConfigs" :basic-form="formData" />
        </n-form>
        <n-button block type="primary" @click="onSubmit()">下一步</n-button>
        <!-- 图标选择弹窗 -->
        <n-modal v-model:show="iconModalShow">
            <n-card class="w-1000px" closable title="选择图标" @close="closeIconModal">
                <div class="w-100% h-500px">
                    <n-scrollbar trigger="hover">
                        <n-grid cols="16" y-gap="20">
                            <n-grid-item
                                v-for="item in iconsList"
                                class="flex-center cursor-pointer"
                                @click="selectIcon(item)"
                            >
                                <dynamic-icon :icon="item" size="30" />
                            </n-grid-item>
                        </n-grid>
                    </n-scrollbar>
                </div>
            </n-card>
        </n-modal>
    </div>
</template>
