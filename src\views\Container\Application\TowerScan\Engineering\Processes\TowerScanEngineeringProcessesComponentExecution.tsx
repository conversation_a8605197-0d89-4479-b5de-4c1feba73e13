import { computed, defineComponent, reactive, ref, watchEffect } from "vue";
import {
    TableSearchbar,
    type TableSearchbarConfig,
    type TableSearchbarData,
    type TableSearchbarOptions
} from "@/components/TableSearchbar";
import { GET_IRON_COMPONENT_FILL_PAGE_LIST, DELETE_IRON_COMPONENT_FILL } from "@/api/application/TowerScan";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import { TableActions } from "@/components/TableActions";
import TowerScanEngineeringProcessesComponentExecutionHistory from "./TowerScanEngineeringProcessesComponentExecutionHistory";
import TowerScanEngineeringProcessesComponentExecutionEdit from "./TowerScanEngineeringProcessesComponentExecutionEdit";

/**
 * 配方执行情况组件
 *
 * @description 此组件支持权限控制，可以根据不同模块的需求显示或隐藏操作按钮
 *
 * @permissions 权限配置对象
 * - edit: boolean - 是否显示修改按钮 (默认: true)
 * - delete: boolean - 是否显示删除按钮 (默认: true)
 * - history: boolean - 是否显示查看修改记录按钮 (默认: true)
 *
 * @example
 * // 只读模式 (生产模块使用)
 * <TowerScanEngineeringProcessesComponentExecution
 *   permissions={{ edit: false, delete: false, history: true }}
 * />
 *
 * // 完整功能模式 (工程模块使用)
 * <TowerScanEngineeringProcessesComponentExecution
 *   permissions={{ edit: true, delete: true, history: true }}
 * />
 */

export default defineComponent({
    name: "TowerScanEngineeringProcessesComponentExecution",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) },
        permissions: {
            type: Object,
            default: () => ({
                edit: true, // 是否显示修改按钮
                delete: true, // 是否显示删除按钮
                history: true // 是否显示查看修改记录按钮
            })
        }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        const onClose = () => {
            changeModalShow(false);
            emit("refresh");
        };

        // 搜索项
        const searchConfig = ref<TableSearchbarConfig>([]);

        const searchOptions = ref<TableSearchbarOptions>({});

        const searchForm = ref<TableSearchbarData>({});

        const onSearch = () => {
            tablePagination.page = 1;
            tablePagination.pageSize = 10;
            getTableData();
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            // 只有在允许删除时才显示选择列
            ...(props.permissions.delete ? [{ type: "selection" as const }] : []),
            { title: "操作工", key: "belongUserNames", align: "center", render: (row) => row.belongUserNames ?? "/" },
            { title: "质检员", key: "fillByName", align: "center", render: (row) => row.fillByName ?? "/" },
            {
                title: "工序类型",
                key: "processType",
                align: "center",
                render: (row) => {
                    const processTypeText = row.processType === 5 ? "组装" : row.processType === 6 ? "电焊" : "/";
                    return <n-text type="info">{processTypeText}</n-text>;
                }
            },
            {
                title: "工艺类型",
                key: "techniqueName",
                align: "center",
                render: (row) => <n-text type="info">{row.techniqueName ?? "/"}</n-text>
            },
            { title: "主件号", key: "mainMaterialCode", align: "center", render: (row) => row.mainMaterialCode ?? "/" },
            {
                title: "填报数量",
                key: "fillQuantity",
                align: "center",
                render: (row) => <n-text type="success">{row.fillQuantity ?? "/"}</n-text>
            },
            {
                title: "填报总重(KG)",
                key: "fillWeight",
                align: "center",
                render: (row) => row.fillWeight ?? "/"
            },
            { title: "填报日期", key: "fillDate", align: "center", render: (row) => row.fillDate ?? "/" },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 260,
                render: (row) => {
                    // 根据权限配置生成按钮数组
                    const buttonActions = [];

                    if (props.permissions.edit) {
                        buttonActions.push({
                            label: "修改",
                            tertiary: true,
                            type: "warning" as const,
                            onClick: () => handleEdit(row)
                        });
                    }

                    if (props.permissions.delete) {
                        buttonActions.push({
                            label: "删除",
                            tertiary: true,
                            type: "error" as const,
                            onClick: () => handleDelete(row)
                        });
                    }

                    if (props.permissions.history) {
                        buttonActions.push({
                            label: "查看修改记录",
                            tertiary: true,
                            type: "primary" as const,
                            onClick: () => handleViewHistory(row)
                        });
                    }

                    return <TableActions type="button" buttonActions={buttonActions} />;
                }
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;

            const params: any = {
                current: tablePagination.page,
                size: tablePagination.pageSize,
                componentId: props.configData.componentId,
                processId: props.configData.processId,
                processType: props.configData.processType,
                ...searchForm.value
            };

            GET_IRON_COMPONENT_FILL_PAGE_LIST(params)
                .then((res) => {
                    tableLoading.value = false;
                    if (res.data.code === 0) {
                        tableData.value = res.data.data.records || res.data.data || [];
                        tablePagination.itemCount = res.data.data.total || 0;
                    } else {
                        tableData.value = [];
                        tablePagination.itemCount = 0;
                        window.$message?.error(res.data.msg || "获取数据失败");
                    }
                })
                .catch(() => {
                    tableLoading.value = false;
                    tableData.value = [];
                    tablePagination.itemCount = 0;
                    window.$message?.error("获取数据失败");
                });
        };

        // 删除记录
        const handleDelete = (row: RowProps) => {
            window.$dialog?.warning({
                title: "确认删除",
                content: "确定要删除这条配方填报记录吗？删除后无法恢复！",
                positiveText: "确定",
                negativeText: "取消",
                onPositiveClick: () => {
                    DELETE_IRON_COMPONENT_FILL({ ids: row.id })
                        .then((res) => {
                            if (res.data.code === 0) {
                                window.$message?.success("删除成功");
                                getTableData();
                            } else {
                                window.$message?.error(res.data.msg || "删除失败");
                            }
                        })
                        .catch(() => {
                            window.$message?.error("删除失败");
                        });
                }
            });
        };

        // 批量删除
        const handleBatchDelete = () => {
            if (tableSelection.value.length === 0) {
                window.$message?.warning("请选择要删除的记录");
                return;
            }

            window.$dialog?.warning({
                title: "确认批量删除",
                content: `确定要删除选中的 ${tableSelection.value.length} 条配方填报记录吗？删除后无法恢复！`,
                positiveText: "确定",
                negativeText: "取消",
                onPositiveClick: () => {
                    const ids = tableSelection.value.join(",");
                    DELETE_IRON_COMPONENT_FILL({ ids })
                        .then((res) => {
                            if (res.data.code === 0) {
                                window.$message?.success("批量删除成功");
                                getTableData();
                                changeTableSelection([]); // 清空选择
                            } else {
                                window.$message?.error(res.data.msg || "批量删除失败");
                            }
                        })
                        .catch(() => {
                            window.$message?.error("批量删除失败");
                        });
                }
            });
        };

        // 历史记录弹窗
        const historyShow = ref(false);
        const historyConfigData = ref<RowProps>({});

        // 查看修改记录
        const handleViewHistory = (row: RowProps) => {
            historyConfigData.value = row;
            historyShow.value = true;
        };

        // 修改弹窗
        const editShow = ref(false);
        const editConfigData = ref<RowProps>({});

        // 修改记录
        const handleEdit = (row: RowProps) => {
            editConfigData.value = row;
            editShow.value = true;
        };

        // 修改完成刷新数据
        const handleEditRefresh = () => {
            getTableData();
        };

        watchEffect(() => {
            if (show.value) {
                getTableData();
            }
        });

        return () => [
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card
                    title={`${props.configData?.mainMaterialCode ?? "/"}配方执行详情 - ${
                        props.configData?.typeName ?? "/"
                    } - ${props.configData?.projectName ?? "/"}`}
                    class="w-1200px"
                    closable
                    onClose={onClose}
                >
                    <n-form label-placement="left" label-width="auto">
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi required span={4} label="工程名称：">
                                {props.configData?.projectName ?? "/"}
                            </n-form-item-gi>
                            <n-form-item-gi required span={4} label="塔型：">
                                {props.configData?.typeName ?? "/"}
                            </n-form-item-gi>
                            <n-form-item-gi required span={4} label="主件号：">
                                <n-text type="info">{props.configData?.mainMaterialCode ?? "/"}</n-text>
                            </n-form-item-gi>
                        </n-grid>
                    </n-form>

                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        pagination={tablePagination}
                        row-key={tableRowKey}
                        single-line={false}
                        bordered
                        remote
                        striped
                        onUpdate:checked-row-keys={changeTableSelection}
                    />
                </n-card>
            </n-modal>,

            // 历史记录弹窗
            <TowerScanEngineeringProcessesComponentExecutionHistory
                v-model:show={historyShow.value}
                configData={historyConfigData.value}
            />,

            // 修改弹窗
            <TowerScanEngineeringProcessesComponentExecutionEdit
                v-model:show={editShow.value}
                configData={editConfigData.value}
                onRefresh={handleEditRefresh}
            />
        ];
    }
});
