<template>
    <div>
        <n-card content-style="padding-top:8px;padding-bottom:4px" hoverable>
            <n-tabs v-model:value="planMonth" animated size="small" type="bar">
                <n-tab-pane v-for="item in planMonthList" :name="item.name" :tab="item.tab" />
            </n-tabs>
        </n-card>
        <div class="flex mt">
            <n-card class="flex-fixed-200" content-style="padding:0" hoverable>
                <n-menu
                    v-model:value="tabActive"
                    :indent="20"
                    :options="tabOptions"
                    class="flex-fixed-150 border-r-1px border-[#E5E5E5]"
                    mode="vertical"
                    @update:value="changeTabActive"
                />
            </n-card>
            <n-card class="flex-1 ml" hoverable>
                <ReportingFillMonthOtherExternal v-if="tabActive === 'external'" :planMonth="planMonth" />
                <ReportingFillMonthOtherOther v-if="tabActive === 'other'" :planMonth="planMonth" />
            </n-card>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import dayjs from "dayjs";
import ReportingFillMonthOtherExternal from "./ReportingFillMonthOtherExternal.vue";
import ReportingFillMonthOtherOther from "./ReportingFillMonthOtherOther.vue";

let tabActive = ref("external");

let changeTabActive = (key: string) => {};

let tabOptions = ref<any[]>([
    { label: "外购/外采计划", key: "external" },
    { label: "其他费用", key: "other" }
]);

// 当前月份
let planMonth = ref("");

let currentYear = dayjs().year();

let planMonthList = ref<any[]>([]);

let setPlanMonthList = () => {
    for (let i = 1; i <= 12; i++) {
        let month = String(i).padStart(2, "0");
        planMonthList.value.push({
            tab: `${month}月`,
            name: currentYear + month
        });
    }
    planMonth.value = planMonthList.value[0].name;
};

onMounted(() => {
    setPlanMonthList();
});
</script>

<style lang="scss" scoped>
::v-deep(.n-menu) {
    .n-menu-item {
        &:first-child {
            margin-top: 0;
        }

        .n-menu-item-content {
            &:before {
                left: 0;
                right: 0;
            }
        }

        .n-menu-item-content--selected {
            &:before {
                border-right: 2px solid var(--n-item-text-color-active);
            }
        }
    }
}
</style>
