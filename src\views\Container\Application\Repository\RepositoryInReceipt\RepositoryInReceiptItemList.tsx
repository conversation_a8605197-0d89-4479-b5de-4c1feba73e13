import { computed, defineComponent, ref, watchEffect } from "vue";
import { GET_REPOSITORY_VIRTUAL_STOREROOM_DETAIL } from "@/api/application/repository";
import type { DataTableColumns } from "naive-ui";

export default defineComponent({
    name: "RepositoryInReceiptItemList",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        const tableColumns = ref<DataTableColumns<any>>([
            { title: "物品名称", key: "goodsName", align: "center" },
            { title: "需入库数量", key: "applyQuality", align: "center" },
            {
                title: "物品类别",
                key: "goodsType",
                align: "center",
                render: (row) => {
                    if (row.goodsType == 1) {
                        return "原材料";
                    } else if (row.goodsType == 2) {
                        return "合格品";
                    } else if (row.goodsType == 3) {
                        return "不合格品";
                    }
                }
            },
            { title: "物品单位", key: "goodsUnitName", align: "center" }
        ]);

        const onClose = () => {
            changeModalShow(false);
            emit("refresh");
        };

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card title="入库物品列表" class="w-800px" closable onClose={onClose}>
                    <n-data-table
                        columns={tableColumns.value}
                        data={props.configData.recordItemList}
                        single-line={false}
                        bordered
                        striped
                    />
                </n-card>
            </n-modal>
        );
    }
});
