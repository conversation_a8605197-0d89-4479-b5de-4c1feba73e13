{
    "compilerOptions": {
        "target": "esnext", // 指定ECMAScript目标版本
        "module": "esnext", // 指定生成哪个模块系统代码
        "strict": true, // 启用所有严格类型检查选项。
        "jsx": "preserve", // 支持jsx语法
        "importHelpers": true, // 从 tslib 导入辅助工具函数（比如 __extends， __rest等）
        "moduleResolution": "node", // 决定如何处理模块。
        "skipLibCheck": true, // 忽略所有的声明文件（ *.d.ts）的类型检查。
        "esModuleInterop": true,
        "allowSyntheticDefaultImports": true, // 允许从没有设置默认导出的模块中默认导入。这并不影响代码的输出，仅为了类型检查。
        "sourceMap": true, // 生成相应的 .map文件。
        "baseUrl": ".", // 解析非相对模块名的基准目录
        "types": [], // 要包含的类型声明文件名列表
        // 模块名到基于baseUrl的路径映射的列表。
        "paths": {
            "@/*": ["src/*"],
            "#/*": ["types/*"]
        },
        "lib": ["esnext", "dom", "dom.iterable", "scripthost"] // 编译过程中需要引入的库文件的列表。
    },
    // 解析的文件
    "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "types/**/*.ts", "types/**/*.tsx", "vite.config.ts"],
    "exclude": ["node_modules"]
}
