import { defineComponent, onMounted, reactive, ref, watch, watchEffect } from "vue";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";

import { GET_PURCHASING_DEMAND_MANAGEMENT_LIST } from "@/api/application/purchase";

export default defineComponent({
    name: "PurchaseManageSupplyList",
    setup(props) {
        // 搜索项
        const searchConfig = ref<TableSearchbarConfig>([
            {
                label: "筛选状态",
                prop: "states",
                type: "select"
            }
        ]);
        const searchOptions = ref<TableSearchbarOptions>({
            states: [
                { label: "未发货", value: 0 },
                { label: "已发货", value: 1 }
            ]
        });
        const getSearchOptions = async () => {};
        const searchForm = ref<TableSearchbarData>({
            states: 0
        });
        const onSearch = () => {
            tablePagination.page = 1;
            tablePagination.pageSize = 10;
            getTableData();
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            { title: "需求单号", key: "no", align: "center", render: (row) => row.no ?? "/" }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_PURCHASING_DEMAND_MANAGEMENT_LIST({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                ...searchForm.value
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        onMounted(async () => {
            await getSearchOptions();
            getTableData();
        });

        return () => (
            <div class="plastic-mes-repair-inside-list">
                <n-card>
                    <TableSearchbar
                        form={searchForm.value}
                        config={searchConfig.value}
                        options={searchOptions.value}
                        onSearch={onSearch}
                    />
                </n-card>
                <n-card class="mt">
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        pagination={tablePagination}
                        row-key={tableRowKey}
                        single-line={false}
                        bordered
                        remote
                        striped
                        onUpdate:checked-row-keys={changeTableSelection}
                    />
                </n-card>
            </div>
        );
    }
});
