<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-space class="mb">
                <n-button secondary type="primary" @click="openEditModal()">新增领料单</n-button>
            </n-space>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <!--新增编辑-->
        <OaLaunch v-model:show="editModal.show" :configData="editModal.configData" @refresh="getTableData()" />
        <!--审批单-->
        <ProcessDetail v-model:show="processDetailModal.show" :config-data="processDetailModal.configData" />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from "vue";
import { DataTableColumns, NButton } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { TableActions } from "@/components/TableActions";
import { useCommonTable } from "@/hooks";
import {
    CONFIRM_WAREHOUSE_GOODS_RECEIVE_APPLY,
    GET_WAREHOUSE_GOODS_RECEIVE_APPLY_LIST,
    REFUSE_WAREHOUSE_GOODS_RECEIVE_APPLY
} from "@/api/application/warehouse";
import OaLaunch from "./WarehouseEntryExitReceiveLaunch.vue";
import { ProcessDetail } from "@/views/Container/Application/Process/components";
import { GET_OA_INSTANCE_FORM } from "@/api/application/oa";

interface RowProps {
    [key: string]: any;
}

onMounted(async () => {
    await getSearchOptions();
    getTableData();
});

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([]);

let searchOptions = ref<TableSearchbarOptions>({});

let searchForm = ref<TableSearchbarData>({});

let getSearchOptions = async () => {};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "领料OA单",
        key: "businessKey",
        align: "center"
    },
    {
        title: "领料部门",
        key: "applyDepartName",
        align: "center",
        render: (row) => {
            return row.applyDepartName || "/";
        }
    },
    {
        title: "领料人",
        key: "applyByName",
        align: "center",
        render: (row) => {
            return row.applyByName || "/";
        }
    },
    {
        title: "领料明细",
        key: "id",
        align: "center",
        render(row) {
            return h(
                NButton,
                {
                    type: "primary",
                    text: true,
                    onClick: () => openProcessDetailModal(row.processInstanceId)
                },
                () => "点击查看"
            );
        }
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: "220",
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "确认发料",
                        tertiary: true,
                        onClick: () => {
                            onConfirmSend(row);
                        }
                    },
                    {
                        label: "拒绝发料",
                        type: "error",
                        tertiary: true,
                        onClick: () => {
                            onRefuseSend(row);
                        }
                    }
                ]
            });
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_WAREHOUSE_GOODS_RECEIVE_APPLY_LIST({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        applyState: 1,
        ...searchForm.value
    }).then((res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data.records;
            tablePagination.itemCount = res.data.data.total;
            tableLoading.value = false;
            console.log(1111, tableData.value);
        }
    });
};

// 搜索
let onSearch = () => {
    tablePagination.page = 1;
    tablePagination.pageSize = 10;
    getTableData();
};

// 新增
let editModal = ref<{ show: boolean; configData: UnKnownObject }>({
    show: false,
    configData: {}
});

let openEditModal = (row?: RowProps) => {
    editModal.value = {
        show: true,
        configData: row ?? {}
    };
};

// 查看审批单
let processDetailModal = ref<{ show: boolean; configData: Record<string, any> }>({
    show: false,
    configData: {}
});

let openProcessDetailModal = (id: string | number) => {
    GET_OA_INSTANCE_FORM({ processInstId: id }).then((res) => {
        if (res.data.code === 0) {
            processDetailModal.value.show = true;
            processDetailModal.value.configData = res.data.data;
        } else {
            window.$message.error("该审批单不存在");
        }
    });
};

// 确认发料
let onConfirmSend = (row: RowProps) => {
    window.$dialog.warning({
        title: "提示",
        content: "确认发料后，将无法撤回，是否继续？",
        positiveText: "确认",
        negativeText: "取消",
        onPositiveClick: () => {
            CONFIRM_WAREHOUSE_GOODS_RECEIVE_APPLY({ id: row.id }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("操作成功");
                    getTableData();
                } else {
                    window.$message.error(res.data.msg);
                }
            });
        }
    });
};

// 拒绝发料
let onRefuseSend = (row: RowProps) => {
    window.$dialog.warning({
        title: "提示",
        content: "拒绝发料后，将无法撤回，是否继续？",
        positiveText: "确认",
        negativeText: "取消",
        onPositiveClick: () => {
            REFUSE_WAREHOUSE_GOODS_RECEIVE_APPLY({ id: row.id }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("操作成功");
                    getTableData();
                } else {
                    window.$message.error(res.data.msg);
                }
            });
        }
    });
};
</script>
