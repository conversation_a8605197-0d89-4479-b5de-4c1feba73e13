import { defineComponent, h, onMounted, reactive, ref } from "vue";
import {
    TableSearchbar,
    type TableSearchbarConfig,
    type TableSearchbarData,
    type TableSearchbarOptions
} from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import { TableActions } from "@/components/TableActions";
import { GET_QUALITY_TASK_PAGE_LIST } from "@/api/application/TowerScan";
import TowerScanQualityInspectionComponentBelong from "./TowerScanQualityInspectionComponentBelong";
import TowerScanQualityInspectionComponentDetail from "./TowerScanQualityInspectionComponentDetail";

export default defineComponent({
    name: "TowerScanQualityInspectionComponentList",
    setup(props) {
        // 完成状态tab
        const activeStatusTab = ref<string>("all");

        // 完成状态选项
        const statusOptions = [
            { label: "全部", value: "all" },
            { label: "待完成", value: "0" },
            { label: "进行中", value: "1" },
            { label: "已完成", value: "2" }
        ];

        // 搜索项（移除了finishStatus）
        const searchConfig = ref<TableSearchbarConfig>([
            { prop: "planName", label: "计划名称", type: "input", labelWidth: "100" },
            { prop: "projectName", label: "工程名称", type: "input", labelWidth: "100" },
            { prop: "mainMaterialCode", label: "主件号", type: "input", labelWidth: "100" },
            { prop: "processType", label: "工序类型", type: "select", labelWidth: "100" },
            {
                prop: "startIssuedDate",
                label: "下发开始时间",
                type: "date",
                labelWidth: "100",
                dateFormat: "yyyy-MM-dd"
            },
            { prop: "endIssuedDate", label: "下发结束时间", type: "date", labelWidth: "100", dateFormat: "yyyy-MM-dd" },
            {
                prop: "startFinishTime",
                label: "完成开始时间",
                type: "date",
                labelWidth: "100",
                dateFormat: "yyyy-MM-dd"
            },
            { prop: "endFinishTime", label: "完成结束时间", type: "date", labelWidth: "100", dateFormat: "yyyy-MM-dd" }
        ]);

        const searchOptions = ref<TableSearchbarOptions>({
            processType: [
                { label: "组装", value: 5 },
                { label: "电焊", value: 6 }
            ]
        });

        const searchForm = ref<TableSearchbarData>({
            planName: null,
            projectName: null,
            mainMaterialCode: null,
            processType: null,
            startIssuedDate: null,
            endIssuedDate: null,
            startFinishTime: null,
            endFinishTime: null
        });

        // 获取当前完成状态值
        const getCurrentFinishStatus = () => {
            return activeStatusTab.value === "all" ? null : activeStatusTab.value;
        };

        const onSearch = () => {
            tablePagination.page = 1;
            tablePagination.pageSize = 10;
            getTableData();
        };

        // 监听状态tab变化时触发搜索
        const handleStatusTabChange = (value: string) => {
            activeStatusTab.value = value;
            onSearch();
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            {
                title: "主件号",
                key: "mainMaterialCode",
                align: "center",
                ellipsis: true
            },
            {
                title: "主件归属计划",
                key: "mainMaterialId",
                align: "center",
                render: (row) => {
                    return (
                        <n-button type="primary" text onClick={() => openMainPartsAttributionPlanModal(row)}>
                            点击查看
                        </n-button>
                    );
                }
            },
            {
                title: "工程名称",
                key: "projectName",
                align: "center",
                ellipsis: true
            },
            {
                title: "塔型名称",
                key: "typeName",
                align: "center",
                ellipsis: true
            },
            {
                title: "组装需求数",
                key: "composeQuantity",
                align: "center",
                render: (row) => {
                    return <n-text type="info">{row.composeQuantity || "/"}</n-text>;
                }
            },
            {
                title: "工序类型",
                key: "processType",
                align: "center",
                render: (row) => {
                    switch (row.processType) {
                        case 5:
                            return <n-text type="info">组装</n-text>;
                        case 6:
                            return <n-text type="info">电焊</n-text>;
                        default:
                            return <n-text type="info">/</n-text>;
                    }
                }
            },
            {
                title: "组装需求总量",
                key: "composeWeight",
                align: "center",
                render: (row) => {
                    return <n-text type="info">{row.composeWeight ? `${row.composeWeight}kg` : "/"}</n-text>;
                }
            },
            {
                title: "质检完成状态",
                key: "finishStatus",
                align: "center",
                render: (row) => {
                    switch (row.finishStatus) {
                        case 0:
                            return <n-text type="error">待完成</n-text>;
                        case 1:
                            return <n-text type="info">进行中</n-text>;
                        case 2:
                            return <n-text type="success">已完成</n-text>;
                        default:
                            return <n-text>/</n-text>;
                    }
                }
            },
            {
                title: "待检总重",
                key: "pendingQuantity",
                align: "center",
                render: (row) => {
                    return <n-text type="info">{row.pendingQuantity || "/"}</n-text>;
                }
            },
            {
                title: "配方已完成总重",
                key: "componentFinishWeight",
                align: "center",
                render: (row) => {
                    return (
                        <n-text type="info">
                            {row.componentFinishWeight ? `${row.componentFinishWeight}kg` : "/"}
                        </n-text>
                    );
                }
            },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 100,
                fixed: "right",
                render: (row) => {
                    return (
                        <TableActions
                            type="button"
                            buttonActions={[
                                {
                                    label: "查看详情",
                                    tertiary: true,
                                    type: "primary",
                                    onClick: () => openComponentDetailModal(row)
                                }
                            ]}
                        />
                    );
                }
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_QUALITY_TASK_PAGE_LIST({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                ...searchForm.value,
                finishStatus: getCurrentFinishStatus() // 添加完成状态到请求参数
            })
                .then((res) => {
                    if (res.data.code === 0) {
                        tableData.value = res.data.data.records;
                        tablePagination.itemCount = res.data.data.total;
                        tableLoading.value = false;
                    } else {
                        window.$message.error(res.data.msg || "获取数据失败");
                        tableLoading.value = false;
                    }
                })
                .catch(() => {
                    window.$message.error("获取数据失败");
                    tableLoading.value = false;
                });
        };

        // 组装电焊详情弹窗
        const componentDetailModal = ref<{ show: boolean; configData: UnKnownObject }>({
            show: false,
            configData: {}
        });

        const openComponentDetailModal = (row?: RowProps) => {
            componentDetailModal.value = {
                show: true,
                configData: {
                    ...row
                }
            };
        };

        // 主件归属计划弹窗
        const mainPartsAttributionPlanModal = ref<{ show: boolean; configData: UnKnownObject }>({
            show: false,
            configData: {}
        });

        const openMainPartsAttributionPlanModal = (row?: RowProps) => {
            mainPartsAttributionPlanModal.value = {
                show: true,
                configData: {
                    componentId: row?.componentId,
                    mainMaterialCode: row?.mainMaterialCode,
                    typeName: row?.typeName
                }
            };
        };

        onMounted(() => {
            getTableData();
        });

        return () => (
            <div class="tower-scan-assembly-welding-quality-inspection-list">
                <n-tabs v-model:value={activeStatusTab.value} onUpdate:value={handleStatusTabChange}>
                    {statusOptions.map((option) => (
                        <n-tab-pane key={option.value} name={option.value} tab={option.label + "质检任务"} />
                    ))}
                </n-tabs>

                <n-card>
                    <TableSearchbar
                        form={searchForm.value}
                        config={searchConfig.value}
                        options={searchOptions.value}
                        onSearch={onSearch}
                    />
                </n-card>
                <n-card class="mt">
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        pagination={tablePagination}
                        row-key={tableRowKey}
                        single-line={false}
                        bordered
                        remote
                        striped
                        onUpdate:checked-row-keys={changeTableSelection}
                        scroll-x={1650}
                    />
                </n-card>
                {/* 组装电焊详情弹窗组件 */}
                <TowerScanQualityInspectionComponentDetail
                    v-model:show={componentDetailModal.value.show}
                    configData={componentDetailModal.value.configData}
                    onRefresh={() => {
                        getTableData();
                    }}
                />

                {/* 主件归属计划弹窗 */}
                <TowerScanQualityInspectionComponentBelong
                    v-model:show={mainPartsAttributionPlanModal.value.show}
                    configData={mainPartsAttributionPlanModal.value.configData}
                />
            </div>
        );
    }
});
