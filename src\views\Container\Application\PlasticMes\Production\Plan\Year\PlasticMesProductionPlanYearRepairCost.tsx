import { computed, defineComponent, onMounted, ref } from "vue";
import dayjs from "dayjs";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { GET_USAGE_YEAR_PLAN_BY_YEAR, POST_USAGE_YEAR_PLAN } from "@/api/application/plasticMes";

export default defineComponent({
    name: "PlasticMesProductionPlanYearRepairCost",
    setup() {
        // 年份搜索
        const searchFormRef = ref<FormInst | null>(null);
        const searchFormData = ref<UnKnownObject>({
            planYear: null
        });
        const planYearOptions = ref<UnKnownObject[]>([]);
        const getSearchFormOptions = async () => {
            const options = [];
            for (let i = 0; i < 10; i++) {
                options.push({
                    label: dayjs().add(i, "year").year(),
                    value: dayjs().add(i, "year").year()
                });
            }
            planYearOptions.value = options;
        };
        const searchFormRules = computed(() => ({
            planYear: [{ required: true, message: "请选择计划年度", trigger: ["blur", "change"], type: "number" }]
        }));

        const onSearch = async () => {
            const validateError = await searchFormRef.value?.validate((errors) => !!errors);
            if (validateError) return false;

            GET_USAGE_YEAR_PLAN_BY_YEAR({
                planYear: searchFormData.value.planYear
            }).then((res) => {
                const resData = res.data.data ?? null;
                formData.value = {
                    id: resData?.id ?? null,
                    outsourcedMaintenanceCost: resData?.outsourcedMaintenanceCost ?? null,
                    innerMaintenanceCost: resData?.innerMaintenanceCost ?? null
                };
            });
        };

        // 左侧菜单切换
        const tabActive = ref<string>("outsourcedMaintenanceCost");

        const changeTabActive = async (key: string) => {
            await onSearch();
        };

        const tabOptions = ref<any[]>([
            { label: "委外维修", key: "outsourcedMaintenanceCost" },
            { label: "内部维修", key: "innerMaintenanceCost" }
        ]);

        // 表单数据
        interface FormDataProps {
            [key: string]: any;
        }

        const formRef = ref<FormInst | null>(null);

        const getFormOptions = async () => {};

        const initFormData: FormDataProps = {
            outsourcedMaintenanceCost: null,
            innerMaintenanceCost: null
        };

        const formRules = computed(() => ({
            outsourcedMaintenanceCost: [
                { required: true, message: "请输入委外维修费用（元）", trigger: ["input", "blur"] }
            ],
            innerMaintenanceCost: [{ required: true, message: "请输入内部维修费用（元）", trigger: ["input", "blur"] }]
        }));

        const formData = ref(cloneDeep(initFormData));

        const clearForm = () => {
            formData.value = cloneDeep(initFormData);
        };

        const onSubmit = async () => {
            const searchValidateError = await searchFormRef.value?.validate((errors) => !!errors);
            const validateError = await formRef.value?.validate((errors) => !!errors);
            if (!!validateError || !!searchValidateError) return false;

            const params = {
                ...formData.value,
                usageType: tabActive.value === 'innerMaintenanceCost' ? 6 : 1,
                planYear: searchFormData.value.planYear
            };

            try {
                const res = await POST_USAGE_YEAR_PLAN(params);
                if (res.data.code === 0) {
                    window.$message.success("保存成功");
                    onSearch();
                }
            } catch (error) {
                window.$message.error("保存失败");
            }
        };

        const onClear = () => {
            clearForm();
        };

        onMounted(async () => {
            await getSearchFormOptions();
            await getFormOptions();
        });

        return () => (
            <div class="plastic-mes-production-plan-year mt-2">
                <n-form
                    inline
                    ref={searchFormRef}
                    model={searchFormData.value}
                    rules={searchFormRules.value}
                    label-placement="left"
                    label-width="auto"
                >
                    <n-form-item label="计划年度" path="planYear">
                        <n-select
                            class="w-300px"
                            v-model:value={searchFormData.value.planYear}
                            options={planYearOptions.value}
                            clearable
                            filterable
                            placeholder="请选择计划年度"
                        />
                    </n-form-item>
                    <n-form-item>
                        <n-button type="primary" onClick={() => onSearch()}>
                            查询
                        </n-button>
                    </n-form-item>
                </n-form>
                <div class="flex mt">
                    <n-menu
                        v-model:value={tabActive.value}
                        indent={20}
                        options={tabOptions.value}
                        class={"common-left-menu flex-fixed-180 border-r-1px border-[#E5E5E5]"}
                        mode="vertical"
                        onUpdate:value={changeTabActive}
                    />
                    <div class="flex-fixed-800 ml">
                        <n-space>
                            <n-button type="primary" onClick={onSubmit}>
                                保存配置
                            </n-button>
                            <n-button type="error" onClick={onClear}>
                                清空内容
                            </n-button>
                        </n-space>
                        <n-form
                            class="mt"
                            ref={formRef}
                            model={formData.value}
                            rules={formRules.value}
                            label-placement="left"
                            label-width="auto"
                        >
                            <n-grid cols={12} x-gap={16}>
                                {tabActive.value === "outsourcedMaintenanceCost" && (
                                    <n-form-item-gi
                                        span={6}
                                        label="委外维修费用（元）"
                                        path="outsourcedMaintenanceCost"
                                    >
                                        <n-input
                                            v-model:value={formData.value.outsourcedMaintenanceCost}
                                            class="w-100%"
                                            clearable
                                            placeholder="请输入委外维修费用（元）"
                                        />
                                    </n-form-item-gi>
                                )}
                                {tabActive.value === "innerMaintenanceCost" && (
                                    <n-form-item-gi span={6} label="内部维修费用（元）" path="innerMaintenanceCost">
                                        <n-input
                                            v-model:value={formData.value.innerMaintenanceCost}
                                            class="w-100%"
                                            clearable
                                            placeholder="请输入内部维修费用（元）"
                                        />
                                    </n-form-item-gi>
                                )}
                            </n-grid>
                        </n-form>
                    </div>
                </div>
            </div>
        );
    }
});
