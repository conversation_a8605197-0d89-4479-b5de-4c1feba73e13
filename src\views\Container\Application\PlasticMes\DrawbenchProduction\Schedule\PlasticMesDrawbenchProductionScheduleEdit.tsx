import { computed, defineComponent, ref, watchEffect } from "vue";
import { type FormInst, type DataTableColumns } from "naive-ui";
import { cloneDeep } from "lodash-es";
import {
    GET_DRAWBENCH_SCHEDULE_INFO,
    POST_DRAWBENCH_SCHEDULE,
    PUT_DRAWBENCH_SCHEDULE,
    GET_PRODUCTION_MATERIAL_LIST
} from "@/api/application/plasticMes";
import { GET_WORK_GROUP_PAGE_LIST, GET_WORK_GROUP_GROUP_USER_LIST } from "@/api/application/production";
import { useDicts } from "@/hooks";
import dayjs from "dayjs";
import { UserSelector } from "@/components/UserSelector";
import { TableActions } from "@/components/TableActions";

export default defineComponent({
    name: "PlasticMesDrawbenchProductionScheduleEdit",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        // 表单数据
        interface FormDataProps {
            [key: string]: any;
        }

        // 字典操作
        const { dictLibs, getDictLibs } = useDicts();

        const setDictLibs = async () => {
            const dictName = ["common_units", "drawbench_type"];
            await getDictLibs(dictName);
        };

        // 获取详情
        const getDetail = async () => {
            await GET_DRAWBENCH_SCHEDULE_INFO({ id: props.configData.id }).then((res) => {
                if (res.data.code === 0) {
                    formData.value = {
                        ...res.data.data,
                        ptType: String(res.data.data.ptType),
                        planBeginTime: dayjs(res.data.data.planBeginTime).format("YYYY-MM-DD HH:mm:00") ?? null,
                        planEndTime: dayjs(res.data.data.planEndTime).format("YYYY-MM-DD HH:mm:00") ?? null
                    };
                    tableData.value = res.data.data.scheduleItemList ?? [];
                    scheduleUserList.value = res.data.data.scheduleUserList ?? [];
                }
            });
        };

        const formRef = ref<FormInst | null>(null);

        // 获取表单选项
        const workGroupIdOptions = ref<any[]>([]);
        const ptTypeOptions = ref<any>([]);

        const getFormOptions = async () => {
            await GET_WORK_GROUP_PAGE_LIST({
                current: 1,
                size: 9999,
                companyId: "1596770763313336322"
            }).then((res) => {
                workGroupIdOptions.value = (res.data.data.records ?? []).map((item: any) => ({
                    label: item.companyName + "-" + item.workshopName + "-" + item.groupName,
                    value: item.id
                }));
            });
            ptTypeOptions.value = dictLibs["drawbench_type"] ?? [];
        };

        watchEffect(async () => {
            if (show.value) {
                await setDictLibs();
                await getFormOptions();
                if (props.configData.id) await getDetail();
            }
        });

        const initFormData: FormDataProps = {
            ptType: null,
            productionUnit: null,
            planQuantity: null,
            planBeginTime: null,
            planEndTime: null,
            workGroupId: null,
            remark: null
        };

        const formRules = computed(() => ({
            ptType: [{ required: true, message: "请选择拉丝类型", trigger: ["blur", "change"] }],
            planQuantity: [{ required: true, message: "请输入计划完成数量", trigger: ["input", "blur"] }],
            planBeginTime: [{ required: true, message: "请选择计划开始时间", trigger: ["blur", "change"] }],
            planEndTime: [{ required: true, message: "请选择计划结束时间", trigger: ["blur", "change"] }],
            productionUnit: [{ required: true, message: "请选择生产单位", trigger: ["blur", "change"] }],
            workGroupId: [{ required: true, message: "请选择排产班组", trigger: ["blur", "change"] }]
        }));

        const formData = ref(cloneDeep(initFormData));

        // 材料表格
        interface RowProps {
            [key: string]: any;
        }

        const goodsIdOptions = ref<any[]>([]);

        const getTableOptions = async () => {
            GET_PRODUCTION_MATERIAL_LIST({ current: 1, size: 9999, goodsType: 1, purposeType: 1 }).then((res) => {
                goodsIdOptions.value = res.data.data.records ?? [];
            });
        };

        watchEffect(async () => {
            if (show.value) await getTableOptions();
        });

        const tableData = ref<RowProps[]>([]);

        const tableColumns = ref<DataTableColumns<RowProps>>([
            {
                title: "材料名称",
                key: "goodsName",
                align: "center",
                render: (row) => (
                    <n-select
                        value={row.goodsId}
                        options={goodsIdOptions.value}
                        clearable
                        filterable
                        placeholder="请选择材料名称"
                        labelField="goodsName"
                        valueField="goodsId"
                        onUpdateValue={(v: any, o: any) => {
                            row.goodsId = v;
                            row.goodsUnitName = o?.goodsUnitName;
                            row.goodsSpec = o?.goodsSpec;
                        }}
                    />
                )
            },
            {
                title: "用量",
                key: "goodsConsumption",
                align: "center",
                width: 150,
                render: (row) => (
                    <n-input
                        value={row.goodsConsumption}
                        onUpdateValue={(v: any) => {
                            row.goodsConsumption = v;
                        }}
                    />
                )
            },
            {
                title: "用料单位",
                key: "goodsUnitName",
                align: "center",
                width: 150,
                render: (row) => row.goodsUnitName ?? "/"
            },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 80,
                render: (row, index) => (
                    <TableActions
                        type="button"
                        buttonActions={[
                            {
                                label: "删除",
                                tertiary: true,
                                type: "error",
                                onClick: () => deleteItem(row, index)
                            }
                        ]}
                    />
                )
            }
        ]);

        // 可编辑表单配置
        const tableItem: RowProps = {
            goodsId: null,
            goodsConsumption: null,
            delFlag: 0
        };

        const addTableItem = () => {
            tableData.value.push(cloneDeep(tableItem));
        };

        const deleteItem = (row: RowProps, index: number) => {
            if (row.id) {
                row.delFlag = 1;
            } else {
                tableData.value.splice(index, 1);
            }
        };

        // 班组查询排产人员列表
        const scheduleUserList = ref<any[]>([]);

        const getScheduleUserList = async () => {
            GET_WORK_GROUP_GROUP_USER_LIST({
                workGroupId: formData.value.workGroupId
            }).then((res) => {
                scheduleUserList.value = (res.data.data.records ?? [])
                    .filter((item: any) => {
                        return item.jobType === 5; // 改动点: jobType从3改为5
                    })
                    .map((item: any) => ({
                        jobType: item.jobType,
                        jobTypeName: item.jobTypeName,
                        productUser: item.username
                    }));
            });
        };

        const onClose = () => {
            formData.value = cloneDeep(initFormData);
            tableData.value = [];
            scheduleUserList.value = [];
            changeModalShow(false);
            emit("refresh");
        };

        const onSubmit = async () => {
            const validateError = await formRef.value?.validate((errors) => !!errors);
            if (validateError) return false;

            const submitData = {
                ...formData.value,
                scheduleItemList: tableData.value,
                scheduleUserList: scheduleUserList.value
            };

            if (props.configData.id) {
                PUT_DRAWBENCH_SCHEDULE(submitData).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("编辑成功");
                        onClose();
                    } else window.$message.error(res.data.msg);
                });
            } else {
                POST_DRAWBENCH_SCHEDULE(submitData).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("新增成功");
                        onClose();
                    } else window.$message.error(res.data.msg);
                });
            }
        };

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card
                    title={props.configData.id ? "编辑排产单" : "新建排产单"}
                    class="w-1000px"
                    closable
                    onClose={onClose}
                >
                    <n-form
                        ref={formRef}
                        model={formData.value}
                        rules={formRules.value}
                        label-placement="left"
                        label-width="auto"
                    >
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi span={6} label="拉丝类型" path="ptType">
                                <n-select
                                    v-model:value={formData.value.ptType}
                                    options={ptTypeOptions.value}
                                    clearable
                                    placeholder="请选择拉丝类型"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="计划完成数量" path="planQuantity">
                                <n-input
                                    v-model:value={formData.value.planQuantity}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入计划完成数量"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="生产单位" path="productionUnit">
                                <n-select
                                    v-model:value={formData.value.productionUnit}
                                    options={dictLibs["common_units"]}
                                    className="w-100%"
                                    clearable
                                    placeholder="请选择单位"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="计划开始时间" path="planBeginTime">
                                <n-date-picker
                                    v-model:formatted-value={formData.value.planBeginTime}
                                    class="w-100%"
                                    clearable
                                    type="datetime"
                                    value-format="yyyy-MM-dd HH:mm:00"
                                    placeholder="请选择计划开始时间"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="计划结束时间" path="planEndTime">
                                <n-date-picker
                                    v-model:formatted-value={formData.value.planEndTime}
                                    class="w-100%"
                                    clearable
                                    type="datetime"
                                    value-format="yyyy-MM-dd HH:mm:00"
                                    placeholder="请选择计划结束时间"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="备注" path="remark">
                                <n-input
                                    v-model:value={formData.value.remark}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入备注"
                                />
                            </n-form-item-gi>

                            <n-form-item-gi span={12}>
                                <div class="w-100%">
                                    <div class="text-18px">排产排班</div>
                                </div>
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="排产班组" path="workGroupId">
                                <n-select
                                    v-model:value={formData.value.workGroupId}
                                    options={workGroupIdOptions.value}
                                    clearable
                                    filterable
                                    placeholder="请选择排产班组"
                                    onUpdateValue={() => getScheduleUserList()}
                                />
                            </n-form-item-gi>

                            {scheduleUserList.value.length > 0 && (
                                <n-form-item-gi span={12}>
                                    <n-table single-line={false} class="text-center">
                                        <thead>
                                            <tr>
                                                <th>工种</th>
                                                <th>人员</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {scheduleUserList.value.map((item) => (
                                                <tr>
                                                    <td>{item.jobTypeName}</td>
                                                    <td>
                                                        <UserSelector
                                                            v-model:value={item.productUser}
                                                            class="w-100%"
                                                            clearable
                                                            multiple={false}
                                                            key-name="username"
                                                            placeholder="请选择人员"
                                                        />
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </n-table>
                                </n-form-item-gi>
                            )}

                            <n-form-item-gi span={12}>
                                <div class="w-100%">
                                    <div class="text-18px">用料清单</div>
                                    <div class="mt">
                                        <n-data-table
                                            columns={tableColumns.value}
                                            data={tableData.value.filter((item) => item.delFlag !== 1)}
                                            single-line={false}
                                            bordered
                                            striped
                                        />
                                    </div>
                                    <div class="flex-x-center mt">
                                        <n-space>
                                            <n-button type="success" onClick={addTableItem}>
                                                新增一行
                                            </n-button>
                                        </n-space>
                                    </div>
                                </div>
                            </n-form-item-gi>

                            <n-form-item-gi span={12}>
                                <n-space>
                                    <n-button type="primary" onClick={onSubmit}>
                                        提交
                                    </n-button>
                                    <n-button onClick={onClose}>取消</n-button>
                                </n-space>
                            </n-form-item-gi>
                        </n-grid>
                    </n-form>
                </n-card>
            </n-modal>
        );
    }
});
