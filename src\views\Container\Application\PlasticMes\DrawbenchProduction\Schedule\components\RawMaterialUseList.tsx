import { computed, defineComponent, reactive, ref, watchEffect } from "vue";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import {
    GET_DRAWBENCH_SCHEDULE_DETAIL_ITEM_PAGE_LIST,
    GET_DRAWBENCH_SCHEDULE_GET_ITEM_PAGE_LIST
} from "@/api/application/plasticMes";

export default defineComponent({
    name: "PlasticMesProductionScheduleRawMaterialUseList",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { title: "原料名称", key: "goodsName", align: "center" },
            { title: "用量", key: "goodsUsage", align: "center" },
            { title: "用料单位", key: "goodsUnitName", align: "center" }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_DRAWBENCH_SCHEDULE_GET_ITEM_PAGE_LIST({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                scheduleId: props.configData.scheduleId,
                scheduleDetailId: props.configData.scheduleDetailId,
                recordId: props.configData.id
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        const onClose = () => {
            changeModalShow(false);
        };

        watchEffect(async () => {
            if (show.value) {
                getTableData();
            }
        });

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card title="查看用料清单" class="w-800px" closable onClose={onClose}>
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        pagination={tablePagination}
                        row-key={tableRowKey}
                        single-line={false}
                        bordered
                        remote
                        striped
                        onUpdate:checked-row-keys={changeTableSelection}
                    />
                </n-card>
            </n-modal>
        );
    }
});
