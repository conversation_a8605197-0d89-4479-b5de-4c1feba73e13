import { defineComponent, onMounted, reactive, ref } from "vue";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import PurchaseSupplierCategoryEdit from "./PurchaseSupplierCategoryEdit";
import { DELETE_SUPPLY_CATEGORY_MANAGEMENT, GET_SUPPLY_CATEGORY_MANAGEMENT_LIST } from "@/api/application/purchase";
import { TableActions } from "@/components/TableActions";
import PurchaseSupplierCategoryDetail from "@/views/Container/Application/Purchase/Supplier/Category/PurchaseSupplierCategoryDetail";

export default defineComponent({
    name: "PurchaseSupplierCategoryList",
    setup(props) {
        // 搜索项
        const searchConfig = ref<TableSearchbarConfig>([
            {
                label: "类别名称",
                prop: "categoryName",
                type: "input"
            }
        ]);
        const searchOptions = ref<TableSearchbarOptions>({});
        const getSearchOptions = async () => {};
        const searchForm = ref<TableSearchbarData>({});
        const onSearch = () => {
            tablePagination.page = 1;
            tablePagination.pageSize = 10;
            getTableData();
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            { title: "类别名称", key: "categoryName", align: "center", render: (row) => row.categoryName ?? "/" },
            { title: "创建时间", key: "createTime", align: "center", render: (row) => row.createTime ?? "/" },
            { title: "创建人", key: "createByName", align: "center", render: (row) => row.createByName ?? "/" },
            { title: "备注", key: "remarks", align: "center", render: (row) => row.remarks ?? "/" },
            {
                title: "操作",
                key: "actions",
                align: "center",
                width: 230,
                render: (row) => (
                    <TableActions
                        type="button"
                        buttonActions={[
                            {
                                label: "编辑",
                                type: "warning",
                                tertiary: true,
                                onClick: () => openEditModal(row)
                            },
                            {
                                label: "查看详情",
                                type: "primary",
                                tertiary: true,
                                onClick: () => openDetailModal(row)
                            },
                            {
                                label: "删除",
                                tertiary: true,
                                type: "error",
                                onClick: () => onDelete(row)
                            }
                        ]}
                    />
                )
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_SUPPLY_CATEGORY_MANAGEMENT_LIST({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                techState: 0,
                ...searchForm.value
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        // 新增编辑弹窗
        const editModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const openEditModal = (row?: RowProps) => {
            editModal.value.show = true;
            editModal.value.configData = row ? { id: row.id } : {};
        };

        // 详情弹窗
        const detailModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const openDetailModal = (row: RowProps) => {
            detailModal.value = { show: true, configData: row };
        };

        // 删除
        const onDelete = (row: RowProps) => {
            window.$dialog.warning({
                title: "警告",
                content: "确认删除该条数据？该操作不可逆",
                positiveText: "确认删除",
                negativeText: "我再想想",
                onPositiveClick: () => {
                    DELETE_SUPPLY_CATEGORY_MANAGEMENT({ id: row.id }).then((res) => {
                        if (res.data.code === 0) {
                            window.$message.success("删除成功");
                            onSearch();
                        }
                    });
                }
            });
        };

        onMounted(async () => {
            await getSearchOptions();
            getTableData();
        });

        return () => (
            <div class="plastic-mes-repair-inside-list">
                <n-card>
                    <TableSearchbar
                        form={searchForm.value}
                        config={searchConfig.value}
                        options={searchOptions.value}
                        onSearch={onSearch}
                    />
                </n-card>
                <n-card class="mt">
                    <n-space class="mb">
                        <n-button type="primary" onClick={() => openEditModal()}>
                            新增类别标签
                        </n-button>
                    </n-space>
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        pagination={tablePagination}
                        row-key={tableRowKey}
                        single-line={false}
                        bordered
                        remote
                        striped
                        onUpdate:checked-row-keys={changeTableSelection}
                    />
                </n-card>
                <PurchaseSupplierCategoryEdit
                    v-model:show={editModal.value.show}
                    configData={editModal.value.configData}
                    onRefresh={getTableData}
                />
                <PurchaseSupplierCategoryDetail
                    v-model:show={detailModal.value.show}
                    configData={detailModal.value.configData}
                />
            </div>
        );
    }
});
