<template>
    <n-card hoverable>
        <n-form label-placement="left" label-width="auto">
            <n-grid cols="12" x-gap="16">
                <n-form-item-gi :span="4" label="超时时间设置" required>
                    <n-input v-model:value="nodeTimeout" class="w-100%" clearable placeholder="请输入超时时间设置">
                        <template #suffix>小时</template>
                    </n-input>
                </n-form-item-gi>
                <n-form-item-gi :span="12">
                    <n-button type="primary" @click="onSubmit">保存应用</n-button>
                </n-form-item-gi>
            </n-grid>
        </n-form>
    </n-card>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { GET_NODE_TIMEOUT, SAVE_NODE_TIMEOUT } from "@/api/application/project";

onMounted(() => {
    getNodeTimeout();
});

let publicId = ref("");
let publicKey = ref("");
let nodeTimeout = ref("");

let getNodeTimeout = () => {
    GET_NODE_TIMEOUT({ publicKey: "PROJECT_TIMEOUT_TIME" }).then((res) => {
        publicId.value = res.data.data.publicId || "";
        publicKey.value = res.data.data.publicKey || "";
        nodeTimeout.value = res.data.data.publicValue || "";
    });
};

let onSubmit = () => {
    if (!nodeTimeout.value) return window.$message.error("请输入超时时间设置");
    SAVE_NODE_TIMEOUT({
        publicKey: publicKey.value,
        publicId: publicId.value,
        publicValue: nodeTimeout.value
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("保存成功");
        } else {
            window.$message.error(res.data.msg);
        }
        getNodeTimeout();
    });
};
</script>
