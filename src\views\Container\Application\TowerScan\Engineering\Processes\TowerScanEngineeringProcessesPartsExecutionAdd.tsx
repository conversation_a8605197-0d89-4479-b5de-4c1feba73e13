import { computed, defineComponent, ref, watchEffect } from "vue";
import { type FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import {
    ADD_MATERIAL_FILL,
    GET_MATERIAL_FILL_QUANTITY_CHECK,
    GET_IRON_TECHNIQUE_PAGE_LIST
} from "@/api/application/TowerScan";
import UserSelector from "@/components/UserSelector/src/UserSelector.vue";

export default defineComponent({
    name: "TowerScanEngineeringProcessesPartsExecutionAdd",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => {
            emit("update:show", show);
            if (!show) {
                clearForm();
            }
        };

        // 表单数据
        interface FormDataProps {
            [key: string]: any;
        }

        const formRef = ref<FormInst | null>(null);

        // 表单校验规则
        const formRules = computed(() => {
            const rules: any = {
                fillQuantity: [
                    { required: true, message: "请输入质检数量", trigger: ["input", "blur"] },
                    { pattern: /^\d+(\.\d+)?$/, message: "请输入有效的数量", trigger: ["input", "blur"] }
                ],
                belongUsers: [{ required: true, message: "请选择操作工", trigger: ["input", "blur"] }],
                fillBy: [{ required: true, message: "请选择质检员", trigger: ["input", "blur"] }],
                fillDate: [{ required: true, message: "请选择质检日期", trigger: ["blur", "change"] }],
                fillTechniqueId: [{ required: true, message: "请选择实际工艺", trigger: ["blur", "change"] }],
                processFillFrom: [
                    { required: true, message: "请选择工艺填报来源", trigger: ["blur", "change"], type: "number" }
                ]
            };

            // 非标准工艺时，工艺填报数量为必填（但如果有单重可以自动计算，则不需要手动输入）
            if (formData.value.processFillFrom === 2) {
                const hasSingleWeight = props.configData?.singleWeight && Number(props.configData.singleWeight) > 0;
                
                if (!hasSingleWeight) {
                    // 没有单重时，需要用户手动输入
                    rules.fillTechniqueQuantity = [
                        { required: true, message: "请输入工艺填报数量", trigger: ["input", "blur"] },
                        { pattern: /^\d+(\.\d+)?$/, message: "请输入有效的数量", trigger: ["input", "blur"] }
                    ];
                } else {
                    // 有单重时，只需要验证数字格式（会自动计算）
                    rules.fillTechniqueQuantity = [
                        { pattern: /^\d+(\.\d+)?$/, message: "请输入有效的数量", trigger: ["input", "blur"] }
                    ];
                }
            }

            // 制孔工艺(processType===3)时，工艺填报数量为必填
            if (props.configData?.processType === 3) {
                const hasSingleWeight = props.configData?.singleWeight && Number(props.configData.singleWeight) > 0;
                
                if (!hasSingleWeight) {
                    // 没有单重时，需要用户手动输入
                    rules.fillTechniqueQuantity = [
                        { required: true, message: "请输入工艺填报数量", trigger: ["input", "blur"] },
                        { pattern: /^\d+(\.\d+)?$/, message: "请输入有效的数量", trigger: ["input", "blur"] }
                    ];
                } else {
                    // 有单重时，只需要验证数字格式（会自动计算）
                    rules.fillTechniqueQuantity = [
                        { pattern: /^\d+(\.\d+)?$/, message: "请输入有效的数量", trigger: ["input", "blur"] }
                    ];
                }
            }

            return rules;
        });

        const initFormData: FormDataProps = {
            fillQuantity: "",
            belongUsers: "",
            fillBy: "",
            fillDate: "",
            // 以下字段根据不同模块从configData获取
            fillLineId: null,
            fillTechniqueId: null,
            fillTechniqueQuantity: "",
            materialId: "",
            planMaterialId: "",
            planTechniqueId: "",
            processFillFrom: 1, // 默认为标准工艺
            processType: 2,
            qualityTaskMaterialId: ""
        };

        const formData = ref(cloneDeep(initFormData));

        const clearForm = () => {
            formData.value = cloneDeep(initFormData);
        };

        // 工序选项
        const techniqueOptions = ref<any[]>([]);

        // 获取工序选项
        const getTechniqueOptions = async () => {
            try {
                const res = await GET_IRON_TECHNIQUE_PAGE_LIST({
                    current: 1,
                    size: 999
                });
                if (res.data.code === 0) {
                    techniqueOptions.value = res.data.data.records.map((item: any) => ({
                        label: item.techniqueName,
                        value: item.id
                    }));
                }
            } catch (error) {
                console.error("获取工序选项失败:", error);
            }
        };

        // 初始化表单数据
        const initFormDataFromConfig = () => {
            if (props.configData) {
                // 根据不同模块设置不同的字段
                if (props.configData.taskMaterialId) {
                    // 质检模块模式
                    formData.value.qualityTaskMaterialId = props.configData.taskMaterialId;
                } else if (props.configData.planMaterialId) {
                    // 生产模块模式
                    formData.value.planMaterialId = props.configData.planMaterialId;
                } else if (props.configData.materialId) {
                    // 工程模块模式
                    formData.value.materialId = props.configData.materialId;
                    formData.value.planTechniqueId = props.configData.planTechniqueId;
                }

                // 设置processType，如果configData中有则使用，否则默认为2
                formData.value.processType = props.configData.processType || 2;

                // 设置默认的质检日期为当前日期
                formData.value.fillDate = new Date().toISOString().split("T")[0];
            }
        };

        watchEffect(() => {
            if (show.value) {
                initFormDataFromConfig();
                getTechniqueOptions();
            }
        });

        // 监听工艺填报来源变化，重置工艺填报数量
        watchEffect(() => {
            if (formData.value.processFillFrom === 1) {
                // 标准工艺，工艺填报数量等于质检数量（在提交时设置）
                formData.value.fillTechniqueQuantity = "";
            } else if (formData.value.processFillFrom === 2) {
                // 非标准工艺，尝试自动计算工艺填报数量
                calculateFillTechniqueQuantity();
            }
        });

        // 监听质检数量变化，在非标准工艺或制孔工艺情况下自动计算工艺填报数量
        watchEffect(() => {
            if ((formData.value.processFillFrom === 2 || props.configData?.processType === 3) && formData.value.fillQuantity) {
                calculateFillTechniqueQuantity();
            }
        });

        // 自动计算工艺填报数量
        const calculateFillTechniqueQuantity = () => {
            const fillQuantity = Number(formData.value.fillQuantity);
            const singleWeight = Number(props.configData?.singleWeight);
            
            // 如果存在单重字段且为有效数字，自动计算工艺填报数
            if (singleWeight && !isNaN(singleWeight) && singleWeight > 0 && fillQuantity && !isNaN(fillQuantity) && fillQuantity > 0) {
                const calculatedQuantity = fillQuantity * singleWeight;
                // 处理浮点数精度问题，保留4位小数后去除多余的0
                const roundedQuantity = Math.round(calculatedQuantity * 10000) / 10000;
                formData.value.fillTechniqueQuantity = roundedQuantity.toString();
                console.log(
                    "自动计算工艺填报数:",
                    fillQuantity,
                    "*",
                    singleWeight,
                    "=",
                    calculatedQuantity,
                    "→ 修正后:",
                    roundedQuantity
                );
            } else if (singleWeight && !isNaN(singleWeight) && singleWeight > 0) {
                // 有单重但质检数量无效时，清空工艺填报数量
                formData.value.fillTechniqueQuantity = "";
            }
            // 如果没有单重，则保持用户输入的值不变
        };

        const onClose = () => {
            changeModalShow(false);
            emit("refresh");
        };

        const onSubmit = async () => {
            const validateError = await formRef.value?.validate((errors) => !!errors);
            if (validateError) return false;

            try {
                // 根据工艺填报来源设置工艺填报数量
                const submitData = {
                    ...formData.value
                };

                // 如果是标准工艺，工艺填报数量等于质检数量
                if (formData.value.processFillFrom === 1) {
                    submitData.fillTechniqueQuantity = formData.value.fillQuantity;
                }
                // 如果是非标准工艺，使用用户填写的工艺填报数量
                // submitData.fillTechniqueQuantity 已经是用户输入的值

                // 先调用校验接口
                const checkResult = await GET_MATERIAL_FILL_QUANTITY_CHECK(submitData);

                if (checkResult.data.code !== 0) {
                    // 校验失败，显示错误信息
                    window.$message.error(checkResult.data.msg || "校验失败");
                    return false;
                }

                // 校验通过，执行新增操作
                const addResult = await ADD_MATERIAL_FILL(submitData);

                if (addResult.data.code === 0) {
                    window.$message.success("新增成功");
                    onClose();
                } else {
                    window.$message.error(addResult.data.msg || "新增失败");
                }
            } catch (error) {
                window.$message.error("操作失败");
            }
        };

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card
                    title={`新增零件填报记录 - ${props.configData?.materialCode ?? "/"}`}
                    class="w-1200px"
                    closable
                    onClose={() => changeModalShow(false)}
                >
                    <n-form
                        ref={formRef}
                        model={formData.value}
                        rules={formRules.value}
                        label-placement="left"
                        label-width="auto"
                    >
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi span={12} label="工艺填报来源" path="processFillFrom">
                                <n-radio-group v-model:value={formData.value.processFillFrom}>
                                    <n-space>
                                        <n-radio value={1}>标准工艺填报</n-radio>
                                        <n-radio value={2}>非标准工艺填报</n-radio>
                                    </n-space>
                                </n-radio-group>
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="质检数量" path="fillQuantity">
                                <n-input
                                    v-model:value={formData.value.fillQuantity}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入质检数量"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="操作工" path="belongUsers">
                                <UserSelector
                                    v-model:value={formData.value.belongUsers}
                                    class="w-100%"
                                    key-name="username"
                                    placeholder="请选择操作工"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="质检员" path="fillBy">
                                <UserSelector
                                    v-model:value={formData.value.fillBy}
                                    class="w-100%"
                                    key-name="username"
                                    placeholder="请选择质检员"
                                    multiple={false}
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="质检日期" path="fillDate">
                                <n-date-picker
                                    v-model:formatted-value={formData.value.fillDate}
                                    class="w-100%"
                                    type="date"
                                    placeholder="请选择质检日期"
                                    value-format="yyyy-MM-dd"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="实际工艺" path="fillTechniqueId">
                                <n-select
                                    v-model:value={formData.value.fillTechniqueId}
                                    class="w-100%"
                                    options={techniqueOptions.value}
                                    placeholder="请选择实际工艺"
                                    clearable
                                    filterable
                                    label-field="label"
                                    value-field="value"
                                />
                            </n-form-item-gi>
                            {/* 非标准工艺时显示单重信息 */}
                            {formData.value.processFillFrom === 2 && props.configData?.singleWeight && (
                                <n-form-item-gi span={12} label="单重信息">
                                    <n-text type="info">
                                        单重：{props.configData.singleWeight}，工艺填报数量将自动计算（质检数量 × 单重）
                                    </n-text>
                                </n-form-item-gi>
                            )}
                            {/* 制孔工艺时显示单重信息 */}
                            {props.configData?.processType === 3 && props.configData?.singleWeight && (
                                <n-form-item-gi span={12} label="单重信息">
                                    <n-text type="info">
                                        单重：{props.configData.singleWeight}，工艺填报数量将自动计算（质检数量 × 单重）
                                    </n-text>
                                </n-form-item-gi>
                            )}
                            {/* 非标准工艺时显示工艺填报数量输入框 */}
                            {formData.value.processFillFrom === 2 && (
                                <n-form-item-gi span={6} label="工艺填报数量" path="fillTechniqueQuantity">
                                    <n-input
                                        v-model:value={formData.value.fillTechniqueQuantity}
                                        class="w-100%"
                                        clearable={!props.configData?.singleWeight}
                                        // readonly={!!(props.configData?.singleWeight && Number(props.configData.singleWeight) > 0)}
                                        placeholder={
                                            props.configData?.singleWeight 
                                                ? "自动计算：质检数量 × 单重" 
                                                : "请输入工艺填报数量"
                                        }
                                    />
                                </n-form-item-gi>
                            )}
                            {/* 制孔工艺时显示工艺填报数量输入框 */}
                            {props.configData?.processType === 3 && (
                                <n-form-item-gi span={6} label="工艺填报数量" path="fillTechniqueQuantity">
                                    <n-input
                                        v-model:value={formData.value.fillTechniqueQuantity}
                                        class="w-100%"
                                        clearable={!props.configData?.singleWeight}
                                        placeholder={
                                            props.configData?.singleWeight 
                                                ? "自动计算：质检数量 × 单重" 
                                                : "请输入工艺填报数量"
                                        }
                                    />
                                </n-form-item-gi>
                            )}
                            <n-form-item-gi span={12}>
                                <n-space>
                                    <n-button type="primary" onClick={onSubmit}>
                                        提交
                                    </n-button>
                                    <n-button onClick={() => changeModalShow(false)}>取消</n-button>
                                </n-space>
                            </n-form-item-gi>
                        </n-grid>
                    </n-form>
                </n-card>
            </n-modal>
        );
    }
});
