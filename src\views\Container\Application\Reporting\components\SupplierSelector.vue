<template>
    <div>
        <n-select
            v-if="type === 'select'"
            v-model:value="value"
            :options="options"
            clearable
            filterable
            multiple
            label-field="supplierName"
            value-field="id"
            placeholder="请选择供应商"
        />
        <div v-else-if="type === 'text'">{{ getSupplierText(value) ?? "未知" }}</div>
    </div>
</template>

<script lang="ts" setup>
import { computed } from "vue";

let props = withDefaults(
    defineProps<{
        type: "select" | "text";
        options: any[];
        value: Nullable<string>;
    }>(),
    {}
);

let emits = defineEmits(["update:value", "submit"]);

let value = computed({
    get: () => {
        return props.value ? props.value.split(",") : [];
    },
    set: (val: any[]) => {
        emits("update:value", val);
        emits("submit", val);
    }
});

let getSupplierText = (value: Nullable<string>): string => {
    let object = props.options.find((item) => item.value === value);
    return object?.label ?? "未知";
};
</script>
