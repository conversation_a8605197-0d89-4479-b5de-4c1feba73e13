<template>
    <div class="flex items-center p-4">
        <div class="flex items-center">
            <n-avatar :size="88" :src="avatarUrl" round object-fit="cover" />
            <div class="ml-4">
                <p class="m-0 pt-2">{{ nowTime }}，{{ storeUser.getUserData.sysUser?.trueName }}</p>
                <p class="m-0 pt-2">{{"今日佳句："+yiyanData||"今天又是充满活力的一天！"}}</p>
            </div>
        </div>
        <div class="flex items-center ml-a">
            <n-space size="large">
              <n-grid x-gap="10">
                <n-grid-item n-element v-for="item in shortcutList" :span="8"  @click="routerTo(item.path)">
                  <n-el>
                    <n-card class="text-center cursor-pointer quick-operation-block"
                            :bordered="false"
                            size="small"
                    >
                      <dynamic-icon :icon="item.icon" size="20" />
                      <span class="block mt-2">{{ item.title }}</span>
                    </n-card>
                  </n-el>
                </n-grid-item>
              </n-grid>
                <!--<n-statistic label="项目数">18</n-statistic>-->
                <!--<n-statistic label="待办">66</n-statistic>-->
                <!--<n-statistic label="消息">88</n-statistic>-->
            </n-space>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { NAvatar, NCard, NGrid, NGridItem, NSpace, NStatistic } from "naive-ui";
import { useStoreUser } from "@/store";
import { onMounted, ref } from "vue";
import { usePublic } from "@/hooks";
import { DynamicIcon } from "@/components/DynamicIcon";
import { load, Res } from 'jinrishici';


let { avatarUrl } = usePublic();

let nowTime = ref("");
let yiyanData = ref("");
let { $router } = usePublic();

let getNowTime = () => {
    let date = new Date();
    let hour = date.getHours();
    if (hour >= 0 && hour <= 10) {
        nowTime.value = `早上好`;
    } else if (hour > 10 && hour <= 12) {
        nowTime.value = `中午好`;
    } else if (hour > 12 && hour <= 18) {
        nowTime.value = `下午好`;
    } else if (hour > 18 && hour <= 24) {
        nowTime.value = `晚上好`;
    }
};

let getAWord = () =>{
  console.log('获取一言被触发')
  load((result: any) => {
    console.log(result.data.content);
    yiyanData.value = result.data.content;
  })
}

let shortcutList = [
  { title: "员工通讯录", icon: "UserOutlined", path: "/staff/list" },
  { title: "OA流程", icon: "DeploymentUnitOutlined", path: "/process/lib" },
  { title: "项目管理", icon: "FolderOpenOutlined", path: "/sale/project/list/launch" },
];

let routerTo = (path: string) => {
  if (!path) return false;
  $router.push(path);
};



onMounted(() => {
    getNowTime();
    getAWord();
});

let storeUser = useStoreUser();
</script>
<style lang="scss" scoped>
.quick-operation-block:hover{
  color:var(--primary-color);
}
</style>