import { defineComponent, reactive, ref, watchEffect } from "vue";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import { GET_IM_PRODUCTION_SCHEDULE_PAUSE_CONTINUE_RECORD_LIST } from "@/api/application/plasticMes";

export default defineComponent({
    name: "PlasticMesIMProductionSchedulePauseContinueRecord",
    props: {
        scheduleId: { type: String }
    },
    setup(props, { emit }) {
        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            {
                title: "操作类型",
                key: "recordType",
                align: "center",
                render: (row) => {
                    if (row.recordType === 2) {
                        return <n-text type="error">暂停排产</n-text>;
                    } else if (row.recordType === 3) {
                        return <n-text type="info">继续排产</n-text>;
                    } else {
                        return "/";
                    }
                }
            },
            { title: "操作时间", key: "createTime", align: "center" },
            { title: "操作人", key: "createByName", align: "center" }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_IM_PRODUCTION_SCHEDULE_PAUSE_CONTINUE_RECORD_LIST({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                scheduleId: props.scheduleId
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        watchEffect(async () => {
            if (props.scheduleId) getTableData();
        });

        return () => (
            <n-data-table
                columns={tableColumns.value}
                data={tableData.value}
                loading={tableLoading.value}
                pagination={tablePagination}
                row-key={tableRowKey}
                single-line={false}
                bordered
                remote
                striped
                max-height={250}
                onUpdate:checked-row-keys={changeTableSelection}
            />
        );
    }
});
