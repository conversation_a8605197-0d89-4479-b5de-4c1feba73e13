<template>
    <div>
        <n-tabs v-model:value="tabActive" animated type="bar" @before-leave="onTabBeforeLeave">
            <n-tab-pane :name="1" tab="产成品计划用量" />
            <n-tab-pane :name="2" tab="混凝土计划用量" />
        </n-tabs>
        <div class="mt-2">
            <ReportingFillMonthMainAmountProduct :planMonth="planMonth" v-if="tabActive === 1" />
            <ReportingFillMonthMainAmountConcrete :planMonth="planMonth" v-if="tabActive === 2" />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import ReportingFillMonthMainAmountProduct from "./ReportingFillMonthMainAmountProduct.vue";
import ReportingFillMonthMainAmountConcrete from "./ReportingFillMonthMainAmountConcrete.vue";
import { usePublic } from "@/hooks";

let { onTabBeforeLeave } = usePublic();

let props = withDefaults(
    defineProps<{
        planMonth: string;
    }>(),
    {}
);

let tabActive = ref(1);
</script>
