<template>
    <div>
        <n-drawer
            v-model:show="show"
            :close-on-esc="false"
            :height="600"
            :mask-closable="false"
            placement="bottom"
            @update:show="changeModalShow(false)"
        >
            <n-drawer-content closable :title="configData.id ? '编辑半成品' : '添加半成品'">
                <n-form
                    ref="formRef"
                    :model="formData"
                    :rules="formRules"
                    class="w-600px mx-a"
                    label-placement="left"
                    label-width="auto"
                >
                    <n-grid :cols="12" x-gap="16">
                        <n-form-item-gi :span="12" label="所属公司" path="companyId">
                            <n-cascader
                                :disabled="!!configData.id"
                                v-model:value="formData.companyId"
                                :options="companyIdOptions"
                                class="w-100%"
                                clearable
                                filterable
                                label-field="companyName"
                                placeholder="请选择所属公司"
                                value-field="id"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="半成品分类" path="categoryId">
                            <n-tree-select
                                v-model:value="formData.categoryId"
                                :options="categoryIdOptions"
                                children-field="childrenList"
                                class="w-100%"
                                clearable
                                default-expand-all
                                key-field="id"
                                label-field="categoryName"
                                placeholder="请选择半成品分类"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="半成品名称" path="poleName">
                            <n-input
                                v-model:value="formData.poleName"
                                class="w-100%"
                                clearable
                                placeholder="请输入半成品名称"
                            />
                        </n-form-item-gi>
                    </n-grid>
                </n-form>
                <BindTableData v-model:value="tableData" :showButtons="!configData.id" @confirm="onSubmit()" />
            </n-drawer-content>
        </n-drawer>
    </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref, watchEffect } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import {
    BATCH_SAVE_SEMI_MANUFACTURE_LIST,
    GET_CONFIG_COMPANY_LIST,
    GET_MATERIAL_CATEGORY_TREE_BY_GENRE
} from "@/api/application/reporting";
import BindTableData from "./BindTableData.vue";

let props = withDefaults(defineProps<{ show: boolean; configData: UnKnownObject }>(), { show: () => false });

let emits = defineEmits(["update:show", "refresh"]);

// 弹窗展示
let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

let changeModalShow = (show: boolean) => emits("update:show", show);

onMounted(async () => {
    await getCategoryIdOptions();
    await getCompanyIdOptions();
});

// 编辑
watchEffect(() => {
    if (props.configData.id) {
        formData.value = {
            companyId: props.configData.companyId,
            categoryId: props.configData.categoryId,
            poleName: props.configData.poleName
        };
        tableData.value = [{ bindMId: props.configData.bindMId, concreteAmount: props.configData.concreteAmount }];
    }
});

// 获取公司选项-填报专属修改2023年8月9日
let companyIdOptions = ref<any[]>([]);

let getCompanyIdOptions = async () => {
    await GET_CONFIG_COMPANY_LIST({ needFill: 1 }).then((res) => {
        companyIdOptions.value = res.data.data || [];
    });
};

// 获取分类
let categoryIdOptions = ref([]);

let getCategoryIdOptions = async () => {
    await GET_MATERIAL_CATEGORY_TREE_BY_GENRE({
        productGenre: 3
    }).then((res) => {
        if (res.data.code === 0) categoryIdOptions.value = res.data.data;
    });
};

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {
    companyId: { required: true, message: "请选择所属公司", trigger: ["blur", "change"] },
    categoryId: { required: true, message: "请选择半成品分类", trigger: ["blur", "change"] },
    poleName: { required: true, message: "请输入半成品名称", trigger: ["input", "blur"] }
};

// 表单数据
interface FormDataProps {
    [key: string]: any;
}

let initFormData: FormDataProps = {
    companyId: null,
    categoryId: null,
    poleName: ""
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

let tableData = ref<any[]>([]);

// 提交
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;

    let newTableData: any[] = tableData.value.map((item) => {
        return {
            ...item,
            ...formData.value
        };
    });

    BATCH_SAVE_SEMI_MANUFACTURE_LIST(newTableData).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("添加成功");
            changeModalShow(false);
            clearFrom();
            emits("refresh");
        } else {
            window.$message.error(res.data.message);
        }
    });
};
</script>
