<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-800px" closable title="确认回款" @close="closeModal">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-grid :cols="24" :x-gap="16">
                        <n-form-item-gi :span="12" label="所属订单" path="preReturnTime">
                            <div>{{ configData.projectOrderManagement.pomNumber }}</div>
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="预计到款时间" path="preReturnTime">
                            <n-date-picker
                                v-model:formatted-value="formData.preReturnTime"
                                class="w-100%"
                                clearable
                                placeholder="请选择预计到款时间"
                                type="datetime"
                                value-format="yyyy-MM-dd HH:mm:ss"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="预计到款金额" path="preReturnAmount">
                            <n-input-number v-model:value="formData.preReturnAmount" class="w-100%" clearable />
                        </n-form-item-gi>
                        <n-form-item-gi :span="24" label="款项类型" path="returnType">
                            <n-radio-group v-model:value="formData.returnType">
                                <n-space>
                                    <!--回款类型（1：预付款；2：首付款；3：进度款；4：尾款；5：履约保证金；6：投标保证金；7：质量保证金）-->
                                    <n-radio :value="1" label="预付款" />
                                    <n-radio :value="2" label="首付款" />
                                    <n-radio :value="3" label="进度款" />
                                    <n-radio :value="4" label="尾款" />
                                    <n-radio :value="5" label="履约保证金" />
                                    <n-radio :value="6" label="投标保证金" />
                                    <n-radio :value="7" label="质量保证金" />
                                </n-space>
                            </n-radio-group>
                        </n-form-item-gi>
                        <n-form-item-gi :span="24">
                            <n-space>
                                <n-button type="primary" @click="onSubmit">提交</n-button>
                                <n-button @click="closeModal">取消</n-button>
                            </n-space>
                        </n-form-item-gi>
                    </n-grid>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { POST_CHILD_PAYMENT_RETURN_APPLY, POST_PARENT_PAYMENT_RETURN_APPLY } from "@/api/application/power";

let props = defineProps({
    show: { type: Boolean, default: false },
    configData: { type: Object as PropType<any> }
});

let emits = defineEmits(["update:show", "refresh"]);

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = computed(() => {
    return {
        returnType: [{ required: true, message: "请选择款项类型", trigger: ["blur", "change"], type: "number" }],
        preReturnTime: [{ required: true, message: "请选择预计到款时间", trigger: ["blur", "change"] }],
        preReturnAmount: [{ required: true, message: "请输入预计到款金额", trigger: ["input", "blur"], type: "number" }]
    };
});

// 表单数据
interface FormDataProps {
    [key: string]: any;
}

let initFormData: FormDataProps = {
    returnType: 1,
    preReturnTime: null,
    preReturnAmount: 0
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    emits("update:show", false);
};

/*
 * 目前是子项目确认回款
 */

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    if (formData.value.returnType < 5) {
        POST_CHILD_PAYMENT_RETURN_APPLY({
            ...formData.value,
            projectId: props.configData.projectId,
            childProjectId: props.configData.childProjectId
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("操作成功");
                closeModal();
                emits("refresh");
            }
        });
    } else {
        POST_PARENT_PAYMENT_RETURN_APPLY({
            ...formData.value,
            projectId: props.configData.projectId
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("操作成功");
                closeModal();
                emits("refresh");
            }
        });
    }
};
</script>
