import { computed, defineComponent, ref, watchEffect } from "vue";
import { type FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { UPDATE_IRON_COMPONENT_FILL, GET_IRON_COMPONENT_FILL_DETAIL } from "@/api/application/TowerScan";
import UserSelector from "@/components/UserSelector/src/UserSelector.vue";

export default defineComponent({
    name: "TowerScanEngineeringProcessesComponentExecutionEdit",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => {
            emit("update:show", show);
            if (!show) {
                clearForm();
            }
        };

        // 表单数据
        interface FormDataProps {
            [key: string]: any;
        }

        const formRef = ref<FormInst | null>(null);

        // 表单校验规则
        const formRules = computed(() => ({
            fillQuantity: [
                { required: true, message: "请输入填报数", trigger: ["input", "blur"] },
                { pattern: /^\d+(\.\d+)?$/, message: "请输入有效的数量", trigger: ["input", "blur"] }
            ],
            belongUsers: [{ required: true, message: "请输入操作工", trigger: ["input", "blur"] }],
            processType: [{ required: true, message: "请选择工序类型", trigger: ["blur", "change"], type: "number" }]
        }));

        const initFormData: FormDataProps = {
            id: null,
            fillQuantity: "",
            belongUsers: "",
            processType: 5, // 默认组装工序
            // 以下字段从详情获取，不可编辑，原样返回
            componentId: "",
            fillTechniqueId: ""
        };

        const formData = ref(cloneDeep(initFormData));

        const clearForm = () => {
            formData.value = cloneDeep(initFormData);
        };

        // 工序类型选项
        const processTypeOptions = [
            { label: "组装", value: 5 },
            { label: "电焊", value: 6 }
        ];

        // 获取详情
        const getDetail = () => {
            GET_IRON_COMPONENT_FILL_DETAIL({ id: props.configData.id }).then((res) => {
                if (res.data.code === 0) {
                    const detail = res.data.data;
                    formData.value = {
                        id: detail.id,
                        fillQuantity: detail.fillQuantity?.toString() || "",
                        belongUsers: detail.belongUsers || "",
                        processType: detail.processType ?? 5,
                        // 以下字段原样返回
                        componentId: detail.componentId || "",
                        fillTechniqueId: detail.fillTechniqueId || ""
                    };
                }
            });
        };

        watchEffect(() => {
            if (show.value) {
                if (props.configData.id) {
                    getDetail();
                }
            }
        });

        const onClose = () => {
            changeModalShow(false);
            emit("refresh");
        };

        const onSubmit = async () => {
            const validateError = await formRef.value?.validate((errors) => !!errors);
            if (validateError) return false;

            await UPDATE_IRON_COMPONENT_FILL({
                ...formData.value
            }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("修改成功");
                    onClose();
                }
            });
        };

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card
                    title={`修改配方填报记录 - ${props.configData?.mainMaterialCode ?? "/"}`}
                    class="w-1200px"
                    closable
                    onClose={() => changeModalShow(false)}
                >
                    <n-form
                        ref={formRef}
                        model={formData.value}
                        rules={formRules.value}
                        label-placement="left"
                        label-width="auto"
                    >
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi span={6} label="填报数：" path="fillQuantity">
                                <n-input
                                    v-model:value={formData.value.fillQuantity}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入填报数"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="操作工：" path="belongUsers">
                                <UserSelector
                                    v-model:value={formData.value.belongUsers}
                                    class="w-100%"
                                    key-name="username"
                                    placeholder="请选择操作工"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="工序类型：" path="processType">
                                <n-select
                                    v-model:value={formData.value.processType}
                                    class="w-100%"
                                    options={processTypeOptions}
                                    placeholder="请选择工序类型"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={12}>
                                <n-space>
                                    <n-button type="primary" onClick={onSubmit}>
                                        提交
                                    </n-button>
                                    <n-button onClick={() => changeModalShow(false)}>取消</n-button>
                                </n-space>
                            </n-form-item-gi>
                        </n-grid>
                    </n-form>
                </n-card>
            </n-modal>
        );
    }
});
