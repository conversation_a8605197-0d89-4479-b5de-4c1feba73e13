import { computed, defineComponent, ref, watchEffect } from "vue";
import { type FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { useStoreUser } from "@/store";
import {
    GET_PRODUCTION_MOLDING_TYPE_BY_ID,
    POST_PRODUCTION_MOLDING_TYPE,
    PUT_PRODUCTION_MOLDING_TYPE
} from "@/api/application/plasticMes";
import { useDicts } from "@/hooks";

export default defineComponent({
    name: "PlasticMesProductionInjectionMoldingEdit",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        // 字典操作
        const { dictLibs, getDictLibs } = useDicts();

        const setDictLibs = async () => {
            const dictName = ["common_units", "plastic_mould_variety"];
            await getDictLibs(dictName);
        };

        // 获取详情
        const getDetail = () => {
            GET_PRODUCTION_MOLDING_TYPE_BY_ID({ id: props.configData.id }).then((res) => {
                if (res.data.code === 0) {
                    formData.value = {
                        moldingName: res.data.data.moldingName,
                        moldingSpec: res.data.data.moldingSpec,
                        moldingVariety: String(res.data.data.moldingVariety),
                        singleWeight: res.data.data.singleWeight,
                        productUnit: res.data.data.productUnit,
                        weightUnit: res.data.data.weightUnit,
                        remark: res.data.data.remark
                    };
                }
            });
        };

        // 表单数据
        interface FormDataProps {
            [key: string]: any;
        }

        const formRef = ref<FormInst | null>(null);

        // 获取表单选项
        const weightUnitOptions = ref<any[]>([]);
        const productUnitOptions = ref<any[]>([]);

        const getFormOptions = async () => {
            weightUnitOptions.value = dictLibs["common_units"];
            productUnitOptions.value = dictLibs["common_units"];
        };

        const initFormData: FormDataProps = {
            moldingName: null,
            moldingSpec: null,
            moldingVariety: null,
            singleWeight: null,
            weightUnit: null,
            productUnit: null,
            remark: null
        };

        const formRules = computed(() => ({
            moldingName: [{ required: true, message: "请输入注塑产品名称", trigger: ["input", "blur"] }],
            moldingSpec: [{ required: true, message: "请输入注塑产品规格", trigger: ["input", "blur"] }],
            moldingVariety: [{ required: true, message: "请选择注塑产品分类", trigger: ["blur", "change"] }],
            singleWeight: [{ required: true, message: "请输入单重", trigger: ["input", "blur"] }],
            weightUnit: [{ required: true, message: "请选择重量单位", trigger: ["blur", "change"] }],
            productUnit: [{ required: true, message: "请选择产品单位", trigger: ["blur", "change"] }],
            remark: [{ required: false, message: "请输入备注", trigger: ["input", "blur"] }]
        }));

        const formData = ref(cloneDeep(initFormData));

        const clearForm = () => {
            formData.value = cloneDeep(initFormData);
        };

        const onClose = () => {
            clearForm();
            changeModalShow(false);
            emit("refresh");
        };

        const onSubmit = async () => {
            const validateError = await formRef.value?.validate((errors) => !!errors);
            if (validateError) return false;

            if (props.configData.id) {
                PUT_PRODUCTION_MOLDING_TYPE({
                    id: props.configData.id,
                    ...formData.value
                }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("新增成功");
                        onClose();
                    } else window.$message.error(res.data.msg);
                });
            } else {
                POST_PRODUCTION_MOLDING_TYPE({
                    ...formData.value
                }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("新增成功");
                        onClose();
                    } else window.$message.error(res.data.msg);
                });
            }
        };

        watchEffect(async () => {
            if (show.value) {
                await setDictLibs();
                await getFormOptions();
                if (props.configData.id) getDetail();
            }
        });

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card
                    title={props.configData.id ? "编辑注塑产品" : "新增注塑产品"}
                    class="w-800px"
                    closable
                    onClose={onClose}
                >
                    <n-form
                        ref={formRef}
                        model={formData.value}
                        rules={formRules.value}
                        label-placement="left"
                        label-width="auto"
                    >
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi span={6} label="注塑产品名称" path="moldingName">
                                <n-input
                                    v-model:value={formData.value.moldingName}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入注塑产品名称"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="注塑产品规格" path="moldingSpec">
                                <n-input
                                    v-model:value={formData.value.moldingSpec}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入注塑产品规格"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="注塑产品分类" path="moldingVariety">
                                <n-select
                                    class="w-100%"
                                    v-model:value={formData.value.moldingVariety}
                                    options={dictLibs["plastic_mould_variety"] ?? []}
                                    clearable
                                    filterable
                                    placeholder="请选择注塑产品分类"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="单重" path="singleWeight">
                                <n-input
                                    v-model:value={formData.value.singleWeight}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入单重"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="重量单位" path="weightUnit">
                                <n-select
                                    options={weightUnitOptions.value}
                                    v-model:value={formData.value.weightUnit}
                                    class="w-100%"
                                    clearable
                                    placeholder="请选择重量单位"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="数量单位" path="productUnit">
                                <n-select
                                    options={productUnitOptions.value}
                                    v-model:value={formData.value.productUnit}
                                    class="w-100%"
                                    clearable
                                    placeholder="请选择数量单位"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="备注" path="remark">
                                <n-input
                                    v-model:value={formData.value.remark}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入备注"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={12}>
                                <n-space>
                                    <n-button type="primary" onClick={onSubmit}>
                                        提交
                                    </n-button>
                                    <n-button onClick={onClose}>取消</n-button>
                                </n-space>
                            </n-form-item-gi>
                        </n-grid>
                    </n-form>
                </n-card>
            </n-modal>
        );
    }
});
