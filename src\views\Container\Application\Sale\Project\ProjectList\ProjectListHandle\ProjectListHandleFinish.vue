<template>
    <div class="project-list-launch">
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, ref } from "vue";
import type { DataTableColumns, PaginationProps } from "naive-ui";
import { NText } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable, useDicts } from "@/hooks";
import { GET_PROJECT_LIST_HANDLE } from "@/api/application/sale";

interface RowProps {
    [key: string]: any;
}

onMounted(async () => {
    await setDictLibs();
    getSearchOptions();
    getTableData();
});

let { dictLibs, getDictLibs, dictValueToLabel, dictValueToAll } = useDicts();

let setDictLibs = async () => {
    let dictName = ["node_status", "win_bind_status", "project_type", "project_status"];
    await getDictLibs(dictName);
};

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { prop: "projectType", type: "select", label: "项目类型" },
    { prop: "projectStatus", type: "select", label: "项目状态" },
    { prop: "projectName", type: "input", label: "关键词" }
]);

let searchOptions = ref<TableSearchbarOptions>({
    projectType: [],
    projectStatus: []
});

let searchForm = ref<TableSearchbarData>({
    projectType: null,
    projectStatus: null,
    projectName: null
});

let getSearchOptions = () => {
    searchOptions.value.projectType = dictLibs.project_type;
    searchOptions.value.projectStatus = dictLibs.project_status;
};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, changeTableSelection } =
    useCommonTable<RowProps>("projectId");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "项目编号",
        key: "projectNumber",
        align: "center"
    },
    {
        title: "项目名称",
        key: "projectName",
        align: "center",
        render: (row: RowProps) => {
            return h(NText, { type: "primary" }, () => row.projectName);
        }
    },
    {
        title: "招投标项目编号",
        key: "projectCode",
        align: "center",
        render: (row: RowProps) => {
            return row.projectCode || "暂无";
        }
    },
    {
        title: "订单编号",
        key: "contractNumber",
        align: "center",
        render: (row: RowProps) => {
            return row.contractNumber || "暂无";
        }
    },
    {
        title: "处理节点",
        key: "nodeName",
        align: "center"
    },
    {
        title: "处理结果",
        key: "UNKNOWN",
        align: "center",
        render: () => {
            return h(NText, { type: "success" }, () => "已处理");
        }
    },
    {
        title: "前置负责人",
        key: "nodeDirectorName",
        align: "center",
        render: (row: RowProps) => {
            return h(NText, { type: "primary" }, () => row.projectLeaderName || "暂无");
        }
    },
    {
        title: "是否超时",
        key: "timeOutFlag",
        align: "center",
        render: (row: RowProps) => {
            if (row.timeOutFlag) {
                return h(NText, { type: "error" }, () => "已超时");
            } else {
                return h(NText, { type: "success" }, () => "未超时");
            }
        }
    }
]);

let tablePagination = ref<PaginationProps>({
    page: 1,
    pageSize: 10,
    itemCount: 0,
    pageSizes: [10, 50, 100],
    showSizePicker: true,
    showQuickJumper: true,
    displayOrder: ["size-picker", "pages", "quick-jumper"],
    onChange: (page: number) => {
        tablePagination.value.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.value.pageSize = pageSize;
        tablePagination.value.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_PROJECT_LIST_HANDLE({
        current: tablePagination.value.page,
        size: tablePagination.value.pageSize,
        ...searchForm.value
    }).then((res) => {
        tableData.value = res.data.data.records || [];
        tablePagination.value.itemCount = res.data.data.total;
        tableLoading.value = false;
    });
};

let onSearch = () => {
    getTableData();
};
</script>
