import { computed, defineComponent, ref, watchEffect } from "vue";
import { type FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { useDicts } from "@/hooks";
import { CompanyUserSelector } from "@/components/UserSelector";
import { ADD_ATTENDANCE_WHITE_LIST } from "@/api/permission";

export default defineComponent({
    name: "AttendanceRuleWhiteListEdit",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "update:configData", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        // 表单数据
        interface FormDataProps {
            [key: string]: any;
        }

        // 字典操作
        const { dictLibs, getDictLibs } = useDicts();

        const setDictLibs = async () => {
            const dictName = ["common_units"];
            await getDictLibs(dictName);
        };

        // 表单实例
        const formRef = ref<FormInst | null>(null);

        const getFormOptions = async () => {};

        const initFormData: FormDataProps = {
            userIds: null
        };

        const formRules = computed(() => ({
            userIds: [{ required: true, message: "请选择操作人", trigger: ["blur", "change"] }]
        }));

        const formData = ref(cloneDeep(initFormData));

        const clearForm = () => {
            formData.value = cloneDeep(initFormData);
        };

        // 关闭
        const onClose = () => {
            clearForm();
            changeModalShow(false);
            emit("refresh");
        };

        // 提交
        const onSubmit = async () => {
            const validateError = await formRef.value?.validate((errors) => !!errors);
            if (validateError) return false;

            const userList = (formData.value.userIds.split(",") ?? []).map((item: any) => {
                return item;
            });

            ADD_ATTENDANCE_WHITE_LIST(userList).then(async (res) => {
                if (res.data.code === 0) {
                    window.$message.success("操作成功");
                    onClose();
                }
            });
        };

        watchEffect(async () => {
            if (show.value) {
            }
        });

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card title="新增考勤白名单" class={["w-600px"]} closable onClose={onClose}>
                    <n-form ref={formRef} model={formData.value} rules={formRules.value} label-placement="left">
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi span={12} label="白名单新增人员" path="userIds">
                                <CompanyUserSelector
                                    v-model:value={formData.value.userIds}
                                    class="w-100%"
                                    multiple
                                    key-name="userId"
                                    placeholder="请选择白名单新增人员"
                                    companyId={props.configData.corpId}
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={12}>
                                <n-space>
                                    <n-button onClick={() => onClose()}>取消操作</n-button>
                                    <n-button type="primary" onClick={() => onSubmit()}>
                                        提交保存
                                    </n-button>
                                </n-space>
                            </n-form-item-gi>
                        </n-grid>
                    </n-form>
                </n-card>
            </n-modal>
        );
    }
});
