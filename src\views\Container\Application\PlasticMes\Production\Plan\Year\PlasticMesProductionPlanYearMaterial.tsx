import { computed, defineComponent, onMounted, reactive, ref, h } from "vue";
import dayjs from "dayjs";
import type { DataTableColumns, FormInst } from "naive-ui";
import { useCommonTable } from "@/hooks";
import { TableActions } from "@/components/TableActions";
import {
    GET_GOODS_YEAR_PLAN_PAGE_LIST,
    POST_GOODS_YEAR_PLAN,
    PUT_GOODS_YEAR_PLAN,
    DELETE_GOODS_YEAR_PLAN
} from "@/api/application/plasticMes";
import { GET_REPOSITORY_GOODS_LIST } from "@/api/application/repository";

export default defineComponent({
    name: "PlasticMesProductionPlanYearMaterial",
    setup() {
        // 搜索表单
        const searchFormRef = ref<FormInst | null>(null);
        const searchFormData = ref<UnKnownObject>({
            planYear: null
        });
        const planYearOptions = ref<UnKnownObject[]>([]);

        const getSearchFormOptions = async () => {
            const options = [];
            for (let i = 0; i < 10; i++) {
                options.push({
                    label: dayjs().add(i, "year").year(),
                    value: dayjs().add(i, "year").year()
                });
            }
            planYearOptions.value = options;
        };

        const searchFormRules = computed(() => ({
            planYear: [
                {
                    required: true,
                    message: "请选择计划年度",
                    trigger: ["blur", "change"],
                    type: "number"
                }
            ]
        }));

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
            useCommonTable<RowProps>("id");

        // 新增空行数据
        const handleAdd = () => {
            const tempId = -(tableData.value.length + 1);
            tableData.value.unshift({
                id: tempId,
                goodsId: null,
                goodsKind: tabActive.value === "1" ? 1 : 2,
                planUnitPrice: "",
                planUsageTonnage: "",
                planYear: searchFormData.value.planYear
            });
        };

        // 保存行数据
        const handleSave = async (row: RowProps) => {
            // 先校验搜索表单
            const searchValidateError = await searchFormRef.value?.validate((errors) => !!errors);
            if (searchValidateError) return false;

            // 再校验行数据
            if (!row.goodsId) return window.$message.warning("请选择物资");
            if (!row.planUnitPrice) return window.$message.warning("请输入预计单价成本");
            if (!row.planUsageTonnage) return window.$message.warning("请输入预计使用吨数");

            const params = {
                id: row.id > 0 ? row.id : undefined,
                goodsId: row.goodsId,
                goodsKind: row.goodsKind,
                planUnitPrice: row.planUnitPrice,
                planUsageTonnage: row.planUsageTonnage,
                planYear: searchFormData.value.planYear
            };

            try {
                let res;
                if (row.id > 0) {
                    res = await PUT_GOODS_YEAR_PLAN(params);
                } else {
                    res = await POST_GOODS_YEAR_PLAN(params);
                }
                if (res.data.code === 0) {
                    window.$message.success("保存成功");
                    getTableData();
                } else {
                    window.$message.error(res.data.message || "保存失败");
                }
            } catch (error) {
                window.$message.error("保存失败");
            }
        };

        // 删除行
        const handleDelete = async (row: RowProps) => {
            window.$dialog.warning({
                title: "确认删除",
                content: "确定要删除该条数据吗？",
                positiveText: "确定",
                negativeText: "取消",
                onPositiveClick: async () => {
                    if (row.id < 0) {
                        tableData.value = tableData.value.filter((item) => item !== row);
                        return false;
                    }
                    try {
                        const res = await DELETE_GOODS_YEAR_PLAN({
                            ids: String(row.id)
                        });
                        if (res.data.code === 0) {
                            window.$message.success("删除成功");
                            getTableData();
                        } else {
                            window.$message.error(res.data.message || "删除失败");
                        }
                    } catch (error) {
                        window.$message.error("删除失败");
                    }
                }
            });
        };

        const goodsIdOptions = ref<any[]>([]);

        const getTableOptions = async () => {
            GET_REPOSITORY_GOODS_LIST({
                current: 1,
                size: 9999
            }).then((res) => {
                goodsIdOptions.value = res.data.data.records ?? [];
            });
        };

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            {
                title: "物资",
                key: "goodsId",
                align: "center",
                render: (row: RowProps) => {
                    return (
                        <n-select
                            options={goodsIdOptions.value}
                            clearable
                            filterable
                            placeholder="请选择物资"
                            label-field="goodsName"
                            value-field="id"
                            value={row.goodsId}
                            render-option={({ node, option }: any) => (
                                <n-tooltip>
                                    {{
                                        trigger: () => node,
                                        default: () => option.goodsName
                                    }}
                                </n-tooltip>
                            )}
                            onUpdate:value={(v: any, o: any) => {
                                row.goodsId = v;
                                row.unit = o?.unit;
                                row.goodsSpec = o?.goodsSpec;
                            }}
                        />
                    );
                }
            },
            {
                title: "预计单价成本（元）",
                key: "planUnitPrice",
                align: "center",
                width: 200,
                render: (row: RowProps) => {
                    return <n-input v-model:value={row.planUnitPrice} clearable />;
                }
            },
            {
                title: "预计使用吨数（吨）",
                key: "planUsageTonnage",
                align: "center",
                width: 200,
                render: (row: RowProps) => {
                    return <n-input v-model:value={row.planUsageTonnage} clearable />;
                }
            },
            {
                title: "操作",
                key: "actions",
                align: "center",
                width: 160,
                render: (row) => (
                    <TableActions
                        type="button"
                        buttonActions={[
                            {
                                label: "保存",
                                tertiary: true,
                                onClick: () => handleSave(row)
                            },
                            {
                                label: "删除",
                                tertiary: true,
                                type: "error",
                                onClick: () => handleDelete(row)
                            }
                        ]}
                    />
                )
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        // 左侧菜单切换
        const tabActive = ref<string>("1");
        const tabOptions = ref<any[]>([
            { label: "主要原材料", key: "1" },
            { label: "辅料", key: "2" }
        ]);

        const getTableData = () => {
            tableLoading.value = true;
            GET_GOODS_YEAR_PLAN_PAGE_LIST({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                planYear: searchFormData.value.planYear,
                goodsKind: tabActive.value
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data?.records ?? [];
                    tablePagination.itemCount = res.data.data?.total;
                } else {
                    window.$message.error(res.data.message || "获取数据失败");
                }
                tableLoading.value = false;
            });
        };

        const onSearch = async () => {
            const validateError = await searchFormRef.value?.validate((errors) => !!errors);
            if (validateError) return false;
            getTableData();
        };

        const changeTabActive = async (key: string) => {
            tabActive.value = key;
            await onSearch();
        };

        // 添加批量删除方法
        const handleBatchDelete = async () => {
            if (!tableSelection.value.length) {
                return window.$message.warning("请选择要删除的数据");
            }

            window.$dialog.warning({
                title: "确认删除",
                content: "确定要删除选中的数据吗？",
                positiveText: "确定",
                negativeText: "取消",
                onPositiveClick: async () => {
                    try {
                        const res = await DELETE_GOODS_YEAR_PLAN({
                            ids: tableSelection.value.join(",")
                        });
                        if (res.data.code === 0) {
                            window.$message.success("删除成功");
                            getTableData();
                        } else {
                            window.$message.error(res.data.message || "删除失败");
                        }
                    } catch (error) {
                        window.$message.error("删除失败");
                    }
                }
            });
        };

        onMounted(async () => {
            await getSearchFormOptions();
            await getTableOptions();
        });

        return () => (
            <div class="plastic-mes-production-plan-year mt-2">
                <n-form
                    inline
                    ref={searchFormRef}
                    model={searchFormData.value}
                    rules={searchFormRules.value}
                    label-placement="left"
                    label-width="auto"
                >
                    <n-form-item label="计划年度" path="planYear">
                        <n-select
                            class="w-300px"
                            v-model:value={searchFormData.value.planYear}
                            options={planYearOptions.value}
                            clearable
                            filterable
                            placeholder="请选择计划年度"
                        />
                    </n-form-item>
                    <n-form-item>
                        <n-button type="primary" onClick={() => onSearch()}>
                            查询
                        </n-button>
                    </n-form-item>
                </n-form>
                <div class="flex mt">
                    <n-menu
                        v-model:value={tabActive.value}
                        indent={20}
                        options={tabOptions.value}
                        class={"common-left-menu flex-fixed-180 border-r-1px border-[#E5E5E5]"}
                        mode="vertical"
                        onUpdate:value={changeTabActive}
                    />
                    <div class="flex-1 ml">
                        <n-space>
                            <n-button type="success" onClick={handleAdd}>
                                新增配置项
                            </n-button>
                            <n-button type="error" onClick={handleBatchDelete}>
                                批量删除
                            </n-button>
                        </n-space>
                        <div class="mt">
                            <n-data-table
                                columns={tableColumns.value}
                                data={tableData.value}
                                loading={tableLoading.value}
                                pagination={tablePagination}
                                row-key={tableRowKey}
                                single-line={false}
                                bordered
                                remote
                                striped
                                onUpdate:checked-row-keys={changeTableSelection}
                            />
                        </div>
                    </div>
                </div>
            </div>
        );
    }
});
