<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <!--项目详情弹窗-->
        <project-detail-modal v-model:id="detailModal.id" v-model:show="detailModal.show" @refresh="getTableData" />
        <!--流程弹窗-->
        <ProcessDetail v-model:show="processDetailModal.show" :config-data="processDetailModal.configData" />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, ref } from "vue";
import type { DataTableColumns, PaginationProps } from "naive-ui";
import { NText } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { TableActions } from "@/components/TableActions";
import { useCommonTable, useDicts } from "@/hooks";
import { GET_HANDLE_PROJECT_LIST } from "@/api/application/project";
import { useStoreUser } from "@/store";
import ProjectDetailModal from "../ProjectDetailModal.vue";
import { ProcessDetail } from "@/views/Container/Application/Process/components";

let storeUser = useStoreUser();

interface RowProps<T = string | null> {
    projectId: T | number;
    projectName: T;
    nodeStatus: T | number;
    nodeName: T;
    nodeDirector: T;
    nodeDirectorName: T;
    nextNodeDirector: T;
    nextNodeName: T;
    projectCode: T;
    projectType: T | number;
    projectStatus: T | number;
    winBidStatus: T | number;
    nextNodeKey: T | number;
    customerId: T | number;
    timeOutFlag: boolean;
    childProjectInfoVoList: any[];
    processInstanceId: T;
}

onMounted(async () => {
    await setDictLibs();
    getSearchOptions();
    getTableData();
});

let { dictLibs, getDictLibs, dictValueToLabel } = useDicts();

let setDictLibs = async () => {
    let dictName = ["node_status", "win_bind_status", "project_type", "project_status"];
    await getDictLibs(dictName);
};

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    {
        prop: "projectType",
        type: "select",
        label: "项目类型"
    },
    {
        prop: "projectStatus",
        type: "select",
        label: "项目状态"
    },
    {
        prop: "projectName",
        type: "input",
        label: "关键词"
    }
]);

let searchOptions = ref<TableSearchbarOptions>({
    projectType: [],
    projectStatus: []
});

let searchForm = ref<TableSearchbarData>({
    projectType: null,
    projectStatus: null,
    projectName: null
});

let getSearchOptions = () => {
    searchOptions.value.projectType = dictLibs.project_type;
    searchOptions.value.projectStatus = dictLibs.project_status;
};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, changeTableSelection } =
    useCommonTable<RowProps>("projectId");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        title: "项目编号",
        key: "projectId",
        align: "center"
    },
    {
        title: "项目名称",
        key: "projectName",
        align: "center",
        render: (row: RowProps) => {
            return h(NText, { type: "primary" }, () => row.projectName || "暂无");
        }
    },
    {
        title: "招投标项目编号",
        key: "projectCode",
        align: "center",
        render: (row: RowProps) => {
            return row.projectCode || "暂无";
        }
    },
    {
        title: "待处理节点",
        key: "nextNodeName",
        align: "center",
        render: (row: RowProps) => {
            return row.nextNodeName || "暂无";
        }
    },
    {
        title: "处理结果",
        key: "nodeStatus",
        align: "center",
        render: (row: RowProps) => {
            return dictValueToLabel(row.nodeStatus, "node_status") || "未知";
        }
    },
    {
        title: "前置负责人",
        key: "nodeDirectorName",
        align: "center",
        render: (row: RowProps) => {
            return h(NText, { type: "primary" }, () => row.nodeDirectorName || "暂无");
        }
    },
    {
        title: "是否超时",
        key: "timeOutFlag",
        align: "center",
        render: (row: RowProps) => {
            if (row.timeOutFlag) {
                return h(NText, { type: "error" }, () => "已超时");
            } else {
                return h(NText, { type: "success" }, () => "未超时");
            }
        }
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: 200,
        render(row: RowProps) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "查看",
                        tertiary: true,
                        type: "primary",
                        onClick: () => {
                            openDetailModal(row.projectId);
                        }
                    },
                    {
                        label: "查看处理结果",
                        tertiary: true,
                        type: "success",
                        disabled: () => !row.processInstanceId,
                        onClick: () => {
                            openProcessDetailModal(row);
                        }
                    }
                ]
            });
        }
    }
]);

let tablePagination = ref<PaginationProps>({
    page: 1,
    pageSize: 10,
    itemCount: 0,
    pageSizes: [10, 50, 100],
    showSizePicker: true,
    showQuickJumper: true,
    displayOrder: ["size-picker", "pages", "quick-jumper"],
    onChange: (page: number) => {
        tablePagination.value.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.value.pageSize = pageSize;
        tablePagination.value.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_HANDLE_PROJECT_LIST({
        current: tablePagination.value.page,
        size: tablePagination.value.pageSize,
        ...searchForm.value
    }).then((res) => {
        tableData.value = res.data.data.records || [];
        tablePagination.value.itemCount = res.data.data.total;
        tableLoading.value = false;
    });
};

let onSearch = () => {
    getTableData();
};

// 查看详情弹窗
let detailModal = ref<{ show: boolean; id: string | number | null }>({
    show: false,
    id: null
});

let openDetailModal = (id?: string | number | null) => {
    detailModal.value.show = true;
    detailModal.value.id = id || null;
};

// 单据详情
let processDetailModal = ref<any>({
    show: false,
    configData: {}
});

let openProcessDetailModal = (row: any) => {
    processDetailModal.value.show = true;
    processDetailModal.value.configData = row;
};
</script>
