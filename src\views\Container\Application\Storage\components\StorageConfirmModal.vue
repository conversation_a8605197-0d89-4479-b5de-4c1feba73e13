<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-666px" closable title="确认单据" @close="changeModalShow(false)">
                <n-table :single-line="false" class="mt text-center">
                    <thead>
                        <tr>
                            <th>规格型号</th>
                            <th>库存数量</th>
                            <th>原材料是否满足</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="item in configData.modelFormulaList">
                            <td>{{ item.specification }}</td>
                            <td>
                                <n-input-number v-model:value="item.stockCount" />
                            </td>
                            <td>
                                <n-radio-group v-model:value="item.materialIsEnough">
                                    <n-radio :value="0">否</n-radio>
                                    <n-radio :value="1">是</n-radio>
                                </n-radio-group>
                            </td>
                        </tr>
                    </tbody>
                </n-table>
                <n-space class="mt">
                    <n-button type="primary" @click="onSubmit">提交</n-button>
                    <n-button @click="changeModalShow(false)">取消</n-button>
                </n-space>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref, watchEffect } from "vue";
import { STOCK_CHECK_CONFIRM_CHECK } from "@/api/application/production";

let props = withDefaults(defineProps<{ show: boolean; configData: any }>(), {
    show: () => false
});

let emits = defineEmits(["update:show", "refresh"]);

let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

let changeModalShow = (show: boolean) => emits("update:show", show);

let modelFormulaList = ref<any[]>([]);

watchEffect(() => {
    modelFormulaList.value = (props.configData.modelFormulaList || []) as any[];
});

let onSubmit = () => {
    let list = modelFormulaList.value.map((item) => {
        return {
            id: item.potId,
            stockCount: item.stockCount,
            materialIsEnough: item.materialIsEnough
        };
    });
    STOCK_CHECK_CONFIRM_CHECK({ poId: props.configData?.poId, list }).then((res) => {
        if (res.data.code === 0) {
            changeModalShow(false);
            emits("refresh");
        }
    });
};
</script>
