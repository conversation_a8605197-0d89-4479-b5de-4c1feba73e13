import { computed, defineComponent, reactive, ref, watchEffect } from "vue";
import {
    EXPORT_OPERATION_TABLE_EXPORT,
    GET_IRON_PRODUCTION_PLAN_DETAIL,
    GET_IRON_PRODUCTION_PLAN_LINE_PAGE_LIST,
    DOWNLOAD_IRON_TOWER
} from "@/api/application/TowerScan";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import { TableActions } from "@/components/TableActions";
import TowerScanProductionMaterialDetail from "./TowerScanProductionMaterialDetail";

export default defineComponent({
    name: "TowerScanProductionDetail",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        // 获取详情
        interface DetailDataProps {
            [key: string]: any;
        }

        const detailData = ref<DetailDataProps>({});

        const getDetail = () => {
            GET_IRON_PRODUCTION_PLAN_DETAIL({ id: props.configData.id }).then((res) => {
                if (res.data.code === 0) {
                    detailData.value = res.data.data;
                }
            });
        };

        const onClose = () => {
            changeModalShow(false);
            emit("refresh");
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        // 下料明细弹窗
        const materialDetailShow = ref(false);
        const materialDetailConfig = ref({});

        const openMaterialDetail = (row: any) => {
            materialDetailConfig.value = row;
            materialDetailShow.value = true;
        };

        // 导出下载
        const exportDownload = async (row: any) => {
            let exportRes: any;
            try {
                window.$message.loading("正在导出下载...", { duration: 0 });

                exportRes = await EXPORT_OPERATION_TABLE_EXPORT({
                    planId: props.configData.id,
                    processType: "2",
                    status: 1,
                    planLineId: row.id
                });

                if (exportRes.data.code === 0) {
                    // 导出成功后下载文件
                    const downloadRes = await DOWNLOAD_IRON_TOWER({
                        fileName: exportRes.data.data.fileName || `下料表_${row.lineName}.xlsx`
                    });

                    // 创建下载链接
                    const blob = new Blob([downloadRes.data], {
                        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    });
                    const url = window.URL.createObjectURL(blob);
                    const link = document.createElement("a");
                    link.href = url;
                    link.download = exportRes.data.data.fileName || `下料表_${row.lineName}.xlsx`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    window.URL.revokeObjectURL(url);

                    window.$message.destroyAll();
                    window.$message.success("导出成功");
                } else {
                    window.$message.destroyAll();
                    window.$message.error(exportRes.data.msg || "导出失败");
                }
            } catch (error) {
                window.$message.destroyAll();
                window.$message.error(exportRes.data.msg || "导出失败");
            }
        };

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            { title: "下料产线名称", key: "lineName", align: "center" },
            { title: "负责人", key: "director", align: "center" },
            {
                title: "下料完成情况",
                // key: "lineStatus",
                key: "deliverLineStatus",
                align: "center",
                render: (row) => {
                    switch (row.deliverLineStatus) {
                        case 1:
                            return <n-text type="error">未开始</n-text>;
                        case 2:
                            return <n-text type="info">进行中</n-text>;
                        case 3:
                            return <n-text type="success">已完成</n-text>;
                        default:
                            return <n-text>/</n-text>;
                    }
                }
            },
            {
                title: "计划总重",
                key: "totalPlanWeight",
                align: "center",
                render: (row) => {
                    return <n-text type="info">{row.totalPlanWeight ?? "0"}KG</n-text>;
                }
            },
            {
                title: "已完成总重",
                key: "totalFinishWeight",
                align: "center",
                render: (row) => {
                    return <n-text type="info">{row.totalFinishWeight ?? "0"}KG</n-text>;
                }
            },
            {
                title: "下料已完成总重",
                key: "totalDeliverFinishWeight",
                align: "center",
                render: (row) => {
                    return <n-text type="info">{row.totalDeliverFinishWeight ?? "0"}KG</n-text>;
                }
            },
            {
                title: "查看下料执行情况",
                key: "action",
                align: "center",
                width: 250,
                render: (row) => {
                    return (
                        <TableActions
                            type="button"
                            buttonActions={[
                                {
                                    label: "查看下料明细",
                                    tertiary: true,
                                    type: "primary",
                                    onClick: () => openMaterialDetail(row)
                                },
                                {
                                    label: "导出下载",
                                    tertiary: true,
                                    type: "success",
                                    onClick: () => exportDownload(row)
                                }
                            ]}
                        />
                    );
                }
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_IRON_PRODUCTION_PLAN_LINE_PAGE_LIST({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                planId: props.configData.id
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        watchEffect(async () => {
            if (show.value) {
                if (props.configData.id) {
                    getDetail();
                    getTableData();
                }
            }
        });

        return () => (
            <div>
                <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                    <n-card title={props.configData.planName} class="w-1200px" closable onClose={onClose}>
                        <n-form label-placement="left" label-width="auto">
                            <n-grid cols={12} x-gap={16}>
                                <n-form-item-gi required span={4} label="计划名称">
                                    {detailData.value?.planName ?? "/"}
                                </n-form-item-gi>
                                <n-form-item-gi required span={4} label="操作人">
                                    {detailData.value?.operByName ?? "/"}
                                </n-form-item-gi>
                                <n-form-item-gi required span={4} label="操作时间">
                                    {detailData.value?.operTime ?? "/"}
                                </n-form-item-gi>
                                <n-form-item-gi required span={4} label="生产计划状态">
                                    {detailData.value?.planStatus === 1 ? (
                                        <n-text type="error">未开始</n-text>
                                    ) : detailData.value?.planStatus === 2 ? (
                                        <n-text type="info">进行中</n-text>
                                    ) : detailData.value?.planStatus === 3 ? (
                                        <n-text type="success">已完成</n-text>
                                    ) : detailData.value?.planStatus === 4 ? (
                                        <n-text type="warning">已结束</n-text>
                                    ) : (
                                        <n-text>/</n-text>
                                    )}
                                </n-form-item-gi>
                                <n-form-item-gi required span={8} label="原始计划文件">
                                    <n-button
                                        text
                                        type="primary"
                                        onClick={() => {
                                            window.open(
                                                import.meta.env.VITE_API_URL + detailData.value?.originalFile?.url
                                            );
                                        }}
                                    >
                                        {detailData.value?.originalFile?.fileName ?? "/"}
                                    </n-button>
                                </n-form-item-gi>
                                <n-form-item-gi required span={4} label="计划总重">
                                    <n-text type="info">{detailData.value?.totalPlannedWeight ?? "0"}KG</n-text>
                                </n-form-item-gi>
                                <n-form-item-gi required span={4} label="已完成总重">
                                    <n-text type="info">{detailData.value?.completedWeight ?? "0"}KG</n-text>
                                </n-form-item-gi>
                                <n-form-item-gi required span={4} label="下料已完成总重">
                                    <n-text type="info">{detailData.value?.totalDeliverFinishWeight ?? "0"}KG</n-text>
                                </n-form-item-gi>
                            </n-grid>
                        </n-form>
                        <n-card class="mt">
                            <n-data-table
                                columns={tableColumns.value}
                                data={tableData.value}
                                loading={tableLoading.value}
                                pagination={tablePagination}
                                row-key={tableRowKey}
                                single-line={false}
                                bordered
                                remote
                                striped
                                onUpdate:checked-row-keys={changeTableSelection}
                            />
                        </n-card>
                    </n-card>
                </n-modal>
                <TowerScanProductionMaterialDetail
                    v-model:show={materialDetailShow.value}
                    configData={materialDetailConfig.value}
                />
            </div>
        );
    }
});
