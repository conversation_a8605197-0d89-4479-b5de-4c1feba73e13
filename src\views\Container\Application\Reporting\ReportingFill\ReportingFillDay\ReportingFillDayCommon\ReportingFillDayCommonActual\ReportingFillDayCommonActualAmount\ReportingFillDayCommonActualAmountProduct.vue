<template>
    <n-spin :show="loadingShow">
        <template #description>正在处理中，请耐心等候</template>
        <n-card>
            <table-searchbar
                auto-search
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
                @componentClick="onComponentClick"
            />
        </n-card>
        <n-card class="mt">
            <div class="flex-y-center mb">
                <n-space>
                    <n-button secondary type="primary" @click="addTableItem">新增产成品实际用量</n-button>
                    <n-button secondary type="success" @click="saveTableData">保存填写</n-button>
                </n-space>
                <div class="h-100% flex-y-center ml-a">实际数量合计：{{ totalNumber }}根</div>
            </div>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
    </n-spin>
</template>

<script lang="ts" setup>
import { computed, h, onMounted, ref } from "vue";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable, useDicts } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import { NButton, NInput, NInputGroup, NInputGroupLabel, NTooltip } from "naive-ui";
import { cloneDeep } from "lodash-es";
import {
    GET_CONFIG_WORK_GROUP_LIST,
    GET_MANUFACTURE_PAGE_LIST,
    GET_PRODUCT_AMOUNT_LIST,
    GET_SEMI_MANUFACTURE_PAGE_LIST,
    SAVE_PRODUCT_AMOUNT_LIST
} from "@/api/application/reporting";
import { SpecSelector, UnitSelector } from "@/views/Container/Application/Reporting/components";
import { TableActions } from "@/components/TableActions";
import dayjs from "dayjs";
import { useStoreReportingSearch } from "@/store";
import { useThrottleFn } from "@vueuse/core";

let storeReportingSearch = useStoreReportingSearch();

interface RowProps {
    [key: string]: any;
}

let loadingShow = ref(false);

// 字典操作
let { dictLibs, getDictLibs } = useDicts();

let setDictLibs = async () => {
    let dictName = ["common_units"];
    await getDictLibs(dictName);
};

onMounted(async () => {
    await getWorkGroupIdOptions();
    await setDictLibs();
    getTableData();
});

let getWorkGroupIdOptions = async () => {
    await GET_CONFIG_WORK_GROUP_LIST({}).then((res) => {
        searchOptions.value.workGroupId = (res.data.data ?? []).map((item: any) => ({
            label: item.companyName + "-" + item.workshopName + "-" + item.groupName,
            value: item.id
        }));
        searchForm.value.workGroupId = searchOptions.value.workGroupId[0].value;
        searchForm.value.planDate = dayjs().format("YYYY-MM-DD");
        if (storeReportingSearch.getSearchForm.workGroupId) {
            searchForm.value.workGroupId = storeReportingSearch.getSearchForm.workGroupId;
        }
        if (storeReportingSearch.getSearchForm.planDate) {
            searchForm.value.planDate = storeReportingSearch.getSearchForm.planDate;
        }
    });
};

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { label: "班组", type: "select", prop: "workGroupId", span: 2 },
    { label: "日期筛选", type: "date", dateFormat: "yyyy-MM-dd", prop: "planDate" }
]);

let searchOptions = ref<TableSearchbarOptions>({ workGroupId: [] });

let searchForm = ref<TableSearchbarData>({ workGroupId: null, planDate: null });

let onSearch = () => {
    storeReportingSearch.setSearchForm({
        workGroupId: searchForm.value.workGroupId ?? null,
        planDate: searchForm.value.planDate ?? null
    });
    getTableData();
};

// 搜索栏自动保存逻辑
let autoSave = useThrottleFn(async () => {
    let totalPlanAmount = 0;
    let totalProductAmount = 0;
    tableData.value.forEach((item: any) => (totalPlanAmount += Number(item.planAmount)));
    tableData.value.forEach((item: any) => (totalProductAmount += Number(item.productAmount)));
    let params = {
        ...searchForm.value,
        id: planId.value,
        totalPlanAmount: totalPlanAmount,
        totalProductAmount: totalProductAmount,
        dailyAmountList: tableData.value
    };
    await SAVE_PRODUCT_AMOUNT_LIST({ ...params }).then((res) => {
        if (res.data.code === 0) window.$message.success("自动保存成功");
    });
}, 1000);

let onComponentClick = async (val: TableSearchbarData) => {
    // await autoSave();
};

let planId = ref<Nullable<string | number>>(null);

// 数据列表
let { tableRowKey, tableData, tableLoading, changeTableSelection } = useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    { type: "selection" },
    {
        title: "名称",
        align: "center",
        key: "poleName",
        render: (row) => {
            return row.poleName ?? "未知";
        }
    },
    {
        title: "规格型号",
        align: "center",
        key: "productModel",
        render: (row) => {
            if (row.id) {
                return row.productModel;
            } else {
                return h(SpecSelector, {
                    value: row.contentId,
                    onSubmit: async (v: unknown) => {
                        row.contentId = v;
                        let selectArray: any[] = [];
                        let [semiManufacture, manufacture] = await Promise.all([
                            GET_SEMI_MANUFACTURE_PAGE_LIST({ ids: v }),
                            GET_MANUFACTURE_PAGE_LIST({ ids: v })
                        ]);
                        let semiManufactureData = (semiManufacture.data.data.records ?? []).map((item: any) => {
                            return { ...item, contentType: 2 };
                        });
                        let manufactureData = (manufacture.data.data.records ?? []).map((item: any) => {
                            return { ...item, contentType: 1 };
                        });
                        selectArray = [...semiManufactureData, ...manufactureData];
                        row.contentType = selectArray[0].contentType;
                        row.poleName = selectArray[0].poleName;
                        row.amountUnit = selectArray[0].manufactureUnit ?? selectArray[0].semiUnit ?? "未知";
                        if (!v) {
                            row.poleName = null;
                            row.contentType = null;
                            row.amountUnit = null;
                        }
                    }
                });
            }
        }
    },
    // 2023年9月8日单位变更需要对接数据-已处理
    {
        title: "计划数量",
        align: "center",
        key: "planAmount",
        render: (row) => {
            if (row.id) {
                return row.planAmount + "根";
            } else {
                return h(NInputGroup, {}, () => [
                    h(NInput, { value: row.planAmount, onUpdateValue: (v) => (row.planAmount = v) }),
                    h(NInputGroupLabel, {}, () => [
                        h(UnitSelector, {
                            type: "text",
                            value: row.amountUnit,
                            options: dictLibs["common_units"] ?? []
                        })
                    ])
                ]);
            }
        }
    },
    {
        title: "实际数量",
        align: "center",
        key: "productAmount",
        render: (row) => {
            return h(
                NTooltip,
                {},
                {
                    trigger: () => {
                        return h(NInputGroup, {}, () => [
                            h(NInput, {
                                value: row.productAmount,
                                onUpdateValue: (v) => (row.productAmount = v),
                                onFocus: () => {
                                    if (row.productAmount === "0") row.productAmount = "";
                                },
                                onBlur: () => {
                                    if (!row.productAmount) row.productAmount = "0";
                                }
                            }),
                            h(NInputGroupLabel, {}, () => [
                                h(UnitSelector, {
                                    type: "text",
                                    value: row.amountUnit,
                                    options: dictLibs["common_units"] ?? []
                                })
                            ])
                        ]);
                    },
                    default: () => {
                        return row.productAmount;
                    }
                }
            );
        }
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: "80",
        render(row, index) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "删除",
                        tertiary: true,
                        type: "error",
                        // disabled: () => !!row.id,
                        onClick: () => deleteItem(index)
                    }
                ]
            });
        }
    }
]);

let getTableData = () => {
    tableLoading.value = true;
    GET_PRODUCT_AMOUNT_LIST({
        ...searchForm.value
    }).then((res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data.dailyAmountList ?? [];
            planId.value = res.data.data.id ?? null;
        }
        tableLoading.value = false;
    });
};

// 可编辑表单配置
let tableItem: RowProps = {
    id: "",
    contentId: "",
    contentType: "",
    planAmount: "",
    productAmount: ""
};

//新增计划
let addTableItem = () => {
    if (!searchForm.value.workGroupId || !searchForm.value.planDate) {
        window.$message.error("请先选择班组和日期！");
        return false;
    }
    tableData.value.unshift(cloneDeep(tableItem));
};

let deleteItem = (index: number) => {
    tableData.value.splice(index, 1);
};

// 合计
let totalNumber = computed(() => {
    let total = 0;
    tableData.value.forEach((item: any) => {
        total += Number(item.productAmount);
    });
    return total;
});

//保存填写
let saveTableData = async () => {
    loadingShow.value = true;
    let totalPlanAmount = 0;
    let totalProductAmount = 0;
    tableData.value.forEach((item: any) => {
        totalPlanAmount += Number(item.planAmount);
    });
    tableData.value.forEach((item: any) => {
        totalProductAmount += Number(item.productAmount);
    });
    let params = {
        ...searchForm.value,
        id: planId.value,
        totalPlanAmount: totalPlanAmount,
        totalProductAmount: totalProductAmount,
        dailyAmountList: tableData.value
    };
    if (!searchForm.value.workGroupId) {
        window.$message.error("请先选择班组！");
        return false;
    }
    if (!searchForm.value.planDate) {
        window.$message.error("请先选择日期！");
        return false;
    }
    await SAVE_PRODUCT_AMOUNT_LIST({ ...params }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("保存成功");
            onSearch();
        }
    });
    loadingShow.value = false;
};
</script>
