import { computed, defineComponent, ref, watchEffect } from "vue";
import type { UploadFileInfo, FormInst } from "naive-ui";
import { DynamicIcon } from "@/components/DynamicIcon";
import { UserSelector } from "@/components/UserSelector";
import { useStoreUser } from "@/store";
import { RE_IMPORT_IRON_PRODUCTION_PLAN } from "@/api/application/TowerScan";
import dayjs from "dayjs";

export default defineComponent({
    name: "TowerScanProductionReImport",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        const storeUser = useStoreUser();
        const formRef = ref<FormInst | null>(null);

        // 表单数据
        const formData = ref({
            operator: storeUser.getUserData.sysUser?.username || "",
            operationTime: dayjs().format("YYYY-MM-DD HH:mm:ss")
        });

        // 文件列表
        const fileList = ref<UploadFileInfo[]>([]);

        // 错误弹窗相关状态
        const showErrorModal = ref(false);
        const errorList = ref<string[]>([]);
        const errorMessage = ref("");

        const onBeforeUpload = async (fileData: { file: UploadFileInfo; fileList: UploadFileInfo[] }) => {
            // 限制文件类型
            const allowedTypes = [
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "application/vnd.ms-excel"
            ];

            if (!allowedTypes.includes(fileData.file.file?.type || "")) {
                window.$message.error("请上传Excel文件（.xlsx或.xls格式）");
                return false;
            }

            return true;
        };

        const onDownloadTemplate = () => {
            // 这里需要根据实际的模板下载地址进行调整
            window.location.href = import.meta.env.VITE_API_URL + "/admin/sys-file/local/IronPlan.xlsx";
        };

        const onClose = () => {
            changeModalShow(false);
            formData.value.operator = storeUser.getUserData.sysUser?.username || "";
            formData.value.operationTime = dayjs().format("YYYY-MM-DD HH:mm:ss");
            fileList.value = [];
        };

        const onCloseErrorModal = () => {
            showErrorModal.value = false;
            errorList.value = [];
            errorMessage.value = "";
        };

        const onSubmit = async () => {
            let validateError = await formRef.value?.validate((errors) => !!errors);
            if (validateError) return false;

            if (!formData.value.operator) {
                window.$message.error("请选择操作人");
                return;
            }

            if (!fileList.value.length) {
                window.$message.error("请上传文件");
                return;
            }

            const submitFormData = new FormData();
            submitFormData.append("file", fileList.value[0].file as File);
            submitFormData.append("operator", formData.value.operator);
            submitFormData.append("operationTime", formData.value.operationTime);
            submitFormData.append("planId", props.configData.id);

            try {
                const res = await RE_IMPORT_IRON_PRODUCTION_PLAN(submitFormData as any);
                if (res.data.code === 0) {
                    window.$message.success("重新导入成功");
                    onClose();
                    emit("refresh");
                } else {
                    // 处理重新导入失败的情况
                    if (res.data.data?.errors && Array.isArray(res.data.data.errors) && res.data.data.errors.length > 0) {
                        // 显示详细错误信息弹窗
                        errorMessage.value = res.data.msg || "重新导入失败";
                        errorList.value = res.data.data.errors;
                        showErrorModal.value = true;
                    } else {
                        // 如果没有详细错误信息，显示基本错误消息
                        window.$message.error(res.data.msg || "重新导入失败");
                    }
                }
            } catch (error) {
                window.$message.error("重新导入失败");
            }
        };

        return () => (
            <>
                <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                    <n-card class="w-600px" title="重新导入下料生产计划清单" closable onClose={onClose}>
                        <n-form ref={formRef} model={formData.value} label-placement="left" label-width="auto">
                            <n-grid cols={12} x-gap={16}>
                                <n-form-item-gi span={6} label="操作人" required>
                                    <UserSelector
                                        v-model:value={formData.value.operator}
                                        class="w-100%"
                                        key-name="username"
                                        placeholder="请选择操作人"
                                        multiple={false}
                                        disabled
                                    />
                                </n-form-item-gi>
                                <n-form-item-gi span={6} label="操作时间">
                                    <n-date-picker
                                        v-model:formatted-value={formData.value.operationTime}
                                        class="w-100%"
                                        clearable
                                        placeholder="请选择操作时间"
                                        type="datetime"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                    />
                                </n-form-item-gi>
                            </n-grid>

                            <n-form-item label="上传文件" required>
                                <div class="w-100%">
                                    <n-upload
                                        v-model:file-list={fileList.value}
                                        directory-dnd
                                        onBeforeUpload={onBeforeUpload}
                                        max={1}
                                        accept=".xlsx,.xls"
                                    >
                                        <n-upload-dragger>
                                            <div class="mb-4">
                                                <DynamicIcon icon="UploadOutlined" />
                                            </div>
                                            <n-p class="text-16px">点击或拖动文件到该区域上传</n-p>
                                            <n-text depth="3" class="text-12px">
                                                支持Excel文件格式（.xlsx、.xls）
                                            </n-text>
                                        </n-upload-dragger>
                                    </n-upload>
                                    <n-button class="mt-10px" text type="primary" onClick={onDownloadTemplate}>
                                        点击此处下载批量导入模板
                                    </n-button>
                                </div>
                            </n-form-item>

                            <n-form-item>
                                <n-space>
                                    <n-button type="primary" onClick={onSubmit}>
                                        提交
                                    </n-button>
                                    <n-button onClick={onClose}>取消</n-button>
                                </n-space>
                            </n-form-item>
                        </n-form>
                    </n-card>
                </n-modal>

                {/* 错误详情弹窗 */}
                <n-modal v-model:show={showErrorModal.value} close-on-esc={true} mask-closable={true}>
                    <n-card 
                        class="w-700px max-h-600px" 
                        title="重新导入错误详情" 
                        closable 
                        onClose={onCloseErrorModal}
                        v-slots={{
                            action: () => (
                                <n-space justify="end">
                                    <n-button type="primary" onClick={onCloseErrorModal}>
                                        我知道了
                                    </n-button>
                                </n-space>
                            )
                        }}
                    >
                        <div class="space-y-4">
                            <n-alert 
                                type="error" 
                                show-icon
                                v-slots={{
                                    header: () => errorMessage.value
                                }}
                            >
                                <div class="mt-3">
                                    共发现 <n-text type="error" strong>{errorList.value.length}</n-text> 个错误，请修正后重新导入：
                                </div>
                            </n-alert>
                            
                            <n-scrollbar style="max-height: 350px;">
                                <n-list bordered>
                                    {errorList.value.map((error, index) => (
                                        <n-list-item key={index}>
                                            <div class="flex items-start space-x-3">
                                                <n-tag type="error" size="small" round>
                                                    {index + 1}
                                                </n-tag>
                                                <n-text class="flex-1 leading-relaxed">
                                                    {error}
                                                </n-text>
                                            </div>
                                        </n-list-item>
                                    ))}
                                </n-list>
                            </n-scrollbar>
                        </div>
                    </n-card>
                </n-modal>
            </>
        );
    }
}); 