<template>
    <div class="ml-30px">
        <div class="text-20px mb-30px">安全设置</div>
        <div>
            <n-space>
                <span>账号密码：</span>
                <!--<span class="color-[#D03050]">*</span>-->
                <span class="color-[#A6A6A6]">当前密码已设置</span>
                <a class="color-[#165DFF] cursor-pointer" @click="openPasswordModal()">修改</a>
            </n-space>
            <!--<n-space class="mt">-->
            <!--<span>密码强度：</span>-->
            <!--<span class="color-[#D03050]">低</span>-->
            <!--<span class="color-[#FDD835]">中</span>-->
            <!--<span class="color-[#0E9945]">高</span>-->
            <!--</n-space>-->
        </div>
        <ChangePassword v-model:show="passwordModalShow" />
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { useStoreUser } from "@/store";
import { ChangePassword } from "@/components/System";

let storeUser = useStoreUser();

// 修改密码弹窗
let passwordModalShow = ref(false);

let openPasswordModal = () => {
    passwordModalShow.value = true;
};
</script>
