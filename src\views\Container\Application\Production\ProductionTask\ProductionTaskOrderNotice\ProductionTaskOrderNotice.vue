<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <!--审批单-->
        <Requirements v-model:show="requirementsModal.show" :config-data="requirementsModal.configData" />
        <!--需求详情-->
        <ProcessDetail v-model:show="processDetailModal.show" :config-data="processDetailModal.configData" />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from "vue";
import type { DataTableColumns } from "naive-ui";
import { NButton, NText } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { TableActions } from "@/components/TableActions";
import { useCommonTable } from "@/hooks";
import {
    GET_PRODUCTION_ORDER_FORMULAS,
    GET_PRODUCTION_ORDER_LIST,
    PRODUCTION_ORDER_CONFIRM
} from "@/api/application/production";
import { ProcessDetail } from "@/views/Container/Application/Process/components";
import { Requirements } from "./components";
import { GET_STAFF_BY_USERNAMES } from "@/api/permission";
import { GET_OA_INSTANCE_FORM } from "@/api/application/oa";

interface RowProps {
    poId: string | number;
    pomNumber: string | number;
    projectName: string;
    deliveryDate: string;
    processInstanceId: string | number;
    salesPerson: string;
    salesPersonName: string;
    orderCheckState: string | number;
    orderCheckTime: string;
}

onMounted(() => {
    getSearchOptions();
    getTableData();
});

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([]);

let searchOptions = ref<TableSearchbarOptions>({});

let searchForm = ref<TableSearchbarData>({});

let getSearchOptions = () => {};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("poId");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "订单号",
        key: "pomNumber",
        align: "center",
        render: (row) => {
            return row.pomNumber || "/";
        }
    },
    {
        title: "项目名称",
        key: "projectName",
        align: "center"
    },
    {
        title: "需求详情",
        key: "poId",
        align: "center",
        render(row) {
            return h(
                NButton,
                {
                    type: "primary",
                    text: true,
                    onClick: () => openRequirementsModal(row.poId)
                },
                () => "点击查看"
            );
        }
    },
    {
        title: "审批单",
        key: "processInstanceId",
        align: "center",
        render(row) {
            return h(
                NButton,
                {
                    type: "primary",
                    text: true,
                    onClick: () => openProcessDetailModal(row.processInstanceId)
                },
                () => "点击查看"
            );
        }
    },
    {
        title: "确认状态",
        key: "orderCheckState",
        align: "center",
        render(row) {
            // 订单确认状态（0：未确认，1：已确认，2：已委外）
            if (String(row.orderCheckState) === "0") {
                return h(NText, { type: "error" }, () => "未确认");
            } else if (String(row.orderCheckState) === "1") {
                return h(NText, { type: "success" }, () => "已确认");
            } else if (String(row.orderCheckState) === "2") {
                return h(NText, { type: "default" }, () => "已委外");
            } else {
                return h(NText, { type: "default" }, () => "未知");
            }
        }
    },
    {
        title: "交货日期",
        key: "deliveryDate",
        align: "center"
    },
    {
        title: "销售联系人",
        key: "salesPersonName",
        align: "center",
        render: (row) => {
            return row.salesPersonName || "/";
        }
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        // width: 220,
        render(row) {
            if (String(row.orderCheckState) === "1") {
                return `确认时间：${row.orderCheckTime}`;
            } else {
                return h(TableActions, {
                    type: "button",
                    buttonActions: [
                        {
                            label: "确认生产",
                            tertiary: true,
                            type: "primary",
                            onClick: () => onConfirmProduction(row)
                        },
                        {
                            label: "委外生产",
                            tertiary: true,
                            type: "error",
                            onClick: () => {
                                window.$message.warning("敬请期待");
                            }
                        }
                    ]
                });
            }
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_PRODUCTION_ORDER_LIST({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        ...searchForm.value
    }).then(async (res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data.records || [];
            tablePagination.itemCount = res.data.data.total;
            tableLoading.value = false;
        }
    });
};

// 搜索
let onSearch = () => {
    getTableData();
};

// 查看需求详情
let requirementsModal = ref<{ show: boolean; configData: Record<string, any> }>({
    show: false,
    configData: {}
});

let openRequirementsModal = (id: string | number) => {
    GET_PRODUCTION_ORDER_FORMULAS({ poId: id }).then((res) => {
        if (res.data.code === 0) {
            requirementsModal.value.show = true;
            requirementsModal.value.configData = {
                modelFormulaList: res.data?.data?.modelFormulaList || []
            };
        } else {
            window.$message.error("该订单不存在");
        }
    });
};

// 查看审批单
let processDetailModal = ref<{ show: boolean; configData: Record<string, any> }>({
    show: false,
    configData: {}
});

let openProcessDetailModal = (id: string | number) => {
    GET_OA_INSTANCE_FORM({ processInstId: id }).then((res) => {
        if (res.data.code === 0) {
            processDetailModal.value.show = true;
            processDetailModal.value.configData = res.data.data;
        } else {
            window.$message.error("该审批单不存在");
        }
    });
};

// 确认生产
let onConfirmProduction = (row: RowProps) => {
    window.$dialog.warning({
        title: "确认信息",
        content: "是否确认生产该订单？",
        positiveText: "确认",
        negativeText: "取消",
        onPositiveClick: () => {
            PRODUCTION_ORDER_CONFIRM({ poId: row.poId }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("操作成功");
                    getTableData();
                }
            });
        }
    });
};
</script>
