import { computed, defineComponent, h, ref, watchEffect } from "vue";
import { type DataTableColumns, type FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import {
    GET_ALL_USER_LIST,
    GET_ATTENDANCE_RULE_CONFIG_DETAIL,
    GET_STAFF_BY_USERNAMES,
    SAVE_ATTENDANCE_RULE_CONFIG
} from "@/api/permission";
import { TableActions } from "@/components/TableActions";
import { UserSelector } from "@/components/UserSelector";

export default defineComponent({
    name: "AttendanceRuleConfigEdit",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        // 获取详情
        const getDetail = () => {
            GET_ATTENDANCE_RULE_CONFIG_DETAIL({ id: props.configData.id }).then((res) => {
                const resData = res.data.data;
                console.log(resData);
                formData.value = {
                    ruleName: resData.ruleName ?? null,
                    ruleType: resData.ruleType ?? null,
                    ruleStatus: resData.ruleStatus ?? null,
                    // 迟到早退配置
                    lateTime: resData.lateTime ?? 0,
                    lateCount: resData.lateCount ?? 0,
                    latePeriod: resData.latePeriod ?? null,
                    earlyTime: resData.earlyTime ?? 0,
                    earlyCount: resData.earlyCount ?? 0,
                    earlyPeriod: resData.earlyPeriod ?? null,
                    // 考勤工作日配置
                    workDay1: resData.workDay1 ?? 0,
                    workDay2: resData.workDay2 ?? 0,
                    workDay3: resData.workDay3 ?? 0,
                    workDay4: resData.workDay4 ?? 0,
                    workDay5: resData.workDay5 ?? 0,
                    workDay6: resData.workDay6 ?? 0,
                    workDay7: resData.workDay7 ?? 0
                };
                itemList.value = resData.itemList ?? [];
                timeList.value = resData.timeList ?? [];
            });
        };

        // 表单数据
        interface FormDataProps {
            [key: string]: any;
        }

        const formRef = ref<FormInst | null>(null);

        // 获取表单选项
        const getFormOptions = async () => {};

        const initFormData: FormDataProps = {
            ruleName: null,
            ruleType: 1,
            ruleStatus: 1,
            // 迟到早退配置
            lateTime: 0,
            lateCount: 0,
            latePeriod: null,
            earlyTime: 0,
            earlyCount: 0,
            earlyPeriod: null,
            // 考勤工作日配置
            workDay1: 0,
            workDay2: 0,
            workDay3: 0,
            workDay4: 0,
            workDay5: 0,
            workDay6: 0,
            workDay7: 0
        };

        const formRules = computed(() => ({
            ruleName: [{ required: true, message: "请输入规则名称", trigger: ["input", "blur"] }],
            ruleType: [{ required: true, message: "请选择规则类型", trigger: ["blur", "change"], type: "number" }],
            ruleStatus: [{ required: true, message: "请选择是否启用", trigger: ["blur", "change"], type: "number" }]
        }));

        const formData = ref(cloneDeep(initFormData));

        const clearForm = () => {
            formData.value = cloneDeep(initFormData);
            timeList.value = [];
            itemList.value = [];
        };

        // 打卡时间段配置
        const timeTableColumns = computed<DataTableColumns<any>>(() => [
            {
                title: "上班打卡时间",
                key: "onTime",
                align: "center",
                render: (row) => (
                    <n-time-picker
                        v-model:formatted-value={row.onTime}
                        class="w-100%"
                        clearable
                        placeholder="请选择"
                        format="HH:mm:ss"
                    />
                )
            },
            {
                title: "上班卡最早时间",
                key: "onBeginTime",
                align: "center",
                render: (row) => (
                    <n-time-picker
                        v-model:formatted-value={row.onBeginTime}
                        class="w-100%"
                        clearable
                        placeholder="请选择"
                        format="HH:mm:ss"
                    />
                )
            },
            {
                title: "上班卡最晚时间",
                key: "onEndTime",
                align: "center",
                render: (row) => (
                    <n-time-picker
                        v-model:formatted-value={row.onEndTime}
                        class="w-100%"
                        clearable
                        placeholder="请选择"
                        format="HH:mm:ss"
                    />
                )
            },
            {
                title: "下班打卡时间",
                key: "offTime",
                align: "center",
                render: (row) => (
                    <n-time-picker
                        v-model:formatted-value={row.offTime}
                        class="w-100%"
                        clearable
                        placeholder="请选择"
                        format="HH:mm:ss"
                    />
                )
            },
            {
                title: "下班卡最早时间",
                key: "offBeginTime",
                align: "center",
                render: (row) => (
                    <n-time-picker
                        v-model:formatted-value={row.offBeginTime}
                        class="w-100%"
                        clearable
                        placeholder="请选择"
                        format="HH:mm:ss"
                    />
                )
            },
            {
                title: "下班卡最晚时间",
                key: "offEndTime",
                align: "center",
                render: (row) => (
                    <n-time-picker
                        v-model:formatted-value={row.offEndTime}
                        class="w-100%"
                        clearable
                        placeholder="请选择"
                        format="HH:mm:ss"
                    />
                )
            },
            {
                title: "启用状态",
                key: "status",
                align: "center",
                width: 85,
                render: (row) => <n-switch v-model:value={row.status} checked-value={1} unchecked-value={0} />
            },
            {
                title: "班次",
                key: "workCount",
                align: "center",
                width: 145,
                render: (row) => (
                    <n-input-number v-model:value={row.workCount} class="w-100%" clearable placeholder="请输入班次" />
                )
            },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 80,
                render(row, index) {
                    return h(TableActions, {
                        type: "button",
                        buttonActions: [
                            {
                                label: "删除",
                                tertiary: true,
                                type: "error",
                                onClick: () => deleteTime(row, index)
                            }
                        ]
                    });
                }
            }
        ]);

        const timeList = ref<any[]>([]);

        const addTime = () => {
            timeList.value.push({
                onTime: null,
                offTime: null,
                onBeginTime: null,
                onEndTime: null,
                offBeginTime: null,
                offEndTime: null,
                status: null,
                workCount: null
            });
        };

        const deleteTime = (row: any, index: number) => {
            timeList.value.splice(index, 1);
        };

        // 人员相关配置
        const itemTableColumns = ref<DataTableColumns<any>>([
            { title: "姓名", key: "trueName", align: "center" },
            { title: "手机号", key: "phone", align: "center" },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 100,
                render: (row, index) => {
                    return (
                        <TableActions
                            type="button"
                            buttonActions={[
                                { label: "删除", tertiary: true, type: "error", onClick: () => deleteItem(row, index) }
                            ]}
                        />
                    );
                }
            }
        ]);

        const itemList = ref<any[]>([]);

        const users = ref<any>(null);

        const addItem = () => {
            window.$dialog.success({
                title: "选择人员",
                content: () => (
                    <UserSelector
                        v-model:value={users.value}
                        class="w-100%"
                        key-name="userId"
                        placeholder="请选择人员"
                    />
                ),
                positiveText: "新增",
                negativeText: "取消",
                onPositiveClick: () => {
                    GET_ALL_USER_LIST({ userIds: users.value }).then((res) => {
                        const existingUserIds = new Set(itemList.value.map((item: any) => item.userId));
                        const newUsers = res.data.data.filter((item: any) => !existingUserIds.has(item.userId));
                        itemList.value.push(...newUsers);
                    });
                    users.value = null;
                }
            });
        };

        const deleteItem = (row: any, index: number) => {
            if (row.id) {
                const itemIndex = itemList.value.findIndex((item) => item.id === row.id);
                if (itemIndex !== -1) {
                    itemList.value[itemIndex].delFlag = 1;
                    itemList.value.splice(itemIndex, 1);
                }
            } else {
                itemList.value.splice(index, 1);
            }
        };

        watchEffect(() => {
            if (props.configData.id) {
                getDetail();
            }
        });

        // 交互按钮
        const onClose = () => {
            clearForm();
            changeModalShow(false);
            emit("refresh");
        };

        const onSubmit = async () => {
            const validateError = await formRef.value?.validate((errors) => !!errors);
            if (validateError) return false;

            // 校验打卡时间段
            if (!timeList.value.length) return window.$message.error("请至少添加一个打卡时间段");
            for (const time of timeList.value) {
                if (!time.onTime || !time.offTime) return window.$message.error("请完善打卡时间段配置");
                if (time.onBeginTime && time.onEndTime && time.onBeginTime > time.onEndTime)
                    return window.$message.error("上班卡最早时间不能晚于最晚时间");
                if (time.offBeginTime && time.offEndTime && time.offBeginTime > time.offEndTime)
                    return window.$message.error("下班卡最早时间不能晚于最晚时间");
            }

            // 校验迟到早退配置
            if (formData.value.lateTime === null || formData.value.lateTime === undefined)
                return window.$message.error("请输入允许迟到时间");
            if (formData.value.lateCount === null || formData.value.lateCount === undefined)
                return window.$message.error("请输入允许迟到次数");
            if (!formData.value.latePeriod) return window.$message.error("请选择迟到周期");
            if (formData.value.earlyTime === null || formData.value.earlyTime === undefined)
                return window.$message.error("请输入允许早退时间");
            if (formData.value.earlyCount === null || formData.value.earlyCount === undefined)
                return window.$message.error("请输入允许早退次数");
            if (!formData.value.earlyPeriod) return window.$message.error("请选择早退周期");

            // 校验考勤工作日
            const hasWorkDay = Object.keys(formData.value).some(
                (key) => key.startsWith("workDay") && formData.value[key] === 1
            );
            if (!hasWorkDay) return window.$message.error("请至少选择一个工作日");

            // 校验参与人员
            if (!itemList.value.length) return window.$message.error("请至少添加一个参与人员");

            SAVE_ATTENDANCE_RULE_CONFIG({
                ...(props.configData.id && { id: props.configData.id }),
                ...formData.value,
                corpId: props.configData.corpId,
                timeList: timeList.value ?? [],
                itemList: (itemList.value ?? []).map((item: any) => ({ userId: item.userId }))
            }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success(props.configData.id ? "编辑成功" : "新增成功");
                    onClose();
                } else {
                    window.$message.error(res.data.msg);
                }
            });
        };

        watchEffect(async () => {
            if (show.value) {
                await getFormOptions();
                if (props.configData.id) getDetail();
            }
        });

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card
                    title={props.configData.id ? "编辑考勤规则" : "新增考勤规则"}
                    class="w-1200px"
                    closable
                    onClose={onClose}
                >
                    <n-form
                        ref={formRef}
                        model={formData.value}
                        rules={formRules.value}
                        label-placement="left"
                        label-width="auto"
                    >
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi span={6} label="规则名称" path="ruleName">
                                <n-input
                                    v-model:value={formData.value.ruleName}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入规则名称"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="规则类型" path="ruleType">
                                <n-radio-group v-model:value={formData.value.ruleType}>
                                    <n-space>
                                        <n-radio value={1} label="固定制" />
                                        <n-radio value={2} label="排班" />
                                        <n-radio value={3} label="自由时间" />
                                    </n-space>
                                </n-radio-group>
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="是否启用" path="ruleStatus">
                                <n-switch
                                    v-model:value={formData.value.ruleStatus}
                                    checked-value={1}
                                    unchecked-value={0}
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={12}>
                                <n-tabs animated type="bar" defaultValue={1}>
                                    <n-tab-pane name={1} tab="打卡时间段">
                                        <n-data-table
                                            columns={timeTableColumns.value}
                                            data={timeList.value}
                                            single-line={false}
                                            bordered
                                            striped
                                        />
                                        <div class="flex-center mt">
                                            <n-button type="warning" onClick={addTime}>
                                                新增时间段
                                            </n-button>
                                        </div>
                                    </n-tab-pane>
                                    <n-tab-pane name={2} tab="迟到早退">
                                        <n-table single-line={false} class="text-center">
                                            <thead>
                                                <tr>
                                                    <th>允许迟到时间</th>
                                                    <th>允许迟到次数</th>
                                                    <th>迟到周期</th>
                                                    <th>允许早退时间</th>
                                                    <th>允许早退次数</th>
                                                    <th>早退周期</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <n-input-number
                                                            class="w-150px"
                                                            v-model:value={formData.value.lateTime}
                                                            clearable
                                                            placeholder="请输入"
                                                        />
                                                    </td>
                                                    <td>
                                                        <n-input-number
                                                            class="w-150px"
                                                            v-model:value={formData.value.lateCount}
                                                            clearable
                                                            placeholder="请输入"
                                                        />
                                                    </td>
                                                    <td>
                                                        <n-select
                                                            class="w-150px"
                                                            v-model:value={formData.value.latePeriod}
                                                            options={[
                                                                { label: "年", value: "年" },
                                                                { label: "月", value: "月" },
                                                                { label: "日", value: "日" }
                                                            ]}
                                                            clearable
                                                            placeholder="请选择迟到周期"
                                                        />
                                                    </td>
                                                    <td>
                                                        <n-input-number
                                                            class="w-150px"
                                                            v-model:value={formData.value.earlyTime}
                                                            clearable
                                                            placeholder="请输入"
                                                        />
                                                    </td>
                                                    <td>
                                                        <n-input-number
                                                            class="w-150px"
                                                            v-model:value={formData.value.earlyCount}
                                                            clearable
                                                            placeholder="请输入"
                                                        />
                                                    </td>
                                                    <td>
                                                        <n-select
                                                            class="w-150px"
                                                            v-model:value={formData.value.earlyPeriod}
                                                            options={[
                                                                { label: "年", value: "年" },
                                                                { label: "月", value: "月" },
                                                                { label: "日", value: "日" }
                                                            ]}
                                                            clearable
                                                            placeholder="请选择早退周期"
                                                        />
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </n-table>
                                    </n-tab-pane>
                                    <n-tab-pane name={3} tab="考勤工作日">
                                        <n-space size={24}>
                                            <div class="flex-y-center">
                                                <n-switch
                                                    v-model:value={formData.value.workDay1}
                                                    checked-value={1}
                                                    unchecked-value={0}
                                                />
                                                <div class="ml-2">周一</div>
                                            </div>
                                            <div class="flex-y-center">
                                                <n-switch
                                                    v-model:value={formData.value.workDay2}
                                                    checked-value={1}
                                                    unchecked-value={0}
                                                />
                                                <div class="ml-2">周二</div>
                                            </div>
                                            <div class="flex-y-center">
                                                <n-switch
                                                    v-model:value={formData.value.workDay3}
                                                    checked-value={1}
                                                    unchecked-value={0}
                                                />
                                                <div class="ml-2">周三</div>
                                            </div>
                                            <div class="flex-y-center">
                                                <n-switch
                                                    v-model:value={formData.value.workDay4}
                                                    checked-value={1}
                                                    unchecked-value={0}
                                                />
                                                <div class="ml-2">周四</div>
                                            </div>
                                            <div class="flex-y-center">
                                                <n-switch
                                                    v-model:value={formData.value.workDay5}
                                                    checked-value={1}
                                                    unchecked-value={0}
                                                />
                                                <div class="ml-2">周五</div>
                                            </div>
                                            <div class="flex-y-center">
                                                <n-switch
                                                    v-model:value={formData.value.workDay6}
                                                    checked-value={1}
                                                    unchecked-value={0}
                                                />
                                                <div class="ml-2">周六</div>
                                            </div>
                                            <div class="flex-y-center">
                                                <n-switch
                                                    v-model:value={formData.value.workDay7}
                                                    checked-value={1}
                                                    unchecked-value={0}
                                                />
                                                <div class="ml-2">周日</div>
                                            </div>
                                        </n-space>
                                    </n-tab-pane>
                                    <n-tab-pane name={4} tab="参与人员">
                                        <n-space class="mb">
                                            <n-button type="success" onClick={() => addItem()}>
                                                新增添加
                                            </n-button>
                                        </n-space>
                                        <n-data-table
                                            columns={itemTableColumns.value}
                                            data={itemList.value}
                                            single-line={false}
                                            bordered
                                            striped
                                        />
                                    </n-tab-pane>
                                </n-tabs>
                            </n-form-item-gi>
                            <n-form-item-gi span={12}>
                                <n-space>
                                    <n-button type="primary" onClick={onSubmit}>
                                        提交
                                    </n-button>
                                    <n-button onClick={onClose}>取消</n-button>
                                </n-space>
                            </n-form-item-gi>
                        </n-grid>
                    </n-form>
                </n-card>
            </n-modal>
        );
    }
});
