<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            >
                <template #buttons>
                    <n-space>
                        <n-button type="success" @click="openAddModal()">创建配方</n-button>
                    </n-space>
                </template>
            </table-searchbar>
        </n-card>
        <n-card class="mt-20px" hoverable>
            <template v-if="tableData && tableData.length > 0">
                <n-grid :col="24" :x-gap="16" :y-gap="16" item-responsive>
                    <n-grid-item span="12 800:8 1000:6 1800:3 2500:2">
                        <n-card class="w-100% h-100%" hoverable>
                            <div class="flex-center cursor-pointer w-100% h-100%" @click="openAddModal()">
                                <div>
                                    <dynamic-icon class="block m-a" color="#999999" icon="PlusOutlined" size="35" />
                                    <div class="mt-10px color-[#999999]">点击创建配方</div>
                                </div>
                            </div>
                        </n-card>
                    </n-grid-item>
                    <n-grid-item v-for="item in tableData" span="12 800:8 1000:6 1800:3 2500:2">
                        <n-card content-style="padding:0 15px" hoverable>
                            <div class="flex-y-center pt-15px">
                                <n-element tag="div">
                                    <dynamic-icon
                                        class="block color-[var(--primary-color)]"
                                        icon="GroupOutlined"
                                        size="50"
                                    />
                                </n-element>
                                <div class="ml-10px">
                                    <div class="text-18px">{{ item.formulaName }}</div>
                                    <div class="text-12px mt-5px color-[#666666]">更新时间：{{ item.updateTime }}</div>
                                </div>
                            </div>
                            <div class="flex-y-center justify-between pt-25px pb-15px">
                                <n-button text type="primary" @click="openEditModal(item)">编辑配方</n-button>
                                <n-button text type="warning" @click="openHistoryModal()">历史版本</n-button>
                                <n-button text type="error" @click="onDelete(item.formulaId)">删除配方</n-button>
                            </div>
                        </n-card>
                    </n-grid-item>
                </n-grid>
                <div class="flex-center mt-20px">
                    <n-pagination
                        v-model:page="pagination.current"
                        v-model:page-size="pagination.size"
                        :display-order="['pages', 'size-picker']"
                        :item-count="pagination.total"
                        :page-sizes="[10, 50, 100]"
                        show-size-picker
                        @update:page="getTableData"
                        @update:page-size="getTableData"
                    />
                </div>
            </template>
            <n-result v-else class="pt-50px pb-50px" status="404" title="暂无配方" />
        </n-card>
        <!--新增配方-->
        <ProductionFormulaAddModal v-model:show="addModal.show" @refresh="getTableData()" />
        <!--新增编辑配方-->
        <ProductionFormulaEditModal
            v-model:show="editModal.show"
            :config-data="editModal.configData"
            @refresh="getTableData()"
        />
    </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { DynamicIcon } from "@/components/DynamicIcon";
import { DELETE_PRODUCTION_FORMULA, GET_PRODUCTION_FORMULA_LIST } from "@/api/application/production";
import ProductionFormulaAddModal from "./ProductionFormulaAddModal.vue";
import ProductionFormulaEditModal from "./ProductionFormulaEditModal.vue";

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([]);

let searchOptions = ref<TableSearchbarOptions>({});

let searchForm = ref<TableSearchbarData>({});

let onSearch = () => {
    getTableData();
};

// 获取列表
interface RowProps {
    formulaId: Nullable<string | number>;
    formulaName: Nullable<string>;
    chargerSheet: Nullable<string>;
    updateTime: Nullable<string>;
}

let pagination = ref({
    current: 1,
    size: 10,
    total: 0
});

let tableData = ref<RowProps[]>([]);

let getTableData = () => {
    GET_PRODUCTION_FORMULA_LIST({
        ...searchForm.value,
        current: pagination.value.current,
        size: pagination.value.size
    }).then((res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data.records;
            pagination.value.total = res.data.data.total;
        }
    });
};

onMounted(() => {
    getTableData();
});

// 新增编辑
let addModal = ref<{ show: boolean }>({
    show: false
});

let openAddModal = () => {
    addModal.value.show = true;
};

let editModal = ref<{ show: boolean; configData: RowProps }>({
    show: false,
    configData: {
        formulaId: null,
        formulaName: null,
        chargerSheet: null,
        updateTime: null
    }
});

let openEditModal = (row: RowProps) => {
    editModal.value.show = true;
    editModal.value.configData = row;
};

// 删除
let onDelete = (formulaId: Nullable<string | number>) => {
    window.$dialog.warning({
        title: "警告",
        content: `确定删除该配方吗？`,
        positiveText: "删除",
        negativeText: "取消",
        onPositiveClick: () => {
            DELETE_PRODUCTION_FORMULA({ formulaId }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("删除成功");
                    getTableData();
                }
            });
        }
    });
};

// 历史版本
let openHistoryModal = () => {
    window.$message.warning("暂未开放");
};
</script>
