<template>
    <n-card content-style="padding-top:10px">
        <n-tabs type="bar" animated>
            <n-tab-pane name="原材料管理">
                <ReportingMaterialRaw class="pt-1" />
            </n-tab-pane>
            <n-tab-pane name="产成品管理">
                <ReportingMaterialProduct class="pt-1" />
            </n-tab-pane>
            <n-tab-pane name="半成品管理">
                <ReportingMaterialSemiProduct class="pt-1" />
            </n-tab-pane>
        </n-tabs>
    </n-card>
</template>

<script setup lang="ts">
import ReportingMaterialRaw from "./ReportingMaterialRaw.vue";
import ReportingMaterialProduct from "./ReportingMaterialProduct.vue";
import ReportingMaterialSemiProduct from "./ReportingMaterialSemiProduct.vue";
</script>
