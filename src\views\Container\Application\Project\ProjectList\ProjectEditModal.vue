<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card :title="id ? '编辑项目' : '新增项目'" class="w-800px" closable @close="changeModalShow(false)">
                <n-tabs type="line">
                    <n-tab-pane name="基础信息">
                        <n-form
                            ref="formRef"
                            :model="formData"
                            :rules="formRules"
                            label-placement="left"
                            label-width="auto"
                        >
                            <n-form-item label="项目名称" path="projectName">
                                <n-input
                                    v-model:value="formData.projectName"
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入项目名称"
                                />
                            </n-form-item>
                            <n-form-item label="招投标项目编号" path="projectCode">
                                <n-input
                                    v-model:value="formData.projectCode"
                                    :disabled="!!id"
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入招投标项目编号"
                                />
                            </n-form-item>
                            <n-form-item label="项目类型" path="projectType">
                                <n-radio-group
                                    v-model:value="formData.projectType"
                                    :disabled="!!id"
                                    name="radiogroup"
                                    @update:value="getNodeDirectorList"
                                >
                                    <n-radio
                                        v-for="item in dictLibs.project_type"
                                        :key="item.value"
                                        :value="item.value"
                                    >
                                        {{ item.label }}
                                    </n-radio>
                                </n-radio-group>
                            </n-form-item>
                            <n-form-item label="所属客户" path="customerId">
                                <n-select
                                    v-model:value="formData.customerId"
                                    :disabled="!!id && hasCustomer"
                                    :options="customerOptions"
                                    label-field="customerName"
                                    placeholder="请选择所属客户"
                                    value-field="customerId"
                                />
                            </n-form-item>
                            <n-form-item>
                                <n-space>
                                    <n-button type="primary" @click="onSubmit">提交</n-button>
                                    <n-button @click="changeModalShow(false)">取消</n-button>
                                </n-space>
                            </n-form-item>
                        </n-form>
                    </n-tab-pane>
                    <n-tab-pane name="节点负责人设置">
                        <n-form v-if="nodeDirectorList.length" label-placement="left" label-width="auto">
                            <n-grid cols="12" x-gap="16">
                                <n-form-item-gi
                                    v-for="(item, index) in nodeDirectorList"
                                    :key="index"
                                    :label="item.nodeName"
                                    :span="6"
                                >
                                    <UserSelector
                                        :multiple="false"
                                        v-model:value="item.nodeDirector"
                                        class="w-100%"
                                        key-name="username"
                                        placeholder="请选择节点负责人"
                                    />
                                </n-form-item-gi>
                                <n-form-item-gi :span="12">
                                    <n-space>
                                        <n-button type="primary" @click="onSubmit">提交</n-button>
                                        <n-button @click="changeModalShow(false)">取消</n-button>
                                    </n-space>
                                </n-form-item-gi>
                            </n-grid>
                        </n-form>
                        <n-result v-else class="py-66px" status="404" title="请先选择项目类型" />
                    </n-tab-pane>
                </n-tabs>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import {
    ADD_PROJECT,
    GET_NODE_LIST_BY_PROJECT_TYPE,
    GET_PROJECT_BY_ID,
    UPDATE_PROJECT
} from "@/api/application/project";
import { GET_CUSTOMER_LIST } from "@/api/application/customer";
import { useDicts } from "@/hooks";
import { UserSelector } from "@/components/UserSelector";

let props = defineProps({
    show: { type: Boolean, default: false },
    type: { type: String, default: "add" },
    id: { type: [String, Number] as PropType<any> }
});

let emits = defineEmits(["update:show", "refresh"]);

let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

let changeModalShow = (show: boolean) => {
    emits("update:show", show);
    if (!show) {
        clearFrom();
    }
};

let { dictLibs, getDictLibs, dictValueToLabel } = useDicts();

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {
    projectName: { required: true, message: "请输入项目名称", trigger: ["input", "blur"] },
    projectCode: { required: false, message: "请输入招投标项目编号", trigger: ["input", "blur"] },
    projectType: { required: true, message: "请选择项目类型" },
    customerId: { required: false, message: "请选择所属客户", trigger: ["blur", "change"] }
};

// 表单数据
interface FormDataProps {
    customerId: Nullable<string>;
    projectName: Nullable<string>;
    projectCode: Nullable<string | number>;
    projectType: Nullable<string>;
    nodeDirectorList?: NodeDirectorProps[];
}

let initFormData: FormDataProps = {
    customerId: null,
    projectName: null,
    projectCode: null,
    projectType: null
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
    nodeDirectorList.value = [];
};

let setDictLibs = async () => {
    let dictName = ["project_type"];
    await getDictLibs(dictName);
};

// 编辑时获取详情
watch(
    () => ({ id: props.id, show: props.show }),
    (newVal) => {
        if (newVal.show) getOptions();
        if (newVal.show && newVal.id) getDetail();
    },
    { deep: true }
);

// 是否存在客户
let hasCustomer = ref(false);

// 获取详情
let getDetail = () => {
    GET_PROJECT_BY_ID({ id: props.id }).then((res) => {
        let rowItem: FormDataProps = res.data.data;
        hasCustomer.value = !!rowItem.customerId;
        formData.value = {
            customerId: rowItem.customerId,
            projectName: rowItem.projectName,
            projectCode: rowItem.projectCode,
            projectType: String(rowItem.projectType)
        };
        nodeDirectorList.value = rowItem.nodeDirectorList || [];
    });
};

// 表单选项
let customerOptions = ref([]);

let getOptions = async () => {
    await setDictLibs();
    GET_CUSTOMER_LIST({ current: 1, size: 1000 }).then((res) => {
        customerOptions.value = res.data.data.records;
    });
};

/*
 * 根据项目类型获取流程节点列表
 */
interface NodeDirectorProps {
    nodeId: string | number;
    nodeName: string;
    nodeKey: string;
    formKey: string;
    nodeDirector: Nullable<string>;
}

let nodeDirectorList = ref<NodeDirectorProps[]>([]);

let getNodeDirectorList = () => {
    GET_NODE_LIST_BY_PROJECT_TYPE({ projectType: formData.value.projectType }).then((res) => {
        nodeDirectorList.value = (res.data.data || []).map((item: NodeDirectorProps) => ({
            ...item,
            nodeDirector: null
        }));
    });
};

/*
 * 提交表单
 */
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    if (!props.id) {
        ADD_PROJECT({
            ...formData.value,
            nodeDirectorList: nodeDirectorList.value
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("新增成功");
                changeModalShow(false);
                emits("refresh");
            }
        });
    } else {
        UPDATE_PROJECT({
            projectId: props.id,
            ...formData.value,
            nodeDirectorList: nodeDirectorList.value
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("编辑成功");
                changeModalShow(false);
                emits("refresh");
            }
        });
    }
};
</script>
