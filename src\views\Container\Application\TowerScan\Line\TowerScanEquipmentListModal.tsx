import { computed, defineComponent, reactive, ref, watchEffect } from "vue";
import type { DataTableColumns } from "naive-ui";
import { GET_EQUIPMENT_PAGE_LIST } from "@/api/application/TowerScan";

export default defineComponent({
    name: "TowerScanEquipmentListModal",
    props: {
        show: { type: Boolean, default: false },
        lineId: { type: Number, default: null },
        lineName: { type: String, default: "" }
    },
    emits: ["update:show"],
    setup(props, { emit }) {
        const show = computed({ 
            get: () => props.show, 
            set: (val) => emit("update:show", val) 
        });

        // 设备列表数据
        const equipmentData = ref<any[]>([]);
        const equipmentLoading = ref(false);

        // 分页配置
        const pagination = reactive({
            page: 1,
            pageSize: 10,
            itemCount: 0,
            onChange: (page: number) => {
                pagination.page = page;
                getEquipmentData();
            },
            onUpdatePageSize: (pageSize: number) => {
                pagination.pageSize = pageSize;
                pagination.page = 1;
                getEquipmentData();
            }
        });

        // 表格列配置
        const columns = ref<DataTableColumns<any>>([
            { title: "设备名称", key: "equipmentName", align: "center" },
            { 
                title: "设备编号", 
                key: "equipmentNumber", 
                align: "center",
                render: (row) => {
                    return <n-text type="info">{row.equipmentNumber}</n-text>;
                }
            },
            { 
                title: "设备工序", 
                key: "techniqueNames", 
                align: "center",
                render: (row) => {
                    return <n-text type="info">{row.techniqueNames}</n-text>;
                }
            },
            { title: "设备操作工", key: "operByName", align: "center" },
            { title: "备注", key: "remark", align: "center" }
        ]);

        // 获取设备数据
        const getEquipmentData = () => {
            if (!props.lineId) return;
            
            equipmentLoading.value = true;
            GET_EQUIPMENT_PAGE_LIST({
                current: pagination.page,
                size: pagination.pageSize,
                lineId: props.lineId
            }).then((res) => {
                if (res.data.code === 0) {
                    equipmentData.value = res.data.data.records;
                    pagination.itemCount = res.data.data.total;
                }
                equipmentLoading.value = false;
            }).catch(() => {
                equipmentLoading.value = false;
                window.$message.error("获取设备列表失败");
            });
        };

        // 关闭弹窗
        const onClose = () => {
            show.value = false;
            // 清空数据
            equipmentData.value = [];
            pagination.page = 1;
        };

        // 监听弹窗显示状态，自动加载数据
        watchEffect(() => {
            if (show.value && props.lineId) {
                pagination.page = 1;
                getEquipmentData();
            }
        });

        return () => (
            <n-modal v-model:show={show.value} close-on-esc mask-closable>
                <n-card 
                    title={`${props.lineName} - 设备清单`}
                    class="w-1000px"
                    closable
                    onClose={onClose}
                >
                    <n-data-table
                        columns={columns.value}
                        data={equipmentData.value}
                        loading={equipmentLoading.value}
                        pagination={pagination}
                        single-line={false}
                        bordered
                        remote
                        striped
                    />
                </n-card>
            </n-modal>
        );
    }
}); 