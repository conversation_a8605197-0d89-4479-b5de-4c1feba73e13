import { defineComponent, reactive, ref, watchEffect } from "vue";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import { GET_DRAWBENCH_SCHEDULE_DETAIL_REPORT_LIST } from "@/api/application/plasticMes";
import { RawMaterialUseChangeList, RawMaterialUseList } from "../components";

export default defineComponent({
    name: "PlasticMesDrawbenchProductionScheduleDrawbenchRecord",
    props: {
        scheduleDetailId: { type: String }
    },
    setup(props, { emit }) {
        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { title: "产生时间", key: "createTime", align: "center" },
            {
                title: "用料清单",
                key: "id",
                align: "center",
                width: 120,
                render: (row) => (
                    <n-button type="primary" onClick={() => openListModal(row)}>
                        点击查看
                    </n-button>
                )
            },
            { title: "拉丝工", key: "createByName", align: "center" },
            {
                title: "清单修改记录",
                key: "id",
                align: "center",
                width: 120,
                render: (row) => (
                    <n-button type="primary" onClick={() => openChangeModal(row)}>
                        修改记录
                    </n-button>
                )
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_DRAWBENCH_SCHEDULE_DETAIL_REPORT_LIST({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                scheduleDetailId: props.scheduleDetailId
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        // 清单弹窗
        const listModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const openListModal = (row: RowProps) => {
            listModal.value.show = true;
            listModal.value.configData = {
                scheduleId: row.scheduleId,
                scheduleDetailId: row.scheduleDetailId,
                mixReportId: row.id
            };
        };

        // 变更记录弹窗
        const changeModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const openChangeModal = (row: RowProps) => {
            changeModal.value.show = true;
            changeModal.value.configData = {
                scheduleId: row.scheduleId,
                scheduleDetailId: row.scheduleDetailId,
                mixReportId: row.id
            };
        };

        watchEffect(async () => {
            if (props.scheduleDetailId) getTableData();
        });

        return () => (
            <div>
                <n-data-table
                    columns={tableColumns.value}
                    data={tableData.value}
                    loading={tableLoading.value}
                    pagination={tablePagination}
                    row-key={tableRowKey}
                    single-line={false}
                    bordered
                    remote
                    striped
                    max-height={250}
                    onUpdate:checked-row-keys={changeTableSelection}
                />
                <RawMaterialUseList v-model:show={listModal.value.show} configData={listModal.value.configData} />
                <RawMaterialUseChangeList
                    v-model:show={changeModal.value.show}
                    configData={changeModal.value.configData}
                />
            </div>
        );
    }
}); 