<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-space class="mb">
                <n-button secondary type="primary" @click="openEditModal()">新增</n-button>
                <n-button secondary type="error" @click="onDelete()">批量删除</n-button>
            </n-space>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                children-key="childrenList"
                remote
                striped
                :cascade="false"
                v-model:expanded-row-keys="expandedRowKeys"
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <ReportingCategoryEditModal
            v-model:show="editModal.show"
            :configData="editModal.configData"
            @refresh="getTableData"
        />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from "vue";
import type { DataTableColumns } from "naive-ui";
import { NButton, NText } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import { TableActions } from "@/components/TableActions";
import { DELETE_MATERIAL_CATEGORYS, GET_MATERIAL_CATEGORY_TREE_LIST } from "@/api/application/reporting";
import ReportingCategoryEditModal from "./ReportingCategoryEditModal.vue";

interface RowProps {
    [key: string]: any;
}

onMounted(() => {
    getSearchOptions();
    getTableData();
});

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    {
        label: "分类名称",
        type: "input",
        prop: "categoryName"
    }
]);

let searchOptions = ref<TableSearchbarOptions>({});

let searchForm = ref<TableSearchbarData>({
    categoryName: null
});

let getSearchOptions = () => {};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, changeTableSelection } = useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "分类名称",
        key: "categoryName"
    },
    {
        title: "分类类型",
        key: "parentId",
        align: "center",
        render: (row) => h(NText, { type: "primary" }, () => (String(row.parentId) === "0" ? "顶层大类" : "子类"))
    },
    {
        title: "产品类型",
        key: "productGenre",
        align: "center",
        render: (row) => {
            if (row.productGenre === 1) {
                return "原材料";
            } else if (row.productGenre === 2) {
                return "产成品";
            } else if (row.productGenre === 3) {
                return "半成品";
            } else {
                return "未知";
            }
        }
    },
    {
        title: "排序",
        align: "center",
        key: "showOrder"
    },
    {
        title: "操作",
        key: "action",
        align: "center",
        width: 150,
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    // {
                    //     label: "添加子类",
                    //     tertiary: true,
                    //     type: "success",
                    //     onClick: () => window.$message.warning("敬请期待")
                    // },
                    {
                        label: "编辑",
                        tertiary: true,
                        type: "primary",
                        onClick: () => openEditModal(row)
                    },
                    {
                        label: "删除",
                        tertiary: true,
                        type: "error",
                        onClick: () => onDelete(row.id)
                    }
                ]
            });
        }
    }
]);

let getTableData = async () => {
    await GET_MATERIAL_CATEGORY_TREE_LIST({
        ...searchForm.value
    }).then((res) => {
        tableData.value = res.data.data;
        tableLoading.value = false;
    });
};

// 搜索
let expandedRowKeys = ref<any[]>([]);

let filterChildren = (node: any, categoryName: string) => {
    // 先判断当前节点是否满足条件
    if (node.categoryName.indexOf(categoryName) > -1) return true;
    // 节点没有子项时，返回false
    if (!node.childrenList) return false;
    // 递归筛选子项
    let filteredChildren = node.childrenList.filter((item: any) => filterChildren(item, categoryName));
    // 更新子项
    node.childrenList = filteredChildren;
    // 返回当前节点是否有满足条件的子项
    return filteredChildren.length > 0;
};

let onSearch = async () => {
    await getTableData();
    // 清空expandedRowKeys数组
    expandedRowKeys.value = [];
    if (searchForm.value.categoryName) {
        tableData.value = tableData.value.filter((item) => {
            if (searchForm.value.categoryName) {
                return filterChildren(item, searchForm.value.categoryName);
            }
        });

        // 遍历tableData的每一个节点
        tableData.value.forEach((node: any) => {
            // 递归遍历所有子节点，并将其id添加到expandedRowKeys数组中
            let traverseChildren = (node: any) => {
                expandedRowKeys.value.push(node.id);
                if (node.childrenList) {
                    node.childrenList.forEach((child: any) => {
                        traverseChildren(child);
                    });
                }
            };
            traverseChildren(node);
        });
    }
};

// 新增编辑
let editModal = ref<{ show: boolean; configData: Record<string, any> }>({ show: false, configData: {} });

let openEditModal = (row?: Record<string, any>) => {
    editModal.value.show = true;
    editModal.value.configData = row ? { ...row, categoryType: 1 } : { categoryType: 1 };
};

// 删除
let onDelete = (id?: string | number) => {
    if (!id && tableSelection.value.length < 1) {
        window.$message.error("请选择要删除的数据");
        return false;
    }
    window.$dialog.warning({
        title: "警告",
        content: `确定删除${id ? "该" : "选中"}分类吗？`,
        positiveText: "删除",
        negativeText: "取消",
        onPositiveClick: () => {
            let ids: (string | number)[];
            id ? (ids = [id]) : (ids = tableSelection.value);
            DELETE_MATERIAL_CATEGORYS({ ids: ids.join(",") }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("删除成功");
                    getTableData();
                }
            });
        }
    });
};
</script>
