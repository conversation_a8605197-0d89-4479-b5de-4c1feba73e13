import { defineComponent, h, onMounted, reactive, ref } from "vue";
import {
    TableSearchbar,
    TableSearchbarConfig,
    TableSearchbarData,
    TableSearchbarOptions
} from "@/components/TableSearchbar";
import { useCommonTable, useDicts } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import { TableActions } from "@/components/TableActions";
import InjectionMoldingEdit from "./InjectionMoldingEdit";
import { DELETE_PRODUCTION_MOLDING_TYPE, GET_PRODUCTION_MOLDING_TYPE_LIST } from "@/api/application/plasticMes";

export default defineComponent({
    name: "PlasticMesProductionInjectionMoldingList",
    setup(props, { expose }) {
        // 搜索项
        const searchConfig = ref<TableSearchbarConfig>([
            { label: "注塑产品名称", prop: "moldingName", type: "input", labelWidth: "100" }
        ]);

        const searchOptions = ref<TableSearchbarOptions>({});

        const getSearchOptions = async () => {};

        const searchForm = ref<TableSearchbarData>({
            moldingName: null
        });

        const onSearch = () => {
            tablePagination.page = 1;
            tablePagination.pageSize = 10;
            getTableData();
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            { title: "注塑产品名称", key: "moldingName", align: "center" },
            { title: "注塑产品规格", key: "moldingSpec", align: "center" },
            // { title: "注塑产品品种", key: "moldingVarietyName", align: "center" },
            { title: "单重", key: "singleWeight", align: "center" },
            { title: "重量单位", key: "weightUnitName", align: "center" },
            { title: "数量单位", key: "productUnitName", align: "center" },
            { title: "备注", key: "remark", align: "center" },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 160,
                render(row) {
                    return h(TableActions, {
                        type: "button",
                        buttonActions: [
                            {
                                label: "编辑",
                                tertiary: true,
                                type: "primary",
                                onClick: () => openEditModal(row)
                            },
                            {
                                label: "删除",
                                tertiary: true,
                                type: "error",
                                onClick: () => onDelete(row)
                            }
                        ]
                    });
                }
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_PRODUCTION_MOLDING_TYPE_LIST({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                ...searchForm.value
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        // 新增编辑弹窗
        const editModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const openEditModal = (row?: RowProps) => {
            editModal.value.show = true;
            editModal.value.configData = row ?? {};
        };

        // 删除
        const onDelete = (row: RowProps) => {
            window.$dialog.warning({
                title: "警告",
                content: "确认删除该条数据？该操作不可逆",
                positiveText: "确认删除",
                negativeText: "我再想想",
                onPositiveClick: () => {
                    DELETE_PRODUCTION_MOLDING_TYPE({ ids: row.id }).then((res) => {
                        if (res.data.code === 0) {
                            window.$message.success("删除成功");
                            onSearch();
                        }
                    });
                }
            });
        };

        onMounted(async () => {
            await getSearchOptions();
            getTableData();
        });

        return () => (
            <div>
                <n-card>
                    <TableSearchbar
                        form={searchForm.value}
                        config={searchConfig.value}
                        options={searchOptions.value}
                        onSearch={onSearch}
                    />
                </n-card>
                <n-card class="mt">
                    <n-space class="mb">
                        <n-button type="primary" onClick={() => openEditModal()}>
                            新增注塑产品
                        </n-button>
                    </n-space>
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        pagination={tablePagination}
                        row-key={tableRowKey}
                        single-line={false}
                        bordered
                        remote
                        striped
                        onUpdate:checked-row-keys={changeTableSelection}
                    />
                </n-card>
                <InjectionMoldingEdit
                    v-model:show={editModal.value.show}
                    config-data={editModal.value.configData}
                    onRefresh={getTableData}
                />
            </div>
        );
    }
});
