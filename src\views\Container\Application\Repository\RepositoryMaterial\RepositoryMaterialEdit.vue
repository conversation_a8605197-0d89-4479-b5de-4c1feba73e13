<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card :title="configData.id ? '编辑物资' : '新增物资'" class="w-1000px" closable @close="closeModal">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-grid :cols="12" :x-gap="16">
                        <n-form-item-gi :span="6" label="物资品类" path="categoryId">
                            <n-tree-select
                                v-model:value="formData.categoryId"
                                :options="categoryIdOptions"
                                children-field="childrenList"
                                class="w-100%"
                                clearable
                                filterable
                                key-field="id"
                                label-field="categoryName"
                                placeholder="请选择物资品类"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="物资编号" path="goodsCode">
                            <n-input v-model:value="formData.goodsCode" class="w-100%" placeholder="请输入物资编号" />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="物资名称" path="goodsName">
                            <n-input v-model:value="formData.goodsName" class="w-100%" placeholder="请输入物资名称" />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="物资规格" path="goodsSpec">
                            <n-input v-model:value="formData.goodsSpec" class="w-100%" placeholder="请输入物资规格" />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="单位" path="unit">
                            <n-select
                                v-model:value="formData.unit"
                                :options="dictLibs['common_units']"
                                class="w-100%"
                                clearable
                                placeholder="请选择单位"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="辅助单位" path="auxiliaryUnit">
                            <n-select
                                v-model:value="formData.auxiliaryUnit"
                                :options="dictLibs['common_units']"
                                class="w-100%"
                                clearable
                                placeholder="请选择辅助单位"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="备注" path="remark">
                            <n-input v-model:value="formData.remark" class="w-100%" placeholder="请输入备注" />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12">
                            <n-space>
                                <n-button type="primary" @click="onSubmit">提交</n-button>
                                <n-button @click="closeModal">取消</n-button>
                            </n-space>
                        </n-form-item-gi>
                    </n-grid>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref, watchEffect } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import {
    ADD_REPOSITORY_GOODS,
    GET_REPOSITORY_CATEGORY_LIST,
    GET_REPOSITORY_GOODS_DETAIL,
    UPDATE_REPOSITORY_GOODS
} from "@/api/application/repository";
import { useDicts } from "@/hooks";

let props = withDefaults(defineProps<{ show: boolean; configData: UnKnownObject }>(), { show: () => false });

let emits = defineEmits(["update:show", "refresh"]);

let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

let { dictLibs, getDictLibs } = useDicts();

let changeModalShow = (show: boolean) => emits("update:show", show);

watchEffect(async () => {
    if (props.show) {
        await setDictLibs();
        await getOptions();
    }
});

// 获取选项
let categoryIdOptions = ref<any[]>([]);

let setDictLibs = async () => {
    let dictName = ["common_units"];
    await getDictLibs(dictName);
};

let getOptions = async () => {
    await GET_REPOSITORY_CATEGORY_LIST({}).then((res) => {
        categoryIdOptions.value = res.data.data || [];
    });
};

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = computed(() => {
    return {
        categoryId: [{ required: true, message: "请选择物资品类", trigger: ["blur", "change"] }],
        goodsCode: [{ required: true, message: "请输入物资编号", trigger: ["input", "blur"] }],
        goodsName: [{ required: true, message: "请输入物资名称", trigger: ["input", "blur"] }],
        goodsSpec: [{ required: false, message: "请输入物资规格", trigger: ["input", "blur"] }],
        unit: [{ required: true, message: "请选择物料单位", trigger: ["blur", "change"] }],
        auxiliaryUnit: [{ required: true, message: "请选择辅助单位", trigger: ["blur", "change"] }],
        remark: [{ required: false, message: "请输入备注", trigger: ["input", "blur"] }]
    };
});

// 表单数据
interface FormDataProps {
    [key: string]: any;
}

let initFormData: FormDataProps = {
    categoryId: null,
    goodsCode: null,
    goodsName: null,
    goodsSpec: null,
    unit: null,
    auxiliaryUnit: null,
    remark: null
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 获取详情
let getDetail = () => {
    GET_REPOSITORY_GOODS_DETAIL({ id: props.configData.id }).then((res) => {
        if (res.data.code === 0) {
            formData.value = {
                categoryId: res.data.data.categoryId,
                goodsCode: res.data.data.goodsCode,
                goodsName: res.data.data.goodsName,
                goodsSpec: res.data.data.goodsSpec,
                unit: String(res.data.data.unit),
                auxiliaryUnit: String(res.data.data.auxiliaryUnit),
                remark: res.data.data.remark
            };
        }
    });
};

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    changeModalShow(false);
};

// 监听
watchEffect(() => {
    if (props.show && props.configData.id) {
        getDetail();
    }
});

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;

    if (props.configData.id) {
        await UPDATE_REPOSITORY_GOODS({
            ...formData.value,
            id: props.configData.id
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("编辑成功");
                closeModal();
                emits("refresh");
            }
        });
    } else {
        await ADD_REPOSITORY_GOODS({
            ...formData.value
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("新增成功");
                closeModal();
                emits("refresh");
            }
        });
    }
};
</script>
