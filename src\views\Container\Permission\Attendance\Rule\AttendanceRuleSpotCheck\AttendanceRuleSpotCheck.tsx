import { defineComponent, reactive, ref, watchEffect } from "vue";
import type { DataTableColumns } from "naive-ui";
import { DELETE_SPOT_CHECK_ATTENDANCE_CONFIGURATION, GET_SPOT_CHECK_ATTENDANCE_CONFIGURATION } from "@/api/permission";
import { useCommonTable } from "@/hooks";
import { TableActions } from "@/components/TableActions";
import AttendanceRuleConfigEdit from "@/views/Container/Permission/Attendance/Rule/AttendanceRuleConfig/AttendanceRuleConfigEdit";
import AttendanceRuleSpotCheckEdit from "@/views/Container/Permission/Attendance/Rule/AttendanceRuleSpotCheck/AttendanceRuleSpotCheckEdit";
import { DELETE_HANDWRITTEN_ACCOUNT } from "@/api/application/plasticMes";
import AttendanceRuleSpotCheckUsers from "@/views/Container/Permission/Attendance/Rule/AttendanceRuleSpotCheck/AttendanceRuleSpotCheckUsers";

export default defineComponent({
    name: "AttendanceRuleSpotCheck",
    props: {
        corpId: { type: String as PropType<string | null>, default: null }
    },
    setup(props, { emit }) {
        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { title: "抽查规则名称", key: "checkName", align: "center" },
            {
                title: "抽查名单",
                key: "id",
                align: "center",
                width: 120,
                render: (row) => (
                    <n-button type="primary" onClick={() => openUsersModal(row)}>
                        点击查看
                    </n-button>
                )
            },
            { title: "抽查开始时间", key: "startTime", align: "center" },
            { title: "抽查结束时间", key: "endTime", align: "center" },
            { title: "抽查次数", key: "count", align: "center" },
            { title: "允许超出时间", key: "allowTime", align: "center" },
            {
                title: "启用状态",
                key: "isStatus",
                align: "center",
                width: 100,
                render: (row) => {
                    if (row.isStatus === 1) {
                        return <n-text type="success">启用</n-text>;
                    } else if (row.isStatus === 0) {
                        return <n-text type="error">禁用</n-text>;
                    } else {
                        return <n-text type="info">/</n-text>;
                    }
                }
            },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 160,
                render: (row) => {
                    return (
                        <TableActions
                            type="button"
                            buttonActions={[
                                { label: "编辑", tertiary: true, type: "primary", onClick: () => openEditModal(row) },
                                { label: "删除", tertiary: true, type: "error", onClick: () => onDelete(row) }
                            ]}
                        />
                    );
                }
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_SPOT_CHECK_ATTENDANCE_CONFIGURATION({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                corpId: props.corpId
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        // 新增功能
        const editModal = ref<{ show: boolean; configData: RowProps }>({ show: false, configData: {} });

        const openEditModal = (row?: RowProps) => {
            editModal.value.show = true;
            editModal.value.configData = {
                ...(row ?? {}),
                corpId: props.corpId
            };
        };

        // 名单弹窗
        const usersModal = ref<{ show: boolean; configData: RowProps }>({ show: false, configData: {} });

        const openUsersModal = (row?: RowProps) => {
            usersModal.value.show = true;
            usersModal.value.configData = row ?? [];
        };

        // 删除功能
        const onDelete = (row: RowProps) => {
            window.$dialog.warning({
                title: "警告",
                content: "确认删除该条数据？该操作不可逆",
                positiveText: "确认删除",
                negativeText: "我再想想",
                onPositiveClick: () => {
                    DELETE_SPOT_CHECK_ATTENDANCE_CONFIGURATION({ id: row.id }).then((res) => {
                        if (res.data.code === 0) {
                            window.$message.success("删除成功");
                            getTableData();
                        }
                    });
                }
            });
        };

        watchEffect(() => {
            if (props.corpId) {
                getTableData();
            }
        });

        return () => (
            <div>
                <n-space class="mb">
                    <n-button type="primary" onClick={() => openEditModal()}>
                        新增抽查规则
                    </n-button>
                </n-space>
                <n-data-table
                    columns={tableColumns.value}
                    data={tableData.value}
                    loading={tableLoading.value}
                    pagination={tablePagination}
                    row-key={tableRowKey}
                    single-line={false}
                    bordered
                    remote
                    striped
                    onUpdate:checked-row-keys={changeTableSelection}
                />
                <AttendanceRuleSpotCheckEdit
                    v-model:show={editModal.value.show}
                    v-model:configData={editModal.value.configData}
                    onRefresh={getTableData}
                />
                <AttendanceRuleSpotCheckUsers
                    v-model:show={usersModal.value.show}
                    v-model:configData={usersModal.value.configData}
                    onRefresh={getTableData}
                />
            </div>
        );
    }
});
