import { defineComponent, h, onMounted, reactive, ref } from "vue";
import {
    TableSearchbar,
    type TableSearchbarConfig,
    type TableSearchbarData,
    type TableSearchbarOptions
} from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import { TableActions } from "@/components/TableActions";
import { GET_IRON_PROJECT_PAGE_LIST, DELETE_IRON_PROJECT } from "@/api/application/TowerScan";
import TowerScanEngineeringEdit from "./TowerScanEngineeringEdit";
import TowerScanEngineeringDetail from "./TowerScanEngineeringDetail";

export default defineComponent({
    name: "TowerScanEngineeringList",
    setup(props) {
        // 搜索项
        const searchConfig = ref<TableSearchbarConfig>([
            { prop: "projectName", label: "工程名称", type: "input" },
            { prop: "contractNumber", label: "合同号", type: "input" },
            { label: "工程状态", prop: "projectStatus", type: "select" }
        ]);

        const searchOptions = ref<TableSearchbarOptions>({
            projectStatus: [
                { label: "未开始", value: 0 },
                { label: "进行中", value: 1 },
                { label: "已完成", value: 2 }
            ]
        });

        const getSearchOptions = async () => {};

        const searchForm = ref<TableSearchbarData>({
            projectName: null,
            contractNumber: null,
            projectStatus: null
        });

        const onSearch = () => {
            tablePagination.page = 1;
            tablePagination.pageSize = 10;
            getTableData();
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            { title: "工程名称", key: "projectName", align: "center" },
            { title: "工程简称", key: "projectAs", align: "center" },
            { title: "合同号", key: "contractNumber", align: "center" },
            {
                title: "工程状态",
                key: "projectStatus",
                align: "center",
                render: (row) => {
                    switch (row.projectStatus) {
                        case 0:
                            return <n-text type="error">未开始</n-text>;
                        case 1:
                            return <n-text type="info">进行中</n-text>;
                        case 2:
                            return <n-text type="success">已完成</n-text>;
                        default:
                            return <n-text>/</n-text>;
                    }
                }
            },
            { title: "材料表号", key: "materialNo", align: "center", render: (row) => row.materialNo || "/" },
            { title: "电压等级（KV）", key: "powerLevel", align: "center" },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 200,
                render: (row) => {
                    return (
                        <TableActions
                            type="button"
                            buttonActions={[
                                {
                                    label: "查看",
                                    tertiary: true,
                                    type: "primary",
                                    onClick: () => openDetailModal(row)
                                },
                                {
                                    label: "编辑",
                                    tertiary: true,
                                    type: "warning",
                                    onClick: () => openEditModal(row)
                                },
                                {
                                    label: "删除",
                                    tertiary: true,
                                    type: "error",
                                    onClick: () => handleDelete(row.id)
                                }
                            ]}
                        />
                    );
                }
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_IRON_PROJECT_PAGE_LIST({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                ...searchForm.value
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        // 删除功能
        const handleDelete = (projectId: string) => {
            window.$dialog.warning({
                title: "删除确认",
                content: "确定要删除这个工程吗？删除后不可恢复。",
                positiveText: "确定",
                negativeText: "取消",
                onPositiveClick: () => {
                    deleteProjects(projectId);
                }
            });
        };

        // 批量删除
        const handleBatchDelete = () => {
            if (tableSelection.value.length === 0) {
                window.$message.warning("请先选择要删除的工程");
                return;
            }

            window.$dialog.warning({
                title: "批量删除确认",
                content: `确定要删除选中的 ${tableSelection.value.length} 个工程吗？删除后不可恢复。`,
                positiveText: "确定",
                negativeText: "取消",
                onPositiveClick: () => {
                    const projectIds = tableSelection.value.join(",");
                    deleteProjects(projectIds);
                }
            });
        };

        // 执行删除
        const deleteProjects = async (projectIds: string) => {
            try {
                const res = await DELETE_IRON_PROJECT({ projectIds });
                if (res.data.code === 0) {
                    window.$message.success("删除成功");
                    // 清空选择
                    changeTableSelection([]);
                    // 刷新列表
                    getTableData();
                } else {
                    window.$message.error(res.data.message || "删除失败");
                }
            } catch (error) {
                window.$message.error("删除失败");
                console.error("删除工程失败:", error);
            }
        };

        // 新增编辑弹窗
        const editModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const openEditModal = (row?: RowProps) => {
            editModal.value = { show: true, configData: row ?? {} };
        };

        // 详情弹窗
        const detailModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const openDetailModal = (row: RowProps) => {
            detailModal.value = { show: true, configData: row };
        };

        onMounted(async () => {
            await getSearchOptions();
            getTableData();
        });

        return () => (
            <div class="tower-scan-engineering-list">
                <n-card>
                    <TableSearchbar
                        form={searchForm.value}
                        config={searchConfig.value}
                        options={searchOptions.value}
                        onSearch={onSearch}
                    />
                </n-card>
                <n-card class="mt">
                    <n-space class="mb">
                        <n-button type="primary" onClick={() => openEditModal()}>
                            新增工程
                        </n-button>
                        <n-button type="error" onClick={handleBatchDelete} disabled={tableSelection.value.length === 0}>
                            批量删除
                        </n-button>
                    </n-space>
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        pagination={tablePagination}
                        row-key={tableRowKey}
                        single-line={false}
                        bordered
                        remote
                        striped
                        onUpdate:checked-row-keys={changeTableSelection}
                    />
                </n-card>
                <TowerScanEngineeringEdit
                    v-model:show={editModal.value.show}
                    config-data={editModal.value.configData}
                    onRefresh={getTableData}
                />
                <TowerScanEngineeringDetail
                    v-model:show={detailModal.value.show}
                    config-data={detailModal.value.configData}
                    onRefresh={getTableData}
                />
            </div>
        );
    }
});
