<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card
                class="w-800px"
                closable
                content-style="padding:0"
                title="超额校验提醒"
                @close="changeModalShow(false)"
            >
                <div class="h-80vh">
                    <n-scrollbar trigger="hover">
                        <div class="p-20px pt-0!">
                            <n-space class="mb">
                                <n-button secondary type="success" @click="onSubmit">保存填写</n-button>
                            </n-space>
                            <n-tabs animated type="bar">
                                <n-tab-pane name="原材料清单">
                                    <n-collapse>
                                        <n-collapse-item
                                            v-for="(item, index) in configData.manufactureFormulaSpecAmountList"
                                            :name="index"
                                        >
                                            <template #header>
                                                <n-text
                                                    :type="
                                                        item.specAmountList.some((i:any) => i.checkFlag !== 1)
                                                            ? 'error'
                                                            : 'default'
                                                    "
                                                >
                                                    {{ item.materialName }}
                                                </n-text>
                                            </template>
                                            <n-data-table
                                                :columns="tableColumns"
                                                :data="item.specAmountList"
                                                :single-line="false"
                                                bordered
                                                remote
                                                striped
                                            />
                                        </n-collapse-item>
                                    </n-collapse>
                                </n-tab-pane>
                                <n-tab-pane name="独立原材料清单">
                                    <n-collapse>
                                        <n-collapse-item
                                            v-for="(item, index) in configData.manufactureSpecAmountList"
                                            :name="index"
                                        >
                                            <template #header>
                                                <n-text
                                                    :type="
                                                        item.specAmountList.some((i:any) => i.checkFlag !== 1)
                                                            ? 'error'
                                                            : 'default'
                                                    "
                                                >
                                                    {{ item.materialName }}
                                                </n-text>
                                            </template>
                                            <n-data-table
                                                :columns="tableColumns"
                                                :data="item.specAmountList"
                                                :single-line="false"
                                                bordered
                                                remote
                                                striped
                                            />
                                        </n-collapse-item>
                                    </n-collapse>
                                </n-tab-pane>
                                <n-tab-pane name="半成品清单">
                                    <n-data-table
                                        :columns="semiTableColumns"
                                        :data="configData.manufactueSemiAmountList"
                                        :single-line="false"
                                        bordered
                                        remote
                                        striped
                                    />
                                </n-tab-pane>
                            </n-tabs>
                        </div>
                    </n-scrollbar>
                </div>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { computed, h, onMounted, ref } from "vue";
import type { DataTableColumns } from "naive-ui";
import { NButton, NInput, NInputGroup, NInputGroupLabel, NText } from "naive-ui";
import { SAVE_DAILY_SPEC_AMOUNT_LIST } from "@/api/application/reporting";
import { UnitSelector } from "@/views/Container/Application/Reporting/components";
import { useDicts } from "@/hooks";

let props = withDefaults(defineProps<{ show: boolean; configData: any }>(), { show: () => false });

let emits = defineEmits(["update:show", "update:configData", "refresh"]);

let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

let configData = computed({ get: () => props.configData, set: (val) => emits("update:configData", val) });

let changeModalShow = (show: boolean) => {
    emits("update:show", show);
};

onMounted(async () => {
    await setDictLibs();
});

// 字典操作
let { dictLibs, getDictLibs } = useDicts();

let setDictLibs = async () => {
    let dictName = ["common_units"];
    await getDictLibs(dictName);
};

interface RowProps {
    [key: string]: any;
}

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        title: "规格型号",
        align: "center",
        key: "spec",
        render: (row) => {
            return `${row.materialName} - ${row.spec}`;
        }
    },
    { title: "定额用量", align: "center", key: "amount" },
    {
        title: "实际数量",
        align: "center",
        key: "trueAmount",
        render: (row) => {
            return h(NInputGroup, {}, () => [
                h(NInput, { value: row.trueAmount, onUpdateValue: (v) => (row.trueAmount = v) }),
                h(NInputGroupLabel, {}, () => [
                    h(UnitSelector, {
                        type: "text",
                        value: row.amountUnit,
                        options: dictLibs["common_units"] ?? []
                    })
                ])
            ]);
        }
    },
    {
        title: "填写超额备注",
        key: "remark",
        align: "center",
        render: (row) => {
            if (row.checkFlag === 1) {
                return h(NText, { type: "success" }, () => "未超额");
            } else {
                return h(NInput, {
                    status: row.remark ? undefined : "error",
                    value: row.remark,
                    placeholder: "请填写超额原因",
                    onUpdateValue: (v) => (row.remark = v)
                });
            }
        }
    }
]);

let semiTableColumns = ref<DataTableColumns<RowProps>>([
    { title: "规格型号", align: "center", key: "semiProductModel" },
    { title: "定额用量", align: "center", key: "amount" },
    {
        title: "实际数量",
        align: "center",
        key: "trueAmount",
        render: (row) => {
            return h(NInputGroup, {}, () => [
                h(NInput, { value: row.trueAmount, onUpdateValue: (v) => (row.trueAmount = v) }),
                h(NInputGroupLabel, {}, () => [
                    h(UnitSelector, {
                        type: "text",
                        value: row.amountUnit,
                        options: dictLibs["common_units"] ?? []
                    })
                ])
            ]);
        }
    }
    // {
    //     title: "填写超额备注",
    //     key: "remark",
    //     align: "center",
    //     render: (row) => {
    //         if (row.checkFlag === 1) {
    //             return h(NText, { type: "success" }, () => "未超额");
    //         } else {
    //             return h(NInput, {
    //                 status: row.remark ? undefined : "error",
    //                 value: row.remark,
    //                 placeholder: "请填写超额原因",
    //                 onUpdateValue: (v) => (row.remark = v)
    //             });
    //         }
    //     }
    // }
]);

// 提交
let onSubmit = async () => {
    let manufactureFormulaSpecAmountListCheck = configData.value.manufactureFormulaSpecAmountList.some((item: any) =>
        item.specAmountList.some((citem: any) => citem.checkFlag !== 1 && !citem.remark)
    );
    let manufactureSpecAmountListCheck = configData.value.manufactureSpecAmountList.some((item: any) =>
        item.specAmountList.some((citem: any) => citem.checkFlag !== 1 && !citem.remark)
    );
    if (manufactureFormulaSpecAmountListCheck || manufactureSpecAmountListCheck) {
        return window.$message.error("请填写超额备注");
    }
    SAVE_DAILY_SPEC_AMOUNT_LIST({ ...configData.value }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("保存成功");
            changeModalShow(false);
            emits("refresh");
        }
    });
};
</script>
