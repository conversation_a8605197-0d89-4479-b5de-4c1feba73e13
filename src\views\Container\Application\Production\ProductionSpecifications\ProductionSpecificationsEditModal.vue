<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card :title="id ? '编辑规格型号' : '新增规格型号'" class="w-800px" closable @close="closeModal">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-grid cols="12" x-gap="16">
                        <n-form-item-gi :span="6" label="规格名称" path="specification">
                            <n-input
                                v-model:value="formData.specification"
                                class="w-100%"
                                clearable
                                placeholder="请输入规格名称"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="外径" path="externalDiameter">
                            <n-input
                                v-model:value="formData.externalDiameter"
                                class="w-100%"
                                clearable
                                placeholder="请输入外径"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="壁厚" path="wallThickness">
                            <n-input
                                v-model:value="formData.wallThickness"
                                class="w-100%"
                                clearable
                                placeholder="请输入壁厚"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="米重" path="meterWeight">
                            <n-input
                                v-model:value="formData.meterWeight"
                                class="w-100%"
                                clearable
                                placeholder="请输入米重"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="最小米重" path="minimumMeterWeight">
                            <n-input
                                v-model:value="formData.minimumMeterWeight"
                                class="w-100%"
                                clearable
                                placeholder="请输入最小米重"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="产品类型" path="type">
                            <n-select
                                v-model:value="formData.type"
                                :options="dictLibs.product_type"
                                placeholder="请选择产品类型"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12">
                            <n-space>
                                <n-button type="primary" @click="onSubmit">提交</n-button>
                                <n-button @click="closeModal">取消</n-button>
                            </n-space>
                        </n-form-item-gi>
                    </n-grid>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { useStoreUser } from "@/store";
import { ADD_PRODUCTION_TYPE, GET_PRODUCTION_TYPE_BY_ID, UPDATE_PRODUCTION_TYPE } from "@/api/application/production";
import { useDicts } from "@/hooks";

let storeUser = useStoreUser();

let props = defineProps({
    show: { type: Boolean, default: false },
    id: { type: [String, Number] as PropType<Nullable<String | Number>> }
});

let emits = defineEmits(["update:show", "refresh"]);

// 字典操作
let { dictLibs, getDictLibs, dictValueToLabel } = useDicts();

let setDictLibs = async () => {
    let dictName = ["product_type"];
    await getDictLibs(dictName);
};

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {
    specification: [{ required: true, message: "请输入规格名称", trigger: ["input", "blur"] }],
    externalDiameter: [{ required: true, message: "请输入外径", type: "number", trigger: ["input", "blur"] }],
    wallThickness: [{ required: true, message: "请输入壁厚", type: "number", trigger: ["input", "blur"] }],
    meterWeight: [{ required: true, message: "请输入米重", type: "number", trigger: ["input", "blur"] }],
    minimumMeterWeight: [{ required: true, type: "number", message: "请输入最小米重", trigger: ["input", "blur"] }],
    type: [{ required: true, message: "请选择产品类型", trigger: ["blur", "change"] }]
};

// 表单数据
interface FormDataProps {
    specification: Nullable<string>;
    externalDiameter: Nullable<string | number>;
    wallThickness: Nullable<string | number>;
    meterWeight: Nullable<string | number>;
    minimumMeterWeight: Nullable<string | number>;
}

let initFormData: FormDataProps = {
    specification: null,
    externalDiameter: null,
    wallThickness: null,
    meterWeight: null,
    minimumMeterWeight: null
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 编辑时获取详情
watch(
    () => ({ id: props.id, show: props.show }),
    (newVal) => {
        if (newVal.show) getOptions();
        if (newVal.show && newVal.id) getDetail();
    },
    { deep: true }
);

// 获取详情
let getDetail = () => {
    GET_PRODUCTION_TYPE_BY_ID({ id: props.id }).then((res) => {
        if (res.data.code === 0) {
            let resData = res.data.data;
            formData.value = {
                specification: resData.specification,
                externalDiameter: resData.externalDiameter,
                wallThickness: resData.wallThickness,
                meterWeight: resData.meterWeight,
                minimumMeterWeight: resData.minimumMeterWeight,
                type: String(resData.type)
            };
        }
    });
};

// 表单选项
let categoryOptions = ref<any[]>([]);

let getOptions = async () => {
    await setDictLibs();
};

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    emits("update:show", false);
};

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    if (!props.id) {
        ADD_PRODUCTION_TYPE({
            ...formData.value
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("新增成功");
                closeModal();
                emits("refresh");
            }
        });
    } else {
        UPDATE_PRODUCTION_TYPE({
            id: props.id,
            ...formData.value
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("修改成功");
                closeModal();
                emits("refresh");
            }
        });
    }
};
</script>
