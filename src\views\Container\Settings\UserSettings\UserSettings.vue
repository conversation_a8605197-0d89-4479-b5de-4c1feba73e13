<template>
    <div class="user-settings">
        <n-card hoverable>
            <div class="flex h-100%">
                <n-menu
                    v-model:value="menuActive"
                    :indent="20"
                    :options="menuOptions"
                    class="flex-fixed-220 border-r-1px border-[#E5E5E5]"
                    mode="vertical"
                    @update:value="handleUpdateMenu"
                />
                <basic-info v-if="menuActive === 'basicInfoSettings'" />
                <security v-if="menuActive === 'securitySettings'" />
                <n-result
                    v-if="menuActive === 'bindingSettings'"
                    class="w-100% pt-100px pb-100px"
                    description="该功能暂未开放"
                    size="large"
                    status="403"
                    title="非常抱歉"
                />
            </div>
        </n-card>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { BasicInfo, Security } from "./components";

// 当前选中
let menuActive = ref("basicInfoSettings");

// 菜单点击跳转
let handleUpdateMenu = (key: string) => {};

// 菜单内容
let menuOptions = ref<any[]>([
    { label: "基本信息设置", key: "basicInfoSettings" },
    { label: "安全设置", key: "securitySettings" },
    { label: "绑定设置", key: "bindingSettings" }
]);
</script>

<style lang="scss" scoped>
.user-settings {
    ::v-deep(.n-card) {
        .n-card__content {
            padding: 20px 0;

            .n-menu {
                .n-menu-item {
                    &:first-child {
                        margin-top: 0;
                    }

                    .n-menu-item-content {
                        &:before {
                            left: 0;
                            right: 0;
                        }
                    }

                    .n-menu-item-content--selected {
                        &:before {
                            border-right: 2px solid var(--n-item-text-color-active);
                        }
                    }
                }
            }
        }
    }
}
</style>
