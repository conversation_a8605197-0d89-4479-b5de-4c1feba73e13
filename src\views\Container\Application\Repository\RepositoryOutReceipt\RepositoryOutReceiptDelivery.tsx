import { computed, defineComponent, h, ref, watchEffect } from "vue";
import { type DataTableColumns, type FormInst, NInput, NSelect, NTooltip } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { TableActions } from "@/components/TableActions";
import {
    ADD_REPOSITORY_OUT_RECEIPT,
    GET_REPOSITORY_GOODS_LIST,
    GET_REPOSITORY_OUT_RECEIPT_DETAIL
} from "@/api/application/repository";
import { useDicts } from "@/hooks";

export default defineComponent({
    name: "RepositoryOutReceiptDelivery",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        // 字典操作
        let { dictLibs, getDictLibs } = useDicts();

        let setDictLibs = async () => {
            let dictName = ["common_units"];
            await getDictLibs(dictName);
        };

        // 表单数据
        interface FormDataProps {
            [key: string]: any;
        }

        const formRef = ref<FormInst | null>(null);

        const getFormOptions = async () => {};

        const initFormData: FormDataProps = {
            deliveryTime: null,
            receiveUser: null,
            contactPhone: null,
            receiveAddress: null,
            deliveryWay: 1,
            driverName: null,
            driverNo: null,
            driverPhone: null,
            logisticsCompany: null,
            logisticsNo: null,
            remark: null
        };

        const formRules = computed(() => ({
            deliveryTime: [{ required: true, message: "请选择发货时间", trigger: ["blur", "change"] }],
            receiveUser: [{ required: true, message: "请输入收货人姓名", trigger: ["input", "blur"] }],
            contactPhone: [{ required: true, message: "请输入联系方式", trigger: ["input", "blur"] }],
            receiveAddress: [{ required: true, message: "请输入收货地址", trigger: ["input", "blur"] }],
            deliveryWay: [{ required: true, message: "请选择发货方式", trigger: ["blur", "change"], type: "number" }],
            driverName: [{ required: true, message: "请输入司机姓名", trigger: ["input", "blur"] }],
            driverNo: [{ required: true, message: "请输入车牌号", trigger: ["input", "blur"] }],
            driverPhone: [{ required: true, message: "请输入司机电话", trigger: ["input", "blur"] }],
            logisticsCompany: [{ required: true, message: "请输入物流公司", trigger: ["input", "blur"] }],
            logisticsNo: [{ required: true, message: "请输入物流单号", trigger: ["input", "blur"] }],
            remark: [{ required: false, message: "请输入备注", trigger: ["input", "blur"] }]
        }));

        const formData = ref(cloneDeep(initFormData));

        const clearForm = () => {
            formData.value = cloneDeep(initFormData);
        };

        // 表单数据
        interface RowProps {
            [key: string]: any;
        }

        const goodsIdOptions = ref<any[]>([]);

        const getTableOptions = async () => {
            GET_REPOSITORY_GOODS_LIST({ current: 1, size: 9999, hasCapacity: 1 }).then((res) => {
                goodsIdOptions.value = res.data.data.records ?? [];
            });
        };

        const tableData = ref<RowProps[]>([]);

        const tableColumns = ref<DataTableColumns<RowProps>>([
            {
                title: "物料名称",
                key: "goodsName",
                align: "center",
                render: (row) => {
                    return h(NSelect, {
                        options: goodsIdOptions.value,
                        clearable: true,
                        filterable: true,
                        placeholder: "请选择物料名称",
                        labelField: "goodsName",
                        valueField: "id",
                        value: row.goodsId,
                        renderOption: ({ node, option }: any) => {
                            return h(NTooltip, null, { trigger: () => node, default: () => option.goodsName });
                        },
                        onUpdateValue: (v, o: any) => {
                            row.goodsId = v;
                            row.unit = o?.unit;
                            row.goodsSpec = o?.goodsSpec;
                            row.storageCapacity = o?.storageCapacity;
                        }
                    });
                }
            },
            {
                title: "出库数量",
                key: "applyQuality",
                align: "center",
                render: (row) => {
                    return h(NInput, {
                        value: row.applyQuality,
                        onUpdateValue: (v) => {
                            row.applyQuality = v;
                        }
                    });
                }
            },
            {
                title: "物品单位",
                key: "unit",
                align: "center",
                render: (row) => {
                    let object = (dictLibs["common_units"] || []).find((i: any) => i.value === String(row.unit));
                    return object?.label ?? "/";
                }
            },
            {
                title: "规格型号",
                key: "goodsSpec",
                align: "center",
                render: (row) => row.goodsSpec ?? "/"
            },
            {
                title: "库存数量",
                key: "storageCapacity",
                align: "center",
                render: (row) => row.storageCapacity ?? "/"
            },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 80,
                render(row, index) {
                    return h(TableActions, {
                        type: "button",
                        buttonActions: [
                            {
                                label: "删除",
                                tertiary: true,
                                type: "error",
                                onClick: () => deleteItem(row, index)
                            }
                        ]
                    });
                }
            }
        ]);

        // 可编辑表单配置
        const tableItem: RowProps = {
            goodsId: null,
            applyQuality: null
        };

        const addTableItem = () => {
            tableData.value.push(cloneDeep(tableItem));
        };

        const deleteItem = (row: RowProps, index: number) => {
            if (row.id) {
                tableData.value.forEach((citem, cindex) => {
                    if (row.id === citem.id) citem.delFlag = 1;
                });
            } else tableData.value.splice(index, 1);
        };

        // 获取详情
        let getDetail = () => {
            GET_REPOSITORY_OUT_RECEIPT_DETAIL({ id: props.configData.id }).then((res) => {
                if (res.data.code === 0) {
                    formData.value = {
                        deliveryTime: res.data.data.deliveryTime,
                        receiveUser: res.data.data.receiveUser,
                        contactPhone: res.data.data.contactPhone,
                        receiveAddress: res.data.data.receiveAddress,
                        deliveryWay: res.data.data.deliveryWay,
                        driverName: res.data.data.driverName,
                        driverNo: res.data.data.driverNo,
                        driverPhone: res.data.data.driverPhone,
                        logisticsCompany: res.data.data.logisticsCompany,
                        logisticsNo: res.data.data.logisticsNo,
                        remark: res.data.data.remark
                    };
                    tableData.value = res.data.data.recordItemList ?? [];
                }
            });
        };

        watchEffect(async () => {
            if (show.value) {
                await getFormOptions();
                await getTableOptions();
                await setDictLibs();
                if (props.configData.id) getDetail();
            }
        });

        const onClose = () => {
            clearForm();
            changeModalShow(false);
            emit("refresh");
        };

        const onSubmit = async () => {
            let validateError = await formRef.value?.validate((errors) => !!errors);
            if (validateError) return false;

            if (props.configData.id) {
            } else {
                await ADD_REPOSITORY_OUT_RECEIPT({
                    ...formData.value,
                    outType: 2,
                    recordItemList: tableData.value ?? []
                }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("新增成功");
                        onClose();
                    }
                });
            }
        };

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card
                    title={props.configData.id ? "编辑发货单" : "新增发货单"}
                    class="w-1200px"
                    closable
                    onClose={onClose}
                >
                    <n-form
                        ref={formRef}
                        model={formData.value}
                        rules={formRules.value}
                        label-placement="left"
                        label-width="auto"
                    >
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi span={6} label="发货时间" path="deliveryTime">
                                <n-date-picker
                                    v-model:formatted-value={formData.value.deliveryTime}
                                    class="w-100%"
                                    clearable
                                    placeholder="请选择发货时间"
                                    type="date"
                                    value-format="yyyy-MM-dd"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="收货人姓名" path="receiveUser">
                                <n-input
                                    v-model:value={formData.value.receiveUser}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入收货人姓名"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="联系方式" path="contactPhone">
                                <n-input
                                    v-model:value={formData.value.contactPhone}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入联系方式"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="收货地址" path="receiveAddress">
                                <n-input
                                    v-model:value={formData.value.receiveAddress}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入收货地址"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={12} label="发货方式" path="deliveryWay">
                                <n-radio-group v-model:value={formData.value.deliveryWay}>
                                    <n-space>
                                        <n-radio label="配送" value={1} />
                                        <n-radio label="物流" value={2} />
                                    </n-space>
                                </n-radio-group>
                            </n-form-item-gi>
                            {formData.value.deliveryWay === 1 && (
                                <>
                                    <n-form-item-gi span={4} label="司机姓名" path="driverName">
                                        <n-input
                                            v-model:value={formData.value.driverName}
                                            class="w-100%"
                                            clearable
                                            placeholder="请输入司机姓名"
                                        />
                                    </n-form-item-gi>
                                    <n-form-item-gi span={4} label="车牌号" path="driverNo">
                                        <n-input
                                            v-model:value={formData.value.driverNo}
                                            class="w-100%"
                                            clearable
                                            placeholder="请输入车牌号"
                                        />
                                    </n-form-item-gi>
                                    <n-form-item-gi span={4} label="司机电话" path="driverPhone">
                                        <n-input
                                            v-model:value={formData.value.driverPhone}
                                            class="w-100%"
                                            clearable
                                            placeholder="请输入司机电话"
                                        />
                                    </n-form-item-gi>
                                </>
                            )}
                            {formData.value.deliveryWay === 2 && (
                                <>
                                    <n-form-item-gi span={4} label="物流公司" path="logisticsCompany">
                                        <n-input
                                            v-model:value={formData.value.logisticsCompany}
                                            class="w-100%"
                                            clearable
                                            placeholder="请输入物流公司"
                                        />
                                    </n-form-item-gi>
                                    <n-form-item-gi span={4} label="物流单号" path="logisticsNo">
                                        <n-input
                                            v-model:value={formData.value.logisticsNo}
                                            class="w-100%"
                                            clearable
                                            placeholder="请输入物流单号"
                                        />
                                    </n-form-item-gi>
                                </>
                            )}
                            <n-form-item-gi span={12} label="备注" path="remark">
                                <n-input
                                    v-model:value={formData.value.remark}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入备注"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={12}>
                                <div class="w-100%">
                                    <div class="text-18px">物品清单</div>
                                    <div class="mt">
                                        <n-data-table
                                            columns={tableColumns.value}
                                            data={(tableData.value || []).filter((item) => item.delFlag !== 1)}
                                            single-line={false}
                                            bordered
                                            striped
                                        />
                                    </div>
                                    <div class="flex-x-center mt">
                                        <n-space>
                                            <n-button type="success" onClick={addTableItem}>
                                                新增一行
                                            </n-button>
                                        </n-space>
                                    </div>
                                </div>
                            </n-form-item-gi>
                            <n-form-item-gi span={12}>
                                <n-space>
                                    <n-button type="primary" onClick={onSubmit}>
                                        提交
                                    </n-button>
                                    <n-button onClick={onClose}>取消</n-button>
                                </n-space>
                            </n-form-item-gi>
                        </n-grid>
                    </n-form>
                </n-card>
            </n-modal>
        );
    }
});
