<template>
    <div class="project-list-launch">
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <!--详情-->
        <ProjectDetailModal
            v-model:show="detailModal.show"
            :configData="detailModal.configData"
            @refresh="getTableData"
        />
        <!--发起审批-->
        <ProjectApproval
            v-model:show="projectApprovalModal.show"
            :config-data="projectApprovalModal.configData"
            @refresh="getTableData"
        />
        <!--发货通知-->
        <ProjectDeliveryNotice
            v-model:show="deliveryNoticeModal.show"
            :config-data="deliveryNoticeModal.configData"
            @refresh="getTableData"
        />
        <!--绑定框架合同-->
        <ProjectBindFrameContract
            v-model:show="bindFrameContractModal.show"
            :config-data="bindFrameContractModal.configData"
            @refresh="getTableData"
        />
        <!--绑定订单合同-->
        <ProjectMatchFirstOrder
            v-model:show="matchFirstOrderModal.show"
            :config-data="matchFirstOrderModal.configData"
            @refresh="getTableData"
        />
        <!--填写投标费用-->
        <ProjectFillTenderFees
            v-model:show="buyBidCostFillModal.show"
            :config-data="buyBidCostFillModal.configData"
            @refresh="getTableData"
        />
        <!--标书制作完成确认-->
        <ProjectBidCompleteConfirm
            v-model:show="bidCompleteConfirmModal.show"
            :config-data="bidCompleteConfirmModal.configData"
            @refresh="getTableData"
        />
        <!--标书上传-->
        <ProjectBidUploadMail
            v-model:show="bidUploadModal.show"
            :config-data="bidUploadModal.configData"
            @refresh="getTableData"
        />
        <!--是否中标-->
        <ProjectIsWinBid
            v-model:show="isWinBidModal.show"
            :config-data="isWinBidModal.configData"
            @refresh="getTableData"
        />
        <!--中标费用填写-->
        <ProjectFillWinBidFees
            v-model:show="fillWinBidFeesModal.show"
            :config-data="fillWinBidFeesModal.configData"
            @refresh="getTableData"
        />
        <!--取中标通知书-->
        <ProjectTakeWinBidNotice
            v-model:show="bidWinNotificationModal.show"
            :config-data="bidWinNotificationModal.configData"
            @refresh="getTableData"
        />
        <!--确认出库数量申请（详情里面的功能点）-->
        <ProjectConfirmOutboundCount
            v-model:show="projectConfirmOutboundCountModal.show"
            :config-data="projectConfirmOutboundCountModal.configData"
            @refresh="getTableData"
        />
        <!--未中标原因反馈-->
        <ProjectFailWinBidReason
            v-model:show="failWinBidReasonModal.show"
            :config-data="failWinBidReasonModal.configData"
            @refresh="getTableData"
        />
        <!--签订订单合同或上传附件（无合同项目）-->
        <ProjectSignOrderContract
            v-model:show="signOrderContractModal.show"
            :config-data="signOrderContractModal.configData"
            @refresh="getTableData"
        />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, ref } from "vue";
import { DataTableColumns, NRadio, NRadioGroup, NText, PaginationProps } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable, useDicts } from "@/hooks";
import {
    GET_PROJECT_LIST_PREHANDLE,
    POST_CHILD_EXIT_PERMIT_APPLY,
    POST_CHILD_QUALITY_INSPECT_CERTIFICATE,
    POST_CHOOSE_DELIVERY_WAY,
    POST_END_PROJECT,
    POST_EXIT_PERMIT_APPLY,
    POST_NEED_QUALITY_INSPECT,
    POST_NEXT_NODE_DIRECTOR,
    POST_QUALITY_INSPECT_CERTIFICATE
} from "@/api/application/power";
import { TableActions } from "@/components/TableActions";
import { useStoreUser } from "@/store";
import { UserSelector } from "@/components/UserSelector";
import {
    ProjectApproval,
    ProjectBidCompleteConfirm,
    ProjectBidUploadMail,
    ProjectBindFrameContract,
    ProjectConfirmOutboundCount,
    ProjectDeliveryNotice,
    ProjectDetailModal,
    ProjectFailWinBidReason,
    ProjectFillTenderFees,
    ProjectFillWinBidFees,
    ProjectIsWinBid,
    ProjectMatchFirstOrder,
    ProjectSignOrderContract,
    ProjectTakeWinBidNotice
} from "../../components";

interface RowProps {
    [key: string]: any;
}

let storeUser = useStoreUser();

onMounted(async () => {
    await setDictLibs();
    getSearchOptions();
    getTableData();
});

let { dictLibs, getDictLibs } = useDicts();

let setDictLibs = async () => {
    let dictName = ["node_status", "win_bind_status", "project_type", "project_status"];
    await getDictLibs(dictName);
};

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { prop: "projectType", type: "select", label: "项目类型" },
    { prop: "projectStatus", type: "select", label: "项目状态" },
    { prop: "projectName", type: "input", label: "关键词" }
]);

let searchOptions = ref<TableSearchbarOptions>({
    projectType: [],
    projectStatus: []
});

let searchForm = ref<TableSearchbarData>({
    projectType: null,
    projectStatus: null,
    projectName: null
});

let getSearchOptions = () => {
    searchOptions.value.projectType = dictLibs.project_type;
    searchOptions.value.projectStatus = dictLibs.project_status;
};

// 下一节点负责人
let nodeDirector = ref<any>(null);

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, changeTableSelection } =
    useCommonTable<RowProps>("projectId");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "项目编号",
        key: "projectNumber",
        align: "center"
    },
    {
        title: "项目名称",
        key: "projectName",
        align: "center",
        render: (row: RowProps) => {
            return h(NText, { type: "primary" }, () => row.projectName);
        }
    },
    {
        title: "招投标项目编号",
        key: "projectCode",
        align: "center",
        render: (row: RowProps) => {
            return row.projectCode || "暂无";
        }
    },
    {
        title: "订单编号",
        key: "contractNumber",
        align: "center",
        render: (row: RowProps) => {
            return row.contractNumber || "暂无";
        }
    },
    {
        title: "待处理节点",
        key: "nextNodeName",
        align: "center"
    },
    {
        title: "前置负责人",
        key: "nodeDirectorName",
        align: "center",
        render: (row: RowProps) => {
            return h(NText, { type: "primary" }, () => row.projectLeaderName || "暂无");
        }
    },
    {
        title: "是否超时",
        key: "timeOutFlag",
        align: "center",
        render: (row: RowProps) => {
            if (row.timeOutFlag) {
                return h(NText, { type: "error" }, () => "已超时");
            } else {
                return h(NText, { type: "success" }, () => "未超时");
            }
        }
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: 260,
        render: (row: RowProps) => {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    // // 无详情暂时放置，后期要放详情里面（详情里面的功能点） //
                    // {
                    //     label: "确认出库数量",
                    //     tertiary: true,
                    //     type: "warning",
                    //     onClick: () => {
                    //         openProjectConfirmOutboundCount(row);
                    //     }
                    // },
                    // 无详情暂时放置，后期要放详情里面（详情里面的功能点） //
                    {
                        label: "查看",
                        tertiary: true,
                        type: "success",
                        onClick: () => openDetailModal(row)
                    },
                    {
                        label: "立即处理",
                        tertiary: true,
                        type: "primary",
                        disabled: () => {
                            return storeUser.userData.sysUser?.username !== row.nextNodeDirector;
                        },
                        onClick: () => {
                            /*
                             * SignContract: 签订框架合同
                             * MatchContractOrderList: 绑定订单合同
                             * BuyBidCostFill: 购买标书费用填写
                             * BidCompleteConfirm: 标书制作完成确认
                             * BidUpload: 标书上传
                             * IsWinBid: 是否中标
                             * FillWinBidCost: 中标费用填写
                             * BidWinNotification: 取中标通知书
                             * FailWinBidReason: 未中标原因反馈
                             * NeedQualityInspect: 是否需要开具检验合格证
                             * QualityInspectCertificate: 质检开具合格证
                             * ProjectEnd: 项目结项
                             * DeliveryNotice：发货通知
                             * SignOrderContract：签订订单合同或上传附件（无合同项目）
                             * ChooseDeliveryWay：选择发货方式（无合同项目）
                             * ExitPermit：行政发起出门证
                             */
                            if (row.projectStatus === 4) return window.$message.error("项目已关闭，无法进行操作");
                            if (row.nodeFlowType === 2) {
                                if (row.nextNodeKey === "NeedQualityInspect") {
                                    openIsNeedQualityInspectionCertificate(row);
                                } else if (row.nextNodeKey === "QualityInspectCertificate") {
                                    openQualityInspectCertificate(row, true);
                                } else if (row.nextNodeKey === "ExitPermit") {
                                    openExitPermit(row);
                                } else {
                                    openProjectApprovalModal(row);
                                }
                            } else {
                                if (row.nextNodeKey === "SignContract") {
                                    openBindFrameContract(row);
                                } else if (row.nextNodeKey === "MatchContractOrderList") {
                                    openMatchFirstOrderModal(row);
                                } else if (row.nextNodeKey === "BuyBidCostFill") {
                                    openBuyBidCostFillModal(row);
                                } else if (row.nextNodeKey === "BidCompleteConfirm") {
                                    openBidCompleteConfirmModal(row);
                                } else if (row.nextNodeKey === "BidUpload") {
                                    openBidUploadModal(row);
                                } else if (row.nextNodeKey === "IsWinBid") {
                                    openIsWinBidModal(row);
                                } else if (row.nextNodeKey === "FillWinBidCost") {
                                    openFillWinBidFeesModal(row);
                                } else if (row.nextNodeKey === "BidWinNotification") {
                                    openBidWinNotificationModal(row);
                                } else if (row.nextNodeKey === "FailWinBidReason") {
                                    openFailWinBidReasonModal(row);
                                } else if (row.nextNodeKey === "NeedQualityInspect") {
                                    openIsNeedQualityInspectionCertificate(row);
                                } else if (row.nextNodeKey === "QualityInspectCertificate") {
                                    openQualityInspectCertificate(row, false);
                                } else if (row.nextNodeKey === "ProjectEnd") {
                                    window.$dialog.warning({
                                        title: "确认信息",
                                        content: "是否结束项目",
                                        positiveText: "结束",
                                        negativeText: "取消",
                                        onPositiveClick: () => {
                                            POST_END_PROJECT({
                                                projectId: row.projectId,
                                                nodeKey: row.nextNodeKey
                                            }).then((res) => {
                                                if (res.data.code === 0) {
                                                    window.$message.success("操作成功");
                                                    getTableData();
                                                } else {
                                                    window.$message.error(res.data.msg || "操作失败");
                                                }
                                            });
                                        }
                                    });
                                } else if (row.nextNodeKey === "DeliveryNotice") {
                                    openDeliveryNoticeModal(row);
                                } else if (row.nextNodeKey === "SignOrderContract") {
                                    openSignOrderContractModal(row);
                                } else if (row.nextNodeKey === "ExitPermit") {
                                    openExitPermit(row);
                                } else if (row.nextNodeKey === "ChooseDeliveryWay") {
                                    openChooseDeliveryWay(row);
                                } else {
                                    openProjectApprovalModal(row);
                                }
                            }
                        }
                    },
                    {
                        label: "立即推送",
                        tertiary: true,
                        type: "warning",
                        disabled: () => {
                            return (
                                storeUser.userData.sysUser?.username !== row.nodeDirector ||
                                row.nodeStatus !== 2 ||
                                !!row.nextNodeDirector ||
                                row.projectStatus === 3
                            );
                        },
                        onClick: () => {
                            nodeDirector.value = row.nextNodeDirector;
                            window.$dialog.warning({
                                title: "下个节点负责人",
                                content: () => {
                                    return h("div", { class: "py" }, [
                                        h(UserSelector, {
                                            value: nodeDirector.value,
                                            multiple: false,
                                            placeholder: "请选择下个节点负责人",
                                            keyName: "username",
                                            onUpdateValue: (value: string) => {
                                                nodeDirector.value = value;
                                            }
                                        })
                                    ]);
                                },
                                positiveText: "提交",
                                negativeText: "取消",
                                onPositiveClick: () => {
                                    if (!nodeDirector.value) return window.$message.error("请选择下个节点负责人");
                                    POST_NEXT_NODE_DIRECTOR({
                                        projectId: row.projectId,
                                        childProjectId: row.childProjectId,
                                        nodeKey: row.nextNodeKey,
                                        nodeDirector: nodeDirector.value
                                    }).then((res) => {
                                        if (res.data.code === 0) {
                                            window.$message.success("操作成功");
                                            getTableData();
                                        }
                                    });
                                },
                                onNegativeClick: () => {
                                    nodeDirector.value = null;
                                }
                            });
                        }
                    }
                ]
            });
        }
    }
]);

let tablePagination = ref<PaginationProps>({
    page: 1,
    pageSize: 10,
    itemCount: 0,
    pageSizes: [10, 50, 100],
    showSizePicker: true,
    showQuickJumper: true,
    displayOrder: ["size-picker", "pages", "quick-jumper"],
    onChange: (page: number) => {
        tablePagination.value.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.value.pageSize = pageSize;
        tablePagination.value.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_PROJECT_LIST_PREHANDLE({
        current: tablePagination.value.page,
        size: tablePagination.value.pageSize,
        ...searchForm.value
    }).then((res) => {
        tableData.value = res.data.data.records || [];
        tablePagination.value.itemCount = res.data.data.total;
        tableLoading.value = false;
    });
};

let onSearch = () => {
    getTableData();
};

// 详情
let detailModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

let openDetailModal = (row: RowProps) => {
    detailModal.value = { show: true, configData: row };
};

// 签订框架合同（SignContract）
let bindFrameContractModal = ref<{ show: boolean; configData: RowProps }>({ show: false, configData: {} });

let openBindFrameContract = (row: RowProps) => {
    bindFrameContractModal.value.show = true;
    bindFrameContractModal.value.configData = row;
};

// 绑定订单合同（MatchContractOrderList）
let matchFirstOrderModal = ref<{ show: boolean; configData: RowProps }>({ show: false, configData: {} });

let openMatchFirstOrderModal = (row: RowProps) => {
    matchFirstOrderModal.value.show = true;
    matchFirstOrderModal.value.configData = row;
};

// 购买标书费用填写（BuyBidCostFill）
let buyBidCostFillModal = ref<{ show: boolean; configData: RowProps }>({ show: false, configData: {} });

let openBuyBidCostFillModal = (row: RowProps) => {
    buyBidCostFillModal.value.show = true;
    buyBidCostFillModal.value.configData = row;
};

// 标书制作完成确认（BidCompleteConfirm）
let bidCompleteConfirmModal = ref<{ show: boolean; configData: RowProps }>({ show: false, configData: {} });

let openBidCompleteConfirmModal = (row: RowProps) => {
    bidCompleteConfirmModal.value.show = true;
    bidCompleteConfirmModal.value.configData = row;
};

// 标书上传（BidUpload）
let bidUploadModal = ref<{ show: boolean; configData: RowProps }>({ show: false, configData: {} });

let openBidUploadModal = (row: RowProps) => {
    bidUploadModal.value.show = true;
    bidUploadModal.value.configData = row;
};

// 是否中标（IsWinBid）
let isWinBidModal = ref<{ show: boolean; configData: RowProps }>({ show: false, configData: {} });

let openIsWinBidModal = (row: RowProps) => {
    isWinBidModal.value.show = true;
    isWinBidModal.value.configData = row;
};

// 中标费用填写（FillWinBidCost）
let fillWinBidFeesModal = ref<{ show: boolean; configData: RowProps }>({ show: false, configData: {} });

let openFillWinBidFeesModal = (row: RowProps) => {
    fillWinBidFeesModal.value.show = true;
    fillWinBidFeesModal.value.configData = row;
};

// 取中标通知书（BidWinNotification）
let bidWinNotificationModal = ref<{ show: boolean; configData: RowProps }>({ show: false, configData: {} });

let openBidWinNotificationModal = (row: RowProps) => {
    bidWinNotificationModal.value.show = true;
    bidWinNotificationModal.value.configData = row;
};

// 确认出库数量
let projectConfirmOutboundCountModal = ref<{ show: boolean; configData: RowProps }>({ show: false, configData: {} });

let openProjectConfirmOutboundCount = (row: RowProps) => {
    projectConfirmOutboundCountModal.value.show = true;
    projectConfirmOutboundCountModal.value.configData = row;
};

// 未中标原因反馈
let failWinBidReasonModal = ref<{ show: boolean; configData: RowProps }>({ show: false, configData: {} });

let openFailWinBidReasonModal = (row: RowProps) => {
    failWinBidReasonModal.value.show = true;
    failWinBidReasonModal.value.configData = row;
};

// 处理OA流程
let projectApprovalModal = ref<{ show: boolean; configData: RowProps }>({ show: false, configData: {} });

let openProjectApprovalModal = (row: RowProps) => {
    projectApprovalModal.value.show = true;
    projectApprovalModal.value.configData = row;
};

// 发货通知
let deliveryNoticeModal = ref<{ show: boolean; configData: RowProps }>({ show: false, configData: {} });

let openDeliveryNoticeModal = (row: RowProps) => {
    deliveryNoticeModal.value.show = true;
    deliveryNoticeModal.value.configData = row;
};

// 签订订单合同或上传附件（无合同项目）
let signOrderContractModal = ref<{ show: boolean; configData: RowProps }>({ show: false, configData: {} });

let openSignOrderContractModal = (row: RowProps) => {
    signOrderContractModal.value.show = true;
    signOrderContractModal.value.configData = row;
};

// 是否需要开具检验合格证
let openIsNeedQualityInspectionCertificate = (row: RowProps) => {
    window.$dialog.warning({
        title: "确认信息",
        content: "是否需要开具质检合格证？",
        positiveText: "需要",
        negativeText: "不需要",
        onPositiveClick: () => handleQualityInspectDecision(row, 1),
        onNegativeClick: () => handleQualityInspectDecision(row, 0)
    });
};

let handleQualityInspectDecision = (row: RowProps, needQualityInspect: number) => {
    POST_NEED_QUALITY_INSPECT({
        projectId: row.projectId,
        nodeKey: row.nextNodeKey,
        needQualityInspect: needQualityInspect
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("操作成功");
            getTableData();
        } else {
            window.$message.error(res.data.msg || "操作失败");
        }
    });
};

// 开具检验合格证
let openQualityInspectCertificate = (row: RowProps, isChild: boolean) => {
    window.$dialog.warning({
        title: "确认信息",
        content: "立即开具质检合格证？",
        positiveText: "确定开具",
        negativeText: "我再想想",
        onPositiveClick: () => {
            let certificateAction = isChild ? POST_CHILD_QUALITY_INSPECT_CERTIFICATE : POST_QUALITY_INSPECT_CERTIFICATE;
            let certificateData = isChild ? { childProjectId: row.childProjectId } : { projectId: row.projectId };
            certificateAction(certificateData).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("操作成功");
                    getTableData();
                } else {
                    window.$message.error(res.data.msg || "操作失败");
                }
            });
        }
    });
};

/*
 * 2024年5月25日
 * 行政发起出门证节点
 */
let openExitPermit = (row: RowProps) => {
    window.$dialog.warning({
        title: "确认信息",
        content: "是否发起出门证？",
        positiveText: "发起",
        negativeText: "取消",
        onPositiveClick: () => {
            if (row.nodeFlowType === 1) {
                POST_EXIT_PERMIT_APPLY({
                    projectId: row.projectId,
                    nodeKey: row.nextNodeKey,
                    // 1:非节点发起 2:节点发起
                    applyMethod: 2
                }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("操作成功");
                        getTableData();
                    } else {
                        window.$message.error(res.data.msg || "操作失败");
                    }
                });
            } else {
                POST_CHILD_EXIT_PERMIT_APPLY({
                    projectId: row.projectId,
                    childProjectId: row.childProjectId,
                    nodeKey: row.nextNodeKey,
                    // 1:非节点发起 2:节点发起
                    applyMethod: 2
                }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("操作成功");
                        getTableData();
                    } else {
                        window.$message.error(res.data.msg || "操作失败");
                    }
                });
            }
        }
    });
};

/*
 * 2024年5月27日
 * 选择发货模式
 */
let deliveryWayValue = ref(1);

let openChooseDeliveryWay = (row: RowProps) => {
    window.$dialog.warning({
        title: "选择发货模式",
        content: () =>
            h(
                "div",
                {
                    class: "py-4"
                },
                [
                    h(
                        NRadioGroup,
                        {
                            value: deliveryWayValue.value,
                            onUpdateValue: (v) => (deliveryWayValue.value = v)
                        },
                        () => [
                            h(NRadio, { label: "配送", value: 1 }),
                            h(NRadio, { label: "客户自提", value: 2 }),
                            h(NRadio, { label: "物流", value: 3 })
                        ]
                    )
                ]
            ),
        positiveText: "提交",
        negativeText: "取消",
        onPositiveClick: () => {
            POST_CHOOSE_DELIVERY_WAY({
                projectId: row.projectId,
                nodeKey: row.nextNodeKey,
                deliveryWay: deliveryWayValue.value
            }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("操作成功");
                    getTableData();
                } else {
                    window.$message.error(res.data.msg || "操作失败");
                }
            });
        }
    });
};
</script>
