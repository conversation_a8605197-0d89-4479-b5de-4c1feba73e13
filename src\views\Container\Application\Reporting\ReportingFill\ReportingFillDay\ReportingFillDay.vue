<template>
    <div>
        <n-card content-style="padding-top:8px;padding-bottom:4px" hoverable>
            <div class="flex-y-center">
                <n-tabs v-model:value="tabActive" animated type="bar" @before-leave="onTabBeforeLeave">
                    <n-tab-pane :name="1" tab="物料/人工填报" />
                    <n-tab-pane :name="2" tab="外协/外购填报" />
                    <n-tab-pane :name="3" tab="其他费用填报" />
                </n-tabs>
                <n-button type="primary" @click="changeActive(0)">返回选择</n-button>
            </div>
        </n-card>
        <div class="mt">
            <ReportingFillDayCommon v-if="tabActive === 1" />
            <ReportingFillDayExternal v-if="tabActive === 2" />
            <ReportingFillDayOther v-if="tabActive === 3" />
        </div>
    </div>
</template>

<script lang="ts" setup>
import ReportingFillDayCommon from "./ReportingFillDayCommon/ReportingFillDayCommon.vue";
import ReportingFillDayExternal from "./ReportingFillDayExternal/ReportingFillDayExternal.vue";
import ReportingFillDayOther from "./ReportingFillDayOther/ReportingFillDayOther.vue";
import { ref } from "vue";
import { usePublic } from "@/hooks";

let props = withDefaults(defineProps<{ active: number }>(), {});

let emits = defineEmits(["update:active"]);

let { onTabBeforeLeave } = usePublic();

let changeActive = (item: number) => emits("update:active", item);

let tabActive = ref(1);
</script>
