<template>
    <div>
        <n-table v-if="(bidCosts || []).length" :single-line="false" class="text-center">
            <thead>
                <tr>
                    <th>费用名称</th>
                    <th>费用类型</th>
                    <th>费用金额（元）</th>
                </tr>
            </thead>
            <tbody>
                <tr v-for="(item, index) in bidCosts || []" :key="index">
                    <td>{{ item.name }}</td>
                    <td>{{ getTypeLabel(item.type) }}</td>
                    <td>{{ item.value }}</td>
                </tr>
            </tbody>
        </n-table>
        <n-result v-else class="py-50px" size="large" status="404" title="暂无附件" />
    </div>
</template>

<script lang="ts" setup>
import { ref, watchEffect } from "vue";
import { useDicts } from "@/hooks";

let props = withDefaults(defineProps<{ fee: string }>(), {});

let emits = defineEmits(["refresh"]);

let { dictLibs, getDictLibs } = useDicts();

let setDictLibs = async () => {
    let dictName = ["project_bid_cost_type", "project_win_bid_cost_type"];
    await getDictLibs(dictName);
};

// 获取费用信息
let bidCosts = ref<any[]>([]);

let getBidCosts = () => {
    bidCosts.value = JSON.parse(decodeURIComponent(props.fee));
};

let getTypeLabel = (type: string) => {
    let dicts = [...(dictLibs["project_bid_cost_type"] || []), ...(dictLibs["project_win_bid_cost_type"] || [])];
    let dict = dicts.find((item) => item.value === type);
    return dict ? dict.label : "未知";
};

watchEffect(async () => {
    if (props.fee) {
        await setDictLibs();
        getBidCosts();
    }
});
</script>
