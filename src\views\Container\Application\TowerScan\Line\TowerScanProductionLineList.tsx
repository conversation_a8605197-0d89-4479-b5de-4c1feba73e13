import { defineComponent, h, onMounted, reactive, ref } from "vue";
import {
    TableSearchbar,
    TableSearchbarConfig,
    TableSearchbarData,
    TableSearchbarOptions
} from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import { TableActions } from "@/components/TableActions";
import { DELETE_PRODUCTION_LINE, GET_PRODUCTION_LINE_PAGE_LIST } from "@/api/application/TowerScan";
import TowerScanProductionLineEdit from "./TowerScanProductionLineEdit";
import TowerScanEquipmentListModal from "./TowerScanEquipmentListModal";

export default defineComponent({
    name: "TowerScanProductionLineList",
    setup(props) {
        // 搜索项
        const searchConfig = ref<TableSearchbarConfig>([]);

        const searchOptions = ref<TableSearchbarOptions>({});

        const getSearchOptions = async () => {};

        const searchForm = ref<TableSearchbarData>({});

        const onSearch = () => {
            tablePagination.page = 1;
            getTableData();
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("lineId");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            { title: "产线名称", key: "lineName", align: "center" },
            {
                title: "设备清单",
                key: "equipmentNames",
                align: "center",
                render: (row) => {
                    return (
                        <n-button text type="info" onClick={() => showEquipmentList(row)}>
                            点击查看
                        </n-button>
                    );
                }
            },
            {
                title: "产线工序",
                key: "techniqueNames",
                align: "center",
                render: (row) => {
                    return <n-text type="info">{row.techniqueNames ?? "/"}</n-text>;
                }
            },
            { title: "产线负责人", key: "directorNames", align: "center", render: (row) => row.directorNames || "/" },
            { title: "备注", key: "remark", align: "center" },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 180,
                render: (row) => {
                    return (
                        <TableActions
                            type="button"
                            buttonActions={[
                                {
                                    label: "编辑",
                                    tertiary: true,
                                    type: "primary",
                                    onClick: () => openEditModal(row)
                                },
                                {
                                    label: "删除产线",
                                    tertiary: true,
                                    type: "error",
                                    onClick: () => {
                                        window.$dialog.warning({
                                            title: "确认删除",
                                            content: "确定要删除该条数据吗？",
                                            positiveText: "确定",
                                            negativeText: "取消",
                                            onPositiveClick: async () => {
                                                try {
                                                    const res = await DELETE_PRODUCTION_LINE({
                                                        lineIds: row.lineId
                                                    });
                                                    if (res.data.code === 0) {
                                                        window.$message.success("删除成功");
                                                        getTableData();
                                                    } else {
                                                        window.$message.error(res.data.message || "删除失败");
                                                    }
                                                } catch (error) {
                                                    window.$message.error("删除失败");
                                                }
                                            }
                                        });
                                    }
                                }
                            ]}
                        />
                    );
                }
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_PRODUCTION_LINE_PAGE_LIST({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                ...searchForm.value
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        // 新增编辑弹窗
        const editModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const openEditModal = (row?: RowProps) => {
            editModal.value = { show: true, configData: row ?? {} };
        };

        // 设备列表弹窗
        const equipmentModal = ref<{ show: boolean; lineId: number | null; lineName: string }>({
            show: false,
            lineId: null,
            lineName: ""
        });

        const showEquipmentList = (row: RowProps) => {
            equipmentModal.value = {
                show: true,
                lineId: row.lineId,
                lineName: row.lineName
            };
        };

        onMounted(async () => {
            await getSearchOptions();
            getTableData();
        });

        return () => (
            <div class="tower-scan-production-line-list">
                <n-card>
                    <TableSearchbar
                        form={searchForm.value}
                        config={searchConfig.value}
                        options={searchOptions.value}
                        onSearch={onSearch}
                    />
                </n-card>
                <n-card class="mt">
                    <n-space class="mb">
                        <n-button type="primary" onClick={() => openEditModal()}>
                            新增产线
                        </n-button>
                    </n-space>
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        pagination={tablePagination}
                        row-key={tableRowKey}
                        single-line={false}
                        bordered
                        remote
                        striped
                        onUpdate:checked-row-keys={changeTableSelection}
                    />
                </n-card>
                <TowerScanProductionLineEdit
                    v-model:show={editModal.value.show}
                    config-data={editModal.value.configData}
                    onRefresh={getTableData}
                />

                <TowerScanEquipmentListModal
                    v-model:show={equipmentModal.value.show}
                    line-id={equipmentModal.value.lineId}
                    line-name={equipmentModal.value.lineName}
                />
            </div>
        );
    }
});
