<template>
    <div class="user-list">
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-space class="mb">
                <n-button secondary type="primary" @click="openEditModal()">新增</n-button>
                <n-button secondary type="error" @click="onDelete()">批量删除</n-button>
            </n-space>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <job-edit-modal v-model:id="editModal.id" v-model:show="editModal.show" @refresh="getTableData" />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from "vue";
import type { DataTableColumns } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { TableActions } from "@/components/TableActions";
import { DELETE_POST, DELETE_POSTS, GET_POST_LIST } from "@/api/permission";
import { useCommonTable, useDicts } from "@/hooks";
import JobEditModal from "./JobEditModal.vue";

interface RowProps<T = string | null> {
    postId: T | number;
    postName: T;
    postGroup: T | number;
}

onMounted(async () => {
    await setDictLibs();
    getSearchOptions();
    getTableData();
});

// 字典操作
let { getDictLibs, dictValueToLabel } = useDicts();

let setDictLibs = async () => {
    let dictName = ["post_group"];
    await getDictLibs(dictName);
};

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    {
        prop: "postName",
        type: "input",
        label: "职位名称"
    }
]);

let searchOptions = ref<TableSearchbarOptions>({});

let searchForm = ref<TableSearchbarData>({
    postName: null
});

let getSearchOptions = () => {};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("postId");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "职位名称",
        key: "postName",
        align: "center"
    },
    {
        title: "职位标识",
        key: "postCode",
        align: "center"
    },
    {
        title: "职位组",
        key: "postGroup",
        align: "center",
        render: (row: RowProps) => {
            return dictValueToLabel(row.postGroup, "post_group");
        }
    },
    {
        title: "职位排序",
        key: "postSort",
        align: "center"
    },
    {
        title: "创建时间",
        key: "createTime",
        align: "center"
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: 150,
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "编辑",
                        tertiary: true,
                        onClick: () => {
                            openEditModal(row.postId);
                        }
                    },
                    {
                        label: "删除",
                        type: "error",
                        tertiary: true,
                        onClick: () => {
                            if (row.postId) onDelete(row.postId);
                        }
                    }
                ]
            });
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_POST_LIST({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        ...searchForm.value
    }).then((res) => {
        if (res.data.code === 0) {
            console.log("职位列表", res.data);
            tableData.value = res.data.data.records;
            tablePagination.itemCount = res.data.data.total;
            tableLoading.value = false;
        }
    });
};

// 搜索
let onSearch = () => {
    tablePagination.page = 1;
    tablePagination.pageSize = 10;
    getTableData();
};

// 新增编辑
let editModal = ref<{ show: boolean; id: string | number | null }>({
    show: false,
    id: null
});

let openEditModal = (id?: string | number | null) => {
    editModal.value.show = true;
    editModal.value.id = id || null;
    console.log(11111, editModal.value.id);
};

// 删除
let onDelete = (id?: string | number) => {
    if (!id && tableSelection.value.length < 1) {
        window.$message.error("请选择要删除的数据");
        return false;
    }
    window.$dialog.warning({
        title: "警告",
        content: `确定删除${id ? "该" : "选中"}职位吗？`,
        positiveText: "删除",
        negativeText: "取消",
        onPositiveClick: () => {
            if (id) {
                DELETE_POST({ id: id }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("删除成功");
                        onSearch();
                    }
                });
            } else {
                DELETE_POSTS({ ids: tableSelection.value.join(",") }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("删除成功");
                        onSearch();
                    }
                });
            }
        }
    });
};
</script>
