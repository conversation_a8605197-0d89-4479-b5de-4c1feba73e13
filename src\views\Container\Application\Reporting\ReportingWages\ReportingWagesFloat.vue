<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <div class="flex mt">
            <n-card class="flex-fixed-250" hoverable>
                <div class="flex-y-center mb">
                    <n-input v-model:value="treePattern" clearable placeholder="搜索" />
                    <!--<n-button class="ml-2" type="default" @click="selectTreeNode()">查看全部</n-button>-->
                </div>
                <n-tree
                    v-model:selected-keys="treeSelectKeys"
                    :cancelable="false"
                    :data="treeData"
                    :pattern="treePattern"
                    :show-irrelevant-nodes="false"
                    block-line
                    children-field="childrenList"
                    default-expand-all
                    key-field="id"
                    label-field="categoryName"
                    selectable
                    @update:selected-keys="selectTreeNode"
                />
            </n-card>
            <n-card class="flex-1 ml" hoverable>
                <!--<n-space class="mb">-->
                <!--<n-button secondary type="success">批量修改</n-button>-->
                <!--</n-space>-->
                <n-data-table
                    :columns="tableColumns"
                    :data="tableData"
                    :loading="tableLoading"
                    :row-key="tableRowKey"
                    :single-line="false"
                    bordered
                    remote
                    striped
                    @update:checked-row-keys="changeTableSelection"
                />
            </n-card>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, ref, watch } from "vue";
import {
    GET_CONFIG_COMPANY_LIST,
    GET_WAGE_CONFIG_ALL_LIST,
    GET_WAGE_CONFIG_CATEGORY_TREE,
    UPDATE_WAGE_CONFIG
} from "@/api/application/reporting";
import type { DataTableColumns } from "naive-ui";
import { NButton, NInput } from "naive-ui";
import { useCommonTable } from "@/hooks";
import { TableActions } from "@/components/TableActions";
import { DynamicTableEditor } from "@/components/Dynamic";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { GET_WORK_GROUP_PAGE_LIST } from "@/api/application/production";

onMounted(async () => {
    await getCompanyIdOptions();
    await getTreeData();
    getTableData();
});

// 获取公司选项
let getCompanyIdOptions = async () => {
    await GET_CONFIG_COMPANY_LIST({ needFill: 1 }).then((res) => {
        searchOptions.value.companyId = (res.data.data || []).map((item: any) => ({
            label: item.companyName,
            value: item.id
        }));
        searchForm.value.companyId = searchOptions.value.companyId[0]?.value;
    });
    await getWorkGroupId(searchForm.value.companyId);
};

let getWorkGroupId = async (companyId: any) => {
    searchForm.value.workGroupId = null;
    searchOptions.value.workGroupId = [];
    await GET_WORK_GROUP_PAGE_LIST({
        current: 1,
        size: 9999,
        companyId: companyId
    }).then((res) => {
        searchOptions.value.workGroupId = (res.data.data.records ?? []).map((item: any) => {
            return { label: item.companyName + "-" + item.workshopName + "-" + item.groupName, value: item.id };
        });
        searchForm.value.workGroupId = searchOptions.value.workGroupId[0]?.value;
    });
};

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { prop: "companyId", type: "select", label: "所属公司" },
    { prop: "workGroupId", type: "select", label: "所属班组", span: 2 }
]);

let searchOptions = ref<TableSearchbarOptions>({
    companyId: [],
    workGroupId: []
});

let searchForm = ref<TableSearchbarData>({
    companyId: null,
    workGroupId: null
});

watch(
    () => searchForm.value.companyId,
    async (val) => {
        if (val) {
            await getWorkGroupId(val);
        } else {
            searchForm.value.workGroupId = null;
            searchOptions.value.workGroupId = [];
        }
    }
);

// 树
let treeData = ref<any[]>([]);
let treePattern = ref("");
let treeSelectKeys = ref<(string | number)[]>([]);

let treeAddDisabled = (array: any[]): any[] => {
    return array.map((item) => {
        let newItem = { ...item };
        newItem.disabled = !!(newItem.childrenList && newItem.childrenList.length > 0);
        if (newItem.childrenList) {
            newItem.childrenList = treeAddDisabled(newItem.childrenList);
        }
        return newItem;
    });
};

let findLastLevel = (array: any[]): any => {
    let lastLevel = array.find((item) => item.childrenList && item.childrenList.length > 0);
    if (lastLevel) {
        return findLastLevel(lastLevel.childrenList);
    } else {
        return array;
    }
};

let getTreeData = async () => {
    await GET_WAGE_CONFIG_CATEGORY_TREE({}).then((res) => {
        if (res.data.code === 0) {
            treeData.value = treeAddDisabled(res.data.data ?? []);
            treeSelectKeys.value = [findLastLevel(treeData.value)[0].id];
        }
    });
};

let selectTreeNode = (keys?: (string | number)[]) => {
    treeSelectKeys.value = keys ?? [];
    onSearch();
};

// 数据列表
interface RowProps {
    [key: string]: any;
}

let { tableRowKey, tableData, tableLoading, tableSelection, changeTableSelection } = useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "当前固定值（元）",
        key: "wage",
        align: "center",
        render: (row) => {
            return h(DynamicTableEditor, {
                type: "input",
                value: row.wage,
                onUpdateValue: (v: unknown) => (row.wage = v)
            });
        }
    },
    {
        title: "系数",
        key: "coefficient",
        align: "center",
        render: (row) => {
            return h(DynamicTableEditor, {
                type: "input",
                value: row.coefficient,
                onUpdateValue: (v: unknown) => (row.coefficient = v)
            });
        }
    },
    // {
    //     title: "修正系数",
    //     key: "correctCoefficient",
    //     align: "center",
    //     render: (row) => {
    //         return h(DynamicTableEditor, {
    //             type: "input",
    //             value: row.correctCoefficient,
    //             onUpdateValue: (v: unknown) => (row.correctCoefficient = v)
    //         });
    //     }
    // },
    {
        title: "最新更新时间",
        key: "updateTime",
        align: "center"
    },
    {
        title: "备注",
        key: "remark",
        align: "center",
        render: (row) => {
            return h(DynamicTableEditor, {
                type: "input",
                value: row.remark,
                onUpdateValue: (v: unknown) => (row.remark = v)
            });
        }
    },
    {
        title: "操作",
        key: "action",
        align: "center",
        width: 120,
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "保存修改",
                        tertiary: true,
                        type: "primary",
                        onClick: () => updateTableItem(row)
                    }
                ]
            });
        }
    }
]);

let getTableData = () => {
    GET_WAGE_CONFIG_ALL_LIST({
        ...searchForm.value,
        wageCategoryId: treeSelectKeys.value[0] ?? null
    }).then((res) => {
        tableData.value = res.data.data ?? [];
        tableLoading.value = false;
    });
};

// 搜索
let onSearch = () => {
    getTableData();
};

// 更新
let updateTableItem = async (row: RowProps) => {
    let res = await UPDATE_WAGE_CONFIG({ ...row });
    if (res.data.code === 0) {
        window.$message.success("保存成功");
        onSearch();
    } else {
        window.$message.error(res.data.msg);
    }
};
</script>
