import { computed, defineComponent, h, ref, watchEffect } from "vue";
import { type DataTableColumns, type FormInst, NInput, NSelect, NTooltip } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { TableActions } from "@/components/TableActions";
import {
    CHECK_REPOSITORY_OUT_RECEIPT,
    CONFIRM_REPOSITORY_OUT_RECEIPT,
    GET_REPOSITORY_GOODS_LIST,
    GET_REPOSITORY_OUT_RECEIPT_DETAIL
} from "@/api/application/repository";
import { useDicts } from "@/hooks";

export default defineComponent({
    name: "RepositoryOutReceiptConfirm",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        // 字典操作
        let { dictLibs, getDictLibs } = useDicts();

        let setDictLibs = async () => {
            let dictName = ["common_units"];
            await getDictLibs(dictName);
        };

        // 表单数据
        interface FormDataProps {
            [key: string]: any;
        }

        const formRef = ref<FormInst | null>(null);

        const getFormOptions = async () => {};

        // 获取详情
        const getDetail = async () => {
            await GET_REPOSITORY_OUT_RECEIPT_DETAIL({ id: props.configData.id }).then((res) => {
                const recordItemList = res.data.data?.recordItemList ?? [];
                recordItemList.forEach((item: any) => {
                    if (item.outStoreroomId && item.storeroomGoodsList) {
                        const selectedStoreroom = item.storeroomGoodsList.find(
                            (store: any) => store.storeroomId === item.outStoreroomId
                        );
                        item.storageCapacity = selectedStoreroom?.storageCapacity ?? "/";
                    }
                });
                tableData.value = recordItemList;
            });
        };

        const initFormData: FormDataProps = {
            outStoreroomId: null
        };

        const formRules = computed(() => ({
            outStoreroomId: [{ required: true, message: "请选择入库仓库", trigger: ["blur", "change"] }]
        }));

        const formData = ref(cloneDeep(initFormData));

        const clearForm = () => {
            formData.value = cloneDeep(initFormData);
        };

        // 表单数据
        interface RowProps {
            [key: string]: any;
        }

        const goodsIdOptions = ref<any[]>([]);

        const getTableOptions = async () => {
            GET_REPOSITORY_GOODS_LIST({ current: 1, size: 9999 }).then((res) => {
                goodsIdOptions.value = res.data.data.records ?? [];
            });
        };

        watchEffect(async () => {
            if (show.value) {
                await getTableOptions();
                await getFormOptions();
                await setDictLibs();
                await getDetail();
            }
        });

        const tableData = ref<RowProps[]>([]);

        const tableColumns = ref<DataTableColumns<RowProps>>([
            {
                title: "出库物品名称",
                key: "goodsName",
                align: "center",
                render: (row) => {
                    return h(NSelect, {
                        options: goodsIdOptions.value,
                        clearable: true,
                        filterable: true,
                        placeholder: "请选择原料名称",
                        labelField: "goodsName",
                        valueField: "id",
                        value: row.goodsId,
                        onUpdateValue: (v, o: any) => {
                            row.goodsId = v;
                            row.unit = o?.unit;
                            row.goodsSpec = o?.goodsSpec;
                        }
                    });
                }
            },
            {
                title: "应出数量",
                key: "applyQuality",
                align: "center",
                render: (row) => {
                    if (!row.actualQuality) row.actualQuality = row.applyQuality;
                    return row.applyQuality ?? "/";
                }
            },
            {
                title: "实际出库数",
                key: "actualQuality",
                align: "center",
                render: (row) => {
                    return h(NInput, {
                        value: row.actualQuality,
                        onUpdateValue: (v) => (row.actualQuality = v)
                    });
                }
            },
            {
                title: "选择出库仓库",
                key: "outStoreroomId",
                align: "center",
                render: (row) => {
                    return h(NSelect, {
                        options: row.storeroomGoodsList ?? [],
                        clearable: true,
                        filterable: true,
                        placeholder: "请选择出库仓库",
                        value: row.outStoreroomId,
                        labelField: "storeroomName",
                        valueField: "storeroomId",
                        renderOption: ({ node, option }: any) => {
                            return h(NTooltip, null, { trigger: () => node, default: () => option.storeroomName });
                        },
                        onUpdateValue: (v: any, o: any) => {
                            row.storageCapacity = o?.storageCapacity ?? "/";
                            row.outStoreroomId = v;
                        }
                    });
                }
            },
            { title: "可出数量", key: "storageCapacity", align: "center", render: (row) => row.storageCapacity ?? "/" },
            {
                title: "物品单位",
                key: "unit",
                align: "center",
                render: (row) => {
                    let object = (dictLibs["common_units"] || []).find((i: any) => i.value === String(row.unit));
                    return object?.label ?? "/";
                }
            },
            { title: "规格型号", key: "goodsSpec", align: "center", render: (row) => row.goodsSpec ?? "/" },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 80,
                render(row, index) {
                    return h(TableActions, {
                        type: "button",
                        buttonActions: [
                            { label: "删除", tertiary: true, type: "error", onClick: () => deleteItem(row, index) }
                        ]
                    });
                }
            }
        ]);

        // 可编辑表单配置
        const tableItem: RowProps = {
            goodsId: null,
            applyQuality: null
        };

        const addTableItem = () => {
            tableData.value.push(cloneDeep(tableItem));
        };

        const deleteItem = (row: RowProps, index: number) => {
            if (row.id) {
                tableData.value.forEach((citem, cindex) => {
                    if (row.id === citem.id) citem.delFlag = 1;
                });
            } else tableData.value.splice(index, 1);
        };

        const onClose = () => {
            clearForm();
            changeModalShow(false);
            emit("refresh");
        };

        const onSubmit = async () => {
            const validateError = await formRef.value?.validate((errors) => !!errors);
            if (validateError) return false;

            const submitData = {
                id: props.configData.id,
                recordItemList: tableData.value.map((item) => {
                    return {
                        id: item.id,
                        goodsId: item.goodsId,
                        actualQuality: item.actualQuality,
                        outStoreroomId: item.outStoreroomId
                    };
                })
            };

            const checkRes = await CHECK_REPOSITORY_OUT_RECEIPT(submitData);

            if (checkRes.data.data && checkRes.data.data.length > 0) return window.$message.error("库存不足，无法出库");

            CONFIRM_REPOSITORY_OUT_RECEIPT(submitData).then((res) => {
                if (res.data.code === 0) {
                    clearForm();
                    changeModalShow(false);
                    emit("refresh");
                }
            });
        };

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card title="领料出库单" class="w-1200px" closable onClose={onClose}>
                    <n-form
                        ref={formRef}
                        model={formData.value}
                        rules={formRules.value}
                        label-placement="left"
                        label-width="auto"
                    >
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi span={12}>
                                <div class="w-100%">
                                    <div class="text-18px">用料清单</div>
                                    <div class="mt">
                                        <n-data-table
                                            columns={tableColumns.value}
                                            data={(tableData.value || []).filter((item) => item.delFlag !== 1)}
                                            single-line={false}
                                            bordered
                                            striped
                                        />
                                    </div>
                                    <div class="flex-x-center mt">
                                        <n-space>
                                            <n-button type="success" onClick={addTableItem}>
                                                新增一行
                                            </n-button>
                                        </n-space>
                                    </div>
                                </div>
                            </n-form-item-gi>
                            <n-form-item-gi span={12}>
                                <n-space>
                                    <n-button type="primary" onClick={onSubmit}>
                                        提交
                                    </n-button>
                                    <n-button onClick={onClose}>取消</n-button>
                                </n-space>
                            </n-form-item-gi>
                        </n-grid>
                    </n-form>
                </n-card>
            </n-modal>
        );
    }
});
