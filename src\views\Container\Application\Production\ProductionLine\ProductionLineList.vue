<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-space class="mb">
                <n-button secondary type="primary" @click="openEditModal()">新增线路</n-button>
            </n-space>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <ProductionLineEdit :id="editModal.id" v-model:show="editModal.show" @refresh="getTableData" />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from "vue";
import type { DataTableColumns } from "naive-ui";
import { NButton } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import { GET_PRODUCTION_LINE_PAGE, OBTAIN_REAL_TIME_STATUS_OF_THE_LINE } from "@/api/application/production";
import ProductionLineEdit from "./ProductionLineEditModal.vue";
import { TableActions } from "@/components/TableActions";
import { httpRequest } from "@/utils/request";
import { useStoreUser } from "@/store";

interface RowProps {
    [key: string]: any;
}

onMounted(async () => {
    getLineList();
    getSearchOptions();
    getTableData();
});

let lineList = ref<any[]>([]);

let getLineList = () => {
    OBTAIN_REAL_TIME_STATUS_OF_THE_LINE().then((res) => {
        lineList.value = res.data.data;
    });
};

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([]);

let searchOptions = ref<TableSearchbarOptions>({});

let searchForm = ref<TableSearchbarData>({});

let getSearchOptions = () => {};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    { title: "线路名称", key: "lineName", align: "center" },
    { title: "线路简称", key: "lineShortName", align: "center" },
    {
        title: "开机允许报废米数",
        key: "onAllowMeterNum",
        align: "center",
        render: (row: RowProps) => {
            return row.onAllowMeterNum ?? "/";
        }
    },
    {
        title: "更换允许报废米数",
        key: "changeAllowMeterNum",
        align: "center",
        render: (row: RowProps) => {
            return row.changeAllowMeterNum ?? "/";
        }
    },
    {
        title: "停机允许报废米数",
        key: "offAllowMeterNum",
        align: "center",
        render: (row: RowProps) => {
            return row.offAllowMeterNum ?? "/";
        }
    },
    {
        title: "清理模头允许报废米数",
        key: "cleanAllowMeterNum",
        align: "center",
        render: (row: RowProps) => {
            return row.cleanAllowMeterNum ?? "/";
        }
    },
    // {
    //   title: "线路状态",
    //   key: "lineStatus",
    //   align: "center",
    //   render: (row: RowProps) => {
    //     return queryLineStatus(row.lineShortName) ?? "无法获取状态";
    //   }
    // },
    // {
    //     title: "线路设备",
    //     key: "lineDevice",
    //     align: "center",
    //     render: (row: RowProps) => {
    //         return row.lineDevice ?? "暂无";
    //     }
    // },
    {
        title: "操作",
        key: "action",
        align: "center",
        width: 120,
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "编辑线路",
                        tertiary: true,
                        type: "primary",
                        onClick: () => openEditModal(row.id)
                    }
                ]
            });
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
    }
});

let getTableData = () => {
    GET_PRODUCTION_LINE_PAGE({}).then((res) => {
        tableData.value = res.data.data.records;
        tablePagination.itemCount = res.data.data.total;
        tableLoading.value = false;
    });
};

// 搜索
let onSearch = () => {
    tablePagination.page = 1;
    tablePagination.pageSize = 10;
    getTableData();
};

// 新增编辑
let editModal = ref<{ show: boolean; id: Nullable<string | number> }>({
    show: false,
    id: null
});

let openEditModal = (id?: Nullable<string | number>) => {
    editModal.value.show = true;
    editModal.value.id = id ?? null;
};

let queryLineStatus = (number: any): any => {
    let Start_Run: any = number + "_State_Run";
    let State_Heat: any = number + "_State_Heat";
    if (lineList.value[State_Heat] == 0 && lineList.value[Start_Run] == 0) {
        return h("div", { style: "color:red" }, ["线路离线"]);
    } else if (lineList.value[State_Heat] == 1 && lineList.value[Start_Run] == 0) {
        return h("div", { style: "color:#fa8052" }, ["线路加热中"]);
    } else if (lineList.value[State_Heat] == 0 && lineList.value[Start_Run] == 1) {
        return h("div", { style: "color:#67c23a" }, ["线路正常运行中"]);
    } else {
        return "线路异常";
    }
};
</script>
