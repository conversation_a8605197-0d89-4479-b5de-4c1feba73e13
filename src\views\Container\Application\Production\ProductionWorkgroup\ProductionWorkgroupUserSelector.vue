<template>
    <div class="w-100%">
        <n-data-table
            :columns="tableColumns"
            :data="(tableData || []).filter((item) => item.delFlag !== 1)"
            :single-line="false"
            bordered
            striped
        />
        <div class="flex-x-center mt">
            <n-space>
                <n-button type="primary" @click="addTableItem">新增一行</n-button>
                <n-button type="success" @click="onSubmit">保存全部</n-button>
            </n-space>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed, h, ref, watchEffect } from "vue";
import { DataTableColumns, NSelect } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { TableActions } from "@/components/TableActions";
import { useDicts } from "@/hooks";
import { UserSelector } from "@/components/UserSelector";
import { GET_STAFF_BY_USERNAMES } from "@/api/permission";

const props = withDefaults(defineProps<{ value: any[] }>(), {
    value: () => []
});

const emits = defineEmits(["confirm", "update:value"]);

// 字典操作
const { dictLibs, getDictLibs } = useDicts();

const setDictLibs = async () => {
    const dictName = ["job_type"];
    await getDictLibs(dictName);
};

watchEffect(() => {
    setDictLibs();
});

// 表单数据
interface RowProps {
    [key: string]: any;
}

const tableColumns = ref<DataTableColumns<RowProps>>([
    {
        title: "人员",
        align: "center",
        key: "username",
        render: (row) => {
            return h(UserSelector, {
                value: row.username,
                multiple: false,
                placeholder: "请选择人员",
                keyName: "username",
                onUpdateValue: (value: string) => {
                    row.username = value;
                    GET_STAFF_BY_USERNAMES({ usernames: value }).then((res) => {
                        if ((res.data.code === 0 && res.data.data[0]?.userId) ?? null) {
                            row.userId = res.data.data[0].userId;
                        }
                    });
                }
            });
        }
    },
    {
        title: "工种",
        align: "center",
        width: 150,
        key: "jobType",
        render: (row) => {
            return h(NSelect, {
                value: row.jobType,
                onUpdateValue: (v: any, o: any) => {
                    row.jobType = v;
                },
                options: (dictLibs["job_type"] ?? []) as any[],
                clearable: true,
                filterable: true,
                placeholder: "请选择工种"
            });
        }
    },
    {
        title: "操作",
        key: "action",
        align: "center",
        width: 80,
        render: (row, index) => {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "删除",
                        tertiary: true,
                        type: "error",
                        onClick: () => deleteItem(row, index)
                    }
                ]
            });
        }
    }
]);

// 可编辑表单配置
const tableItem: RowProps = {
    delFlag: 0,
    username: null,
    userId: null,
    jobType: null
};

const addTableItem = () => {
    tableData.value.push(cloneDeep(tableItem));
};

const deleteItem = (row: RowProps, index: number) => {
    if (row.id) {
        tableData.value.forEach((citem, cindex) => {
            if (row.id === citem.id) {
                citem.delFlag = 1;
            }
        });
    } else {
        tableData.value.splice(index, 1);
    }
};

const tableData = computed({ get: () => props.value, set: (val) => emits("update:value", val) });

// 提交
const onSubmit = () => {
    emits("confirm", tableData.value);
};
</script>
