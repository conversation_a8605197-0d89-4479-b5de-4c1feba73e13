<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, ref } from "vue";
import { DataTableColumns, NText, PaginationProps } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import { GET_PROJECT_PAYMENT_RETURN_APPLY_LIST } from "@/api/application/power";

interface RowProps {
    [key: string]: any;
}

onMounted(async () => {
    getSearchOptions();
    getTableData();
});

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    {
        label: "回款确认状态",
        prop: "applyStatus",
        type: "select",
        labelWidth: "100"
    }
]);

let searchOptions = ref<TableSearchbarOptions>({
    applyStatus: [
        { label: "已回款", value: 2 },
        { label: "未到账", value: 3 },
        { label: "延期到账", value: 4 }
    ]
});

let searchForm = ref<TableSearchbarData>({
    applyStatus: 2
});

let getSearchOptions = () => {};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, changeTableSelection } =
    useCommonTable<RowProps>("projectId");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "项目编号",
        key: "projectNumber",
        align: "center"
    },
    {
        title: "项目名称",
        key: "projectName",
        align: "center",
        render: (row) => {
            return h(NText, { type: "primary" }, () => row.projectName || "暂无");
        }
    },
    {
        title: "项目负责人",
        key: "projectLeaderName",
        align: "center",
        render: (row) => {
            return h(NText, { type: "primary" }, () => row.projectLeaderName || "暂无");
        }
    },
    // {
    //     title: "项目订单",
    //     key: "contractNumber",
    //     align: "center",
    //     render: (row) => {
    //         return row.contractNumber || "暂无";
    //     }
    // },
    {
        title: "实际到款时间",
        key: "returnTime",
        align: "center",
        render: (row) => {
            return row.returnTime || "暂无";
        }
    },
    {
        title: "实际到款金额",
        key: "returnAmount",
        align: "center",
        render: (row) => (row.returnAmount ? h(NText, { type: "primary" }, () => row.returnAmount) : "暂无")
    },
    {
        title: "款项类型",
        key: "returnType",
        align: "center",
        render: (row) => {
            // 1：预付款；2：首付款；3：进度款；4：尾款；5：履约保证金；6：投标保证金；7：质量保证金
            let text = "";
            switch (row.returnType) {
                case 1:
                    text = "预付款";
                    break;
                case 2:
                    text = "首付款";
                    break;
                case 3:
                    text = "进度款";
                    break;
                case 4:
                    text = "尾款";
                    break;
                case 5:
                    text = "履约保证金";
                    break;
                case 6:
                    text = "投标保证金";
                    break;
                case 7:
                    text = "质量保证金";
                    break;
                default:
                    text = "暂无";
                    break;
            }
            return h(NText, { type: "primary" }, () => text);
        }
    },
    {
        title: "核实财务",
        key: "confirmUserName",
        align: "center",
        render: (row) => (row.confirmUserName ? h(NText, { type: "primary" }, () => row.confirmUserName) : "暂无")
    }
]);

let tablePagination = ref<PaginationProps>({
    page: 1,
    pageSize: 10,
    itemCount: 0,
    pageSizes: [10, 50, 100],
    showSizePicker: true,
    showQuickJumper: true,
    displayOrder: ["size-picker", "pages", "quick-jumper"],
    onChange: (page: number) => {
        tablePagination.value.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.value.pageSize = pageSize;
        tablePagination.value.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_PROJECT_PAYMENT_RETURN_APPLY_LIST({
        current: tablePagination.value.page,
        size: tablePagination.value.pageSize,
        ...searchForm.value,
        nonContractFlag: 1
    }).then((res) => {
        tableData.value = res.data.data.records || [];
        tablePagination.value.itemCount = res.data.data.total;
        tableLoading.value = false;
    });
};

let onSearch = () => {
    getTableData();
};
</script>
