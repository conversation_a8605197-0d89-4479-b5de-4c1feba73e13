import { computed, defineComponent, reactive, ref, watch, watchEffect } from "vue";
import {
    TableSearchbar,
    type TableSearchbarConfig,
    type TableSearchbarData,
    type TableSearchbarOptions
} from "@/components/TableSearchbar";
import { GET_INSPECT_TASK_MATERIAL_LIST, GET_IRON_PROJECT_PROCESSES_TOP_DETAIL } from "@/api/application/TowerScan";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import { TableActions } from "@/components/TableActions";
import TowerScanEngineeringProcessesPartsExecution from "../Engineering/Processes/TowerScanEngineeringProcessesPartsExecution";

export default defineComponent({
    name: "TowerScanQualityInspectionPartDetail",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        // 获取详情
        interface DetailDataProps {
            [key: string]: any;
        }

        const detailData = ref<DetailDataProps>({});

        const getDetail = async () => {
            if (props.configData.typeId) {
                await GET_IRON_PROJECT_PROCESSES_TOP_DETAIL({ typeId: props.configData.typeId }).then((res) => {
                    if (res.data.code === 0) {
                        detailData.value = res.data.data;
                    }
                });
            }
        };

        const onClose = () => {
            changeModalShow(false);
            emit("refresh");
            // 清空数据
            detailData.value = {};
            tableData.value = [];
            tablePagination.itemCount = 0;
            materialClassifyActive.value = "";
            techniqueActive.value = "";
            techniqueOptions.value = [];
            searchForm.value = {
                materialCode: null,
                materialSpec: null,
                materialSize: null,
                finishStatus: null
            };
        };

        // 材料分类tab - 根据数据动态生成
        const materialClassifyOptions = computed(() => {
            const options: Array<{ label: string; value: string }> = [];

            // 角钢
            if (detailData.value?.jgTechniqueList && detailData.value.jgTechniqueList.length > 0) {
                options.push({ label: "角钢", value: "1" });
            }

            // 钢板
            if (detailData.value?.gbTechniqueList && detailData.value.gbTechniqueList.length > 0) {
                options.push({ label: "钢板", value: "2" });
            }

            // 圆钢
            if (detailData.value?.ygangTechniqueList && detailData.value.ygangTechniqueList.length > 0) {
                options.push({ label: "圆钢", value: "3" });
            }

            // 圆管
            if (detailData.value?.yguanTechniqueList && detailData.value.yguanTechniqueList.length > 0) {
                options.push({ label: "圆管", value: "4" });
            }

            // 槽钢
            if (detailData.value?.cgTechniqueList && detailData.value.cgTechniqueList.length > 0) {
                options.push({ label: "槽钢", value: "5" });
            }

            return options;
        });

        const materialClassifyActive = ref<string>("");

        // 工艺ID tab（仅用于普通材料）
        const techniqueOptions = ref<any[]>([]);
        const techniqueActive = ref<string>("");

        // 根据材料分类获取对应的工艺选项
        const getTechniqueOptions = () => {
            let techniqueList: any[] = [];

            // 如果传入了具体的techniqueId，则只显示该工艺相关的内容
            if (props.configData.techniqueId) {
                // 遍历所有材料分类，找到包含指定techniqueId的分类
                const allTechniqueLists = [
                    { classify: "1", list: detailData.value?.jgTechniqueList || [] },
                    { classify: "2", list: detailData.value?.gbTechniqueList || [] },
                    { classify: "3", list: detailData.value?.ygangTechniqueList || [] },
                    { classify: "4", list: detailData.value?.yguanTechniqueList || [] },
                    { classify: "5", list: detailData.value?.cgTechniqueList || [] }
                ];

                // 找到包含指定techniqueId的材料分类
                for (const category of allTechniqueLists) {
                    const foundTechnique = category.list.find((item: any) => String(item.id) === String(props.configData.techniqueId));
                    if (foundTechnique) {
                        // 找到了，设置对应的材料分类和工艺
                        materialClassifyActive.value = category.classify;
                        techniqueOptions.value = category.list;
                        techniqueActive.value = String(foundTechnique.id);
                        return;
                    }
                }
                
                // 如果没找到，清空选项
                techniqueOptions.value = [];
                techniqueActive.value = "";
                materialClassifyActive.value = "";
                return;
            }

            // 如果没有传入techniqueId，保持原有逻辑
            // 如果没有选中的材料分类，清空工艺选项
            if (!materialClassifyActive.value) {
                techniqueOptions.value = [];
                techniqueActive.value = "";
                return;
            }

            switch (materialClassifyActive.value) {
                case "1": // 角钢
                    techniqueList = detailData.value?.jgTechniqueList || [];
                    break;
                case "2": // 钢板
                    techniqueList = detailData.value?.gbTechniqueList || [];
                    break;
                case "3": // 圆钢
                    techniqueList = detailData.value?.ygangTechniqueList || [];
                    break;
                case "4": // 圆管
                    techniqueList = detailData.value?.yguanTechniqueList || [];
                    break;
                case "5": // 槽钢
                    techniqueList = detailData.value?.cgTechniqueList || [];
                    break;
                default:
                    techniqueList = [];
            }

            // 格式化工艺选项数据
            techniqueOptions.value = techniqueList.map((item: any) => ({ ...item }));

            // 重置工艺选择为第一个选项，确保每次材料分类切换时都重新选择
            const newTechniqueActive = techniqueOptions.value.length > 0 ? String(techniqueOptions.value[0].id) : "";

            // 只有当工艺选择真正改变时才触发数据更新
            if (techniqueActive.value !== newTechniqueActive) {
                techniqueActive.value = newTechniqueActive;
                // 因为 watch 会监听 techniqueActive 的变化，所以不需要手动调用 handleTabChange
            } else {
                // 如果工艺选择没有改变，但工艺列表已经更新，仍需要刷新数据
                if (show.value) {
                    handleTabChange();
                }
            }
        };

        // 监听变化并重新查询
        const handleTabChange = () => {
            if (show.value && materialClassifyActive.value) {
                tablePagination.page = 1;
                getTableData();
            }
        };

        watch(
            () => materialClassifyActive.value,
            () => {
                // 如果传入了具体的techniqueId，不需要监听材料分类变化
                if (props.configData.techniqueId) {
                    return;
                }
                
                if (show.value && detailData.value) {
                    // 材料分类切换时重新获取工艺选项，这会自动重置工艺选择
                    getTechniqueOptions();
                }
            }
        );

        watch(() => techniqueActive.value, () => {
            // 如果传入了具体的techniqueId，确保使用传入的ID
            if (props.configData.techniqueId && techniqueActive.value !== String(props.configData.techniqueId)) {
                techniqueActive.value = String(props.configData.techniqueId);
                return;
            }
            handleTabChange();
        });

        // 监听材料分类选项变化，确保当前选中的tab有效
        watch(
            () => materialClassifyOptions.value,
            (newOptions) => {
                // 如果传入了具体的techniqueId，不需要处理材料分类选项变化
                if (props.configData.techniqueId) {
                    return;
                }
                
                if (newOptions.length > 0) {
                    // 检查当前选中的分类是否还存在
                    const isCurrentValid = newOptions.some((option) => option.value === materialClassifyActive.value);

                    if (!isCurrentValid) {
                        // 如果当前选中的分类不存在，选择第一个可用的
                        materialClassifyActive.value = newOptions[0].value;
                    }
                } else {
                    // 如果没有可用选项，清空选择
                    materialClassifyActive.value = "";
                }
            },
            { immediate: true }
        );

        // 搜索项 - 根据材料分类动态配置
        const searchConfig = computed<TableSearchbarConfig>(() => [
            { label: "零件号", prop: "materialCode", type: "input" },
            { label: "规格", prop: "materialSpec", type: "input" },
            { label: "尺寸", prop: "materialSize", type: "input" },
            { label: "状态", prop: "finishStatus", type: "select" }
        ]);

        const searchOptions = ref<TableSearchbarOptions>({
            finishStatus: [
                { label: "待完成", value: 1 },
                { label: "已完成", value: 2 },
                { label: "进行中", value: 3 }
            ],
            materialClassify: [
                { label: "角钢", value: 1 },
                { label: "钢板", value: 2 },
                { label: "圆钢", value: 3 },
                { label: "圆管", value: 4 },
                { label: "槽钢", value: 5 }
            ]
        });

        const getSearchOptions = async () => {};

        const searchForm = ref<TableSearchbarData>({
            materialCode: null,
            materialSpec: null,
            materialSize: null,
            finishStatus: null
        });

        const onSearch = () => {
            tablePagination.page = 1;
            tablePagination.pageSize = 10;
            getTableData();
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            { title: "零件编号", key: "materialCode", align: "center" },
            {
                title: "零件类别",
                key: "materialClassify",
                align: "center",
                render: (row) => {
                    if (String(row.materialClassify) === "1") {
                        return <n-text type="info">角钢</n-text>;
                    } else if (String(row.materialClassify) === "2") {
                        return <n-text type="info">钢板</n-text>;
                    } else if (String(row.materialClassify) === "3") {
                        return <n-text type="info">圆钢</n-text>;
                    } else if (String(row.materialClassify) === "4") {
                        return <n-text type="info">圆管</n-text>;
                    } else if (String(row.materialClassify) === "5") {
                        return <n-text type="info">槽钢</n-text>;
                    } else {
                        return <n-text type="info">/</n-text>;
                    }
                }
            },
            {
                title: "质检状态",
                key: "finishStatus",
                align: "center",
                render: (row) => {
                    if (row.finishStatus === 1) {
                        return <n-text type="error">待完成</n-text>;
                    } else if (row.finishStatus === 2) {
                        return <n-text type="success">已完成</n-text>;
                    } else if (row.finishStatus === 3) {
                        return <n-text type="info">进行中</n-text>;
                    } else {
                        return <n-text>/</n-text>;
                    }
                }
            },
            { title: "材质", key: "materialQuality", align: "center", render: (row) => row.materialQuality ?? "/" },
            { title: "规格", key: "materialSpec", align: "center", render: (row) => row.materialSpec ?? "/" },
            { title: "尺寸", key: "materialSize", align: "center", render: (row) => row.materialSize ?? "/" },
            {
                title: "单基数量",
                key: "singleBaseQuantity",
                align: "center",
                render: (row) => row.singleBaseQuantity ?? "/"
            },
            {
                title: "多基数量",
                key: "multiBaseQuantity",
                align: "center",
                render: (row) => row.multiBaseQuantity ?? "/"
            },
            { title: "单重", key: "singleWeight", align: "center", render: (row) => row.singleWeight ?? "/" },
            { title: "多基总重", key: "multiWeight", align: "center", render: (row) => row.multiWeight ?? "/" },
            { title: "下料数量", key: "planQuantity", align: "center", render: (row) => row.planQuantity ?? "/" },
            { title: "下料重量", key: "planWeight", align: "center", render: (row) => row.planWeight ?? "/" },
            {
                title: "备注",
                key: "remark",
                align: "center",
                render: (row) => (!!row.remark && row.remark !== "" ? row.remark : "/")
            },
            {
                title: "质检操作",
                key: "action",
                align: "center",
                width: 110,
                render: (row) => {
                    return (
                        <TableActions
                            type="button"
                            buttonActions={[
                                {
                                    label: "质检记录",
                                    tertiary: true,
                                    type: "primary",
                                    onClick: () => handleQualityCheck(row)
                                }
                            ]}
                        />
                    );
                }
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            // 如果传入了具体的techniqueId，直接使用它
            if (props.configData.techniqueId) {
                tableLoading.value = true;

                const params: any = {
                    current: tablePagination.page,
                    size: tablePagination.pageSize,
                    qualityTaskId: props.configData.id,
                    materialClassify: materialClassifyActive.value,
                    techniqueId: props.configData.techniqueId,
                    ...searchForm.value
                };

                GET_INSPECT_TASK_MATERIAL_LIST(params).then((res) => {
                    tableLoading.value = false;
                    if (res.data.code === 0) {
                        tableData.value = res.data.data.records;
                        tablePagination.itemCount = res.data.data.total;
                    } else {
                        tableData.value = [];
                        tablePagination.itemCount = 0;
                    }
                });
                return;
            }

            // 原有逻辑：如果没有选中的材料分类，不加载数据
            if (!materialClassifyActive.value) {
                tableData.value = [];
                tablePagination.itemCount = 0;
                return;
            }

            tableLoading.value = true;

            const params: any = {
                current: tablePagination.page,
                size: tablePagination.pageSize,
                qualityTaskId: props.configData.id,
                materialClassify: materialClassifyActive.value,
                ...searchForm.value
            };

            // 使用当前选中的工艺ID
            if (techniqueActive.value) {
                params.techniqueId = techniqueActive.value;
            }

            GET_INSPECT_TASK_MATERIAL_LIST(params).then((res) => {
                tableLoading.value = false;
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                } else {
                    tableData.value = [];
                    tablePagination.itemCount = 0;
                }
            });
        };

        // 执行情况弹窗
        const executionShow = ref(false);
        const executionConfigData = ref<any>({});

        // 质检操作处理
        const handleQualityCheck = (row: RowProps) => {
            // 获取当前的工艺ID - 如果传入了具体的techniqueId则使用它，否则使用当前选中的工艺
            const currentTechniqueId = props.configData.techniqueId || techniqueActive.value;
            
            // 获取当前工艺的processType
            let currentProcessType = 2; // 默认值
            
            // 从工艺选项中查找当前工艺的processType
            if (currentTechniqueId && techniqueOptions.value.length > 0) {
                const currentTechnique = techniqueOptions.value.find(
                    (item: any) => String(item.id) === String(currentTechniqueId)
                );
                console.log(currentTechnique);
                if (currentTechnique && currentTechnique.processType) {
                    currentProcessType = currentTechnique.processType;
                }
            }
            
            executionConfigData.value = {
                taskMaterialId: row.id,
                materialCode: row.materialCode,
                typeName: detailData.value?.ironType?.typeName,
                projectName: detailData.value?.ironProject?.projectName,
                // 传递当前的工艺ID作为计划工艺ID
                planTechniqueId: currentTechniqueId,
                // 传递单重字段用于自动计算工艺填报数量
                singleWeight: row.singleWeight,
                // 传递processType给执行组件
                processType: currentProcessType
            };
            executionShow.value = true;
        };

        watchEffect(async () => {
            if (show.value && props.configData.typeId) {
                await getDetail();
                // 获取详情后再获取工艺选项
                getTechniqueOptions();
                
                // 如果传入了具体的techniqueId，在工艺选项设置完成后立即加载数据
                if (props.configData.techniqueId) {
                    // 使用nextTick确保状态更新完成后再加载数据
                    await new Promise(resolve => setTimeout(resolve, 0));
                    getTableData();
                }
            }
        });

        // 计算是否应该显示材料分类tabs - 如果传入了具体的techniqueId，则不显示tabs
        const shouldShowMaterialTabs = computed(() => {
            return !props.configData.techniqueId && materialClassifyOptions.value.length > 0;
        });

        // 计算是否应该显示工艺tabs - 如果传入了具体的techniqueId，则不显示tabs
        const shouldShowTechniqueTabs = computed(() => {
            return !props.configData.techniqueId && techniqueOptions.value.length > 0;
        });

        return () => (
            <div>
                <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                    <n-card
                        title={`质检 - 工序表详情 - ${props.configData.planName || "/"} - ${
                            props.configData.typeName || "/"
                        }`}
                        class={"w-1200px my-5vh"}
                        closable
                        onClose={onClose}
                    >
                        <n-form label-placement="left" label-width="auto">
                            <n-grid cols={12} x-gap={16}>
                                <n-form-item-gi required span={4} label="工程名称">
                                    {detailData.value?.ironProject?.projectName ?? "/"}
                                </n-form-item-gi>
                                <n-form-item-gi required span={4} label="工程简称">
                                    {detailData.value?.ironProject?.projectAs ?? "/"}
                                </n-form-item-gi>
                                <n-form-item-gi required span={4} label="合同号">
                                    {detailData.value?.ironProject?.contractNumber ?? "/"}
                                </n-form-item-gi>
                                <n-form-item-gi required span={4} label="塔型">
                                    {detailData.value?.ironType?.typeName ?? "/"}
                                </n-form-item-gi>
                                <n-form-item-gi required span={4} label="电压等级">
                                    {detailData.value?.ironProject?.powerLevel ?? "/"}
                                </n-form-item-gi>
                                <n-form-item-gi span={12}>
                                    <div>
                                        <div class="text-18px font-bold">导入工序要求：</div>
                                    </div>
                                </n-form-item-gi>
                                <n-form-item-gi required span={4} label="组装工序">
                                    <n-text type="info">
                                        {String(detailData.value?.zzFlag) === "1" ? "有" : "无"}
                                    </n-text>
                                </n-form-item-gi>
                                <n-form-item-gi required span={4} label="电焊工序">
                                    <n-text type="info">
                                        {String(detailData.value?.dhFlag) === "1" ? "有" : "无"}
                                    </n-text>
                                </n-form-item-gi>
                                {(detailData.value?.jgTechniqueList ?? []).length > 0 && (
                                    <n-form-item-gi required span={12} label="角钢工序">
                                        <n-text type="info">
                                            {detailData.value.jgTechniqueList
                                                .map((item: any) => item.techniqueName)
                                                .join(",")}
                                        </n-text>
                                    </n-form-item-gi>
                                )}
                                {(detailData.value?.gbTechniqueList ?? []).length > 0 && (
                                    <n-form-item-gi required span={12} label="钢板工序">
                                        <n-text type="info">
                                            {detailData.value.gbTechniqueList
                                                .map((item: any) => item.techniqueName)
                                                .join(",")}
                                        </n-text>
                                    </n-form-item-gi>
                                )}
                                {(detailData.value?.ygangTechniqueList ?? []).length > 0 && (
                                    <n-form-item-gi required span={12} label="圆钢工序">
                                        <n-text type="info">
                                            {detailData.value.ygangTechniqueList
                                                .map((item: any) => item.techniqueName)
                                                .join(",")}
                                        </n-text>
                                    </n-form-item-gi>
                                )}
                                {(detailData.value?.yguanTechniqueList ?? []).length > 0 && (
                                    <n-form-item-gi required span={12} label="圆管工序">
                                        <n-text type="info">
                                            {detailData.value.yguanTechniqueList
                                                .map((item: any) => item.techniqueName)
                                                .join(",")}
                                        </n-text>
                                    </n-form-item-gi>
                                )}
                                {(detailData.value?.cgTechniqueList ?? []).length > 0 && (
                                    <n-form-item-gi required span={12} label="槽钢工序">
                                        <n-text type="info">
                                            {detailData.value.cgTechniqueList
                                                .map((item: any) => item.techniqueName)
                                                .join(",")}
                                        </n-text>
                                    </n-form-item-gi>
                                )}
                            </n-grid>
                        </n-form>

                        {/* 材料分类tabs - 根据数据动态显示，如果传入了具体的techniqueId则不显示 */}
                        {shouldShowMaterialTabs.value && (
                            <n-tabs
                                value={materialClassifyActive.value}
                                animated
                                class="flex-fixed-200 mt-2"
                                type="bar"
                                onUpdate:value={(val: string) => {
                                    materialClassifyActive.value = val;
                                }}
                            >
                                {materialClassifyOptions.value.map((item) => (
                                    <n-tab-pane key={item.value} name={item.value} tab={item.label} />
                                ))}
                            </n-tabs>
                        )}



                        <n-card class="mt-2">
                            <TableSearchbar
                                form={searchForm.value}
                                config={searchConfig.value}
                                options={searchOptions.value}
                                onSearch={onSearch}
                                buttonAlign="left"
                            />
                        </n-card>

                        {/* 工艺ID tabs - 只有在有工艺选项时才显示，如果传入了具体的techniqueId则不显示 */}
                        {shouldShowTechniqueTabs.value && (
                            <n-tabs
                                key={`technique-tabs-${materialClassifyActive.value}`}
                                class="mt-2"
                                v-model:value={techniqueActive.value}
                                animated
                                type="bar"
                                onUpdate:value={(val: string) => (techniqueActive.value = val)}
                            >
                                {techniqueOptions.value.map((item) => (
                                    <n-tab-pane key={item.id} name={String(item.id)} tab={item.treeTechniqueName} />
                                ))}
                            </n-tabs>
                        )}



                        <n-card class="mt">
                            <n-data-table
                                columns={tableColumns.value}
                                data={tableData.value}
                                loading={tableLoading.value}
                                pagination={tablePagination}
                                row-key={tableRowKey}
                                single-line={false}
                                bordered
                                remote
                                striped
                                onUpdate:checked-row-keys={changeTableSelection}
                            />
                        </n-card>
                    </n-card>
                </n-modal>

                {/* 执行情况弹窗 */}
                <TowerScanEngineeringProcessesPartsExecution
                    v-model:show={executionShow.value}
                    configData={executionConfigData.value}
                    permissions={{
                        add: true, // 质检模块支持新增质检记录
                        edit: true, // 质检模块保持原有功能
                        delete: true, // 质检模块保持原有功能
                        history: true // 质检模块保持原有功能
                    }}
                    onRefresh={() => {
                        getTableData();
                    }}
                />
            </div>
        );
    }
});
