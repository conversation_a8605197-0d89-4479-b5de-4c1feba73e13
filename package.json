{"name": "info-system", "private": true, "version": "1.0.6", "description": "信息管理系统客户端", "author": "浙江永达电力股份有限公司", "main": "app/main.js", "scripts": {"dev": "vite", "dev-eletron": "vite --mode eletron", "build": "vue-tsc --noEmit && vite build", "force-build": "vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "devApp": "wait-on tcp:23333 && electron .", "devApp:server": "concurrently -k \"npm run dev-eletron\" \"npm run hotDevApp\"", "hotDevApp": "nodemon --exec electron . --w . --ext .js,.html,.htm,.css,.vue,.ts,.scss", "buildApp": "vite build --mode eletron && electron-builder"}, "build": {"productName": "永达信息化系统客户端", "publish": [{"provider": "generic", "url": "https://upapp.test.zjyonder.com/"}], "appId": "ydsysteminfo.app", "copyright": "Copyright © 2024 <浙江永达电力股份有限公司>", "win": {"icon": "./public/logo.png", "target": "nsis"}, "mac": {"icon": "./public/logo.png", "category": "ydsysteminfo.app-category.business", "target": "dmg"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}, "files": ["dist/**/*", "public/**/*", "app/**/*", "node_modules/**/*", "package.json"], "directories": {"buildResources": "public", "output": "dist_app"}}, "dependencies": {"@bpmn-io/properties-panel": "3.10.0", "@vicons/antd": "0.12.0", "@vicons/fluent": "0.12.0", "@vue-office/docx": "1.6.2", "@vue-office/excel": "1.7.11", "@vue-office/pdf": "2.0.2", "@vueuse/core": "9.13.0", "@wangeditor/editor": "5.1.23", "@wangeditor/editor-for-vue": "5.1.12", "axios": "0.27.2", "bpmn-js": "14.0.0", "bpmn-js-properties-panel": "5.5.0", "dayjs": "1.11.4", "diagram-js": "12.8.0", "didi": "9.0.2", "echarts": "5.5.1", "electron-is-dev": "3.0.1", "electron-store": "10.0.0", "electron-updater": "6.1.8", "electron-win-state": "1.1.22", "element-plus": "2.8.3", "html2canvas": "1.4.1", "jinrishici": "1.0.6", "js-base64": "3.7.7", "jspdf": "2.5.1", "katex": "0.16.8", "lodash-es": "4.17.21", "naive-ui": "2.34.4", "pinia": "2.0.17", "qrcode.vue": "3.4.1", "vue": "3.2.37", "vue-router": "4.1.3", "vue3-print-nb": "0.1.4", "vue3-tree-org": "4.2.2", "vuedraggable": "4.1.0", "vxe-table": "4.7.59"}, "devDependencies": {"@electron-forge/cli": "7.3.1", "@types/jspdf": "2.0.0", "@types/katex": "0.16.0", "@vitejs/plugin-vue": "3.0.0", "@vitejs/plugin-vue-jsx": "3.0.1", "autoprefixer": "10.4.7", "bytenode": "1.5.6", "concurrently": "8.2.2", "cross-env": "7.0.3", "electron": "29.1.6", "electron-builder": "24.13.3", "mockjs": "1.1.0", "nodemon": "3.1.0", "prettier": "2.7.1", "sass": "1.54.0", "sass-loader": "13.0.2", "typescript": "4.8.4", "unocss": "0.45.12", "vite": "3.0.0", "vite-plugin-mock": "2.9.6", "vue-tsc": "0.38.4", "wait-on": "7.2.0"}}