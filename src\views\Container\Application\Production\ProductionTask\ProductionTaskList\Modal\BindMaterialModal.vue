<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-666px" closable title="绑定领料单" @close="changeModalShow(false)">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-grid :cols="12" x-gap="16">
                        <n-form-item-gi :span="12" label="领料单来源" path="status" required>
                            <n-radio-group v-model:value="formData.status">
                                <n-space>
                                    <n-radio :value="0">订单内</n-radio>
                                    <n-radio :value="1">订单外</n-radio>
                                </n-space>
                            </n-radio-group>
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="领料单" path="materialApplId" v-if="formData.status === 0">
                            <n-select
                                v-model:value="formData.materialApplId"
                                :options="materialApplIdOptions"
                                label-field="businessKey"
                                placeholder="请选择领料单"
                                value-field="id"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi
                            :span="12"
                            label="领料单"
                            path="materialApplId"
                            v-else-if="formData.status === 1"
                        >
                            <n-input
                                v-model:value="formData.materialApplId"
                                class="w-100%"
                                clearable
                                placeholder="请输入领料单号"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12">
                            <n-space>
                                <n-button type="primary" @click="onSubmit">提交</n-button>
                                <n-button @click="changeModalShow(false)">取消</n-button>
                            </n-space>
                        </n-form-item-gi>
                    </n-grid>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref, watchEffect } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { BIND_MATERIAL_APPLY, GET_PO_MATERIAL_RECORD } from "@/api/application/production";

let props = withDefaults(defineProps<{ show: boolean; configData: UnKnownObject }>(), {
    show: () => false
});

let emits = defineEmits(["update:show", "refresh"]);

let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

let changeModalShow = (show: boolean) => emits("update:show", show);

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = computed(() => {
    return {
        materialApplId: {
            required: true,
            message: "请选择或输入领料单号",
            trigger: formData.value.status === 0 ? ["blur", "change"] : ["input", "blur"]
        }
    };
});

// 表单数据
interface FormDataProps {
    status: number;
    materialApplId: Nullable<string | number>;
}

let initFormData: FormDataProps = {
    status: 0,
    materialApplId: null
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 获取选项
let materialApplIdOptions = ref<any[]>([]);

let getOptions = async () => {
    await GET_PO_MATERIAL_RECORD({ poId: props.configData.poId }).then((res) => {
        if (res.data.code === 0) materialApplIdOptions.value = res.data.data ?? [];
    });
};

watchEffect(() => {
    if (show.value) {
        getOptions();
    }
});

let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;

    BIND_MATERIAL_APPLY({ reqId: props.configData.poId, materialApplId: formData.value.materialApplId }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("绑定成功");
            changeModalShow(false);
            clearFrom();
            emits("refresh");
        } else {
            window.$message.error("绑定失败");
        }
    });
};
</script>
