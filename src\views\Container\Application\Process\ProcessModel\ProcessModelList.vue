<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            >
                <template #buttons>
                    <n-button type="success" @click="openAddModal()">新增流程</n-button>
                </template>
            </table-searchbar>
        </n-card>
        <n-card class="mt" hoverable>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <!--新版新增编辑弹窗-->
        <ProcessModelDesigner v-model:show="addModal.show" @refresh="getTableData" />
        <ProcessModelDesigner
            v-model:show="editModal.show"
            :config-data="editModal.configData"
            @refresh="getTableData"
        />
        <process-launch v-model:show="launchModal.show" :config-data="launchModal.configData" />
    </div>
</template>

<script lang="ts" setup>
import type { CSSProperties } from "vue";
import { h, onMounted, reactive, ref } from "vue";
import type { DataTableColumns } from "naive-ui";
import { NSwitch } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { TableActions } from "@/components/TableActions";
import { useCommonTable, useDicts } from "@/hooks";
import ProcessModelDesigner from "../ProcessModelDesigner/ProcessModelDesigner.vue";
import { ProcessLaunch } from "../components";
import { DELETE_OA_MODELS, DISABLE_OA_MODEL, ENABLE_OA_MODEL, GET_OA_MODEL_LIST } from "@/api/application/oa";

type RowProps = Record<string, any>;

onMounted(async () => {
    await getSearchOptions();
    getTableData();
});

let { getDict } = useDicts();

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { prop: "name", type: "input", label: "流程名称" },
    // { prop: "modelNumber", type: "input", label: "流程编号" },
    { prop: "category", type: "select", label: "流程分类" }
]);

let searchOptions = ref<TableSearchbarOptions>({ category: [] });

let searchForm = ref<TableSearchbarData>({ name: null, category: null });

let getSearchOptions = async () => {
    searchOptions.value.category = await getDict("flow_category");
};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "流程分类",
        key: "category",
        align: "center"
    },
    {
        title: "流程编号",
        key: "modelNumber",
        align: "center",
        render: (row: RowProps) => {
            if (row.modelNumber != null) {
                return row.modelNumber;
            } else {
                return "暂无编号";
            }
        }
    },
    {
        title: "流程名称",
        key: "name",
        align: "center"
    },
    {
        title: "创建时间",
        key: "createTime",
        align: "center"
    },
    {
        title: "状态",
        key: "modelState",
        align: "center",
        render(row) {
            return h(
                NSwitch,
                {
                    value: row.modelState,
                    uncheckedValue: 0,
                    checkedValue: 1,
                    railStyle: ({ checked }: { checked: boolean }) => {
                        let style: CSSProperties = {};
                        checked ? (style.background = "#2080f0") : (style.background = "#d03050");
                        return style;
                    },
                    onUpdateValue: async (val) => {
                        if (val === 1) {
                            await ENABLE_OA_MODEL({ modelIds: row.id }).then((res) => {
                                if (res.data.code === 0) {
                                    window.$message.success("启用成功");
                                    row.modelState = val;
                                    getTableData();
                                }
                            });
                        } else {
                            await DISABLE_OA_MODEL({ modelIds: row.id }).then((res) => {
                                if (res.data.code === 0) {
                                    window.$message.success("停用成功");
                                    row.modelState = val;
                                    getTableData();
                                }
                            });
                        }
                    }
                },
                {
                    checked: () => "启用",
                    unchecked: () => "停用"
                }
            );
        }
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: 200,
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "编辑流程",
                        tertiary: true,
                        type: "primary",
                        onClick: () => {
                            openEditModal(row);
                        }
                    },
                    {
                        label: "发起流程",
                        tertiary: true,
                        type: "warning",
                        onClick: () => {
                            openLaunchModal(row);
                        }
                    }
                    // {
                    //     label: "删除",
                    //     type: "error",
                    //     tertiary: true,
                    //     onClick: () => {
                    //         if (row.id) onDelete(row.id);
                    //     }
                    // }
                ]
            });
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_OA_MODEL_LIST({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        ...searchForm.value
    }).then((res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data.records;
            tablePagination.itemCount = res.data.data.total;
            tableLoading.value = false;
        }
    });
};

// 搜索
let onSearch = () => {
    tablePagination.page = 1;
    tablePagination.pageSize = 10;
    getTableData();
};

// 新增
let addModal = ref<{ show: boolean }>({ show: false });
let openAddModal = () => (addModal.value = { show: true });

// 编辑
let editModal = ref<{ show: boolean; configData: UnKnownObject }>({
    show: false,
    configData: {}
});
let openEditModal = (row: RowProps) => {
    editModal.value = {
        show: true,
        configData: row
    };
};

// 删除
let onDelete = (id?: string | number) => {
    if (!id && tableSelection.value.length < 1) return window.$message.error("请选择要删除的数据");
    window.$dialog.warning({
        title: "警告",
        content: `确定删除${id ? "该" : "选中"}流程吗？`,
        positiveText: "删除",
        negativeText: "取消",
        onPositiveClick: () => {
            let ids: (string | number)[];
            id ? (ids = [id]) : (ids = tableSelection.value);
            DELETE_OA_MODELS({ ids: ids.join(",") }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("删除成功");
                    onSearch();
                } else {
                    window.$message.error(res.data.msg ?? "删除失败");
                }
            });
        }
    });
};

// 发起流程
let launchModal = ref<{ show: boolean; configData: RowProps }>({ show: false, configData: {} });

let openLaunchModal = (row: RowProps) => {
    launchModal.value = { show: true, configData: row };
};
</script>
