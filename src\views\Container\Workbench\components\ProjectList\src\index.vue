<template>
    <div class="p-4">
        <!--<n-tabs type="line">-->
        <!--    <n-tab-pane name="待办" />-->
        <!--    <n-tab-pane name="已完成" />-->
        <!--</n-tabs>-->
        <n-data-table
            :columns="tableColumns"
            :data="tableData"
            :loading="tableLoading"
            :row-key="tableRowKey"
            :single-line="false"
            bordered
            remote
            striped
        />
    </div>
</template>

<script lang="ts" setup>
import { useCommonTable } from "@/hooks";
import { h, onMounted, ref } from "vue";
import { DataTableColumns, NText } from "naive-ui";
import { NDataTable } from "naive-ui";
import { GET_PROCESS_TASK_RUNNING } from "@/api/application/oa";
import { GET_PROJECT_LIST_PREHANDLE } from "@/api/application/sale";

interface RowProps<T = string | null> {
    [propName: string]: any;
}

onMounted(() => {
    getTableData();
});

// 数据列表
let { tableRowKey, tableData, tableLoading } = useCommonTable<RowProps>("processInstanceId");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        title: "项目编号",
        key: "projectNumber",
        align: "center"
    },
    {
        title: "项目名称",
        key: "projectName",
        align: "center",
        render: (row: RowProps) => {
            return h(NText, { type: "primary" }, () => row.projectName);
        }
    },
    {
        title: "招投标项目编号",
        key: "projectCode",
        align: "center",
        render: (row: RowProps) => {
            return row.projectCode || "暂无";
        }
    },
    {
        title: "订单编号",
        key: "contractNumber",
        align: "center",
        render: (row: RowProps) => {
            return row.contractNumber || "暂无";
        }
    },
    {
        title: "待处理节点",
        key: "nextNodeName",
        align: "center"
    },
    {
        title: "前置负责人",
        key: "nodeDirectorName",
        align: "center",
        render: (row: RowProps) => {
            return h(NText, { type: "primary" }, () => row.projectLeaderName || "暂无");
        }
    },
    {
        title: "是否超时",
        key: "timeOutFlag",
        align: "center",
        render: (row: RowProps) => {
            if (row.timeOutFlag) {
                return h(NText, { type: "error" }, () => "已超时");
            } else {
                return h(NText, { type: "success" }, () => "未超时");
            }
        }
    }
]);

let getTableData = () => {
    tableLoading.value = true;
    GET_PROJECT_LIST_PREHANDLE({
        current: 1,
        size: 5
    }).then((res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data.records;
            tableLoading.value = false;
        }
    });
};
</script>
