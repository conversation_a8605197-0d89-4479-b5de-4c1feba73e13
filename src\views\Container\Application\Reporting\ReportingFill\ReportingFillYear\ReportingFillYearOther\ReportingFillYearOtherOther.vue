<template>
    <n-spin :show="loadingShow">
        <template #description>正在处理中，请耐心等候</template>
        <div class="flex">
            <n-card class="flex-fixed-200">
                <div class="flex-y-center mb">
                    <n-input v-model:value="treePattern" clearable placeholder="搜索" />
                </div>
                <n-tree
                    v-model:selected-keys="treeSelectKeys"
                    :cancelable="false"
                    :data="treeData"
                    :pattern="treePattern"
                    :show-irrelevant-nodes="false"
                    block-line
                    children-field="childrenList"
                    default-expand-all
                    key-field="id"
                    label-field="categoryName"
                    selectable
                    @update:selected-keys="selectTreeNode"
                />
            </n-card>
            <div class="flex-1 ml">
                <n-card>
                    <table-searchbar
                        auto-search
                        v-model:form="searchForm"
                        :config="searchConfig"
                        :options="searchOptions"
                        @search="onSearch"
                        @componentClick="onComponentClick"
                    />
                </n-card>
                <n-card class="mt">
                    <n-space class="mb">
                        <n-button secondary type="primary" @click="addTableItem">新增记录</n-button>
                        <n-button secondary type="success" @click="saveTableData">保存填写</n-button>
                    </n-space>
                    <n-data-table
                        :columns="tableColumns"
                        :data="tableData"
                        :loading="tableLoading"
                        :row-key="tableRowKey"
                        :single-line="false"
                        bordered
                        remote
                        striped
                        @update:checked-row-keys="changeTableSelection"
                    />
                </n-card>
            </div>
        </div>
    </n-spin>
</template>

<script lang="ts" setup>
import { h, onMounted, ref, watchEffect } from "vue";
import {
    GET_CONFIG_WORK_GROUP_LIST,
    GET_OTHER_CATEGORY_TREE_BY_TYPE,
    GET_YEAR_OTHER_COST_PLAN_COST_LIST,
    SAVE_YEAR_OTHER_COST_PLAN_COST_LIST
} from "@/api/application/reporting";
import type { DataTableColumns } from "naive-ui";
import { NButton, NInput, NTooltip } from "naive-ui";
import { useCommonTable, useReportingTree } from "@/hooks";
import { cloneDeep } from "lodash-es";
import { TableActions } from "@/components/TableActions";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useThrottleFn } from "@vueuse/core";

let props = withDefaults(
    defineProps<{
        planYear: string;
    }>(),
    {}
);

interface RowProps {
    [key: string]: any;
}

let loadingShow = ref(false);

onMounted(async () => {
    await getTreeData();
    await getWorkGroupIdOptions();
});

// 树相关操作
let { treeData, treePattern, treeSelectKeys, treeAddDisabled, findLastLevel } = useReportingTree();

let getTreeData = async () => {
    await GET_OTHER_CATEGORY_TREE_BY_TYPE({
        wageType: 2
    }).then((res) => {
        if (res.data.code === 0) {
            treeData.value = treeAddDisabled(res.data.data ?? []);
            treeSelectKeys.value = [findLastLevel(treeData.value)[0]?.id];
        }
    });
};

let selectTreeNode = (keys?: (string | number)[], option?: any[]) => {
    treeSelectKeys.value = keys ?? [];
    // onSearch();
};

// 搜索项
let getWorkGroupIdOptions = async () => {
    await GET_CONFIG_WORK_GROUP_LIST({}).then((res) => {
        searchOptions.value.workGroupId = (res.data.data ?? []).map((item: any) => ({
            label: item.companyName + "-" + item.workshopName + "-" + item.groupName,
            value: item.id
        }));
    });
    // searchForm.value.workGroupId = searchOptions.value.workGroupId[0].value;
};

let searchConfig = ref<TableSearchbarConfig>([{ label: "班组", type: "select", prop: "workGroupId", span: 2 }]);

let searchOptions = ref<TableSearchbarOptions>({ workGroupId: [] });

let searchForm = ref<TableSearchbarData>({ workGroupId: null });

let onSearch = () => {
    getTableData();
};

// 搜索栏自动保存逻辑
let autoSave = useThrottleFn(async () => {
    let params = {
        ...searchForm.value,
        costYear: props.planYear,
        otherCategoryId: treeSelectKeys.value[0],
        yearOtherCostList: tableData.value
    };
    await SAVE_YEAR_OTHER_COST_PLAN_COST_LIST({ ...params }).then((res) => {
        if (res.data.code === 0) window.$message.success("自动保存成功");
    });
}, 1000);

let onComponentClick = async (val: TableSearchbarData) => {
    // await autoSave();
};

// 数据列表
let { tableRowKey, tableData, tableLoading, changeTableSelection } = useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    { type: "selection" },
    {
        title: "费用标题",
        align: "center",
        key: "costName",
        render: (row) => {
            return h(
                NTooltip,
                {},
                {
                    trigger: () => {
                        return h(NInput, { value: row.costName, onUpdateValue: (v) => (row.costName = v) });
                    },
                    default: () => {
                        return row.costName;
                    }
                }
            );
        }
    },
    {
        title: "费用金额（元）",
        align: "center",
        key: "cost",
        render: (row) => {
            return h(
                NTooltip,
                {},
                {
                    trigger: () => {
                        return h(NInput, {
                            value: row.cost,
                            onUpdateValue: (v) => (row.cost = v),
                            onFocus: () => {
                                if (row.cost === "0") row.cost = "";
                            },
                            onBlur: () => {
                                if (!row.cost) row.cost = "0";
                            }
                        });
                    },
                    default: () => {
                        return row.cost;
                    }
                }
            );
        }
    },

    {
        title: "备注",
        align: "center",
        key: "remark",
        render: (row) => {
            return h(
                NTooltip,
                {},
                {
                    trigger: () => {
                        return h(NInput, { value: row.remark, onUpdateValue: (v) => (row.remark = v) });
                    },
                    default: () => {
                        return row.remark;
                    }
                }
            );
        }
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: "80",
        render(row, index) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "删除",
                        tertiary: true,
                        type: "error",
                        onClick: () => deleteItem(index)
                    }
                ]
            });
        }
    }
]);

let getTableData = () => {
    tableLoading.value = true;
    GET_YEAR_OTHER_COST_PLAN_COST_LIST({
        ...searchForm.value,
        otherCategoryId: treeSelectKeys.value[0],
        costYear: props.planYear
    }).then((res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data ?? [];
        }
        tableLoading.value = false;
    });
};

watchEffect(() => {
    if (props.planYear) {
        getTableData();
    }
});

// 可编辑表单配置
let tableItem: RowProps = {
    id: "",
    contentId: "",
    contentType: "",
    planCost: "",
    remark: ""
};

//新增计划
let addTableItem = () => {
    tableData.value.push(cloneDeep(tableItem));
};

let deleteItem = (index: number) => {
    tableData.value.splice(index, 1);
};

//保存填写
let saveTableData = async () => {
    loadingShow.value = true;
    let params = {
        ...searchForm.value,
        costYear: props.planYear,
        otherCategoryId: treeSelectKeys.value[0],
        yearOtherCostList: tableData.value
    };
    await SAVE_YEAR_OTHER_COST_PLAN_COST_LIST({ ...params }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("保存成功");
            onSearch();
        }
    });
    loadingShow.value = false;
};
</script>
