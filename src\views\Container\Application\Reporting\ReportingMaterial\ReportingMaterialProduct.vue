<template>
    <div>
        <n-card hoverable>
            <n-space></n-space>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-space class="mb">
                <n-button secondary type="primary" @click="openAddModal()">添加产成品</n-button>
                <n-button secondary type="success" @click="onBatchClassify()">批量分类</n-button>
                <n-button secondary type="warning" @click="onBatchSave()">批量修改</n-button>
                <n-button secondary type="default" @click="onExportQuota()">导出产成品定额</n-button>
                <n-button secondary type="error" @click="onDelete()">批量删除</n-button>
            </n-space>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                v-model:checked-row-keys="tableSelection"
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <AddProductModal v-model:show="addModal.show" @refresh="getTableData" />
        <EditProductModal v-model:show="editModal.show" :config-data="editModal.configData" @refresh="getTableData" />
        <ProductFormulaModal
            v-model:show="formulaModal.show"
            :config-data="formulaModal.configData"
            @refresh="getTableData"
        />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from "vue";
import type { DataTableColumns } from "naive-ui";
import { NButton, NTreeSelect } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable, useDicts } from "@/hooks";
import {
    BATCH_DELETE_PRODUCT_MATERIALS,
    BATCH_SAVE_MANUFACTURE_LIST,
    BATCH_UPDATE_PRODUCT_CATEGORY,
    GET_CONFIG_COMPANY_LIST,
    GET_MANUFACTURE_PAGE_LIST,
    GET_MATERIAL_CATEGORY_TREE_BY_GENRE,
    PRODUCT_EXPORT_QUOTA,
    UPDATE_MANUFACTURE
} from "@/api/application/reporting";
import { TableActions } from "@/components/TableActions";
import { AddProductModal, EditProductModal, ProductFormulaModal } from "./components";
import { DynamicTableEditor } from "@/components/Dynamic";
import { UnitSelector } from "@/views/Container/Application/Reporting/components";

interface RowProps {
    [key: string]: any;
}

// 字典操作
let { dictLibs, getDictLibs } = useDicts();

let setDictLibs = async () => {
    let dictName = ["common_units"];
    await getDictLibs(dictName);
};

onMounted(async () => {
    await getCategoryIdOptions();
    await getCompanyIdOptions();
    await setDictLibs();
    getSearchOptions();
    getTableData();
});

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { prop: "poleName", type: "input", label: "物料名称" },
    { prop: "companyId", type: "select", label: "所属公司" },
    { prop: "productModel", type: "input", label: "规格" }
]);

let searchOptions = ref<TableSearchbarOptions>({
    companyId: []
});

let searchForm = ref<TableSearchbarData>({
    poleName: null,
    companyId: null,
    productModel: null
});

let getSearchOptions = () => {
    searchOptions.value.companyId = companyIdOptions.value.map((item) => {
        return { label: item.companyName, value: item.id };
    });
};

// 获取公司选项-填报专属修改2023年8月9日
let companyIdOptions = ref<any[]>([]);

let getCompanyIdOptions = async () => {
    await GET_CONFIG_COMPANY_LIST({ needFill: 1 }).then((res) => {
        companyIdOptions.value = res.data.data || [];
    });
};

// 获取物料分类选项
let categoryIdOptions = ref([]);

let getCategoryIdOptions = async () => {
    await GET_MATERIAL_CATEGORY_TREE_BY_GENRE({
        productGenre: 2
    }).then((res) => (categoryIdOptions.value = res.data.data));
};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "物料名称",
        align: "center",
        key: "poleName",
        render: (row) => {
            return h(DynamicTableEditor, {
                type: "input",
                value: row.poleName,
                onUpdateValue: (v: unknown) => (row.poleName = v)
            });
        }
    },
    {
        title: "物料分类",
        align: "center",
        key: "categoryId",
        render: (row) => {
            return h(DynamicTableEditor, {
                type: "treeSelect",
                value: row.categoryId,
                readonlyValue: row.categoryName,
                onUpdateValue: (v: unknown) => (row.categoryId = v),
                onUpdateReadonlyValue: (o: any) => (row.categoryName = o.categoryName),
                componentConfig: {
                    options: categoryIdOptions.value,
                    clearable: true,
                    filterable: true,
                    keyField: "id",
                    labelField: "categoryName",
                    placeholder: "请选择物料分类",
                    childrenField: "childrenList"
                }
            });
        }
    },
    {
        title: "所属公司",
        align: "center",
        key: "companyName",
        render: (row) => {
            return row.companyName ?? "暂无";
        }
    },
    {
        title: "规格",
        align: "center",
        key: "productModel",
        render: (row) => {
            return h(
                NButton,
                {
                    type: "primary",
                    text: true,
                    onClick: () => openEditModal(row)
                },
                () => row.productModel
            );
        }
    },
    // 2023年9月8日单位变更需要对接数据-已处理
    {
        title: "产品单位",
        align: "center",
        key: "manufactureUnit",
        render: (row) => {
            return h(UnitSelector, {
                type: "select",
                value: row.manufactureUnit,
                options: dictLibs["common_units"] ?? [],
                onSubmit: (v: any) => (row.manufactureUnit = v)
            });
        }
    },
    {
        title: "配方",
        align: "center",
        key: "id",
        render: (row) => {
            return h(
                NButton,
                {
                    type: "primary",
                    text: true,
                    onClick: () => openFormulaModal(row)
                },
                () => (row.formulaId ? "点击查看" : "未绑定配方")
            );
        }
    },
    {
        title: "扣除钢筋混凝土用量",
        align: "center",
        key: "concreteAmount",
        render: (row) => {
            return h(DynamicTableEditor, {
                type: "input",
                value: row.concreteAmount,
                onUpdateValue: (v: unknown) => (row.concreteAmount = v)
            });
        }
    },
    {
        title: "备注",
        align: "center",
        key: "remark",
        render: (row) => {
            return h(DynamicTableEditor, {
                type: "input",
                value: row.remark,
                onUpdateValue: (v: unknown) => (row.remark = v)
            });
        }
    },
    {
        title: "操作",
        key: "action",
        align: "center",
        width: 120,
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "保存修改",
                        tertiary: true,
                        type: "primary",
                        onClick: () => updateTableItem(row)
                    }
                ]
            });
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    GET_MANUFACTURE_PAGE_LIST({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        ...searchForm.value
    }).then((res) => {
        tableData.value = res.data.data.records;
        tablePagination.itemCount = res.data.data.total;
        tableLoading.value = false;
    });
};

// 搜索
let onSearch = () => {
    // 2023年10月9日20:10:04填报关闭分页
    // tablePagination.page = 1;
    // tablePagination.pageSize = 10;
    getTableData();
};

// 更新
let updateTableItem = async (row: RowProps) => {
    let res = await UPDATE_MANUFACTURE({ ...row });
    if (res.data.code === 0) {
        window.$message.success("保存成功");
        onSearch();
    } else {
        window.$message.error(res.data.msg);
    }
};

// 新增
let addModal = ref<{ show: boolean }>({ show: false });

let openAddModal = () => {
    addModal.value = { show: true };
};

// 编辑
let editModal = ref<{ show: boolean; configData: Record<string, any> }>({
    show: false,
    configData: {}
});

let openEditModal = (row: RowProps) => {
    editModal.value = { show: true, configData: row };
};

// 配方
let formulaModal = ref<{ show: boolean; configData: Record<string, any> }>({
    show: false,
    configData: {}
});

let openFormulaModal = (row: RowProps) => {
    formulaModal.value = { show: true, configData: row };
};

// 批量分类
let batchClassifyId = ref<Nullable<string>>(null);

let onBatchClassify = () => {
    if (tableSelection.value.length < 1) {
        window.$message.warning("请先选择需要分类的产成品");
        return false;
    }
    window.$dialog.warning({
        title: "选择物料分类",
        positiveText: "确认",
        negativeText: "取消",
        content: () => {
            return h("div", { class: "py-10px" }, [
                h(NTreeSelect, {
                    value: batchClassifyId.value,
                    onUpdateValue: (v) => (batchClassifyId.value = v),
                    options: categoryIdOptions.value,
                    clearable: true,
                    filterable: true,
                    keyField: "id",
                    labelField: "categoryName",
                    placeholder: "请选择物料分类",
                    childrenField: "childrenList"
                })
            ]);
        },
        onPositiveClick: () => {
            BATCH_UPDATE_PRODUCT_CATEGORY({
                ids: tableSelection.value.join(","),
                categoryId: batchClassifyId.value
            }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("修改成功");
                    onSearch();
                    batchClassifyId.value = null;
                    changeTableSelection([]);
                } else window.$message.error(res.data.msg);
            });
        },
        onNegativeClick: () => (batchClassifyId.value = null)
    });
};

// 批量修改
let onBatchSave = () => {
    window.$dialog.warning({
        title: "提示",
        content: "确定要保存所有修改吗？",
        positiveText: "确定",
        negativeText: "取消",
        onPositiveClick: () => {
            BATCH_SAVE_MANUFACTURE_LIST(tableData.value).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("保存成功");
                    onSearch();
                } else window.$message.error(res.data.msg);
            });
        }
    });
};

// 导出产成品定额
let onExportQuota = () => {
    // if (tableSelection.value.length < 1) {
    //     window.$message.warning("请先选择需要导出的产成品");
    //     return false;
    // }
    window.$dialog.warning({
        title: "提示",
        content: "确定要导出所选产成品定额？",
        positiveText: "确定",
        negativeText: "取消",
        onPositiveClick: () => {
            PRODUCT_EXPORT_QUOTA({
                ids: tableSelection.value.join(",")
            }).then((res) => {
                console.log(res.data);
                let blob = new Blob([res.data]);
                let a = document.createElement("a");
                a.href = URL.createObjectURL(blob);
                a.download = "产成品定额.xlsx";
                a.style.display = "none";
                document.body.appendChild(a);
                a.click();
                a.remove();
            });
        }
    });
};

// 批量删除
let onDelete = (id?: string | number) => {
    if (!id && tableSelection.value.length < 1) {
        window.$message.error("请选择要删除的数据");
        return false;
    }
    window.$dialog.warning({
        title: "警告",
        content: `确定删除${id ? "该" : "选中"}产成品吗？`,
        positiveText: "删除",
        negativeText: "取消",
        onPositiveClick: () => {
            let ids: (string | number)[];
            id ? (ids = [id]) : (ids = tableSelection.value);
            BATCH_DELETE_PRODUCT_MATERIALS({ ids: ids.join(",") }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("删除成功");
                    onSearch();
                } else window.$message.error(res.data.msg);
            });
        }
    });
};
</script>
