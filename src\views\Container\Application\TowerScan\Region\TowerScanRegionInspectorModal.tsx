import { computed, defineComponent, reactive, ref, watchEffect } from "vue";
import type { DataTableColumns } from "naive-ui";
import type { PropType } from "vue";
import { GET_REGION_INSPECTOR_PAGE_LIST } from "@/api/application/TowerScan";

export default defineComponent({
    name: "TowerScanRegionInspectorModal",
    props: {
        show: { type: Boolean, default: false },
        regionId: { type: [Number, String] as PropType<string | number>, default: null },
        regionName: { type: String, default: "" }
    },
    emits: ["update:show"],
    setup(props, { emit }) {
        const show = computed({
            get: () => props.show,
            set: (val) => emit("update:show", val)
        });

        // 质检员列表数据
        const inspectorData = ref<any[]>([]);
        const inspectorLoading = ref(false);

        // 分页配置
        const pagination = reactive({
            page: 1,
            pageSize: 10,
            itemCount: 0,
            onChange: (page: number) => {
                pagination.page = page;
                getInspectorData();
            },
            onUpdatePageSize: (pageSize: number) => {
                pagination.pageSize = pageSize;
                pagination.page = 1;
                getInspectorData();
            }
        });

        // 表格列配置
        const columns = ref<DataTableColumns<any>>([
            {
                title: "质检员",
                key: "inspectorName",
                align: "center"
            },
            {
                title: "工艺列表",
                key: "techniques",
                align: "center",
                render: (row) => {
                    if (!row.techniques || row.techniques.length === 0) {
                        return <n-text type="warning">暂无工艺</n-text>;
                    }
                    const techniqueNames = row.techniques.map((tech: any) => tech.techniqueName).join("、");
                    return <n-text type="info">{techniqueNames}</n-text>;
                }
            },
            {
                title: "创建时间",
                key: "createTime",
                align: "center",
                width: 160,
                render: (row) => {
                    if (!row.createTime) return "/";
                    return new Date(row.createTime).toLocaleString();
                }
            }
        ]);

        // 获取质检员数据
        const getInspectorData = () => {
            if (!props.regionId) return;

            inspectorLoading.value = true;
            GET_REGION_INSPECTOR_PAGE_LIST({
                current: pagination.page,
                size: pagination.pageSize,
                regionId: Number(props.regionId)
            })
                .then((res) => {
                    if (res.data.code === 0) {
                        inspectorData.value = res.data.data.records || [];
                        pagination.itemCount = res.data.data.total || 0;
                    }
                    inspectorLoading.value = false;
                })
                .catch(() => {
                    inspectorLoading.value = false;
                    window.$message.error("获取质检员列表失败");
                });
        };

        // 关闭弹窗
        const onClose = () => {
            show.value = false;
            // 清空数据
            inspectorData.value = [];
            pagination.page = 1;
        };

        // 监听弹窗显示状态，自动加载数据
        watchEffect(() => {
            if (show.value && props.regionId) {
                pagination.page = 1;
                getInspectorData();
            }
        });

        return () => (
            <n-modal v-model:show={show.value} close-on-esc mask-closable>
                <n-card title={`${props.regionName} - 区域质检员`} class="w-1200px" closable onClose={onClose}>
                    <n-data-table
                        columns={columns.value}
                        data={inspectorData.value}
                        loading={inspectorLoading.value}
                        pagination={pagination}
                        single-line={false}
                        bordered
                        remote
                        striped
                    />
                </n-card>
            </n-modal>
        );
    }
});
