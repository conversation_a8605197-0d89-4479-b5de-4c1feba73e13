import { computed, defineComponent, h, ref, watchEffect } from "vue";
import { type DataTableColumns, type FormInst, NInput, NSelect, NTooltip } from "naive-ui";
import { cloneDeep } from "lodash-es";
import {
    ADD_REPOSITORY_MOVE_TASK,
    GET_REPOSITORY_GOODS_LIST,
    GET_REPOSITORY_MOVE_TASK_DETAIL,
    GET_REPOSITORY_STOREROOM_LIST,
    UPDATE_REPOSITORY_MOVE_TASK
} from "@/api/application/repository";
import { UserSelector } from "@/components/UserSelector";
import { TableActions } from "@/components/TableActions";

export default defineComponent({
    name: "RepositoryCheckMoveMoveEdit",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        // 表单数据
        interface FormDataProps {
            [key: string]: any;
        }

        const formRef = ref<FormInst | null>(null);

        const storeroomIdOptions = ref<any[]>([]);

        const getFormOptions = async () => {
            GET_REPOSITORY_STOREROOM_LIST({ current: 1, size: 9999 }).then((res) => {
                if (res.data.code === 0) {
                    storeroomIdOptions.value = res.data.data.records.map((i: any) => ({
                        label: i.storeroomName,
                        value: i.id
                    }));
                }
            });
        };

        const initFormData: FormDataProps = {
            planTime: null,
            moveBy: null,
            fromStoreroomId: null,
            toStoreroomId: null,
            moveType: 1,
            remark: null
        };

        const formRules = computed(() => ({
            planTime: [{ required: true, message: "请选择计划结束时间", trigger: ["blur", "change"] }],
            moveBy: [{ required: true, message: "请选择执行人", trigger: ["blur", "change"] }],
            fromStoreroomId: [{ required: true, message: "请选择转出仓库", trigger: ["blur", "change"] }],
            toStoreroomId: [{ required: true, message: "请选择转入仓库", trigger: ["blur", "change"] }],
            remark: [{ required: false, message: "请输入备注", trigger: ["input", "blur"] }]
        }));

        const formData = ref(cloneDeep(initFormData));

        const clearForm = () => {
            formData.value = cloneDeep(initFormData);
        };

        // 获取详情
        const getDetail = () => {
            GET_REPOSITORY_MOVE_TASK_DETAIL({ id: props.configData.id }).then((res) => {
                if (res.data.code === 0) {
                    formData.value = {
                        planTime: res.data.data.planTime,
                        moveBy: res.data.data.moveBy,
                        fromStoreroomId: res.data.data.fromStoreroomId,
                        toStoreroomId: res.data.data.toStoreroomId,
                        moveType: res.data.data.moveType,
                        remark: res.data.data.remark
                    };
                    tableData.value = res.data.data.taskItemList ?? [];
                }
            });
        };

        // 表单数据
        interface RowProps {
            [key: string]: any;
        }

        const goodsIdOptions = ref<any[]>([]);

        const getTableOptions = async () => {
            GET_REPOSITORY_GOODS_LIST({ current: 1, size: 9999 }).then((res) => {
                goodsIdOptions.value = res.data.data.records ?? [];
            });
        };

        watchEffect(async () => {
            if (show.value) await getTableOptions();
        });

        const tableData = ref<RowProps[]>([]);

        const tableColumns = ref<DataTableColumns<RowProps>>([
            {
                title: "原料名称",
                key: "goodsName",
                align: "center",
                render: (row) => {
                    return h(NSelect, {
                        options: goodsIdOptions.value,
                        clearable: true,
                        filterable: true,
                        placeholder: "请选择原料名称",
                        labelField: "goodsName",
                        valueField: "id",
                        value: row.goodsId,
                        renderOption: ({ node, option }: any) => {
                            return h(NTooltip, null, { trigger: () => node, default: () => option.goodsName });
                        },
                        onUpdateValue: (v, o: any) => {
                            row.goodsId = v;
                            row.unit = o?.unit;
                            row.goodsSpec = o?.goodsSpec;
                        }
                    });
                }
            },
            {
                title: "转出数量",
                key: "moveQuantity",
                align: "center",
                render: (row) => {
                    return h(NInput, {
                        value: row.moveQuantity,
                        onUpdateValue: (v) => {
                            row.moveQuantity = v;
                        }
                    });
                }
            },
            {
                title: "规格型号",
                key: "goodsSpec",
                align: "center",
                render: (row) => row.goodsSpec ?? "/"
            },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 80,
                render(row, index) {
                    return h(TableActions, {
                        type: "button",
                        buttonActions: [
                            {
                                label: "删除",
                                tertiary: true,
                                type: "error",
                                onClick: () => deleteItem(row, index)
                            }
                        ]
                    });
                }
            }
        ]);

        // 可编辑表单配置
        const tableItem: RowProps = {
            goodsId: null,
            moveQuantity: null
        };

        const addTableItem = () => {
            tableData.value.push(cloneDeep(tableItem));
        };

        const deleteItem = (row: RowProps, index: number) => {
            if (row.id) {
                tableData.value.forEach((citem, cindex) => {
                    if (row.id === citem.id) {
                        citem.delFlag = 1;
                    }
                });
            } else {
                tableData.value.splice(index, 1);
            }
        };

        watchEffect(async () => {
            if (show.value) {
                await getFormOptions();
                if (props.configData.id) getDetail();
            }
        });

        const onClose = () => {
            clearForm();
            changeModalShow(false);
            emit("refresh");
        };

        const onSubmit = async () => {
            let validateError = await formRef.value?.validate((errors) => !!errors);
            if (validateError) return false;

            let taskItemList: any[] = tableData.value.map((item) => ({
                goodsId: item.goodsId,
                moveQuantity: item.moveQuantity
            }));

            if (formData.value.moveType === 1) {
                taskItemList = [];
            }

            if (props.configData.id) {
                await UPDATE_REPOSITORY_MOVE_TASK({
                    id: props.configData.id,
                    ...formData.value,
                    taskItemList
                }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("编辑成功");
                        onClose();
                    }
                });
            } else {
                await ADD_REPOSITORY_MOVE_TASK({
                    ...formData.value,
                    taskItemList
                }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("新增成功");
                        onClose();
                    }
                });
            }
        };

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card
                    title={props.configData.id ? "编辑移库任务" : "新增移库任务"}
                    class="w-1200px"
                    closable
                    onClose={onClose}
                >
                    <n-form
                        ref={formRef}
                        model={formData.value}
                        rules={formRules.value}
                        label-placement="left"
                        label-width="auto"
                    >
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi span={6} label="计划结束时间" path="planTime">
                                <n-date-picker
                                    v-model:formatted-value={formData.value.planTime}
                                    class="w-100%"
                                    clearable
                                    placeholder="请选择计划计划结束时间"
                                    type="date"
                                    value-format="yyyy-MM-dd"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="执行人" path="moveBy">
                                <UserSelector
                                    v-model:value={formData.value.moveBy}
                                    class="w-100%"
                                    key-name="username"
                                    placeholder="请选择执行人"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="转出仓库" path="fromStoreroomId">
                                <n-select
                                    class="w-100%"
                                    v-model:value={formData.value.fromStoreroomId}
                                    options={storeroomIdOptions.value}
                                    clearable
                                    filterable
                                    placeholder="请选择转出仓库"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="转入仓库" path="toStoreroomId">
                                <n-select
                                    class="w-100%"
                                    v-model:value={formData.value.toStoreroomId}
                                    options={storeroomIdOptions.value}
                                    clearable
                                    filterable
                                    placeholder="请选择转入仓库"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="备注" path="remark">
                                <n-input
                                    v-model:value={formData.value.remark}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入备注"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="转出类型" path="moveType">
                                <n-radio-group v-model:value={formData.value.moveType}>
                                    <n-space>
                                        <n-radio value={1} label="全仓转出" />
                                        <n-radio value={2} label="部分转出" />
                                    </n-space>
                                </n-radio-group>
                            </n-form-item-gi>
                            {formData.value.moveType === 2 && (
                                <n-form-item-gi span={12}>
                                    <div class="w-100%">
                                        <div class="text-18px">用料清单</div>
                                        <div class="mt">
                                            <n-data-table
                                                columns={tableColumns.value}
                                                data={(tableData.value || []).filter((item) => item.delFlag !== 1)}
                                                single-line={false}
                                                bordered
                                                striped
                                            />
                                        </div>
                                        <div class="flex-x-center mt">
                                            <n-space>
                                                <n-button type="success" onClick={addTableItem}>
                                                    新增一行
                                                </n-button>
                                            </n-space>
                                        </div>
                                    </div>
                                </n-form-item-gi>
                            )}
                            <n-form-item-gi span={12}>
                                <n-space>
                                    <n-button type="primary" onClick={onSubmit}>
                                        提交
                                    </n-button>
                                    <n-button onClick={onClose}>取消</n-button>
                                </n-space>
                            </n-form-item-gi>
                        </n-grid>
                    </n-form>
                </n-card>
            </n-modal>
        );
    }
});
