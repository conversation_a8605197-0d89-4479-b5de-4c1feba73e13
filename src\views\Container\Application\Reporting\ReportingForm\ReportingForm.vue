<template>
    <div>
        <n-card content-style="padding-top:8px;padding-bottom:4px" hoverable>
            <div class="flex-y-center">
                <n-tabs v-model:value="tabActive" animated type="bar">
                    <n-tab-pane :name="1" tab="周报表" />
                    <n-tab-pane :name="2" tab="月报表" />
                    <n-tab-pane :name="3" tab="决算表" />
                </n-tabs>
            </div>
        </n-card>
        <n-card class="mt" hoverable>
            <ReportingFormWeek v-if="tabActive === 1" />
            <ReportingFormMonth v-else-if="tabActive === 2" />
            <ReportingFormCalculation v-else-if="tabActive === 3" />
        </n-card>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import ReportingFormWeek from "./ReportingFormWeek.vue";
import ReportingFormMonth from "./ReportingFormMonth.vue";
import ReportingFormCalculation from "./ReportingFormCalculation.vue";

let tabActive = ref(1);
</script>
