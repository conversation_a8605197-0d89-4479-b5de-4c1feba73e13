<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-space class="mb">
                <n-button secondary type="primary" @click="openEditModal()">新增供应商</n-button>
                <n-button secondary type="success" @click="onBatchChangeStatus(1)">批量启用</n-button>
                <n-button secondary type="warning" @click="onBatchChangeStatus(0)">批量停用</n-button>
                <n-button secondary type="error" @click="onDelete()">批量删除</n-button>
            </n-space>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                :checked-row-keys="tableSelection"
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <PurchaseSupplierEditModal
            v-model:show="editModal.show"
            :config-data="editModal.configData"
            @refresh="getTableData"
        />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from "vue";
import type { DataTableColumns } from "naive-ui";
import { NSpace, NText } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { TableActions } from "@/components/TableActions";
import { useCommonTable } from "@/hooks";
import { BATCH_DELETE_SUPPLIER, BATCH_OPERATE_SUPPLIER, GET_SUPPLIER_LIST } from "@/api/application/purchase";
import PurchaseSupplierEditModal from "./PurchaseSupplierEditModal.vue";

interface RowProps {
    [key: string]: any;
}

onMounted(async () => {
    getSearchOptions();
    getTableData();
});

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    {
        labelWidth: "100",
        prop: "supplierName",
        type: "input",
        label: "供应商名称"
    }
]);

let searchOptions = ref<TableSearchbarOptions>({});

let searchForm = ref<TableSearchbarData>({});

let getSearchOptions = () => {};

let onSearch = () => {
    tablePagination.page = 1;
    tablePagination.pageSize = 10;
    getTableData();
};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "供应商名称",
        key: "supplierName",
        align: "center"
    },
    {
        title: "企业编码",
        key: "socialCreditCode",
        align: "center"
    },
    {
        title: "可见范围",
        key: "visibleRange",
        align: "center",
        render(row) {
            return (row.visibleRange ?? [])
                .map((item: any) => {
                    return item.name;
                })
                .join("，");
        }
    },
    {
        title: "销售产品类型",
        key: "saleProducutType",
        align: "center",
        render(row) {
            return !!row.saleProducutType ? h(NText, { type: "primary" }, () => row.saleProducutType) : "未知";
        }
    },
    {
        title: "供应商状态",
        key: "suplierStatus",
        align: "center",
        render(row) {
            if (row.suplierStatus === 0) {
                return h(NText, { type: "error" }, () => "终止合作");
            } else if (row.suplierStatus === 1) {
                return h(NText, { type: "primary" }, () => "正常合作");
            } else return "未知";
        }
    },
    {
        title: "是否列入黑名单",
        key: "blacklistedOrNot",
        align: "center",
        render(row) {
            if (row.blacklistedOrNot === 0) {
                return h(NText, { type: "success" }, () => "否");
            } else if (row.blacklistedOrNot === 1) {
                return h(NText, { type: "error" }, () => "是");
            } else return "未知";
        }
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: 120,
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "编辑供应商",
                        tertiary: true,
                        onClick: () => openEditModal(row)
                    }
                ]
            });
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_SUPPLIER_LIST({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        ...searchForm.value
    }).then((res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data.records ?? [];
            tablePagination.itemCount = res.data.data.total;
            tableLoading.value = false;
        }
    });
};

// 新增编辑
let editModal = ref<{ show: boolean; configData: Record<string, any> }>({
    show: false,
    configData: {}
});

let openEditModal = (row?: Record<string, any>) => {
    editModal.value.show = true;
    editModal.value.configData = row ?? {};
};

// 批量更改状态
let onBatchChangeStatus = (status: number) => {
    if (tableSelection.value.length < 1) {
        window.$message.warning("请先选择供应商");
        return false;
    }
    window.$dialog.warning({
        title: "更改供应商状态",
        positiveText: "确认",
        negativeText: "取消",
        content: () => (status === 0 ? "确定要停用选中的供应商吗？" : "确定要启用选中的供应商吗？"),
        onPositiveClick: () => {
            BATCH_OPERATE_SUPPLIER({
                ids: tableSelection.value,
                status: status
            }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("修改成功");
                    onSearch();
                    changeTableSelection([]);
                } else window.$message.error(res.data.msg);
            });
        }
    });
};

// 批量删除
let onDelete = () => {
    if (tableSelection.value.length < 1) {
        window.$message.error("请选择要删除的数据");
        return false;
    }
    window.$dialog.warning({
        title: "警告",
        content: "确定删除选中供应商吗？",
        positiveText: "删除",
        negativeText: "取消",
        onPositiveClick: () => {
            BATCH_DELETE_SUPPLIER({
                ids: tableSelection.value.join(",")
            }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("删除成功");
                    onSearch();
                    changeTableSelection([]);
                } else window.$message.error(res.data.msg);
            });
        }
    });
};
</script>
