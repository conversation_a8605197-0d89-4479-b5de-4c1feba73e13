<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <!--居间费详情-->
        <ProjectSpecialFeeDetail
            v-model:show="detailModal.show"
            :config-data="detailModal.configData"
            @refresh="getTableData"
        />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, ref } from "vue";
import { DataTableColumns, NText, PaginationProps } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import { GET_INTERMEDIARY_FEE_LIST } from "@/api/application/sale";
import { TableActions } from "@/components/TableActions";
import ProjectSpecialFeeDetail from "./ProjectSpecialFeeDetail.vue";

interface RowProps {
    [key: string]: any;
}

onMounted(async () => {
    getSearchOptions();
    getTableData();
});

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    {
        label: "项目名称",
        prop: "projectName",
        type: "input"
    }
]);

let searchOptions = ref<TableSearchbarOptions>({});

let searchForm = ref<TableSearchbarData>({
    projectName: ""
});

let getSearchOptions = () => {};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, changeTableSelection } =
    useCommonTable<RowProps>("projectId");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "项目编号",
        key: "projectNumber",
        align: "center"
    },
    {
        title: "项目名称",
        key: "projectName",
        align: "center",
        render: (row: RowProps) => h(NText, { type: "primary" }, () => row.projectName || "暂无")
    },
    {
        title: "招投标项目编号",
        key: "projectCode",
        align: "center",
        render: (row: RowProps) => {
            return row.projectCode || "暂无";
        }
    },
    {
        title: "累计其他费用",
        key: "totalFeeAmount",
        align: "center"
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: 100,
        render(row: RowProps) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "管理",
                        tertiary: true,
                        onClick: () => openDetailModal(row)
                    }
                ]
            });
        }
    }
]);

let tablePagination = ref<PaginationProps>({
    page: 1,
    pageSize: 10,
    itemCount: 0,
    pageSizes: [10, 50, 100],
    showSizePicker: true,
    showQuickJumper: true,
    displayOrder: ["size-picker", "pages", "quick-jumper"],
    onChange: (page: number) => {
        tablePagination.value.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.value.pageSize = pageSize;
        tablePagination.value.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_INTERMEDIARY_FEE_LIST({
        current: tablePagination.value.page,
        size: tablePagination.value.pageSize,
        ...searchForm.value
    }).then((res) => {
        tableData.value = res.data.data.records || [];
        tablePagination.value.itemCount = res.data.data.total;
        tableLoading.value = false;
    });
};

let onSearch = () => {
    getTableData();
};

let detailModal = ref<{ show: boolean; configData: RowProps }>({ show: false, configData: {} });

let openDetailModal = (row: RowProps) => {
    detailModal.value.show = true;
    detailModal.value.configData = row;
};
</script>
