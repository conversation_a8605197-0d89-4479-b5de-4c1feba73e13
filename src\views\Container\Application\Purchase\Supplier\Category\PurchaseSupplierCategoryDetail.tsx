import { computed, defineComponent, reactive, ref, watchEffect } from "vue";
import { DataTableColumns } from "naive-ui";
import { useStoreUser } from "@/store";
import {
    GET_SUPPLY_CATEGORY_MANAGEMENT_DETAIL,
    GET_SUPPLY_CATEGORY_MANAGEMENT_SUPPLIERS_LIST
} from "@/api/application/purchase";
import { useCommonTable } from "@/hooks";
import { GET_WORK_PLAN_RULE_RULE_PAGE_LIST } from "@/api/application/plasticMes";

export default defineComponent({
    name: "PurchaseSupplierCategoryDetail",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        const storeUser = useStoreUser();

        interface FormDataProps {
            [key: string]: any;
        }

        const formData = ref<FormDataProps>({});

        // 获取详情
        const getDetail = () => {
            GET_SUPPLY_CATEGORY_MANAGEMENT_DETAIL({ id: props.configData.id }).then((res) => {
                if (res.data.code === 0) {
                    formData.value = res.data.data;
                }
            });
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { title: "供应商名称", key: "supplierName", align: "center" },
            {
                title: "供应商类型",
                key: "supplierType",
                align: "center",
                render: (row) => {
                    switch (row.supplierType) {
                        case 1:
                            return <n-text type="info">企业</n-text>;
                        case 2:
                            return <n-text type="info">个人</n-text>;
                        default:
                            return <n-text type="info">/</n-text>;
                    }
                }
            },
            {
                title: "供应商状态",
                key: "supplierStatus",
                align: "center",
                render: (row) => {
                    switch (row.supplierStatus) {
                        case 0:
                            return <n-text type="warning">待审批</n-text>;
                        case 1:
                            return <n-text type="success">正常</n-text>;
                        case 2:
                            return <n-text type="warning">暂停合作</n-text>;
                        case 3:
                            return <n-text type="error">终止合作</n-text>;
                        case 4:
                            return <n-text type="error">已拉黑</n-text>;
                        case 5:
                            return <n-text type="info">评审中</n-text>;
                        default:
                            return <n-text type="info">/</n-text>;
                    }
                }
            },
            { title: "备注", key: "remark", align: "center" }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_SUPPLY_CATEGORY_MANAGEMENT_SUPPLIERS_LIST({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                id: props.configData.id
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records ?? [];
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        const onClose = () => {
            changeModalShow(false);
            emit("refresh");
        };

        watchEffect(async () => {
            if (show.value) {
                if (props.configData.id) {
                    getDetail();
                    getTableData();
                }
            }
        });

        return () => (
            <div>
                <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                    <n-card title="查看类别弹窗" class="w-1200px" closable onClose={onClose}>
                        <n-form model={formData.value} label-placement="left" label-width="auto">
                            <n-grid cols={12} x-gap={16}>
                                <n-form-item-gi span={6} label="类别名称" required>
                                    {formData.value.categoryName ?? "/"}
                                </n-form-item-gi>
                                <n-form-item-gi span={6} label="创建人">
                                    {formData.value.createByName ?? "/"}
                                </n-form-item-gi>
                                <n-form-item-gi span={6} label="创建时间">
                                    {formData.value.createTime ?? "/"}
                                </n-form-item-gi>
                            </n-grid>
                        </n-form>
                        <n-data-table
                            columns={tableColumns.value}
                            data={tableData.value}
                            loading={tableLoading.value}
                            pagination={tablePagination}
                            row-key={tableRowKey}
                            single-line={false}
                            bordered
                            remote
                            striped
                            onUpdate:checked-row-keys={changeTableSelection}
                        />
                    </n-card>
                </n-modal>
            </div>
        );
    }
});
