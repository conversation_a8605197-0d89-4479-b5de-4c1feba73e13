import { computed, defineComponent, h, ref, watchEffect } from "vue";
import { type DataTableColumns, type FormInst, NInput, NSelect, NTooltip } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { TableActions } from "@/components/TableActions";
import {
    ADD_REPOSITORY_IN_RECEIPT,
    GET_REPOSITORY_GOODS_LIST,
    GET_REPOSITORY_OUT_RECEIPT_DETAIL,
    GET_REPOSITORY_STOREROOM_LIST
} from "@/api/application/repository";
import { useDicts } from "@/hooks";

export default defineComponent({
    name: "RepositoryInReceiptEdit",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        // 字典操作
        const { dictLibs, getDictLibs } = useDicts();

        const setDictLibs = async () => {
            let dictName = ["common_units"];
            await getDictLibs(dictName);
        };

        // 表单数据
        interface FormDataProps {
            [key: string]: any;
        }

        const formRef = ref<FormInst | null>(null);

        const getFormOptions = async () => {};

        const initFormData: FormDataProps = {
            applyTime: null,
            remark: null
        };

        const formRules = computed(() => ({
            applyTime: [{ required: true, message: "请选择申请时间", trigger: ["blur", "change"] }],
            remark: [{ required: false, message: "请输入备注", trigger: ["input", "blur"] }]
        }));

        const formData = ref(cloneDeep(initFormData));

        const clearForm = () => {
            formData.value = cloneDeep(initFormData);
        };

        // 表单数据
        interface RowProps {
            [key: string]: any;
        }

        const goodsIdOptions = ref<any[]>([]);
        const enterStoreroomIdOptions = ref<any[]>([]);

        const getTableOptions = async () => {
            await GET_REPOSITORY_STOREROOM_LIST({
                current: 1,
                size: 9999
            }).then((res) => {
                if (res.data.code === 0) {
                    enterStoreroomIdOptions.value = res.data.data.records.map((i: any) => ({
                        label: i.storeroomName,
                        value: i.id
                    }));
                }
            });
            await GET_REPOSITORY_GOODS_LIST({ current: 1, size: 9999 }).then((res) => {
                goodsIdOptions.value = res.data.data.records ?? [];
            });
        };

        const tableData = ref<RowProps[]>([]);

        const tableColumns = ref<DataTableColumns<RowProps>>([
            {
                title: "入库物品名称",
                key: "goodsName",
                align: "center",
                render: (row) => {
                    return h(NSelect, {
                        options: goodsIdOptions.value,
                        clearable: true,
                        filterable: true,
                        placeholder: "请选择原料名称",
                        labelField: "goodsName",
                        valueField: "id",
                        value: row.goodsId,
                        renderOption: ({ node, option }: any) => {
                            return h(NTooltip, null, { trigger: () => node, default: () => option.goodsName });
                        },
                        onUpdateValue: (v, o: any) => {
                            row.goodsId = v;
                            row.unit = o?.unit;
                            row.goodsSpec = o?.goodsSpec;
                        }
                    });
                }
            },
            {
                title: "入库数量",
                key: "applyQuality",
                align: "center",
                render: (row) => {
                    return h(NInput, {
                        value: row.applyQuality,
                        onUpdateValue: (v) => {
                            row.applyQuality = v;
                        }
                    });
                }
            },
            {
                title: "物品单位",
                key: "unit",
                align: "center",
                render: (row) => {
                    let object = (dictLibs["common_units"] || []).find((i: any) => i.value === String(row.unit));
                    return object?.label ?? "/";
                }
            },
            {
                title: "规格型号",
                key: "goodsSpec",
                align: "center",
                render: (row) => row.goodsSpec ?? "/"
            },
            {
                title: "采买单价",
                key: "unitPrice",
                align: "center",
                render: (row) => <n-input v-model:value={row.unitPrice} />
            },
            {
                // title: "应入库重量",
                title: "单重",
                key: "applyWeight",
                align: "center",
                render: (row) => <n-input v-model:value={row.applyWeight} />
            },
            {
                title: "重量单位",
                key: "weightUnit",
                align: "center",
                width: 150,
                render: (row) => (
                    <n-select
                        v-model:value={row.weightUnit}
                        options={dictLibs["common_units"]}
                        class="w-100%"
                        clearable
                        placeholder="请选择重量单位"
                    />
                )
            },
            {
                title: "入库库房",
                key: "enterStoreroomId",
                align: "center",
                render: (row) => {
                    return h(NSelect, {
                        options: enterStoreroomIdOptions.value,
                        clearable: true,
                        filterable: true,
                        placeholder: "请选择入库库房",
                        value: row.enterStoreroomId,
                        renderOption: ({ node, option }: any) => {
                            return h(NTooltip, null, { trigger: () => node, default: () => option.label });
                        },
                        onUpdateValue: (v) => {
                            row.enterStoreroomId = v;
                        }
                    });
                }
            },
            {
                title: "物资类型",
                key: "goodsType",
                align: "center",
                render: (row) => {
                    return h(NSelect, {
                        options: [
                            { label: "原材料", value: 1 },
                            { label: "合格产品", value: 2 },
                            { label: "不合格产品", value: 3 }
                        ],
                        clearable: true,
                        filterable: true,
                        placeholder: "请选择物资类型",
                        value: row.goodsType,
                        onUpdateValue: (v) => {
                            row.goodsType = v;
                        }
                    });
                }
            },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 80,
                render(row, index) {
                    return h(TableActions, {
                        type: "button",
                        buttonActions: [
                            {
                                label: "删除",
                                tertiary: true,
                                type: "error",
                                onClick: () => deleteItem(row, index)
                            }
                        ]
                    });
                }
            }
        ]);

        // 可编辑表单配置
        const tableItem: RowProps = {
            goodsId: null,
            applyQuality: null,
            goodsType: 1
        };

        const addTableItem = () => {
            tableData.value.push(cloneDeep(tableItem));
        };

        const deleteItem = (row: RowProps, index: number) => {
            if (row.id) {
                tableData.value.forEach((citem, cindex) => {
                    if (row.id === citem.id) citem.delFlag = 1;
                });
            } else tableData.value.splice(index, 1);
        };

        // 获取详情
        const getDetail = () => {
            GET_REPOSITORY_OUT_RECEIPT_DETAIL({ id: props.configData.id }).then((res) => {
                if (res.data.code === 0) {
                    formData.value = {
                        applyTime: res.data.data.applyTime,
                        remark: res.data.data.remark
                    };
                    tableData.value = res.data.data.recordItemList ?? [];
                }
            });
        };

        watchEffect(async () => {
            if (show.value) {
                await getFormOptions();
                await getTableOptions();
                await setDictLibs();
                if (props.configData.id) getDetail();
            }
        });

        const onClose = () => {
            clearForm();
            changeModalShow(false);
            emit("refresh");
        };

        const onSubmit = async () => {
            let validateError = await formRef.value?.validate((errors) => !!errors);
            if (validateError) return false;

            if (props.configData.id) {
            } else {
                await ADD_REPOSITORY_IN_RECEIPT({
                    ...formData.value,
                    recordItemList: tableData.value ?? []
                }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("新增成功");
                        onClose();
                    }
                });
            }
        };

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card
                    title={props.configData.id ? "编辑入库单" : "新增入库单"}
                    class="w-95vw"
                    closable
                    onClose={onClose}
                >
                    <n-form
                        ref={formRef}
                        model={formData.value}
                        rules={formRules.value}
                        label-placement="left"
                        label-width="auto"
                    >
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi span={6} label="申请时间" path="applyTime">
                                <n-date-picker
                                    v-model:formatted-value={formData.value.applyTime}
                                    class="w-100%"
                                    clearable
                                    placeholder="请选择申请时间"
                                    type="date"
                                    value-format="yyyy-MM-dd"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="备注" path="remark">
                                <n-input
                                    v-model:value={formData.value.remark}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入备注"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={12}>
                                <div class="w-100%">
                                    <div class="text-18px">物品清单</div>
                                    <div class="mt">
                                        <n-data-table
                                            columns={tableColumns.value}
                                            data={(tableData.value || []).filter((item) => item.delFlag !== 1)}
                                            single-line={false}
                                            bordered
                                            striped
                                        />
                                    </div>
                                    <div class="flex-x-center mt">
                                        <n-space>
                                            <n-button type="success" onClick={addTableItem}>
                                                新增一行
                                            </n-button>
                                        </n-space>
                                    </div>
                                </div>
                            </n-form-item-gi>
                            <n-form-item-gi span={12}>
                                <n-space>
                                    <n-button type="primary" onClick={onSubmit}>
                                        提交
                                    </n-button>
                                    <n-button onClick={onClose}>取消</n-button>
                                </n-space>
                            </n-form-item-gi>
                        </n-grid>
                    </n-form>
                </n-card>
            </n-modal>
        );
    }
});
