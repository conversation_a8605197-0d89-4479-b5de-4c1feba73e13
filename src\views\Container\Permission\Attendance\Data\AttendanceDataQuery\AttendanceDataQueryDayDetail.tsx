import { computed, defineComponent, ref, watchEffect } from "vue";
import type { DataTableColumns } from "naive-ui";
import {
    ATTENDANCE_EXPORT_DOWNLOAD,
    GET_ATTENDANCE_INQUIRY_DETAILS,
    GET_ATTENDANCE_QUERY_EXPORT
} from "@/api/permission";
import dayjs from "dayjs";

export default defineComponent({
    name: "AttendanceDataQueryDayDetail",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        const tableData = ref<any[]>([]);
        const tableColumns = ref<DataTableColumns<any>>([
            { title: "姓名", key: "trueName", align: "center", render: (row) => row.trueName ?? "/" },
            { title: "所在部门", key: "deptName", align: "center", render: (row) => row.deptName ?? "/" },
            { title: "允许迟到时间", key: "lateTime", align: "center", render: (row) => row.lateTime ?? "/" },
            { title: "允许早退时间", key: "earlyTime", align: "center", render: (row) => row.earlyTime ?? "/" },
            { title: "上班1打卡时间", key: "on1DTime", align: "center", render: (row) => row.on1DTime ?? "/" },
            {
                title: "上班1考勤状态",
                key: "on1DisposeStatus",
                align: "center",
                width: 120,
                render: (row) => row.on1DisposeStatus ?? "/"
            },
            { title: "下班1打卡时间", key: "off1DTime", align: "center", render: (row) => row.off1DTime ?? "/" },
            {
                title: "下班1考勤状态",
                key: "off1DisposeStatus",
                align: "center",
                width: 120,
                render: (row) => row.off1DisposeStatus ?? "/"
            },
            { title: "上班2打卡时间", key: "on2DTime", align: "center", render: (row) => row.on2DTime ?? "/" },
            {
                title: "上班2考勤状态",
                key: "on2DisposeStatus",
                align: "center",
                width: 120,
                render: (row) => row.on2DisposeStatus ?? "/"
            },
            { title: "下班2打卡时间", key: "off2DTime", align: "center", render: (row) => row.off2DTime ?? "/" },
            {
                title: "下班2考勤状态",
                key: "off2DisposeStatus",
                align: "center",
                width: 120,
                render: (row) => row.off2DisposeStatus ?? "/"
            }
        ]);

        // 获取详情
        const detailData = ref<any>(null);

        const getDetail = async () => {
            await GET_ATTENDANCE_INQUIRY_DETAILS({ id: [props.configData.id] }).then((res) => {
                if (res.data.code === 0) {
                    console.log(res.data.data);
                    detailData.value = res.data.data;
                    tableData.value = detailData.value.attendanceQueryIPage?.records ?? [];
                }
            });
        };

        // 导出功能
        const onExport = (id: string | number) => {
            window.$dialog.warning({
                title: "提示",
                content: "确定导出吗？",
                positiveText: "确定",
                negativeText: "取消",
                onPositiveClick: () => {
                    GET_ATTENDANCE_QUERY_EXPORT({
                        id
                    }).then((res) => {
                        ATTENDANCE_EXPORT_DOWNLOAD({
                            fileName: res.data.data
                        }).then((cres) => {
                            const blob = new Blob([cres.data]);
                            const a = document.createElement("a");
                            a.href = URL.createObjectURL(blob);
                            a.download = res.data.data;
                            a.style.display = "none";
                            document.body.appendChild(a);
                            a.click();
                            a.remove();
                        });
                    });
                }
            });
        };

        const onClose = () => {
            changeModalShow(false);
            emit("refresh");
        };

        watchEffect(async () => {
            if (show.value) {
                if (props.configData.id) await getDetail();
            }
        });

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card
                    title={`查询日期：${
                        props.configData.startTime ? dayjs(props.configData.startTime).format("YYYY-MM-DD") : ""
                    }${
                        props.configData.endTime &&
                        dayjs(props.configData.startTime).format("YYYY-MM-DD") !==
                            dayjs(props.configData.endTime).format("YYYY-MM-DD")
                            ? ` ~ ${dayjs(props.configData.endTime).format("YYYY-MM-DD")}`
                            : ""
                    }`}
                    class="w-90vw"
                    closable
                    onClose={onClose}
                >
                    <n-form label-placement="left" label-width="auto">
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi required span={6} label="操作人">
                                {detailData.value?.operator ?? "/"}
                            </n-form-item-gi>
                            <n-form-item-gi required span={6} label="操作时间">
                                {detailData.value?.createTime ?? "/"}
                            </n-form-item-gi>
                            <n-form-item-gi span={12}>
                                <n-button type="primary" onClick={() => onExport(props.configData?.id)}>
                                    导出报表
                                </n-button>
                            </n-form-item-gi>
                            <n-form-item-gi span={12}>
                                <n-data-table
                                    columns={tableColumns.value}
                                    data={tableData.value}
                                    single-line={false}
                                    bordered
                                    striped
                                />
                            </n-form-item-gi>
                        </n-grid>
                    </n-form>
                </n-card>
            </n-modal>
        );
    }
});
