import { defineComponent, onMounted, reactive, ref } from "vue";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns, PaginationProps } from "naive-ui";
import { NText } from "naive-ui";
import { TableActions } from "@/components/TableActions";
import { GET_REGION_PAGE_LIST, DELETE_REGION } from "@/api/application/TowerScan";
import TowerScanRegionEdit from "./TowerScanRegionEdit";
import TowerScanRegionEquipmentModal from "./TowerScanRegionEquipmentModal";
import TowerScanRegionInspectorModal from "./TowerScanRegionInspectorModal";
import dayjs from "dayjs";

export default defineComponent({
    name: "TowerScanRegionList",
    setup() {
        // 搜索项
        const searchConfig = ref<TableSearchbarConfig>([{ label: "区域名称", prop: "regionName", type: "input" }]);
        const searchOptions = ref<TableSearchbarOptions>({});
        const searchForm = ref<TableSearchbarData>({
            regionName: ""
        });

        const getSearchOptions = () => {};

        const onSearch = () => {
            tablePagination.page = 1;
            getTableData();
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("regionId");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            {
                title: "区域名称",
                key: "regionName",
                align: "center"
            },
            {
                title: "区域设备",
                key: "equipments",
                align: "center",
                render: (row) => {
                    return (
                        <n-button text type="info" onClick={() => showEquipmentModal(row)}>
                            点击查看
                        </n-button>
                    );
                }
            },
            {
                title: "区域质检员",
                key: "inspectors",
                align: "center",
                render: (row) => {
                    return (
                        <n-button text type="info" onClick={() => showInspectorModal(row)}>
                            点击查看
                        </n-button>
                    );
                }
            },
            {
                title: "备注",
                key: "remark",
                align: "center",
                render: (row) => row.remark || "/"
            },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 180,
                render: (row) => (
                    <TableActions
                        type="button"
                        buttonActions={[
                            {
                                label: "编辑",
                                tertiary: true,
                                type: "primary",
                                onClick: () => openEditModal(row)
                            },
                            {
                                label: "删除",
                                tertiary: true,
                                type: "error",
                                onClick: () => deleteRow(row)
                            }
                        ]}
                    />
                )
            }
        ]);

        const tablePagination = reactive<PaginationProps>({
            page: 1,
            pageSize: 10,
            itemCount: 0,
            pageSizes: [10, 20, 50],
            showSizePicker: true,
            showQuickJumper: true,
            displayOrder: ["size-picker", "pages", "quick-jumper"],
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_REGION_PAGE_LIST({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                ...searchForm.value
            })
                .then((res) => {
                    if (res.data.code === 0) {
                        tableData.value = res.data.data.records || [];
                        tablePagination.itemCount = res.data.data.total;
                        tableLoading.value = false;
                    } else {
                        tableLoading.value = false;
                    }
                })
                .catch(() => {
                    tableLoading.value = false;
                });
        };

        // 删除
        const deleteRow = (row: RowProps) => {
            window.$dialog.warning({
                title: "警告",
                content: `确定要删除区域"${row.regionName}"吗？`,
                positiveText: "确定",
                negativeText: "取消",
                onPositiveClick: () => {
                    DELETE_REGION({ regionIds: row.regionId }).then((res) => {
                        if (res.data.code === 0) {
                            window.$message.success("删除成功");
                            getTableData();
                        } else {
                            window.$message.error(res.data.msg);
                        }
                    });
                }
            });
        };

        // 新增编辑弹窗
        const editModal = ref<{ show: boolean; configData: any }>({ show: false, configData: {} });

        const openEditModal = (row?: RowProps) => {
            editModal.value = { show: true, configData: row || {} };
        };

        // 设备弹窗
        const equipmentModal = ref<{ show: boolean; regionId: number | null; regionName: string }>({
            show: false,
            regionId: null,
            regionName: ""
        });

        const showEquipmentModal = (row: RowProps) => {
            equipmentModal.value = {
                show: true,
                regionId: row.regionId || row.id,
                regionName: row.regionName
            };
        };

        // 质检员弹窗
        const inspectorModal = ref<{ show: boolean; regionId: number | null; regionName: string }>({
            show: false,
            regionId: null,
            regionName: ""
        });

        const showInspectorModal = (row: RowProps) => {
            inspectorModal.value = {
                show: true,
                regionId: row.regionId || row.id,
                regionName: row.regionName
            };
        };

        onMounted(async () => {
            getSearchOptions();
            getTableData();
        });

        return () => (
            <div class="tower-scan-region-list">
                <n-card>
                    <TableSearchbar
                        form={searchForm.value}
                        config={searchConfig.value}
                        options={searchOptions.value}
                        onSearch={onSearch}
                    />
                </n-card>
                <n-card class="mt">
                    <n-space class="mb">
                        <n-button type="primary" onClick={() => openEditModal()}>
                            新增区域
                        </n-button>
                        <n-button
                            type="error"
                            disabled={tableSelection.value.length === 0}
                            onClick={() => {
                                window.$dialog.warning({
                                    title: "警告",
                                    content: `确定要删除选中的 ${tableSelection.value.length} 个区域吗？`,
                                    positiveText: "确定",
                                    negativeText: "取消",
                                    onPositiveClick: () => {
                                        DELETE_REGION({ regionIds: tableSelection.value }).then((res) => {
                                            if (res.data.code === 0) {
                                                window.$message.success("批量删除成功");
                                                getTableData();
                                            } else {
                                                window.$message.error(res.data.msg);
                                            }
                                        });
                                    }
                                });
                            }}
                        >
                            批量删除
                        </n-button>
                    </n-space>
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        pagination={tablePagination}
                        row-key={tableRowKey}
                        single-line={false}
                        bordered
                        remote
                        striped
                        onUpdate:checked-row-keys={changeTableSelection}
                    />
                </n-card>
                <TowerScanRegionEdit
                    v-model:show={editModal.value.show}
                    config-data={editModal.value.configData}
                    onRefresh={getTableData}
                />

                <TowerScanRegionEquipmentModal
                    v-model:show={equipmentModal.value.show}
                    region-id={equipmentModal.value.regionId}
                    region-name={equipmentModal.value.regionName}
                />

                <TowerScanRegionInspectorModal
                    v-model:show={inspectorModal.value.show}
                    region-id={inspectorModal.value.regionId}
                    region-name={inspectorModal.value.regionName}
                />
            </div>
        );
    }
});
