<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <!--确认回款-->
        <FinancePaymentConfirm
            v-model:show="confirmModal.show"
            :configData="confirmModal.configData"
            @refresh="getTableData"
        />
        <!--延期到账-->
        <FinancePaymentDelay
            v-model:show="delayModal.show"
            :configData="delayModal.configData"
            @refresh="getTableData"
        />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, ref } from "vue";
import type { DataTableColumns, PaginationProps } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import { TableActions } from "@/components/TableActions";
import { GET_PROJECT_PAYMENT_RETURN_APPLY_LIST, NON_PAYMENT_RETURN } from "@/api/application/power";
import { FinancePaymentConfirm, FinancePaymentDelay } from "../components";

interface RowProps {
    [key: string]: any;
}

onMounted(async () => {
    getSearchOptions();
    getTableData();
});

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([]);

let searchOptions = ref<TableSearchbarOptions>({});

let searchForm = ref<TableSearchbarData>({});

let getSearchOptions = () => {};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, changeTableSelection } =
    useCommonTable<RowProps>("projectId");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "项目编号",
        key: "projectNumber",
        align: "center",
        render: (row) => {
            return row.projectNumber || "未知";
        }
    },
    {
        title: "项目名称",
        key: "projectName",
        align: "center",
        render: (row) => {
            return row.projectName || "未知";
        }
    },
    // {
    //     title: "订单编号",
    //     key: "contractNumber",
    //     align: "center",
    //     render: (row) => {
    //         return row.contractNumber || "未知";
    //     }
    // },
    {
        title: "项目负责人",
        key: "projectLeaderName",
        align: "center",
        render: (row) => {
            return row.projectLeaderName || "未知";
        }
    },
    {
        title: "预计到款时间",
        key: "preReturnTime",
        align: "center",
        render: (row) => {
            return row.preReturnTime || "未知";
        }
    },
    {
        title: "预计到款金额",
        key: "preReturnAmount",
        align: "center",
        render: (row) => {
            return row.preReturnAmount + "元" || "未知";
        }
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: 280,
        render: (row) => {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "确认回款",
                        tertiary: true,
                        onClick: () => openConfirmModal(row)
                    },
                    {
                        label: "延期到账",
                        tertiary: true,
                        type: "warning",
                        onClick: () => openDelayModal(row)
                    },
                    {
                        label: "未到账",
                        tertiary: true,
                        type: "error",
                        onClick: () => onNotArrival(row)
                    }
                ]
            });
        }
    }
]);

let tablePagination = ref<PaginationProps>({
    page: 1,
    pageSize: 10,
    itemCount: 0,
    pageSizes: [10, 50, 100],
    showSizePicker: true,
    showQuickJumper: true,
    displayOrder: ["size-picker", "pages", "quick-jumper"],
    onChange: (page: number) => {
        tablePagination.value.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.value.pageSize = pageSize;
        tablePagination.value.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_PROJECT_PAYMENT_RETURN_APPLY_LIST({
        current: tablePagination.value.page,
        size: tablePagination.value.pageSize,
        ...searchForm.value,
        applyStatus: 1,
        nonContractFlag: 1
    }).then((res) => {
        tableData.value = res.data.data.records || [];
        tablePagination.value.itemCount = res.data.data.total;
        tableLoading.value = false;
    });
};

let onSearch = () => {
    getTableData();
};

// 确认回款
let confirmModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

let openConfirmModal = (row: RowProps) => {
    confirmModal.value.show = true;
    confirmModal.value.configData = row;
};

// 延期到账
let delayModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

let openDelayModal = (row: RowProps) => {
    delayModal.value.show = true;
    delayModal.value.configData = row;
};

// 未到账
let onNotArrival = (row: RowProps) => {
    window.$dialog.warning({
        title: "确认信息",
        content: "是否确认该笔回款未到账？",
        positiveText: "确认",
        negativeText: "取消",
        onPositiveClick: () => {
            NON_PAYMENT_RETURN({
                id: row.id
            }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("操作成功");
                    getTableData();
                }
            });
        }
    });
};
</script>
