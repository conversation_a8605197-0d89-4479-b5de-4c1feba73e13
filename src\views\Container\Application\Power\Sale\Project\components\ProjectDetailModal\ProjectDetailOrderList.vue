<template>
    <div>
        <n-space class="mb">
            <n-button
                :disabled="formData.isComplete === 1"
                secondary
                type="primary"
                @click="openMatchFirstOrderModal()"
            >
                匹配订单合同
            </n-button>
            <n-button :disabled="formData.isComplete === 1" secondary type="error" @click="onComplete()">
                结束匹配订单
            </n-button>
        </n-space>
        <n-data-table
            :columns="tableColumns"
            :data="formData.childProjectInfoVoList || []"
            :loading="tableLoading"
            :row-key="tableRowKey"
            :single-line="false"
            bordered
            children-key="childList"
            remote
            striped
            @update:checked-row-keys="changeTableSelection"
        />
        <!--绑定订单合同-->
        <ProjectDetailAddOrderModal
            v-model:show="matchFirstOrderModal.show"
            :config-data="matchFirstOrderModal.configData"
            @refresh="onRefresh"
        />
        <!--申请发货-->
        <ProjectApplyDelivery
            v-model:show="applyDeliveryModal.show"
            :config-data="applyDeliveryModal.configData"
            @refresh="onRefresh"
        />
        <!--申请开票-->
        <ProjectApplyInvoice
            v-model:show="applyInvoiceModal.show"
            :config-data="applyInvoiceModal.configData"
            @refresh="onRefresh"
        />
        <!--确认回款-->
        <ProjectConfirmPayment
            v-model:show="confirmPaymentModal.show"
            :config-data="confirmPaymentModal.configData"
            @refresh="onRefresh"
        />
        <!--订单详情-->
        <ProjectOrderDetailModal
            v-model:show="detailModal.show"
            :configData="detailModal.configData"
            @refresh="onRefresh"
        />
    </div>
</template>

<script lang="ts" setup>
import { useCommonTable } from "@/hooks";
import { h, ref } from "vue";
import type { DataTableColumns } from "naive-ui";
import { NButton, NText } from "naive-ui";
import { ProjectApplyDelivery, ProjectApplyInvoice, ProjectConfirmPayment } from "../../components";
import { ProjectDetailAddOrderModal } from "../ProjectDetailModal";
import { TableActions } from "@/components/TableActions";
import { POST_SET_ADD_ORDER_FINISH } from "@/api/application/power";
import { ProjectOrderDetailModal } from "../ProjectOrderDetailModal";

interface FormDataProps {
    [key: string]: any;
}

let props = withDefaults(defineProps<{ formData: FormDataProps }>(), {});

let emits = defineEmits(["refresh"]);

let onRefresh = () => emits("refresh");

// 数据列表
let { tableRowKey, tableLoading, changeTableSelection } = useCommonTable<FormDataProps>("id");

let tableColumns = ref<DataTableColumns<FormDataProps>>([
    {
        title: "订单编号",
        key: "projectOrderManagement.pomNumber",
        align: "center"
    },
    {
        title: "订单名称",
        key: "projectOrderManagement.pomName",
        align: "center"
    },
    {
        title: "订单金额（万元）",
        key: "projectOrderManagement.pomAmount",
        align: "center"
    },
    {
        title: "订单负责人",
        key: "childProjectDirectorName",
        align: "center",
        render: (row: FormDataProps) => {
            return row.childProjectDirectorName || "/";
        }
    },
    {
        title: "最新进度",
        key: "nodeName",
        align: "center"
    },
    {
        title: "最新节点负责人",
        key: "nodeDirectorName",
        align: "center",
        render: (row: FormDataProps) => {
            return row.nodeDirectorName || "/";
        }
    },
    {
        title: "是否超时",
        key: "timeOutFlag",
        align: "center",
        render: (row: FormDataProps) => {
            if (row.timeOutFlag) {
                return h(NText, { type: "error" }, () => "已超时");
            } else {
                return h(NText, { type: "success" }, () => "未超时");
            }
        }
    },
    {
        title: "是否收到回款",
        key: "isReceiveFinalPay",
        align: "center",
        render: (row: FormDataProps) => {
            if (row.isReceiveFinalPay) {
                return h(NText, { type: "success" }, () => "已收到");
            } else {
                return h(NText, { type: "error" }, () => "未收到");
            }
        }
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: 360,
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "查看",
                        tertiary: true,
                        type: "default",
                        onClick: () => openDetailModal(row)
                    },
                    {
                        label: "发货申请",
                        tertiary: true,
                        type: "warning",
                        onClick: () => openApplyDeliveryModal(row)
                    },
                    {
                        label: "申请开票",
                        tertiary: true,
                        type: "primary",
                        onClick: () => openApplyInvoiceModal(row)
                    },
                    {
                        label: "确认回款",
                        tertiary: true,
                        type: "success",
                        onClick: () => openConfirmPaymentModal(row)
                    }
                ]
            });
        }
    }
]);

// 订单详情
let detailModal = ref<{ show: boolean; configData: FormDataProps }>({ show: false, configData: {} });

let openDetailModal = (row: FormDataProps) => {
    detailModal.value.show = true;
    detailModal.value.configData = row;
};

// 绑定订单合同（MatchContractOrderList）
let matchFirstOrderModal = ref<{ show: boolean; configData: FormDataProps }>({ show: false, configData: {} });

let openMatchFirstOrderModal = () => {
    matchFirstOrderModal.value.show = true;
    matchFirstOrderModal.value.configData = props.formData;
};

// 申请发货
let applyDeliveryModal = ref<{ show: boolean; configData: FormDataProps }>({ show: false, configData: {} });

let openApplyDeliveryModal = (row: FormDataProps) => {
    applyDeliveryModal.value.show = true;
    applyDeliveryModal.value.configData = {
        ...row,
        nextFormKey: "DeliveryNotice"
    };
};

// 申请开票
let applyInvoiceModal = ref<{ show: boolean; configData: FormDataProps }>({ show: false, configData: {} });

let openApplyInvoiceModal = (row: FormDataProps) => {
    applyInvoiceModal.value.show = true;
    applyInvoiceModal.value.configData = {
        ...row,
        nextFormKey: "InvoicingApply"
    };
};

// 确认回款
let confirmPaymentModal = ref<{ show: boolean; configData: FormDataProps }>({ show: false, configData: {} });

let openConfirmPaymentModal = (row: FormDataProps) => {
    confirmPaymentModal.value.show = true;
    confirmPaymentModal.value.configData = {
        ...row,
        nextFormKey: "ConfirmPayment"
    };
};

// 完成订单
let onComplete = () => {
    window.$dialog.warning({
        title: "警告",
        content: "确定结束匹配订单吗？",
        positiveText: "确定",
        negativeText: "取消",
        onPositiveClick: () => {
            POST_SET_ADD_ORDER_FINISH({ projectId: props.formData.projectId }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("操作成功");
                    onRefresh();
                }
            });
        }
    });
};
</script>
