<template>
    <div class="ml-30px">
        <div class="text-20px mb-30px">基本设置</div>
        <div class="flex">
            <n-form
                ref="formRef"
                :model="formData"
                :rules="formRules"
                class="w-470px"
                label-placement="left"
                label-width="auto"
            >
                <n-form-item label="头像">
                    <AvatarUploader v-model:file-list="avatarList" />
                </n-form-item>
                <n-form-item label="姓名" path="trueName">
                    <n-input v-model:value="formData.trueName" class="w-100%" clearable placeholder="请输入姓名" />
                </n-form-item>
                <!--<n-form-item label="昵称" path="nickname">-->
                <!--    <n-input v-model:value="formData.nickname" class="w-100%" clearable placeholder="请输入昵称" />-->
                <!--</n-form-item>-->
                <n-form-item label="手机号" path="phone">
                    <n-input v-model:value="formData.phone" class="w-100%" clearable placeholder="请输入手机号" />
                </n-form-item>
                <n-form-item label="邮箱地址" path="email">
                    <n-input v-model:value="formData.email" class="w-100%" clearable placeholder="请输入邮箱地址" />
                </n-form-item>
                <n-form-item label="个人简介" path="individualResume">
                    <n-input
                        v-model:value="formData.individualResume"
                        class="w-100%"
                        clearable
                        placeholder="请输入个人简介"
                    />
                </n-form-item>
                <n-form-item label="联系地址" path="contactAddress">
                    <n-input
                        v-model:value="formData.contactAddress"
                        class="w-100%"
                        clearable
                        placeholder="请输入联系地址"
                    />
                </n-form-item>
                <n-form-item>
                    <n-space>
                        <n-button type="primary" @click="onSubmit">保存</n-button>
                        <n-button type="error" @click="clearFrom">重置</n-button>
                    </n-space>
                </n-form-item>
            </n-form>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { useStoreUser } from "@/store";
import { UPDATE_USERINFO } from "@/api/auth";
import { AvatarUploader } from "@/components/Uploader";
import { usePublic } from "@/hooks";

let { avatarUrl } = usePublic();

let storeUser = useStoreUser();

onMounted(async () => {
    await getFormData();
});

// 头像
let avatarList = ref<any[]>([]);

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {
    trueName: [
        {
            required: true,
            message: "请输入姓名",
            trigger: ["input", "blur"]
        }
    ],
    phone: [
        {
            required: true,
            message: "请输入手机号",
            trigger: ["input", "blur"]
        }
    ]
};

// 表单数据
interface FormDataProps<T = string | null> {
    userId: T;
    trueName: T;
    nickname: T;
    phone: T;
    email: T;
    individualResume: T;
    contactAddress: T;
}

let initFormData: FormDataProps = {
    userId: null,
    trueName: null,
    nickname: null,
    phone: null,
    email: null,
    individualResume: null,
    contactAddress: null
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

let getFormData = async () => {
    let userData: any = await storeUser.requestUserData(false);
    formData.value = {
        userId: userData.sysUser.userId || null,
        trueName: userData.sysUser.trueName || null,
        nickname: userData.sysUser.nickname || null,
        phone: userData.sysUser.phone || null,
        email: userData.sysUser.email || null,
        individualResume: userData.sysUser.individualResume || null,
        contactAddress: userData.sysUser.contactAddress || null,
        avatar: userData.sysUser.avatar || null
    };
    avatarList.value = [{ id: new Date().getTime(), status: "finished", url: avatarUrl }];
};

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    formData.value.avatar = UPDATE_USERINFO({
        ...formData.value,
        avatar: avatarList.value[0]?.fullPath ?? null
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("编辑成功");
            getFormData();
        } else window.$message.error(res.data.msg);
    });
};
</script>
