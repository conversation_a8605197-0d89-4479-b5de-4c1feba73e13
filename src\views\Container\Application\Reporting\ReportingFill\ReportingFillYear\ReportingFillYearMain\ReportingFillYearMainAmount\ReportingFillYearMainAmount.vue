<template>
    <div>
        <n-tabs v-model:value="tabActive" animated type="bar" @before-leave="onTabBeforeLeave">
            <n-tab-pane :name="1" tab="产成品计划用量" />
            <n-tab-pane :name="2" tab="混凝土计划用量" />
        </n-tabs>
        <div class="mt-2">
            <ReportingFillYearMainAmountProduct :planYear="planYear" v-if="tabActive === 1" />
            <ReportingFillYearMainAmountConcrete :planYear="planYear" v-if="tabActive === 2" />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import ReportingFillYearMainAmountProduct from "./ReportingFillYearMainAmountProduct.vue";
import ReportingFillYearMainAmountConcrete from "./ReportingFillYearMainAmountConcrete.vue";
import { usePublic } from "@/hooks";

let { onTabBeforeLeave } = usePublic();

let props = withDefaults(
    defineProps<{
        planYear: string;
    }>(),
    {}
);

let tabActive = ref(1);
</script>
