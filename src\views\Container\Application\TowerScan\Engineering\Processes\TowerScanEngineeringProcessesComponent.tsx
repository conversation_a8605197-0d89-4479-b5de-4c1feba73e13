import { computed, defineComponent, onMounted, reactive, ref, watch, watchEffect } from "vue";
import {
    TableSearchbar,
    type TableSearchbarConfig,
    type TableSearchbarData,
    type TableSearchbarOptions
} from "@/components/TableSearchbar";
import { GET_IRON_COMPONENT_PAGE_LIST } from "@/api/application/TowerScan";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import TowerScanEngineeringProcessesImport from "./TowerScanEngineeringProcessesImport";
import TowerScanEngineeringProcessesComponentItem from "./TowerScanEngineeringProcessesComponentItem";
import TowerScanEngineeringProcessesComponentExecution from "./TowerScanEngineeringProcessesComponentExecution";
import { TableActions } from "@/components/TableActions";

export default defineComponent({
    name: "TowerScanEngineeringProcessesComponent",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        const onClose = () => {
            changeModalShow(false);
            emit("refresh");
        };

        // 使用父组件传递的详情数据，避免重复API调用
        const detailData = computed(() => props.configData?.detailData || {});

        // 导入弹窗
        const importModal = ref<{ show: boolean; configData: any }>({ show: false, configData: {} });

        const openImportModal = () => {
            importModal.value = {
                show: true,
                configData: { id: props.configData.typeId }
            };
        };

        // 导入弹窗刷新后的回调
        const onImportRefresh = () => {
            getTableData();
        };

        // 配方详情弹窗
        const detailModal = ref<{ show: boolean; configData: any }>({ show: false, configData: {} });

        const openDetailModal = (row: RowProps) => {
            detailModal.value = {
                show: true,
                configData: { id: row.componentId }
            };
        };

        // 配方执行情况弹窗
        const executionModal = ref<{ show: boolean; configData: any }>({ show: false, configData: {} });

        const openExecutionModal = (row: RowProps) => {
            executionModal.value = {
                show: true,
                configData: {
                    componentId: row.componentId,
                    processId: row.processId,
                    processType: currentProcessType.value,
                    mainMaterialCode: row.mainMaterialCode,
                    typeName: detailData.value?.ironType?.typeName,
                    projectName: detailData.value?.ironProject?.projectName
                }
            };
        };

        // Tab切换工序类型
        const currentProcessType = ref<number>(5); // 默认组装工序

        const processTypeOptions = [
            { label: "组装", value: 5 },
            { label: "电焊", value: 6 }
        ];

        const onProcessTypeChange = (value: number) => {
            currentProcessType.value = value;
            // 切换工序类型时重新获取数据
            onSearch();
        };

        // 搜索项
        const searchConfig = ref<TableSearchbarConfig>([
            { label: "主件号", prop: "mainMaterialCode", type: "input" },
            { label: "状态", prop: "finishStatus", type: "select" }
        ]);

        const searchOptions = ref<TableSearchbarOptions>({
            finishStatus: [
                { label: "待完成", value: 0 },
                { label: "进行中", value: 1 },
                { label: "已完成", value: 2 }
            ]
        });

        const searchForm = ref<TableSearchbarData>({
            mainMaterialCode: null,
            finishStatus: null
        });

        const onSearch = () => {
            tablePagination.page = 1;
            tablePagination.pageSize = 10;
            getTableData();
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            { title: "主件号", key: "mainMaterialCode", align: "center", render: (row) => row.mainMaterialCode ?? "/" },
            {
                title: "组装需求数",
                key: "composeQuantity",
                align: "center",
                render: (row) => row.composeQuantity ?? "/"
            },
            {
                title: "需求总重（KG）",
                key: "composeWeight",
                align: "center",
                render: (row) => row.composeWeight ?? "/"
            },
            {
                title: "工序完成状态",
                key: "finishStatus",
                align: "center",
                render: (row) => {
                    if (row.finishStatus === 0) {
                        return <n-text type="error">待完成</n-text>;
                    } else if (row.finishStatus === 1) {
                        return <n-text type="info">进行中</n-text>;
                    } else if (row.finishStatus === 2) {
                        return <n-text type="success">已完成</n-text>;
                    } else {
                        return <n-text>/</n-text>;
                    }
                }
            },
            {
                title: "配方完成状态",
                key: "composeFinishStatus",
                align: "center",
                render: (row) => {
                    if (row.composeFinishStatus === 1) {
                        return <n-text type="error">待完成</n-text>;
                    } else if (row.composeFinishStatus === 2) {
                        return <n-text type="success">已完成</n-text>;
                    } else if (row.composeFinishStatus === 3) {
                        return <n-text type="info">进行中</n-text>;
                    } else {
                        return <n-text>/</n-text>;
                    }
                }
            },
            { title: "备注", key: "remark", align: "center", render: (row) => row.remark ?? "/" },
            {
                title: "组装零件清单",
                key: "id",
                align: "center",
                width: 120,
                render: (row) => (
                    <n-button type="primary" size="small" tertiary onClick={() => openDetailModal(row)}>
                        点击查看
                    </n-button>
                )
            },
            {
                title: "查看执行情况",
                key: "action",
                align: "center",
                width: 110,
                render: (row) => {
                    return (
                        <TableActions
                            type="button"
                            buttonActions={[
                                {
                                    label: "点击查看",
                                    tertiary: true,
                                    type: "primary",
                                    onClick: () => openExecutionModal(row)
                                }
                            ]}
                        />
                    );
                }
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;

            const params: any = {
                current: tablePagination.page,
                size: tablePagination.pageSize,
                typeId: props.configData.typeId,
                processType: currentProcessType.value,
                ...searchForm.value
            };

            GET_IRON_COMPONENT_PAGE_LIST(params).then((res) => {
                tableLoading.value = false;
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                } else {
                    tableData.value = [];
                    tablePagination.itemCount = 0;
                }
            });
        };

        watch(
            () => show.value,
            (val) => {
                if (val) onSearch();
            }
        );

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card
                    title={`组装电焊工序 - ${detailData.value?.ironType?.typeName ?? "/"} - ${
                        detailData.value?.ironProject?.projectName ?? "/"
                    }`}
                    class="w-1200px"
                    closable
                    onClose={onClose}
                >
                    <n-form label-placement="left" label-width="auto">
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi required span={4} label="工程名称">
                                {detailData.value?.ironProject?.projectName ?? "/"}
                            </n-form-item-gi>
                            <n-form-item-gi required span={4} label="工程简称">
                                {detailData.value?.ironProject?.projectAs ?? "/"}
                            </n-form-item-gi>
                            <n-form-item-gi required span={4} label="合同号">
                                {detailData.value?.ironProject?.contractNumber ?? "/"}
                            </n-form-item-gi>
                            <n-form-item-gi required span={4} label="塔型">
                                {detailData.value?.ironType?.typeName ?? "/"}
                            </n-form-item-gi>
                            <n-form-item-gi required span={4} label="电压等级">
                                {detailData.value?.ironProject?.powerLevel ?? "/"}
                            </n-form-item-gi>
                            <n-form-item-gi span={12}>
                                <div>
                                    <div class="text-18px font-bold">导入工序要求：</div>
                                </div>
                            </n-form-item-gi>
                            <n-form-item-gi required span={4} label="组装工序">
                                <n-text type="info">{String(detailData.value?.zzFlag) === "1" ? "有" : "无"}</n-text>
                            </n-form-item-gi>
                            <n-form-item-gi required span={4} label="电焊工序">
                                <n-text type="info">{String(detailData.value?.dhFlag) === "1" ? "有" : "无"}</n-text>
                            </n-form-item-gi>
                            <n-form-item-gi required span={12} label="角钢工序">
                                <n-text type="info">
                                    {(detailData.value?.jgTechniqueList ?? []).length > 0
                                        ? (detailData.value?.jgTechniqueList ?? [])
                                              .map((item: any) => item.techniqueName)
                                              .join(",")
                                        : "/"}
                                </n-text>
                            </n-form-item-gi>
                            <n-form-item-gi required span={12} label="钢板工序">
                                <n-text type="info">
                                    {(detailData.value?.gbTechniqueList ?? []).length > 0
                                        ? (detailData.value?.gbTechniqueList ?? [])
                                              .map((item: any) => item.techniqueName)
                                              .join(",")
                                        : "/"}
                                </n-text>
                            </n-form-item-gi>
                            <n-form-item-gi required span={12} label="圆钢工序">
                                <n-text type="info">
                                    {(detailData.value?.ygangTechniqueList ?? []).length > 0
                                        ? (detailData.value?.ygangTechniqueList ?? [])
                                              .map((item: any) => item.techniqueName)
                                              .join(",")
                                        : "/"}
                                </n-text>
                            </n-form-item-gi>
                            <n-form-item-gi required span={12} label="圆管工序">
                                <n-text type="info">
                                    {(detailData.value?.yguanTechniqueList ?? []).length > 0
                                        ? (detailData.value?.yguanTechniqueList ?? [])
                                              .map((item: any) => item.techniqueName)
                                              .join(",")
                                        : "/"}
                                </n-text>
                            </n-form-item-gi>
                            <n-form-item-gi required span={12} label="槽钢工序">
                                <n-text type="info">
                                    {(detailData.value?.cgTechniqueList ?? []).length > 0
                                        ? (detailData.value?.cgTechniqueList ?? [])
                                              .map((item: any) => item.techniqueName)
                                              .join(",")
                                        : "/"}
                                </n-text>
                            </n-form-item-gi>
                        </n-grid>
                    </n-form>
                    <n-card class="mt-2">
                        <TableSearchbar
                            form={searchForm.value}
                            config={searchConfig.value}
                            options={searchOptions.value}
                            onSearch={onSearch}
                        />
                    </n-card>
                    <n-tabs value={currentProcessType.value} onUpdate:value={onProcessTypeChange} type="bar" class="mt">
                        {processTypeOptions.map((option) => (
                            <n-tab-pane key={option.value} name={option.value} tab={option.label} />
                        ))}
                    </n-tabs>
                    <n-card class="mt">
                        <n-space class="mb">
                            <n-button type="warning" onClick={openImportModal}>
                                导入工序表
                            </n-button>
                        </n-space>
                        <n-data-table
                            columns={tableColumns.value}
                            data={tableData.value}
                            loading={tableLoading.value}
                            pagination={tablePagination}
                            row-key={tableRowKey}
                            single-line={false}
                            bordered
                            remote
                            striped
                            onUpdate:checked-row-keys={changeTableSelection}
                        />
                    </n-card>

                    <TowerScanEngineeringProcessesImport
                        v-model:show={importModal.value.show}
                        configData={importModal.value.configData}
                        onRefresh={onImportRefresh}
                    />
                    <TowerScanEngineeringProcessesComponentItem
                        v-model:show={detailModal.value.show}
                        configData={detailModal.value.configData}
                    />

                    <TowerScanEngineeringProcessesComponentExecution
                        v-model:show={executionModal.value.show}
                        configData={executionModal.value.configData}
                    />
                </n-card>
            </n-modal>
        );
    }
});
