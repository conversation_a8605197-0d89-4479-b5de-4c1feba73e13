<template>
    <div>
        <n-card content-style="padding-top:8px;padding-bottom:4px" hoverable>
            <n-tabs v-model:value="tabActive" animated type="bar" @before-leave="onTabBeforeLeave">
                <n-tab-pane :name="1" tab="计划" />
                <n-tab-pane :name="2" tab="实际" />
                <n-tab-pane :name="3" tab="废品" />
            </n-tabs>
        </n-card>
        <div class="mt">
            <ReportingFillDayCommonPlan v-if="tabActive === 1" />
            <ReportingFillDayCommonActual v-if="tabActive === 2" />
            <ReportingFillDayCommonWaste v-if="tabActive === 3" />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import ReportingFillDayCommonPlan from "./ReportingFillDayCommonPlan/ReportingFillDayCommonPlan.vue";
import ReportingFillDayCommonActual from "./ReportingFillDayCommonActual/ReportingFillDayCommonActual.vue";
import ReportingFillDayCommonWaste from "./ReportingFillDayCommonWaste/ReportingFillDayCommonWaste.vue";
import { usePublic } from "@/hooks";

let { onTabBeforeLeave } = usePublic();

let tabActive = ref(1);
</script>
