<template>
    <div class="sale-plan-issue-page w-100%">
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                :show-reset-button="false"
                :show-search-button="false"
                @search="onSearch"
            >
                <template #buttons>
                    <n-button type="primary" @click="onSubmit">下达年计划</n-button>
                </template>
            </table-searchbar>
        </n-card>
        <n-card class="mt" hoverable>
            <n-form :model="formData" label-placement="top" label-width="auto">
                <n-grid :cols="24" :x-gap="32">
                    <n-form-item-gi :span="6" label="年计划产值金额">
                        <n-input
                            v-model:value="formData.outputAmount"
                            class="w-100%"
                            clearable
                            placeholder="请输入年计划产值金额"
                        />
                    </n-form-item-gi>
                    <n-form-item-gi :span="6" label="年计划含税销售收入">
                        <n-input
                            v-model:value="formData.salesRevenue"
                            class="w-100%"
                            clearable
                            placeholder="请输入年计划含税销售收入"
                        />
                    </n-form-item-gi>
                    <n-form-item-gi :span="6" label="年计划开票金额">
                        <n-input
                            v-model:value="formData.invoiceAmount"
                            class="w-100%"
                            clearable
                            placeholder="请输入年计划开票金额"
                        />
                    </n-form-item-gi>
                    <n-form-item-gi :span="6" label="年计划预计回款金额">
                        <n-input
                            v-model:value="formData.expectedPaymentAmount"
                            class="w-100%"
                            clearable
                            placeholder="请输入年计划预计回款金额"
                        />
                    </n-form-item-gi>
                    <n-form-item-gi :span="6" label="年计划销售费用">
                        <n-input
                            v-model:value="formData.sellingExpenses"
                            class="w-100%"
                            clearable
                            placeholder="请输入年计划销售费用"
                        />
                    </n-form-item-gi>
                    <n-form-item-gi :span="6" label="年计划质量损失售后服务费用">
                        <n-input
                            v-model:value="formData.aftercareFee"
                            class="w-100%"
                            clearable
                            placeholder="请输入年计划销售费用"
                        />
                    </n-form-item-gi>
                    <n-form-item-gi :span="6" label="年计划二级市场销售额">
                        <n-input
                            v-model:value="formData.secondarySalesRevenue"
                            class="w-100%"
                            clearable
                            placeholder="请输入年计划销售费用"
                        />
                    </n-form-item-gi>
                    <n-form-item-gi :span="6" label="年计划立体销售额">
                        <n-input
                            v-model:value="formData.stereoscopicSalesRevenue"
                            class="w-100%"
                            clearable
                            placeholder="请输入年计划销售费用"
                        />
                    </n-form-item-gi>
                </n-grid>
            </n-form>
            <n-form label-placement="top" label-width="auto">
                <n-grid :cols="24" :x-gap="32">
                    <n-form-item-gi :span="24" label="业务类型">
                        <n-radio-group v-model:value="productTypeActive" @update:value="changeProductTypeActive">
                            <n-radio
                                v-for="(item, index) in productTypeOptions"
                                :key="index"
                                :label="item.label"
                                :value="item.value"
                            />
                        </n-radio-group>
                    </n-form-item-gi>
                </n-grid>
            </n-form>
            <div class="flex">
                <div class="flex-fixed-200 mr b-r-1px b-r-solid b-r-#e8e8e8">
                    <n-radio-group v-if="productTypeActive === 1" v-model:value="typeItemActive">
                        <n-space vertical>
                            <n-radio
                                v-for="(item, index) in productTypeList.plastic"
                                :key="index"
                                :label="item.label"
                                :value="item.productClassify"
                            />
                        </n-space>
                    </n-radio-group>
                    <n-radio-group v-if="productTypeActive === 2" v-model:value="typeItemActive">
                        <n-space vertical>
                            <n-radio
                                v-for="(item, index) in productTypeList.tower"
                                :key="index"
                                :label="item.label"
                                :value="item.productClassify"
                            />
                        </n-space>
                    </n-radio-group>
                    <n-collapse v-if="productTypeActive === 3" :border="false">
                        <n-collapse-item
                            v-for="(item, index) in productTypeList.power"
                            :key="index"
                            :name="item.productClassify"
                            :title="item.label"
                        >
                            <n-radio-group v-model:value="typeItemActive">
                                <n-space vertical>
                                    <n-radio
                                        v-for="(citem, cindex) in item.children"
                                        :key="cindex"
                                        :label="citem.label"
                                        :value="citem.productClassify"
                                    />
                                </n-space>
                            </n-radio-group>
                        </n-collapse-item>
                    </n-collapse>
                </div>
                <n-form v-if="typeItem" class="flex-fixed-600" label-placement="top" label-width="auto">
                    <n-grid :cols="12" :x-gap="32">
                        <n-form-item-gi :span="6" label="年计划发货数量">
                            <n-input
                                v-model:value="typeItem.shipmentQuantity"
                                class="w-100%"
                                clearable
                                placeholder="请输入年计划产值金额"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="年计划运费">
                            <n-input
                                v-model:value="typeItem.shipmentCost"
                                class="w-100%"
                                clearable
                                placeholder="请输入年计划含税销售收入"
                            />
                        </n-form-item-gi>
                    </n-grid>
                </n-form>
            </div>
        </n-card>
    </div>
</template>

<script lang="ts" setup>
import {
    TableSearchbar,
    type TableSearchbarConfig,
    type TableSearchbarData,
    type TableSearchbarOptions
} from "@/components/TableSearchbar";
import { onMounted, ref, watchEffect } from "vue";
import { GET_YD_COMPANY_LIST } from "@/api/permission";
import { GET_GROUP_YEAR_PLAN, SAVE_GROUP_YEAR_PLAN } from "@/api/application/salePlan";
import { cloneDeep } from "lodash-es";
import { useDicts } from "@/hooks";
import { GET_MANUFACTURE_CLASSIFY_TREE_LIST } from "@/api/application/reporting";

let { dictLibs, getDictLibs } = useDicts();

let setDictLibs = async () => {
    let dictName = ["plastic_product_type", "product_classify_tower"];
    await getDictLibs(dictName);
};

onMounted(async () => {
    await getSearchOptions();
    await setDictLibs();
    await getProductTypeList();
});

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { label: "计划年度", type: "year", prop: "planYear", dateFormat: "yyyy" },
    { label: "下发公司", type: "select", prop: "companyId", span: 2 }
]);

let searchOptions = ref<TableSearchbarOptions>({ companyId: [] });

let searchForm = ref<TableSearchbarData>({ companyId: null, planYear: null });

let getSearchOptions = async () => {
    await GET_YD_COMPANY_LIST({}).then((res) => {
        searchOptions.value.companyId = (res.data.data || []).map((i: any) => ({ label: i.company, value: i.id }));
    });
};

let onSearch = async () => {
    await getProductTypeList();
    await GET_GROUP_YEAR_PLAN({ ...searchForm.value }).then((res) => {
        formData.value = res.data.data || cloneDeep(initPlan);
        if (res.data.data?.planItemList?.length > 0) {
            productTypeList.value.plastic = (res.data.data.planItemList || []).filter((i: any) => i.industry === 1);
            productTypeList.value.tower = (res.data.data.planItemList || []).filter((i: any) => i.industry === 2);
            let powerList = (res.data.data.planItemList || []).filter((i: any) => i.industry === 3);

            productTypeList.value.power = productTypeList.value.power.map((item: any) => {
                if (item.children) {
                    item.children = item.children.map((citem: any) => {
                        let foundItem = powerList.find(
                            (ccitem: any) => String(citem.productClassify) === String(ccitem.productClassify)
                        );
                        return foundItem ? foundItem : citem;
                    });
                }
                return item;
            });
        }
    });
};

watchEffect(async () => {
    if (searchForm.value.companyId && searchForm.value.planYear) {
        await onSearch();
    }
});

// 计划内容
let initPlan: any = {
    outputAmount: null,
    salesRevenue: null,
    invoiceAmount: null,
    expectedPaymentAmount: null,
    sellingExpenses: null,
    aftercareFee: null,
    secondarySalesRevenue: null,
    stereoscopicSalesRevenue: null
};

// 表单数据
let formData = ref<any>(cloneDeep(initPlan));

// 业务类型相关
let productTypeOptions = ref<any[]>([
    { label: "塑业业务", value: 1 },
    { label: "铁塔业务", value: 2 },
    { label: "电力业务", value: 3 }
]);

let productTypeActive = ref(1);

let changeProductTypeActive = () => {
    typeItemActive.value = null;
    typeItem.value = null;
};

let productTypeList = ref<any>({ power: [], tower: [], plastic: [] });

// 处理分类
let handlePowerList = (data: any) => {
    let result: any[] = [];
    data.forEach((item: any) => {
        let obj = {
            label: item.classifyName,
            productClassify: item.id,
            industry: 3,
            shipmentQuantity: "0",
            shipmentCost: "0",
            children: item.childClassifyList
        };
        if (item.childClassifyList && item.childClassifyList.length) {
            obj.children = handlePowerList(item.childClassifyList);
        } else {
            delete obj.children;
        }
        result.push(obj);
    });
    return result;
};

let getProductTypeList = async () => {
    let powerList: any[] = [];
    await GET_MANUFACTURE_CLASSIFY_TREE_LIST({}).then((res) => {
        powerList = handlePowerList(res.data.data || []);
    });
    let towerList: any[] = (dictLibs["product_classify_tower"] || []).map((i) => ({
        label: i.label,
        productClassify: i.value,
        industry: 2,
        shipmentQuantity: "0",
        shipmentCost: "0"
    }));
    let plasticList: any[] = (dictLibs["plastic_product_type"] || []).map((i) => ({
        label: i.label,
        productClassify: i.value,
        industry: 1,
        shipmentQuantity: "0",
        shipmentCost: "0"
    }));
    productTypeList.value = { power: powerList, tower: towerList, plastic: plasticList };
};

// 业务类型选中
let typeItemActive = ref<any>(null);

let typeItem = ref<any>(null);

watchEffect(() => {
    if (typeItemActive.value) {
        if (productTypeActive.value === 1) {
            typeItem.value = (productTypeList.value.plastic || []).find(
                (i: any) => i.productClassify === typeItemActive.value
            );
        } else if (productTypeActive.value === 2) {
            typeItem.value = (productTypeList.value.tower || []).find(
                (i: any) => i.productClassify === typeItemActive.value
            );
        } else if (productTypeActive.value === 3) {
            let findItem: any = (list: any[]) => {
                let result = null;
                for (let i = 0; i < list.length; i++) {
                    if (list[i].productClassify === typeItemActive.value) {
                        result = list[i];
                        break;
                    }
                    if (list[i].children && list[i].children.length) {
                        result = findItem(list[i].children);
                        if (result) break;
                    }
                }
                return result;
            };
            typeItem.value = findItem(productTypeList.value.power);
        }
    }
});

// 提交
let onSubmit = () => {
    if (!searchForm.value.companyId || !searchForm.value.planYear) {
        return window.$message.error("请选择下发公司和计划年度");
    }
    let planItemList: any[] = [];
    productTypeList.value.plastic.forEach((i: any) => planItemList.push(i));
    productTypeList.value.tower.forEach((i: any) => planItemList.push(i));

    let powerListResult = productTypeList.value.power
        .flatMap((item: any) => item.children || [])
        .filter(
            (item: any, index: any, arr: any) =>
                arr.findIndex((citem: any) => citem.productClassify === item.productClassify) === index
        );

    planItemList = planItemList.concat(powerListResult);

    SAVE_GROUP_YEAR_PLAN({
        ...searchForm.value,
        ...formData.value,
        planItemList,
        planType: 1 // planType：计划类型（1：集团下达；2：各公司自己制定的）
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("下达成功");
        }
    });
};
</script>
