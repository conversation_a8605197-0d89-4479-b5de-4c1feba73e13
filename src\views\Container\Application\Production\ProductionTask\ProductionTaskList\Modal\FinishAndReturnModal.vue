<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-500px" closable title="完成并退料" @close="changeModalShow(false)">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-grid :cols="12" x-gap="16">
                        <n-form-item-gi :span="12" label="余料状态" path="status" required>
                            <n-radio-group v-model:value="formData.status">
                                <n-space>
                                    <n-radio :value="0">无余料</n-radio>
                                    <n-radio :value="1">退料</n-radio>
                                    <n-radio :value="2">迁料</n-radio>
                                </n-space>
                            </n-radio-group>
                        </n-form-item-gi>
                        <template v-if="formData.status !== 0">
                            <n-form-item-gi :span="12" label="余料数量" path="surplusMaterialCount">
                                <n-input-number
                                    v-model:value="formData.surplusMaterialCount"
                                    class="w-100%"
                                    placeholder="请输入余料数量"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi
                                v-if="formData.status === 2"
                                :span="12"
                                label="迁移项目"
                                path="surplusMaterialMovePoId"
                            >
                                <n-select
                                    v-model:value="formData.surplusMaterialMovePoId"
                                    :options="poIdOptions"
                                    label-field="id"
                                    placeholder="请选择迁移项目"
                                    value-field="id"
                                />
                            </n-form-item-gi>
                        </template>
                        <n-form-item-gi :span="12">
                            <n-space>
                                <n-button type="primary" @click="onSubmit">提交</n-button>
                                <n-button @click="changeModalShow(false)">取消</n-button>
                            </n-space>
                        </n-form-item-gi>
                    </n-grid>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref, watchEffect } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { GET_PRODUCTION_TASK_IN_PRODUCTION, PRODUCTION_TASK_FINISH_AND_RETURN } from "@/api/application/production";

let props = withDefaults(defineProps<{ show: boolean; configData: UnKnownObject }>(), {
    show: () => false
});

let emits = defineEmits(["update:show", "refresh"]);

let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

let changeModalShow = (show: boolean) => emits("update:show", show);

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {
    surplusMaterialCount: { required: true, type: "number", message: "请输入余料数量", trigger: ["input", "blur"] },
    surplusMaterialMovePoId: { required: true, message: "请选择迁移项目", trigger: ["blur", "change"] }
};

// 表单数据
interface FormDataProps {
    status: number;
    surplusMaterialCount: number;
    surplusMaterialMovePoId: Nullable<string | number>;
}

let initFormData: FormDataProps = {
    status: 0,
    surplusMaterialCount: 0,
    surplusMaterialMovePoId: null
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 获取选项
let poIdOptions = ref<UnKnownObject[]>([]);

let getOptions = async () => {
    GET_PRODUCTION_TASK_IN_PRODUCTION({
        current: 1,
        size: 100
    }).then((res) => {
        poIdOptions.value = (res.data.data.records ?? []).filter(
            (item: UnKnownObject) => item.poId !== props.configData.poId
        );
    });
};

watchEffect(() => {
    if (show.value) {
        getOptions();
    }
});

let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    let params: any;
    if (formData.value.status === 0) {
        params = { id: props.configData.id, surplusMaterialCount: 0 };
    } else if (formData.value.status === 1) {
        params = { id: props.configData.id, surplusMaterialCount: formData.value.surplusMaterialCount };
    } else if (formData.value.status === 2) {
        params = {
            id: props.configData.id,
            surplusMaterialCount: formData.value.surplusMaterialCount,
            surplusMaterialMovePoId: formData.value.surplusMaterialMovePoId
        };
    }

    PRODUCTION_TASK_FINISH_AND_RETURN(params).then(() => {
        changeModalShow(false);
        clearFrom();
        emits("refresh");
    });
};
</script>
