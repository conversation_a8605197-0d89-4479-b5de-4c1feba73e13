import { computed, defineComponent, reactive, ref, watch } from "vue";
import type { DataTableColumns } from "naive-ui";
import { useCommonTable } from "@/hooks";
import {
    GET_QUALITY_TASK_COMPONENT_DETAIL,
    GET_QUALITY_TASK_MAIN_PARTS_ATTRIBUTION_PLAN,
    GET_IRON_COMPONENT_FILL_PAGE_LIST,
    DELETE_IRON_COMPONENT_FILL
} from "@/api/application/TowerScan";
import { TableActions } from "@/components/TableActions";
import TowerScanEngineeringProcessesComponentExecutionHistory from "../Engineering/Processes/TowerScanEngineeringProcessesComponentExecutionHistory";
import TowerScanEngineeringProcessesComponentExecutionEdit from "../Engineering/Processes/TowerScanEngineeringProcessesComponentExecutionEdit";

export default defineComponent({
    name: "TowerScanQualityInspectionComponentDetail",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const modalShow = computed({
            get: () => props.show,
            set: (val: boolean) => emit("update:show", val)
        });

        // 详情数据
        const detailData = ref<any>({});
        const detailLoading = ref(false);

        // 零件列表数据
        const materialData = ref<any[]>([]);

        // 计划列表数据
        const planData = ref<any[]>([]);
        const planLoading = ref(false);

        // 质检记录相关
        // const activeProcessType = ref<number>(5); // 默认选择组装 - 已注释，使用外层传入的processType

        // 质检记录表格管理
        interface FillRecordRowProps {
            [key: string]: any;
        }
        const {
            tableRowKey: fillRecordsRowKey,
            tableData: fillRecordsData,
            tablePaginationPreset: fillRecordsPaginationPreset,
            tableLoading: fillRecordsLoading,
            tableSelection: fillRecordsSelection,
            changeTableSelection: changeFillRecordsSelection
        } = useCommonTable<FillRecordRowProps>("id");

        // 质检记录分页配置
        const fillRecordsPagination = reactive({
            ...fillRecordsPaginationPreset,
            onChange: (page: number) => {
                fillRecordsPagination.page = page;
                getFillRecordsData();
            },
            onUpdatePageSize: (pageSize: number) => {
                fillRecordsPagination.pageSize = pageSize;
                fillRecordsPagination.page = 1;
                getFillRecordsData();
            }
        });

        // 工序类型选项 - 已注释，使用外层传入的processType
        // const processTypeOptions = [
        //     { label: "组装", value: 5 },
        //     { label: "电焊", value: 6 }
        // ];

        // 获取工序类型显示名称
        const getProcessTypeName = (processType: number) => {
            switch (processType) {
                case 5:
                    return "组装";
                case 6:
                    return "电焊";
                default:
                    return "未知";
            }
        };

        // 计划表格列配置
        const planColumns = ref<DataTableColumns<any>>([
            { title: "计划名称", key: "planName", align: "center" },
            { title: "计划下发人", key: "issuedByName", align: "center" },
            { title: "计划主件数量", key: "planQuantity", align: "center" },
            {
                title: "计划状态",
                key: "planStatus",
                align: "center",
                render: (row) => {
                    switch (row.planStatus) {
                        case "1":
                            return <n-text type="warning">未开始</n-text>;
                        case "2":
                            return <n-text type="info">进行中</n-text>;
                        case "3":
                            return <n-text type="success">已完成</n-text>;
                        case "4":
                            return <n-text type="error">已结束</n-text>;
                        default:
                            return <n-text>/</n-text>;
                    }
                }
            }
        ]);

        // 质检记录表格列配置
        const fillRecordsColumns = ref<DataTableColumns<any>>([
            { type: "selection" },
            {
                title: "操作工",
                key: "belongUserNames",
                align: "center",
                width: 150,
                render: (row) => row.belongUserNames ?? "/"
            },
            { title: "质检员", key: "fillByName", align: "center", width: 100, render: (row) => row.fillByName ?? "/" },
            {
                title: "工序",
                key: "processType",
                align: "center",
                width: 100,
                render: (row) => {
                    const processTypeText = row.processType === 5 ? "组装" : row.processType === 6 ? "电焊" : "/";
                    return <n-text type="info">{processTypeText}</n-text>;
                }
            },
            {
                title: "工艺",
                key: "techniqueName",
                align: "center",
                width: 120,
                render: (row) => <n-text type="info">{row.techniqueName ?? "/"}</n-text>
            },
            {
                title: "主件号",
                key: "mainMaterialCode",
                align: "center",
                width: 120,
                render: (row) => row.mainMaterialCode ?? "/"
            },
            {
                title: "质检完成数量",
                key: "fillQuantity",
                align: "center",
                width: 120,
                render: (row) => <n-text type="success">{row.fillQuantity ?? "/"}</n-text>
            },
            {
                title: "质检完成总重(KG)",
                key: "fillWeight",
                align: "center",
                width: 140,
                render: (row) => row.fillWeight ?? "/"
            },
            {
                title: "质检日期",
                key: "fillDate",
                align: "center",
                width: 120,
                render: (row) => row.fillDate ?? "/"
            },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 260,
                render: (row) => {
                    return (
                        <TableActions
                            type="button"
                            buttonActions={[
                                {
                                    label: "修改",
                                    tertiary: true,
                                    type: "warning",
                                    onClick: () => handleEditFillRecord(row)
                                },
                                {
                                    label: "删除",
                                    tertiary: true,
                                    type: "error",
                                    onClick: () => handleDeleteFillRecord(row)
                                },
                                {
                                    label: "查看修改记录",
                                    tertiary: true,
                                    type: "primary",
                                    onClick: () => handleViewFillRecordHistory(row)
                                }
                            ]}
                        />
                    );
                }
            }
        ]);

        // 零件列表表格列配置
        const materialColumns = ref<DataTableColumns<any>>([
            { title: "零件号", key: "materialCode", align: "center" },

            {
                title: "组成数量",
                key: "composeQuantity",
                align: "center",
                render: (row) => row.composeQuantity || "/"
            },
            {
                title: "组成总重",
                key: "composeWeight",
                align: "center",
                render: (row) => (row.composeWeight ? `${row.composeWeight}kg` : "/")
            },
            {
                title: "是否主件",
                key: "mainFlag",
                align: "center",
                render: (row) => {
                    return row.mainFlag === 1 ? <n-text type="info">是</n-text> : <n-text>否</n-text>;
                }
            }
        ]);

        // 获取主件归属计划数据
        const getPlanData = async () => {
            const componentId = props.configData.componentId;
            if (!componentId) return;

            planLoading.value = true;
            try {
                const res = await GET_QUALITY_TASK_MAIN_PARTS_ATTRIBUTION_PLAN({
                    componentId: componentId
                });
                if (res.data.code === 0) {
                    planData.value = res.data.data || [];
                } else {
                    window.$message.error(res.data.msg || "获取计划数据失败");
                    planData.value = [];
                }
            } catch (error) {
                window.$message.error("获取计划数据失败");
                planData.value = [];
            } finally {
                planLoading.value = false;
            }
        };

        // 获取质检记录数据
        const getFillRecordsData = async () => {
            const componentId = props.configData.componentId;
            if (!componentId) return;

            fillRecordsLoading.value = true;
            try {
                const res = await GET_IRON_COMPONENT_FILL_PAGE_LIST({
                    current: fillRecordsPagination.page,
                    size: fillRecordsPagination.pageSize,
                    componentId: componentId,
                    processType: props.configData.processType // 使用外层传入的processType
                    // processId 暂时不传，如果接口必须需要的话可能需要从其他地方获取
                });
                if (res.data.code === 0) {
                    fillRecordsData.value = res.data.data?.records || [];
                    fillRecordsPagination.itemCount = res.data.data?.total || 0;
                } else {
                    window.$message.error(res.data.msg || "获取质检记录失败");
                    fillRecordsData.value = [];
                    fillRecordsPagination.itemCount = 0;
                }
            } catch (error) {
                window.$message.error("获取质检记录失败");
                fillRecordsData.value = [];
                fillRecordsPagination.itemCount = 0;
            } finally {
                fillRecordsLoading.value = false;
            }
        };

        // 处理工序类型切换 - 已注释，直接使用外层传入的processType
        // const handleProcessTypeChange = (processType: number) => {
        //     activeProcessType.value = processType;
        //     fillRecordsPagination.page = 1; // 重置页码
        //     getFillRecordsData();
        // };

        // 质检记录操作相关
        // 删除记录
        const handleDeleteFillRecord = (row: FillRecordRowProps) => {
            window.$dialog?.warning({
                title: "确认删除",
                content: "确定要删除这条配方填报记录吗？删除后无法恢复！",
                positiveText: "确定",
                negativeText: "取消",
                onPositiveClick: () => {
                    DELETE_IRON_COMPONENT_FILL({ ids: row.id })
                        .then((res) => {
                            if (res.data.code === 0) {
                                window.$message?.success("删除成功");
                                getFillRecordsData();
                            } else {
                                window.$message?.error(res.data.msg || "删除失败");
                            }
                        })
                        .catch(() => {
                            window.$message?.error("删除失败");
                        });
                }
            });
        };

        // 批量删除
        const handleBatchDeleteFillRecords = () => {
            if (fillRecordsSelection.value.length === 0) {
                window.$message?.warning("请选择要删除的记录");
                return;
            }

            window.$dialog?.warning({
                title: "确认批量删除",
                content: `确定要删除选中的 ${fillRecordsSelection.value.length} 条配方填报记录吗？删除后无法恢复！`,
                positiveText: "确定",
                negativeText: "取消",
                onPositiveClick: () => {
                    const ids = fillRecordsSelection.value.join(",");
                    DELETE_IRON_COMPONENT_FILL({ ids })
                        .then((res) => {
                            if (res.data.code === 0) {
                                window.$message?.success("批量删除成功");
                                getFillRecordsData();
                                changeFillRecordsSelection([]); // 清空选择
                            } else {
                                window.$message?.error(res.data.msg || "批量删除失败");
                            }
                        })
                        .catch(() => {
                            window.$message?.error("批量删除失败");
                        });
                }
            });
        };

        // 历史记录弹窗
        const fillRecordHistoryShow = ref(false);
        const fillRecordHistoryConfigData = ref<FillRecordRowProps>({});

        // 查看修改记录
        const handleViewFillRecordHistory = (row: FillRecordRowProps) => {
            fillRecordHistoryConfigData.value = row;
            fillRecordHistoryShow.value = true;
        };

        // 修改弹窗
        const fillRecordEditShow = ref(false);
        const fillRecordEditConfigData = ref<FillRecordRowProps>({});

        // 修改记录
        const handleEditFillRecord = (row: FillRecordRowProps) => {
            fillRecordEditConfigData.value = row;
            fillRecordEditShow.value = true;
        };

        // 修改完成刷新数据
        const handleFillRecordEditRefresh = () => {
            getFillRecordsData();
        };

        // 获取详情数据
        const getDetailData = async () => {
            const id = props.configData.componentId;
            if (!id) return;

            detailLoading.value = true;
            try {
                const res = await GET_QUALITY_TASK_COMPONENT_DETAIL({ id });
                if (res.data.code === 0) {
                    detailData.value = res.data.data || {};
                    materialData.value = res.data.data?.materialList || [];
                } else {
                    window.$message.error(res.data.msg || "获取详情失败");
                    detailData.value = {};
                    materialData.value = [];
                }
            } catch (error) {
                window.$message.error("获取详情失败");
                detailData.value = {};
                materialData.value = [];
            } finally {
                detailLoading.value = false;
            }
        };

        // 监听弹窗显示状态
        watch(
            () => modalShow.value,
            (show: boolean) => {
                if (show) {
                    getDetailData();
                    getPlanData();
                    getFillRecordsData();
                }
            }
        );

        const onClose = () => {
            modalShow.value = false;
            emit("refresh");
        };

        return () => [
            <n-modal v-model:show={modalShow.value} close-on-esc={false} mask-closable={false}>
                <n-card
                    title={`主件${detailData.value.mainMaterialCode || ""}质检记录`}
                    class="w-1200px"
                    closable
                    onClose={onClose}
                    v-loading={detailLoading.value}
                >
                    <n-form label-placement="left" label-width="auto">
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi span={4} label="工程名称" required>
                                {detailData.value?.projectName || "/"}
                            </n-form-item-gi>
                            <n-form-item-gi span={4} label="工程简称" required>
                                {detailData.value?.projectName || "/"}
                            </n-form-item-gi>
                            <n-form-item-gi span={4} label="合同号" required>
                                {detailData.value?.contractNumber || "/"}
                            </n-form-item-gi>
                            <n-form-item-gi span={4} label="塔型" required>
                                {detailData.value?.typeName || "/"}
                            </n-form-item-gi>
                            <n-form-item-gi span={4} label="主件号" required>
                                <n-text type="info">{detailData.value?.mainMaterialCode || "/"}</n-text>
                            </n-form-item-gi>
                            <n-form-item-gi span={4} label="组装需求数" required>
                                {detailData.value?.composeQuantity || "/"}
                            </n-form-item-gi>
                        </n-grid>
                    </n-form>
                    <n-tabs animated type="bar" defaultValue={1}>
                        <n-tab-pane name={1} tab="质检记录">
                            {/* 工序类型切换已注释，直接使用外层传入的processType */}
                            {/* <div class="mb-4">
                                <n-tabs 
                                    v-model:value={activeProcessType.value} 
                                    onUpdate:value={handleProcessTypeChange}
                                >
                                    {processTypeOptions.map((option) => (
                                        <n-tab-pane key={option.value} name={option.value} tab={option.label} />
                                    ))}
                                </n-tabs>
                            </div> */}

                            <n-data-table
                                columns={fillRecordsColumns.value}
                                data={fillRecordsData.value}
                                loading={fillRecordsLoading.value}
                                pagination={fillRecordsPagination}
                                row-key={fillRecordsRowKey}
                                single-line={false}
                                bordered
                                striped
                                remote
                                scroll-x={1000}
                                onUpdate:checked-row-keys={changeFillRecordsSelection}
                            />
                        </n-tab-pane>
                        <n-tab-pane name={2} tab="组装归属计划">
                            <n-data-table
                                columns={planColumns.value}
                                data={planData.value}
                                loading={planLoading.value}
                                single-line={false}
                                bordered
                                striped
                            />
                        </n-tab-pane>
                        <n-tab-pane name={3} tab="组装配方清单">
                            <n-data-table
                                columns={materialColumns.value}
                                data={materialData.value}
                                loading={detailLoading.value}
                                single-line={false}
                                bordered
                                striped
                            />
                        </n-tab-pane>
                    </n-tabs>
                </n-card>
            </n-modal>,

            // 质检记录修改弹窗
            <TowerScanEngineeringProcessesComponentExecutionEdit
                v-model:show={fillRecordEditShow.value}
                configData={fillRecordEditConfigData.value}
                onRefresh={handleFillRecordEditRefresh}
            />,

            // 质检记录历史记录弹窗
            <TowerScanEngineeringProcessesComponentExecutionHistory
                v-model:show={fillRecordHistoryShow.value}
                configData={fillRecordHistoryConfigData.value}
            />
        ];
    }
});
