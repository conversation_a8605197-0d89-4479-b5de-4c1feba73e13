<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card :title="id ? '编辑线路' : '新增线路'" class="w-800px" closable @close="closeModal">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-grid cols="12" x-gap="16">
                        <n-form-item-gi :span="6" label="线路编码" path="lineCode">
                            <n-input
                                v-model:value="formData.lineCode"
                                class="w-100%"
                                clearable
                                placeholder="请输入线路编码"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="线路名称" path="lineName">
                            <n-input
                                v-model:value="formData.lineName"
                                class="w-100%"
                                clearable
                                placeholder="请输入线路名称"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="线路简称" path="lineShortName">
                            <n-input
                                v-model:value="formData.lineShortName"
                                class="w-100%"
                                clearable
                                placeholder="请输入线路简称"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="开机允许报废米数" path="onAllowMeterNum">
                            <n-input
                                v-model:value="formData.onAllowMeterNum"
                                class="w-100%"
                                clearable
                                placeholder="请输入开机允许报废米数"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="更换允许报废米数" path="changeAllowMeterNum">
                            <n-input
                                v-model:value="formData.changeAllowMeterNum"
                                class="w-100%"
                                clearable
                                placeholder="请输入更换允许报废米数"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="停机允许报废米数" path="offAllowMeterNum">
                            <n-input
                                v-model:value="formData.offAllowMeterNum"
                                class="w-100%"
                                clearable
                                placeholder="请输入停机允许报废米数"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="清理模头允许报废米数" path="cleanAllowMeterNum">
                            <n-input
                                v-model:value="formData.cleanAllowMeterNum"
                                class="w-100%"
                                clearable
                                placeholder="请输入清理模头允许报废米数"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12">
                            <n-space>
                                <n-button type="primary" @click="onSubmit">提交</n-button>
                                <n-button @click="closeModal">取消</n-button>
                            </n-space>
                        </n-form-item-gi>
                    </n-grid>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { ref, watchEffect } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { useStoreUser } from "@/store";
import { ADD_OR_UPDATE_PRODUCTION_LINE, GET_PRODUCTION_LINE_DETAIL } from "@/api/application/production";

let storeUser = useStoreUser();

let props = withDefaults(
    defineProps<{
        show: Boolean;
        id: Nullable<string | number>;
    }>(),
    {
        show: () => false
    }
);

let emits = defineEmits(["update:show", "refresh"]);

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {
    lineCode: [{ required: true, message: "请输入线路编码", trigger: ["input", "blur"] }],
    lineName: [{ required: true, message: "请输入线路名称", trigger: ["input", "blur"] }],
    lineShortName: [{ required: true, message: "请输入线路简称", trigger: ["input", "blur"] }],
    onAllowMeterNum: [{ required: true, message: "请输入开机允许报废米数", trigger: ["input", "blur"] }],
    changeAllowMeterNum: [{ required: true, message: "请输入更换允许报废米数", trigger: ["input", "blur"] }],
    offAllowMeterNum: [{ required: true, message: "请输入停机允许报废米数", trigger: ["input", "blur"] }],
    cleanAllowMeterNum: [{ required: true, message: "请输入清理模头允许报废米数", trigger: ["input", "blur"] }]
};

// 表单数据
interface FormDataProps {
    lineCode: Nullable<string>;
    lineName: Nullable<string>;
    lineShortName: Nullable<string>;
    onAllowMeterNum: Nullable<string>;
    changeAllowMeterNum: Nullable<string>;
    offAllowMeterNum: Nullable<string>;
    cleanAllowMeterNum: Nullable<string>;
}

let initFormData: FormDataProps = {
    lineCode: null,
    lineName: null,
    lineShortName: null,
    onAllowMeterNum: null,
    changeAllowMeterNum: null,
    offAllowMeterNum: null,
    cleanAllowMeterNum: null
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 编辑时获取详情
watchEffect(() => {
    if (props.show) {
        if (props.id) {
            getDetail();
        }
    }
});

// 获取详情
let getDetail = () => {
    GET_PRODUCTION_LINE_DETAIL({ id: props.id }).then((res) => {
        if (res.data.code === 0) {
            let resData = res.data.data;
            formData.value = {
                lineCode: resData.lineCode,
                lineName: resData.lineName,
                lineShortName: resData.lineShortName,
                onAllowMeterNum: resData.onAllowMeterNum,
                changeAllowMeterNum: resData.changeAllowMeterNum,
                offAllowMeterNum: resData.offAllowMeterNum,
                cleanAllowMeterNum: resData.cleanAllowMeterNum
            };
        }
    });
};

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    emits("update:show", false);
};

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    if (!props.id) {
        ADD_OR_UPDATE_PRODUCTION_LINE({
            ...formData.value
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("新增成功");
                closeModal();
                emits("refresh");
            }
        });
    } else {
        ADD_OR_UPDATE_PRODUCTION_LINE({
            id: props.id,
            ...formData.value
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("修改成功");
                closeModal();
                emits("refresh");
            }
        });
    }
};
</script>
