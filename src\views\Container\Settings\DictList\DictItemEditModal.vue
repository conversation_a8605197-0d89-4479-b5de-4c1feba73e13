<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card :title="id ? '编辑字典项' : '新增字典项'" class="w-600px" closable @close="closeModal">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-form-item label="字典项名称" path="label">
                        <n-input
                            v-model:value="formData.label"
                            class="w-100%"
                            clearable
                            placeholder="请输入字典项名称"
                        />
                    </n-form-item>
                    <n-form-item label="字典项值" path="value">
                        <n-input v-model:value="formData.value" class="w-100%" clearable placeholder="请输入字典项值" />
                    </n-form-item>
                    <n-form-item label="字典项排序" path="sortOrder">
                        <n-input-number v-model:value="formData.sortOrder" class="w-100%" />
                    </n-form-item>
                    <n-form-item :span="12" label="配色" path="color">
                        <n-color-picker
                            v-model:value="formData.color"
                            :show-alpha="false"
                            :modes="['hex']"
                            :swatches="themeColorPreset"
                        />
                    </n-form-item>
                    <n-form-item>
                        <n-space>
                            <n-button type="primary" @click="onSubmit">提交</n-button>
                            <n-button @click="closeModal">取消</n-button>
                        </n-space>
                    </n-form-item>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { ADD_DICT_ITEM, GET_DICT_ITEM_BY_ID, UPDATE_DICT_ITEM } from "@/api/public";
import { themeColorPreset } from "@/setttings/theme";

let props = defineProps({
    show: { type: Boolean, default: false },
    id: { type: [String, Number] as PropType<any> },
    dictId: { type: [String, Number] as PropType<any> },
    dictType: { type: [String, Number] as PropType<any> }
});

let emits = defineEmits(["update:show", "refresh"]);

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {
    label: [{ required: true, message: "请输入字典项名称", trigger: ["input", "blur"] }],
    value: [{ required: true, message: "请输入字典项值", trigger: ["input", "blur"] }]
};

// 表单数据
interface FormDataProps<T = string | null> {
    id?: T | number;
    label: T;
    value: T | number;
    sortOrder: number;
    color: T;
}

let initFormData: FormDataProps = { label: null, value: null, sortOrder: 0, color: null };

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 编辑时获取详情
watch(
    () => ({ id: props.id, show: props.show }),
    (newVal) => {
        if (newVal.show && newVal.id) getDetail();
    },
    { deep: true }
);

// 获取详情
let getDetail = () => {
    GET_DICT_ITEM_BY_ID({ id: props.id }).then((res) => {
        let rowItem: FormDataProps = res.data.data;
        formData.value = {
            id: rowItem.id,
            label: rowItem.label,
            value: rowItem.value,
            sortOrder: rowItem.sortOrder,
            color: rowItem.color
        };
    });
};

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    emits("update:show", false);
};

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    if (!props.id) {
        ADD_DICT_ITEM({
            dictId: props.dictId,
            type: props.dictType,
            ...formData.value
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("新增成功");
                closeModal();
                emits("refresh");
            }
        });
    } else {
        UPDATE_DICT_ITEM({
            id: props.id,
            dictId: props.dictId,
            type: props.dictType,
            ...formData.value
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("编辑成功");
                closeModal();
                emits("refresh");
            }
        });
    }
};
</script>
