<template>
    <n-card content-style="padding-top:10px">
        <n-tabs type="bar" animated>
            <n-tab-pane name="物料分类">
                <ReportingCategoryMaterial class="pt-1" />
            </n-tab-pane>
            <n-tab-pane name="工资分类">
                <ReportingCategoryWages class="pt-1" />
            </n-tab-pane>
            <n-tab-pane name="配方分类">
                <ReportingCategoryFormula class="pt-1" />
            </n-tab-pane>
            <n-tab-pane name="其他分类">
                <ReportingCategoryOther class="pt-1" />
            </n-tab-pane>
        </n-tabs>
    </n-card>
</template>

<script setup lang="ts">
import ReportingCategoryMaterial from "./ReportingCategoryMaterial.vue";
import ReportingCategoryWages from "./ReportingCategoryWages.vue";
import ReportingCategoryFormula from "./ReportingCategoryFormula.vue";
import ReportingCategoryOther from "./ReportingCategoryOther.vue";
</script>
