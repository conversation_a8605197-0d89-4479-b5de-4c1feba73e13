<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <div class="mt flex">
            <n-card class="flex-fixed-200 mr" hoverable>
                <n-space size="large" vertical>
                    <n-button
                        v-for="item in pipeTypeList"
                        :tertiary="pipeTypeActive !== item.value"
                        block
                        type="primary"
                        @click="changePipeType(item)"
                    >
                        {{ item.label }}
                    </n-button>
                </n-space>
            </n-card>
            <n-card hoverable>
                <n-space>
                    <n-button
                        v-for="item in formList"
                        :ghost="formActive !== item.sheetId"
                        block
                        size="small"
                        type="primary"
                        @click="changeForm(item)"
                    >
                        {{ item.sheetTitle }}
                    </n-button>
                </n-space>
                <n-data-table
                    :columns="tableColumns"
                    :data="tableData"
                    :loading="tableLoading"
                    :pagination="tablePagination"
                    :row-key="tableRowKey"
                    :single-line="false"
                    bordered
                    class="mt"
                    remote
                    striped
                    @update:checked-row-keys="changeTableSelection"
                />
            </n-card>
        </div>
        <!--普通生产表单-->
        <ProductionCommonFormModal
            v-model:show="commonFormModal.show"
            :config-data="commonFormModal.configData"
            @refresh="getPipeTypeList"
        />
        <!--流程表单-->
        <ProcessFormLaunch v-model:show="launchModal.show" :config-data="launchModal.configData" />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from "vue";
import type { DataTableColumns } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable, useDicts } from "@/hooks";
import { TableActions } from "@/components/TableActions";
import { GET_LABORATORY_FORM_LIST, GET_LABORATORY_TEST_LIST } from "@/api/application/laboratory";
import { ProductionCommonFormModal } from "@/components/Production";
import { PRODUCTION_PUSH_FORM } from "@/api/application/production";
import { ProcessFormLaunch } from "./components";

interface RowProps {
    poId?: string;
    pomNumber?: string;
    reqId?: string;
    sheetFillStatus?: number;

    [key: string]: any;
}

// 字典操作
let { dictLibs, getDictLibs } = useDicts();

let setDictLibs = async () => {
    let dictName = ["product_type"];
    await getDictLibs(dictName);
};

onMounted(async () => {
    await setDictLibs();
    getSearchOptions();
    getPipeTypeList();
});

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([]);

let searchOptions = ref<TableSearchbarOptions>({});

let searchForm = ref<TableSearchbarData>({});

let getSearchOptions = () => {};

// 管道类型列表
let pipeTypeList = ref<{ label?: string; value?: string | number }[]>([]);

let pipeTypeActive = ref<string | number>("");

let getPipeTypeList = () => {
    pipeTypeList.value = dictLibs["product_type"];
    if (pipeTypeList.value.length > 0) {
        pipeTypeActive.value = pipeTypeList.value[0].value!;
        changePipeType(pipeTypeList.value[0]);
    }
};

let changePipeType = (item: { label?: string; value?: string | number }) => {
    pipeTypeActive.value = item.value!;
    GET_LABORATORY_FORM_LIST({
        pipeType: item.value!,
        sheetFillScene: 1
    }).then((res) => {
        if (res.data.data && res.data.data.length > 0) {
            formList.value = res.data.data;
            changeForm(res.data.data[0]);
        } else {
            formList.value = [];
            formActive.value = "";
        }
    });
};

// 表单类型
interface FormProps {
    sheetId: string;
    sheetTitle: string;
}

let formList = ref<FormProps[]>([]);

let formActive = ref<string>("");

let changeForm = (item: FormProps) => {
    formActive.value = item.sheetId;
    onSearch();
};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "订单号",
        key: "pomNumber",
        align: "center",
        render: (row) => {
            return row.pomNumber || "/";
        }
    },
    {
        title: "生产任务单号",
        key: "reqId",
        align: "center"
    },
    {
        title: "操作",
        key: "action",
        align: "center",
        width: 150,
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "填写",
                        tertiary: true,
                        type: "primary",
                        disabled: () => row.sheetFillStatus === 3,
                        onClick: () => {
                            if (row.sheetType === 1) {
                                openCommonFormModal(row);
                            } else if (row.sheetType === 2) {
                                openLaunchModal({
                                    ...row,
                                    name: row.sheetTitle
                                });
                            }
                        }
                    },
                    {
                        label: "推送",
                        tertiary: true,
                        type: "error",
                        disabled: () => row.sheetFillStatus !== 2,
                        onClick: () => onPush(row)
                    }
                ]
            });
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    GET_LABORATORY_TEST_LIST({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        sheetId: formActive.value,
        type: pipeTypeActive.value,
        ...searchForm.value
    }).then((res) => {
        tableData.value = res.data.data.records;
        tablePagination.itemCount = res.data.data.total;
        tableLoading.value = false;
    });
};

// 搜索
let onSearch = () => {
    tablePagination.page = 1;
    tablePagination.pageSize = 10;
    getTableData();
};

// 打开普通表单弹窗
let commonFormModal = ref<{ show: boolean; configData: RowProps }>({
    show: false,
    configData: {}
});

let openCommonFormModal = (row: RowProps) => {
    commonFormModal.value.show = true;
    commonFormModal.value.configData = row;
};

// 发起流程表单
let launchModal = ref<{ show: boolean; configData: RowProps }>({ show: false, configData: {} });

let openLaunchModal = (row: RowProps) => {
    launchModal.value = { show: true, configData: row };
};

// 推送
let onPush = (row: RowProps) => {
    window.$dialog.warning({
        title: "确认信息",
        content: "是否推送该生产表单？",
        positiveText: "确认",
        negativeText: "取消",
        onPositiveClick: () => {
            PRODUCTION_PUSH_FORM({
                sheetId: row.id,
                reqId: row.reqId
            }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("推送成功");
                    getPipeTypeList();
                }
            });
        }
    });
};
</script>
