import { computed, defineComponent, ref, watchEffect } from "vue";
import type { DataTableColumns } from "naive-ui";
import { GET_PRODUCTION_SCHEDULE_BY_ID } from "@/api/application/plasticMes";
import { GET_REPOSITORY_IN_RECEIPT_DETAIL } from "@/api/application/repository";

export default defineComponent({
    name: "RepositoryInReceiptDetail",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        // 获取详情
        const detailData = ref<any>(null);

        const getDetail = async () => {
            await GET_REPOSITORY_IN_RECEIPT_DETAIL({ id: props.configData.id }).then((res) => {
                if (res.data.code === 0) detailData.value = res.data.data;
            });
            // detailData.value = props.configData;
        };

        const renderTableColumns = ref<DataTableColumns<any>>([
            { title: "批次号", key: "batchNumber", align: "center" },
            { title: "单价", key: "unitPrice", align: "center" },
            { title: "入库数", key: "inQuality", align: "center" }
        ]);

        const tableColumns = ref<DataTableColumns<any>>([
            {
                type: "expand",
                expandable: (row) => row.recordItemNumberList && row.recordItemNumberList.length > 0,
                renderExpand: (row) => {
                    console.log(row);
                    return (
                        <n-data-table
                            columns={renderTableColumns.value}
                            data={row.recordItemNumberList}
                            single-line={false}
                            bordered
                            striped
                        />
                    );
                }
            },
            { title: "物料名称", key: "goodsName", align: "center" },
            { title: "物料规格", key: "goodsSpec", align: "center", render: (row) => row.goodsSpec || "/" },
            { title: "物料代码", key: "goodsCode", align: "center" },
            { title: "物料单位", key: "goodsUnitName", align: "center" },
            { title: "应入库数", key: "applyQuality", align: "center" },
            { title: "实入库数", key: "actualQuality", align: "center" },
            { title: "备注", key: "goodsRemark", align: "center", render: (row) => row.goodsRemark || "/" }
        ]);

        const onClose = () => {
            changeModalShow(false);
            emit("refresh");
        };

        watchEffect(() => {
            if (props.configData.id) getDetail();
        });

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card title="查看入库详情" class="w-800px" closable onClose={onClose}>
                    <n-form label-placement="left" label-width="auto">
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi required span={6} label="申请人">
                                {detailData.value?.applyByName ?? "/"}
                            </n-form-item-gi>
                            <n-form-item-gi required span={6} label="所属部门">
                                {detailData.value?.applyDepName ?? "/"}
                            </n-form-item-gi>
                            <n-form-item-gi required span={6} label="入库类型">
                                {detailData.value?.enterType === 1 && "手动入库"}
                                {detailData.value?.enterType === 2 && "产成品入库"}
                                {detailData.value?.enterType === 3 && "退料入库"}
                            </n-form-item-gi>
                            {detailData.value?.enterType === 3 && (
                                <n-form-item-gi required span={6} label="是否入库">
                                    {detailData.value?.enterFlag === 0 && "否"}
                                    {detailData.value?.enterFlag === 1 && "是"}
                                </n-form-item-gi>
                            )}

                            <n-form-item-gi required span={6} label="入库状态">
                                {detailData.value?.enterState === 1 && <n-text type="info">待入库</n-text>}
                                {detailData.value?.enterState === 2 && <n-text type="success">已确认</n-text>}
                                {detailData.value?.enterState === 3 && <n-text type="error">已拒绝</n-text>}
                            </n-form-item-gi>
                        </n-grid>
                        <n-data-table
                            columns={tableColumns.value}
                            data={detailData.value?.recordItemList ?? []}
                            single-line={false}
                            bordered
                            striped
                        />
                    </n-form>
                </n-card>
            </n-modal>
        );
    }
});
