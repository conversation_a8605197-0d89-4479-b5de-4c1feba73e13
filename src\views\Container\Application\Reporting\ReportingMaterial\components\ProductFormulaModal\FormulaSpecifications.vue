<template>
    <div>
        <n-collapse v-if="tabType === 'semiManufactureList'" v-model:expanded-names="openKeys">
            <n-collapse-item v-for="(item, index) in tableData" :name="index + 1" :title="item.poleName">
                <n-data-table
                    :columns="tableColumns"
                    :data="item.semiManufactureSpecList"
                    :single-line="false"
                    bordered
                    striped
                />
            </n-collapse-item>
            <n-collapse-item v-for="(item, index) in semiTableData" :name="index + 101" :title="item.semiProductModel">
                <n-data-table
                    :columns="tableColumns"
                    :data="item.semiManufactureFormulaSpecList"
                    :single-line="false"
                    bordered
                    striped
                />
            </n-collapse-item>
        </n-collapse>
        <template v-else>
            <n-data-table :columns="tableColumns" :data="tableData" :single-line="false" bordered striped />
            <div class="flex-x-center mt">
                <n-space>
                    <n-button type="warning" @click="onImportFormula">导入配方</n-button>
                    <n-button type="error" @click="onCancelBindFormula">取消配方绑定</n-button>
                </n-space>
            </div>
        </template>
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, ref, watchEffect } from "vue";
import type { DataTableColumns } from "naive-ui";
import { NSelect, NText } from "naive-ui";
import {
    GET_FORMULA_PAGE_LIST,
    GET_MANUFACTURE_FORMULA_SPEC_LIST,
    GET_MATERIAL_SPEC_PAGE_LIST,
    MANUFACTURE_IMPORT_FORMULA,
    PRODUCT_CANCEL_BIND_FORMULA,
    PRODUCT_GET_SEMI_LIST,
    SAVE_MANUFACTURE_SPEC_LIST
} from "@/api/application/reporting";
import { UnitSelector } from "@/views/Container/Application/Reporting/components";
import { useDicts } from "@/hooks";

let props = withDefaults(
    defineProps<{ configData: UnKnownObject; tabType: "manufactureSpecList" | "semiManufactureList" }>(),
    {}
);

onMounted(async () => {
    await setDictLibs();
    await getMaterialSpecIdOptions();
    getFormulaIdOptions();
});

// 字典操作
let { dictLibs, getDictLibs } = useDicts();

let setDictLibs = async () => {
    let dictName = ["common_units"];
    await getDictLibs(dictName);
};

// 获取原材料规格
let materialSpecIdOptions = ref<any[]>([]);

let getMaterialSpecIdOptions = async () => {
    await GET_MATERIAL_SPEC_PAGE_LIST({ current: 1, size: 1000 }).then((res) => {
        materialSpecIdOptions.value = (res.data.data.records ?? []).map((item: any) => {
            return {
                ...item,
                label: `${item.materialName} - ${item.spec}`,
                value: item.id
            };
        });
    });
};

// 表单数据
interface RowProps {
    materialSpecId: Nullable<string>; // 原材料
    unitPrice: string; // 单价
    unit: string; // 单位
    amount: string; // 用量
    deviationRatio: string; // 偏差比例
    consumeCoefficient: string; // 用量系数

    [key: string]: any;
}

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        title: "原材料",
        key: "materialName",
        align: "center",
        render: (row) => {
            return `【${row.materialName}】${row.spec}`;
        }
    },
    // 2023年9月8日单位变更需要对接数据-已处理
    {
        title: "单价(元)",
        key: "unitPrice",
        align: "center",
        render: (row) => {
            return `${row.unitPrice}`;
        }
    },
    {
        title: "价格计量单位",
        key: "unit",
        align: "center",
        render: (row) => {
            return h(UnitSelector, {
                type: "text",
                value: row.unit,
                options: dictLibs["common_units"] ?? []
            });
        }
    },
    {
        title: "用量",
        key: "amount",
        align: "center"
    },
    {
        title: "用量计量单位",
        key: "amountUnit",
        align: "center",
        render: (row) => {
            return h(UnitSelector, {
                type: "text",
                value: row.amountUnit,
                options: dictLibs["common_units"] ?? []
            });
        }
    },
    {
        title: "用量系数",
        key: "consumeCoefficient",
        align: "center"
    },
    {
        title: "偏差比例",
        key: "deviationRatio",
        align: "center"
    },
    {
        title: "状态",
        align: "center",
        key: "specDelFlag",
        width: "100",
        render: (row) => {
            if (row.specDelFlag === 1) {
                return h(NText, { type: "error" }, () => "已删除");
            } else {
                return h(NText, { type: "primary" }, () => "正常");
            }
        }
    }
]);

// 可编辑表单配置
let tableItem: RowProps = {
    materialSpecId: null,
    unitPrice: "",
    unit: "",
    amount: "",
    consumeCoefficient: "",
    deviationRatio: ""
};

let tableData = ref<RowProps[]>([]);

let clearFrom = () => {
    tableData.value = [];
};

let openKeys = ref<any[]>([]);

// 半成品专属
// 绑定半成品的数据
let semiTableData = ref<any[]>([]);

let getSemiTableData = () => {
    PRODUCT_GET_SEMI_LIST({ id: props.configData.id }).then((res) => {
        semiTableData.value = res.data.data.manufactureSemiItemList ?? [];
        semiTableData.value.forEach((_, index) => {
            openKeys.value.push(index + 101);
        });
    });
};

let getTableData = () => {
    GET_MANUFACTURE_FORMULA_SPEC_LIST({ id: props.configData.id }).then((res) => {
        tableData.value = res.data.data[props.tabType] ?? [];
        tableData.value.forEach((_, index) => {
            openKeys.value.push(index + 1);
        });
    });
};

watchEffect(() => {
    if (props.configData.id) {
        getTableData();
        if (props.tabType === "semiManufactureList") {
            getSemiTableData();
        }
    }
});

// 提交
let onSubmit = () => {
    let array = tableData.value.map((item) => {
        return {
            id: item.id ?? null,
            formulaId: item.formulaId ?? null,
            materialSpecId: item.materialSpecId,
            unitPrice: item.unitPrice,
            unit: item.unit,
            amount: item.amount,
            consumeCoefficient: item.consumeCoefficient,
            deviationRatio: item.deviationRatio,
            manufactureId: props.configData.id as string
        };
    });
    SAVE_MANUFACTURE_SPEC_LIST({
        manufactureId: props.configData.id,
        manufactureSpecList: array
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("保存成功");
            clearFrom();
            getTableData();
        } else {
            window.$message.error(res.data.message);
        }
    });
};

// 导入配方
let formulaId = ref<Nullable<string>>(null);

let formulaIdOptions = ref<any[]>([]);

let getFormulaIdOptions = () => {
    let productGenre: "2" | "3" = "2";
    if (props.tabType === "manufactureSpecList") {
        productGenre = "2";
    } else if (props.tabType === "semiManufactureList") {
        productGenre = "3";
    }
    GET_FORMULA_PAGE_LIST({
        productGenre,
        current: 1,
        size: 1000,
        companyId: props.configData.companyId
    }).then((res) => {
        formulaIdOptions.value = (res.data.data.records ?? []).map((item: any) => {
            return {
                ...item,
                label: `${item.formulaName}-${item.cementSuffix ?? ""}`,
                value: item.id
            };
        });
    });
};

let onImportFormula = () => {
    window.$dialog.warning({
        title: "选择配方",
        positiveText: "导入",
        negativeText: "取消",
        content: () => {
            return h("div", { class: "py-10px" }, [
                h(NSelect, {
                    options: formulaIdOptions.value,
                    clearable: true,
                    filterable: true,
                    placeholder: "请选择配方",
                    value: formulaId.value,
                    onUpdateValue: (v) => (formulaId.value = v)
                })
            ]);
        },
        onPositiveClick: () => {
            if (props.tabType === "manufactureSpecList") {
                MANUFACTURE_IMPORT_FORMULA({ id: props.configData.id, formulaId: formulaId.value }).then((res) => {
                    res.data.code === 0 ? window.$message.success("导入成功") : window.$message.error(res.data.message);
                    getTableData();
                    formulaId.value = null;
                });
            } else if (props.tabType === "semiManufactureList") {
                // 废弃
            }
        },
        onNegativeClick: () => (formulaId.value = null)
    });
};

// 取消配方绑定
let onCancelBindFormula = () => {
    window.$dialog.warning({
        title: "提示",
        content: "确定要取消？",
        positiveText: "确认",
        negativeText: "取消",
        onPositiveClick: () => {
            PRODUCT_CANCEL_BIND_FORMULA({ id: props.configData.id }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("取消成功");
                    getTableData();
                } else {
                    window.$message.error(res.data.message);
                }
            });
        }
    });
};
</script>
