<template>
    <div>
        <n-tabs v-model:value="tabActive" animated type="bar" @before-leave="onTabBeforeLeave">
            <n-tab-pane :name="1" tab="产成品计划用量" />
            <n-tab-pane :name="2" tab="原材料计划用量" />
            <n-tab-pane :name="3" tab="混凝土计划用量" />
        </n-tabs>
        <div class="mt-2">
            <ReportingFillDayCommonPlanAmountProduct v-if="tabActive === 1" />
            <ReportingFillDayCommonPlanAmountMaterial v-if="tabActive === 2" />
            <ReportingFillDayCommonPlanAmountConcrete v-if="tabActive === 3" />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import ReportingFillDayCommonPlanAmountProduct from "./ReportingFillDayCommonPlanAmountProduct.vue";
import ReportingFillDayCommonPlanAmountMaterial from "./ReportingFillDayCommonPlanAmountMaterial.vue";
import ReportingFillDayCommonPlanAmountConcrete from "./ReportingFillDayCommonPlanAmountConcrete.vue";
import { usePublic } from "@/hooks";

let { onTabBeforeLeave } = usePublic();

let tabActive = ref(1);
</script>
