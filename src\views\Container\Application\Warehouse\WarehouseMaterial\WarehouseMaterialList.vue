<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-space class="mb">
                <n-button secondary type="primary" @click="openEditModal()">新增</n-button>
            </n-space>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <!--新增编辑-->
        <WarehouseMaterialEdit
            v-model:show="editModal.show"
            :configData="editModal.configData"
            @refresh="getTableData()"
        />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from "vue";
import type { DataTableColumns } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { TableActions } from "@/components/TableActions";
import { useCommonTable, useDicts } from "@/hooks";
import { GET_WAREHOUSE_GOODS_LIST } from "@/api/application/warehouse";
import WarehouseMaterialEdit from "./WarehouseMaterialEdit.vue";

interface RowProps {
    [key: string]: any;
}

// 字典操作
let { dictLibs, getDictLibs } = useDicts();

onMounted(async () => {
    await setDictLibs();
    await getSearchOptions();
    getTableData();
});

let setDictLibs = async () => {
    let dictName = ["common_units"];
    await getDictLibs(dictName);
};

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([]);

let searchOptions = ref<TableSearchbarOptions>({});

let searchForm = ref<TableSearchbarData>({});

let getSearchOptions = async () => {};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "物资编码",
        key: "goodsCode",
        align: "center"
    },
    {
        title: "物资名称",
        key: "goodsName",
        align: "center"
    },
    {
        title: "所属品类",
        key: "categoryName",
        align: "center"
    },
    {
        title: "定额重量",
        key: "weight",
        align: "center"
    },
    {
        title: "单价",
        key: "unitPrice",
        align: "center"
    },
    {
        title: "单位",
        key: "unit",
        align: "center",
        render: (row) => getUnitText(row.unit)
    },
    {
        title: "初始数量",
        key: "initialQuantity",
        align: "center"
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: "120",
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "编辑信息",
                        tertiary: true,
                        onClick: () => {
                            openEditModal(row);
                        }
                    }
                ]
            });
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_WAREHOUSE_GOODS_LIST({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        ...searchForm.value
    }).then((res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data.records;
            tablePagination.itemCount = res.data.data.total;
            tableLoading.value = false;
        }
    });
};

// 搜索
let onSearch = () => {
    tablePagination.page = 1;
    tablePagination.pageSize = 10;
    getTableData();
};

// 新增编辑
let editModal = ref<{ show: boolean; configData: UnKnownObject }>({
    show: false,
    configData: {}
});

let openEditModal = (row?: RowProps) => {
    editModal.value = {
        show: true,
        configData: row ?? {}
    };
};

let getUnitText = (value: Nullable<string>): string => {
    let object = (dictLibs["common_units"] || []).find((item: any) => item.value === String(value));
    return object?.label ?? "未知";
};
</script>
