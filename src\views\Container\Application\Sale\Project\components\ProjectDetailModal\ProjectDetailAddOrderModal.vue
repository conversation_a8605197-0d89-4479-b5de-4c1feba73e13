<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-800px" closable title="匹配订单合同" @close="closeModal">
                <n-form
                    v-for="(item, index) in formData"
                    :key="index"
                    :model="item"
                    :rules="formRules"
                    label-placement="left"
                    label-width="auto"
                >
                    <n-grid :cols="24" :x-gap="24">
                        <n-form-item-gi :span="12" label="订单合同" path="contractId">
                            <n-select
                                v-model:value="item.contractId"
                                :options="contractIdOptions"
                                label-field="pomName"
                                placeholder="请选择订单合同"
                                value-field="pomId"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="节点负责人" path="nodeDirector">
                            <UserSelector
                                v-model:value="item.nodeDirector"
                                :multiple="false"
                                class="w-100%"
                                key-name="username"
                                placeholder="请选择节点负责人"
                            />
                        </n-form-item-gi>
                    </n-grid>
                </n-form>
                <n-space>
                    <n-button type="primary" @click="onSubmit">提交</n-button>
                    <n-button @click="closeModal">取消</n-button>
                </n-space>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { ref, watchEffect } from "vue";
import { cloneDeep } from "lodash-es";
import { GET_ORDER_CONTRACT_BY_FRAME_CONTRACT_ID } from "@/api/application/contract";
import { UserSelector } from "@/components/UserSelector";
import { POST_CHILD_BIND_ORDER_CONTRACT } from "@/api/application/sale";

let props = defineProps({
    show: { type: Boolean, default: false },
    configData: { type: Object as PropType<any> }
});

let emits = defineEmits(["update:show", "refresh"]);

// 表单实例
// let formRef = ref<FormInst | null>(null);

// 获取选项
let contractIdOptions = ref<any[]>([]);

let getOptions = () => {
    GET_ORDER_CONTRACT_BY_FRAME_CONTRACT_ID({
        pfcId: props.configData.contractId,
        current: 1,
        size: 9999,
        pomReviewState: 3
    }).then((res) => {
        contractIdOptions.value = res.data.data.records;
    });
};

// 表单校验
let formRules = {
    contractId: { required: false, message: "请选择订单合同", trigger: ["blur", "change"] },
    nodeDirector: { required: false, message: "请选择节点负责人", trigger: ["blur", "change"] }
};

// 表单数据
interface FormDataProps {
    contractId: Nullable<string | number>;
    nodeDirector: Nullable<string | number>;
}

let initFormData: FormDataProps = {
    contractId: null,
    nodeDirector: null
};

let formData = ref<FormDataProps[]>([cloneDeep(initFormData)]);

let clearFrom = () => {
    formData.value = [cloneDeep(initFormData)];
};

let addForm = () => {
    formData.value.push(cloneDeep(initFormData));
};

let removeForm = (index: number) => {
    formData.value.splice(index, 1);
};

watchEffect(() => {
    if (props.show) getOptions();
});

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    emits("update:show", false);
};

// 提交表单
let onSubmit = async () => {
    // let validateError = await formRef.value?.validate((errors) => !!errors);
    // if (validateError) return false;
    if (!formData.value[0].contractId || !formData.value[0].nodeDirector) {
        return window.$message.error("请填写完整信息");
    }
    POST_CHILD_BIND_ORDER_CONTRACT({
        projectId: props.configData.projectId,
        ...formData.value[0]
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("提交成功");
            closeModal();
            emits("refresh");
        }
    });
};
</script>
