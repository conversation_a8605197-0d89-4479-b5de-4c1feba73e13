<template>
    <div>
        <n-card content-style="padding-top:8px;padding-bottom:4px" hoverable>
            <n-tabs v-model:value="tabActive" animated type="bar" @before-leave="onTabBeforeLeave">
                <n-tab-pane :name="1" tab="计划" />
                <n-tab-pane :name="2" tab="实际" />
            </n-tabs>
        </n-card>
        <div class="mt">
            <ReportingFillDayExternalPlan v-if="tabActive === 1" />
            <ReportingFillDayExternalActual v-if="tabActive === 2" />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import ReportingFillDayExternalPlan from "./ReportingFillDayExternalPlan.vue";
import ReportingFillDayExternalActual from "./ReportingFillDayExternalActual.vue";
import { usePublic } from "@/hooks";

let { onTabBeforeLeave } = usePublic();

let tabActive = ref(1);
</script>
