import { createApp } from "vue";
import App from "./App.vue";
import { setupAssets, setupElement, setupNaive } from "./plugins";
import { setupRouter } from "./router";
import { setupStore } from "./store";
import print from "vue3-print-nb";
import vue3TreeOrg from "vue3-tree-org";
import "vue3-tree-org/lib/vue3-tree-org.css";
// import VxeTable from "vxe-table";
// import "vxe-table/lib/style.css";

const bootstrap = () => {
    const app = createApp(App);

    app.use(print);
    app.use(vue3TreeOrg);
    // app.use(VxeTable);

    // 按需引入naive-ui组件
    setupNaive(app);

    setupElement(app);

    // 配置引入资源
    setupAssets();

    // 配置路由
    setupRouter(app);

    // 配置store
    setupStore(app);

    app.mount("#app");
};

bootstrap();
