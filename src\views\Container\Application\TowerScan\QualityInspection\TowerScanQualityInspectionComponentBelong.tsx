import { computed, defineComponent, ref, watch } from "vue";
import type { DataTableColumns } from "naive-ui";
import { GET_QUALITY_TASK_MAIN_PARTS_ATTRIBUTION_PLAN } from "@/api/application/TowerScan";

export default defineComponent({
    name: "TowerScanQualityInspectionComponentBelong",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show"],
    setup(props, { emit }) {
        const modalShow = computed({
            get: () => props.show,
            set: (val: boolean) => emit("update:show", val)
        });

        // 计划列表数据
        const planData = ref([]);
        const planLoading = ref(false);

        // 表格列配置
        const planColumns = ref<DataTableColumns<any>>([
            { title: "计划名称", key: "planName", align: "center" },
            { title: "计划下发人", key: "issuedByName", align: "center" },
            { title: "计划主件数量", key: "planQuantity", align: "center" },
            {
                title: "计划状态",
                key: "planStatus",
                align: "center",
                render: (row) => {
                    switch (row.planStatus) {
                        case "1":
                            return <n-text type="warning">未开始</n-text>;
                        case "2":
                            return <n-text type="info">进行中</n-text>;
                        case "3":
                            return <n-text type="success">已完成</n-text>;
                        case "4":
                            return <n-text type="error">已结束</n-text>;
                        default:
                            return <n-text>/</n-text>;
                    }
                }
            }
        ]);

        // 获取主件归属计划数据
        const getPlanData = async () => {
            const componentId = props.configData.componentId;
            if (!componentId) return;

            planLoading.value = true;
            try {
                const res = await GET_QUALITY_TASK_MAIN_PARTS_ATTRIBUTION_PLAN({
                    componentId: componentId
                });
                if (res.data.code === 0) {
                    planData.value = res.data.data || [];
                } else {
                    window.$message.error(res.data.msg || "获取数据失败");
                    planData.value = [];
                }
            } catch (error) {
                window.$message.error("获取数据失败");
                planData.value = [];
            } finally {
                planLoading.value = false;
            }
        };

        // 监听弹窗显示状态
        watch(
            () => modalShow.value,
            (show: boolean) => {
                if (show) {
                    getPlanData();
                }
            }
        );

        const onClose = () => {
            modalShow.value = false;
        };

        return () => (
            <n-modal v-model:show={modalShow.value} close-on-esc={false} mask-closable={false}>
                <n-card
                    title={`主件归属计划 - ${
                        props.configData.mainMaterialCode || props.configData.typeName || "查看详情"
                    }`}
                    class="w-900px"
                    closable
                    onClose={onClose}
                >
                    <n-data-table
                        columns={planColumns.value}
                        data={planData.value}
                        loading={planLoading.value}
                        single-line={false}
                        bordered
                        striped
                    />
                </n-card>
            </n-modal>
        );
    }
});
