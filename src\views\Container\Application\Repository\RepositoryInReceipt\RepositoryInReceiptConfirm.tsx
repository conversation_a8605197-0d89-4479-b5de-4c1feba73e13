import { computed, defineComponent, h, ref, watchEffect } from "vue";
import { type DataTableColumns, type FormInst, NInput, NSelect, NTooltip } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { TableActions } from "@/components/TableActions";
import {
    CONFIRM_REPOSITORY_IN_RECEIPT,
    GET_REPOSITORY_GOODS_LIST,
    GET_REPOSITORY_IN_RECEIPT_DETAIL,
    GET_REPOSITORY_STOREROOM_LIST
} from "@/api/application/repository";
import { useDicts } from "@/hooks";

export default defineComponent({
    name: "RepositoryInReceiptConfirm",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        // 字典操作
        let { dictLibs, getDictLibs } = useDicts();

        let setDictLibs = async () => {
            let dictName = ["common_units"];
            await getDictLibs(dictName);
        };

        // 表单数据
        interface FormDataProps {
            [key: string]: any;
        }

        const formRef = ref<FormInst | null>(null);

        const enterStoreroomIdOptions = ref<any[]>([]);

        const getFormOptions = async () => {
            GET_REPOSITORY_STOREROOM_LIST({
                current: 1,
                size: 9999
            }).then((res) => {
                if (res.data.code === 0) {
                    enterStoreroomIdOptions.value = res.data.data.records.map((i: any) => ({
                        label: i.storeroomName,
                        value: i.id
                    }));
                }
            });
        };

        const getDetail = async () => {
            // tableData.value = (props.configData.recordItemList ?? []).map((item: any) => {
            //     return { ...item, isOriginal: true };
            // });
            await GET_REPOSITORY_IN_RECEIPT_DETAIL({ id: props.configData.id }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.recordItemList.map((item: any) => {
                        return { ...item, isOriginal: true };
                    });
                }
            });
        };

        const initFormData: FormDataProps = {
            enterFlag: 1
        };

        const formRules = computed(() => ({}));

        const formData = ref(cloneDeep(initFormData));

        const clearForm = () => {
            formData.value = cloneDeep(initFormData);
        };

        // 表单数据
        interface RowProps {
            [key: string]: any;
        }

        const goodsIdOptions = ref<any[]>([]);

        const getTableOptions = async () => {
            GET_REPOSITORY_GOODS_LIST({ current: 1, size: 9999 }).then((res) => {
                goodsIdOptions.value = res.data.data.records ?? [];
            });
        };

        watchEffect(async () => {
            if (show.value) {
                await getTableOptions();
                await getFormOptions();
                await setDictLibs();
                getDetail();
            }
        });

        const tableData = ref<RowProps[]>([]);

        const tableColumns = ref<DataTableColumns<RowProps>>([
            {
                title: "入库物品名称",
                key: "goodsName",
                align: "center",
                render: (row) => {
                    if (row.isOriginal) return row.goodsName;
                    return h(NSelect, {
                        options: goodsIdOptions.value,
                        clearable: true,
                        filterable: true,
                        placeholder: "请选择原料名称",
                        labelField: "goodsName",
                        valueField: "id",
                        value: row.goodsId,
                        disabled: !!row.isOriginal,
                        renderOption: ({ node, option }: any) => {
                            return h(NTooltip, null, { trigger: () => node, default: () => option.goodsName });
                        },
                        onUpdateValue: (v, o: any) => {
                            row.goodsId = v;
                            row.unit = o?.unit;
                            row.goodsSpec = o?.goodsSpec;
                        }
                    });
                }
            },
            {
                title: "应入数量",
                key: "applyQuality",
                align: "center",
                render: (row) => {
                    if (!row.actualQuality) row.actualQuality = row.applyQuality;
                    return row.applyQuality ?? "/";
                }
            },
            {
                title: "实际入库数",
                key: "actualQuality",
                align: "center",
                render: (row) => {
                    return h(NInput, {
                        value: row.actualQuality,
                        onUpdateValue: (v) => (row.actualQuality = v)
                    });
                }
            },
            {
                title: "物品单位",
                key: "unit",
                align: "center",
                render: (row) => {
                    let object = (dictLibs["common_units"] || []).find((i: any) => i.value === String(row.unit));
                    return object?.label ?? "/";
                }
            },
            { title: "规格型号", key: "goodsSpec", align: "center", render: (row) => row.goodsSpec ?? "/" },
            {
                title: "入库库房",
                key: "enterStoreroomId",
                align: "center",
                render: (row) => {
                    return h(NSelect, {
                        options: enterStoreroomIdOptions.value,
                        clearable: true,
                        filterable: true,
                        disabled: formData.value.enterFlag === 0,
                        placeholder: "请选择入库库房",
                        value: row.enterStoreroomId,
                        renderOption: ({ node, option }: any) => {
                            return h(NTooltip, null, { trigger: () => node, default: () => option.label });
                        },
                        onUpdateValue: (v) => {
                            row.enterStoreroomId = v;
                        }
                    });
                }
            },
            {
                title: "产成品类别",
                key: "goodsType",
                align: "center",
                render: (row) => {
                    if (row.goodsType == 2) {
                        return "合格品";
                    } else if (row.goodsType == 3) {
                        return "不合格品";
                    } else {
                        return "/";
                    }
                }
            },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 80,
                render(row, index) {
                    return h(TableActions, {
                        type: "button",
                        buttonActions: [
                            { label: "删除", tertiary: true, type: "error", onClick: () => deleteItem(row, index) }
                        ]
                    });
                }
            }
        ]);

        // 可编辑表单配置
        const tableItem: RowProps = {
            goodsId: null,
            applyQuality: null,
            isOriginal: false
        };

        const addTableItem = () => {
            tableData.value.push(cloneDeep(tableItem));
        };

        const deleteItem = (row: RowProps, index: number) => {
            if (row.id) {
                tableData.value.forEach((citem, cindex) => {
                    if (row.id === citem.id) citem.delFlag = 1;
                });
            } else tableData.value.splice(index, 1);
        };

        const onClose = () => {
            clearForm();
            changeModalShow(false);
            emit("refresh");
        };

        const onSubmit = async () => {
            const validateError = await formRef.value?.validate((errors) => !!errors);
            if (validateError) return false;

            CONFIRM_REPOSITORY_IN_RECEIPT({
                id: props.configData.id,
                enterFlag: formData.value.enterFlag,
                enterType: props.configData.enterType,
                recordItemList: tableData.value ?? []
            }).then((res) => {
                if (res.data.code === 0) {
                    clearForm();
                    changeModalShow(false);
                    emit("refresh");
                }
            });
        };

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card
                    title={
                        props.configData?.enterType === 1
                            ? `手动入库单`
                            : props.configData?.enterType === 2
                            ? `产成品入库单`
                            : props.configData?.enterType === 3
                            ? `退料入库单`
                            : "入库单"
                    }
                    class="w-1000px"
                    closable
                    onClose={onClose}
                >
                    <n-form
                        ref={formRef}
                        model={formData.value}
                        rules={formRules.value}
                        label-placement="left"
                        label-width="auto"
                    >
                        <n-grid cols={12} x-gap={16}>
                            {props.configData?.enterType === 3 && (
                                <n-form-item-gi span={6} label="是否入库" path="enterFlag">
                                    <n-switch
                                        v-model:value={formData.value.enterFlag}
                                        checked-value={1}
                                        unchecked-value={0}
                                    />
                                </n-form-item-gi>
                            )}
                            <n-form-item-gi span={12}>
                                <div class="w-100%">
                                    <div class="text-18px">物品清单</div>
                                    <div class="mt">
                                        <n-data-table
                                            columns={tableColumns.value}
                                            data={(tableData.value || []).filter((item) => item.delFlag !== 1)}
                                            single-line={false}
                                            bordered
                                            striped
                                        />
                                    </div>
                                    <div class="flex-x-center mt">
                                        <n-space>
                                            <n-button type="success" onClick={addTableItem}>
                                                新增一行
                                            </n-button>
                                        </n-space>
                                    </div>
                                </div>
                            </n-form-item-gi>
                            <n-form-item-gi span={12}>
                                <n-space>
                                    <n-button type="primary" onClick={onSubmit}>
                                        提交
                                    </n-button>
                                    <n-button onClick={onClose}>取消</n-button>
                                </n-space>
                            </n-form-item-gi>
                        </n-grid>
                    </n-form>
                </n-card>
            </n-modal>
        );
    }
});
