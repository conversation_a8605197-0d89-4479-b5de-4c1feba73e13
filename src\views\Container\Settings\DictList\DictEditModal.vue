<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card :title="id ? '编辑字典' : '新增字典'" class="w-600px" closable @close="closeModal">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-grid :cols="12" x-gap="16">
                        <n-form-item-gi :span="12" label="字典类型" path="type">
                            <n-input
                                v-model:value="formData.type"
                                class="w-100%"
                                clearable
                                placeholder="请输入字典类型"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="字典描述" path="description">
                            <n-input
                                v-model:value="formData.description"
                                class="w-100%"
                                clearable
                                placeholder="请输入字典描述"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="所属分类" path="dictType">
                            <n-select
                                v-model:value="formData.dictType"
                                :options="dictLibs.dict_type"
                                clearable
                                filterable
                                placeholder="请选择所属分类"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="字典标识" path="systemFlag">
                            <n-radio-group v-model:value="formData.systemFlag" name="systemFlag" disabled>
                                <n-radio label="用户字典" value="0" />
                                <n-radio label="系统字典" value="1" />
                            </n-radio-group>
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="备注" path="remark">
                            <n-input
                                v-model:value="formData.remark"
                                class="w-100%"
                                clearable
                                placeholder="请输入备注"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12">
                            <n-space>
                                <n-button type="primary" @click="onSubmit">提交</n-button>
                                <n-button @click="closeModal">取消</n-button>
                            </n-space>
                        </n-form-item-gi>
                    </n-grid>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { useStoreUser } from "@/store";
import { ADD_DICT_MANAGE, GET_DICT_MANAGE_BY_ID, UPDATE_DICT_MANAGE } from "@/api/public";
import { useDicts } from "@/hooks";

let storeUser = useStoreUser();

let props = defineProps({
    show: { type: Boolean, default: false },
    id: { type: [String, Number] as PropType<any> }
});

let emits = defineEmits(["update:show", "refresh"]);

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {
    type: [{ required: true, message: "请输入字典类型", trigger: ["input", "blur"] }],
    description: [{ required: true, message: "请输入字典描述", trigger: ["input", "blur"] }],
    dictType: [{ required: true, message: "请选择所属分类", trigger: ["blur", "change"] }],
    systemFlag: [{ required: true, message: "请输入字典标识", trigger: ["blur", "change"] }],
    remark: [{ required: false, message: "请输入备注", trigger: ["input", "blur"] }]
};

// 表单数据
interface FormDataProps<T = string | null> {
    id: T | number;
    description: T;
    type: T;
    remark: T;
    dictType: T | number;
    systemFlag: T | number;
}

let initFormData: FormDataProps = {
    id: null,
    description: null,
    type: null,
    remark: null,
    dictType: null,
    systemFlag: "1"
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 编辑时获取详情
watch(
    () => ({ id: props.id, show: props.show }),
    (newVal) => {
        if (newVal.show) getOptions();
        if (newVal.show && newVal.id) getDetail();
    },
    { deep: true }
);

// 获取详情
let getDetail = () => {
    GET_DICT_MANAGE_BY_ID({ id: props.id }).then((res) => {
        let rowItem: FormDataProps = res.data.data;
        formData.value = {
            id: rowItem.id,
            description: rowItem.description,
            type: rowItem.type,
            dictType: rowItem.dictType,
            systemFlag: rowItem.systemFlag,
            remark: rowItem.remark
        };
    });
};

let { dictLibs, getDictLibs, dictValueToLabel } = useDicts();

// 获取选项
let getOptions = async () => {
    let dictName = ["dict_type"];
    await getDictLibs(dictName);
};

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    emits("update:show", false);
};

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    if (!props.id) {
        ADD_DICT_MANAGE({
            ...formData.value
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("新增成功");
                closeModal();
                emits("refresh");
            }
        });
    } else {
        UPDATE_DICT_MANAGE({
            id: props.id,
            ...formData.value
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("编辑成功");
                closeModal();
                emits("refresh");
            }
        });
    }
};
</script>
