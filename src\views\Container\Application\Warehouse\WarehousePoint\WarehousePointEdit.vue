<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card :title="configData.id ? '编辑库点' : '新增库点'" class="w-800px" closable @close="closeModal">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-grid :cols="12" :x-gap="16">
                        <n-form-item-gi :span="6" label="所属公司" path="companyId">
                            <n-select
                                v-model:value="formData.companyId"
                                :options="companyIdOptions"
                                placeholder="请选择所属公司"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="库点名称" path="pointName">
                            <n-input
                                v-model:value="formData.pointName"
                                class="w-100%"
                                clearable
                                placeholder="请输入库点名称"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="库点编号" path="pointCode">
                            <n-input
                                v-model:value="formData.pointCode"
                                class="w-100%"
                                clearable
                                placeholder="请输入库点编号"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="是否启用" path="lockFlag">
                            <n-radio-group v-model:value="formData.lockFlag">
                                <n-space>
                                    <n-radio :value="0">启用</n-radio>
                                    <n-radio :value="1">停用</n-radio>
                                </n-space>
                            </n-radio-group>
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="负责人" path="pointManager">
                            <UserSelector
                                v-model:value="formData.pointManager"
                                :multiple="false"
                                class="w-100%"
                                key-name="username"
                                placeholder="请输入负责人"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="6" label="是否负责下属所有仓库" path="responsibleFlag">
                            <n-radio-group v-model:value="formData.responsibleFlag">
                                <n-space>
                                    <n-radio :value="1">是</n-radio>
                                    <n-radio :value="0">否</n-radio>
                                </n-space>
                            </n-radio-group>
                        </n-form-item-gi>
                        <n-form-item-gi :span="12">
                            <n-space>
                                <n-button type="primary" @click="onSubmit">提交</n-button>
                                <n-button @click="closeModal">取消</n-button>
                            </n-space>
                        </n-form-item-gi>
                    </n-grid>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref, watchEffect } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { GET_YD_COMPANY_LIST } from "@/api/permission";
import { ADD_WAREHOUSE_POINT, GET_WAREHOUSE_POINT_DETAIL, UPDATE_WAREHOUSE_POINT } from "@/api/application/warehouse";
import { UserSelector } from "@/components/UserSelector";

let props = withDefaults(
    defineProps<{
        show: boolean;
        configData: UnKnownObject;
    }>(),
    {
        show: () => false
    }
);

let emits = defineEmits(["update:show", "refresh"]);

onMounted(() => {
    getOptions();
});

// 获取选项
let companyIdOptions = ref<{}[]>([]);

let getOptions = async () => {
    await GET_YD_COMPANY_LIST({}).then((res) => {
        companyIdOptions.value = (res.data.data || []).map((item: { company: string; id: string }) => {
            return {
                label: item.company,
                value: item.id
            };
        });
    });
};

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = computed(() => {
    return {
        companyId: { required: true, message: "请选择所属公司", trigger: ["blur", "change"] },
        pointName: { required: true, message: "请输入库点名称", trigger: ["input", "blur"] },
        pointCode: { required: true, message: "请输入库点编号", trigger: ["input", "blur"] },
        lockFlag: { required: true, message: "请选择是否启用", trigger: ["blur", "change"], type: "number" },
        pointManager: { required: true, message: "请输入负责人", trigger: ["input", "blur"] },
        responsibleFlag: {
            required: true,
            message: "请选择是否负责下属所有仓库",
            trigger: ["blur", "change"],
            type: "number"
        }
    };
});

// 表单数据
interface FormDataProps {
    /**
     * 所属公司
     */
    companyId: Nullable<string>;
    /**
     * 状态（1：禁用；0：启用）
     */
    lockFlag: number;
    /**
     * 库点编号
     */
    pointCode: string;
    /**
     * 负责人
     */
    pointManager: string;
    /**
     * 库点名称
     */
    pointName: string;
    /**
     * 是否负责下属所有仓库（1：是；0：否）
     */
    responsibleFlag: number;
}

let initFormData: FormDataProps = {
    companyId: null,
    lockFlag: 0,
    pointCode: "",
    pointManager: "",
    pointName: "",
    responsibleFlag: 1
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 获取详情
let getDetail = () => {
    GET_WAREHOUSE_POINT_DETAIL({ id: props.configData.id }).then((res) => {
        if (res.data.code === 0) {
            formData.value = {
                companyId: res.data.data.companyId,
                lockFlag: res.data.data.lockFlag,
                pointCode: res.data.data.pointCode,
                pointManager: res.data.data.pointManager,
                pointName: res.data.data.pointName,
                responsibleFlag: res.data.data.responsibleFlag
            };
        }
    });
};

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    emits("update:show", false);
};

// 监听
watchEffect(() => {
    if (props.show && props.configData.id) {
        getDetail();
    }
});

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;

    if (props.configData.id) {
        await UPDATE_WAREHOUSE_POINT({ ...formData.value, id: props.configData.id }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("编辑成功");
                closeModal();
                emits("refresh");
            }
        });
    } else {
        await ADD_WAREHOUSE_POINT({ ...formData.value }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("新增成功");
                closeModal();
                emits("refresh");
            }
        });
    }
};
</script>
