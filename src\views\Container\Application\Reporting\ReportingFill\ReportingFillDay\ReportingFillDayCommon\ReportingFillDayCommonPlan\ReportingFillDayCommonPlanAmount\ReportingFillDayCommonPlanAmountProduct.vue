<template>
    <n-spin :show="loadingShow">
        <template #description>正在处理中，请耐心等候</template>
        <n-card>
            <table-searchbar
                auto-search
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
                @componentClick="onComponentClick"
            >
                <template #buttons>
                    <n-button type="success" @click="onCopyPlan">复制计划</n-button>
                </template>
            </table-searchbar>
        </n-card>
        <n-card class="mt">
            <div class="flex-y-center mb">
                <n-space>
                    <n-button secondary type="primary" @click="addTableItem">新增产成品计划</n-button>
                    <n-button secondary type="success" @click="saveTableData">保存填写</n-button>
                    <n-button v-if="planId" secondary type="warning" @click="changePlanDate">修改计划日期</n-button>
                </n-space>
                <div class="h-100% flex-y-center ml-a">计划数量合计：{{ totalNumber }}根</div>
            </div>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
    </n-spin>
</template>

<script lang="ts" setup>
import { computed, h, onMounted, ref } from "vue";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable, useDicts } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import { NButton, NDatePicker, NInput, NInputGroup, NInputGroupLabel, NTooltip } from "naive-ui";
import { useThrottleFn } from "@vueuse/core";
import {
    COPY_DAILY_PLAN,
    GET_CONFIG_WORK_GROUP_LIST,
    GET_MANUFACTURE_PAGE_LIST,
    GET_PLAN_AMOUNT_LIST,
    GET_SEMI_MANUFACTURE_PAGE_LIST,
    SAVE_PLAN_AMOUNT_LIST,
    UPDATE_PLAN_DATE
} from "@/api/application/reporting";
import { TableActions } from "@/components/TableActions";
import { SpecSelector, UnitSelector } from "@/views/Container/Application/Reporting/components";
import { cloneDeep } from "lodash-es";
import dayjs from "dayjs";
import { useStoreReportingSearch } from "@/store";
import { onBeforeRouteLeave } from "vue-router";

let storeReportingSearch = useStoreReportingSearch();

interface RowProps {
    [key: string]: any;
}

let loadingShow = ref(false);

// 字典操作
let { dictLibs, getDictLibs } = useDicts();

let setDictLibs = async () => {
    let dictName = ["common_units"];
    await getDictLibs(dictName);
};

onMounted(async () => {
    await getWorkGroupIdOptions();
    await setDictLibs();
    getTableData();
});

let getWorkGroupIdOptions = async () => {
    await GET_CONFIG_WORK_GROUP_LIST({}).then((res) => {
        searchOptions.value.workGroupId = (res.data.data ?? []).map((item: any) => {
            return {
                label: item.companyName + "-" + item.workshopName + "-" + item.groupName,
                value: item.id
            };
        });
    });
    searchForm.value.workGroupId = searchOptions.value.workGroupId[0].value;
    searchForm.value.planDate = dayjs().format("YYYY-MM-DD");
    if (storeReportingSearch.getSearchForm.workGroupId) {
        searchForm.value.workGroupId = storeReportingSearch.getSearchForm.workGroupId;
    }
    if (storeReportingSearch.getSearchForm.planDate) {
        searchForm.value.planDate = storeReportingSearch.getSearchForm.planDate;
    }
};

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { label: "班组", type: "select", prop: "workGroupId", span: 2 },
    { label: "日期筛选", type: "date", dateFormat: "yyyy-MM-dd", prop: "planDate" }
]);

let searchOptions = ref<TableSearchbarOptions>({ workGroupId: [] });

let searchForm = ref<TableSearchbarData>({ workGroupId: null, planDate: null });

let onSearch = () => {
    storeReportingSearch.setSearchForm({
        workGroupId: searchForm.value.workGroupId ?? null,
        planDate: searchForm.value.planDate ?? null
    });
    getTableData();
};

// 搜索栏自动保存逻辑
let autoSave = useThrottleFn(async () => {
    let totalPlanAmount = 0;
    tableData.value.forEach((i: any) => (totalPlanAmount += Number(i.planAmount)));
    let params = {
        ...searchForm.value,
        id: planId.value,
        totalPlanAmount: totalPlanAmount,
        dailyAmountList: tableData.value
    };
    await SAVE_PLAN_AMOUNT_LIST({ ...params }).then((res) => {
        if (res.data.code === 0) window.$message.success("自动保存成功");
    });
}, 1000);

let onComponentClick = async (val: TableSearchbarData) => {
    // await autoSave();
};

let planId = ref<Nullable<string | number>>(null);

// 数据列表
let { tableRowKey, tableData, tableLoading, changeTableSelection } = useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "名称",
        align: "center",
        key: "poleName",
        render: (row) => {
            return row.poleName ?? "未知";
        }
    },
    {
        title: "规格型号",
        align: "center",
        key: "contentId",
        render: (row) => {
            return h(SpecSelector, {
                value: row.contentId,
                onSubmit: async (v: unknown) => {
                    row.contentId = v;
                    let selectArray: any[] = [];
                    let [semiManufacture, manufacture] = await Promise.all([
                        GET_SEMI_MANUFACTURE_PAGE_LIST({ ids: v }),
                        GET_MANUFACTURE_PAGE_LIST({ ids: v })
                    ]);
                    let semiManufactureData = (semiManufacture.data.data.records ?? []).map((item: any) => {
                        return { ...item, contentType: 2 };
                    });
                    let manufactureData = (manufacture.data.data.records ?? []).map((item: any) => {
                        return { ...item, contentType: 1 };
                    });
                    selectArray = [...semiManufactureData, ...manufactureData];
                    row.contentType = selectArray[0].contentType;
                    row.poleName = selectArray[0].poleName;
                    row.amountUnit = selectArray[0].manufactureUnit ?? selectArray[0].semiUnit ?? "未知";
                    if (!v) {
                        row.poleName = null;
                        row.contentType = null;
                        row.amountUnit = null;
                    }
                }
            });
        }
    },
    // 2023年9月8日单位变更需要对接数据-已处理
    {
        title: "计划数量",
        align: "center",
        key: "planAmount",
        render: (row) => {
            return h(
                NTooltip,
                {},
                {
                    trigger: () => {
                        return h(NInputGroup, {}, () => [
                            h(NInput, {
                                value: row.planAmount,
                                onUpdateValue: (v) => (row.planAmount = v),
                                onFocus: () => {
                                    if (row.planAmount === "0") row.planAmount = "";
                                },
                                onBlur: () => {
                                    if (!row.planAmount) row.planAmount = "0";
                                }
                            }),
                            h(NInputGroupLabel, {}, () => [
                                h(UnitSelector, {
                                    type: "text",
                                    value: row.amountUnit,
                                    options: dictLibs["common_units"] ?? []
                                })
                            ])
                        ]);
                    },
                    default: () => {
                        return row.planAmount;
                    }
                }
            );
        }
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: "80",
        render(row, index) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "删除",
                        tertiary: true,
                        type: "error",
                        onClick: () => deleteItem(index)
                    }
                ]
            });
        }
    }
]);

let getTableData = () => {
    tableLoading.value = true;
    GET_PLAN_AMOUNT_LIST({
        ...searchForm.value
    }).then((res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data.dailyAmountList ?? [];
            planId.value = res.data.data.id ?? null;
            planDate.value = res.data.data.planDate ?? dayjs().format("YYYY-MM-DD");
        }
        tableLoading.value = false;
    });
};

// 可编辑表单配置
let tableItem: RowProps = {
    id: "",
    contentId: "",
    contentType: "",
    planAmount: "",
    productAmount: "0"
};

//新增计划
let addTableItem = () => {
    if (!searchForm.value.workGroupId || !searchForm.value.planDate) {
        window.$message.error("请先选择班组和日期！");
        return false;
    }
    tableData.value.push(cloneDeep(tableItem));
};

let deleteItem = (index: number) => {
    tableData.value.splice(index, 1);
};

let totalNumber = computed(() => {
    let total = 0;
    tableData.value.forEach((item: any) => {
        total += Number(item.planAmount);
    });
    return total;
});

//保存填写
let saveTableData = async () => {
    loadingShow.value = true;
    let totalPlanAmount = 0;
    tableData.value.forEach((item: any) => {
        totalPlanAmount += Number(item.planAmount);
    });
    let params = {
        ...searchForm.value,
        id: planId.value,
        totalPlanAmount: totalPlanAmount,
        dailyAmountList: tableData.value
    };
    if (!searchForm.value.workGroupId) {
        window.$message.error("请先选择班组！");
        return false;
    }
    if (!searchForm.value.planDate) {
        window.$message.error("请先选择日期！");
        return false;
    }
    await SAVE_PLAN_AMOUNT_LIST({ ...params }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("保存成功");
            onSearch();
        }
    });
    loadingShow.value = false;
};

//修改计划日期
let planDate = ref<string>(dayjs().format("YYYY-MM-DD"));

let changePlanDate = () => {
    window.$dialog.warning({
        title: "修改计划日期",
        positiveText: "确认",
        negativeText: "取消",
        content: () => {
            return h("div", { class: "py-10px" }, [
                h(NDatePicker, {
                    formattedValue: planDate.value,
                    class: "w-100%",
                    clearable: true,
                    placeholder: "请选择计划时间",
                    type: "date",
                    valueFormat: "yyyy-MM-dd",
                    onUpdateFormattedValue: (v: any) => {
                        planDate.value = v;
                    }
                })
            ]);
        },
        onPositiveClick: () => {
            loadingShow.value = true;
            UPDATE_PLAN_DATE({
                id: planId.value,
                planDate: planDate.value
            }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("修改成功");
                    onSearch();
                }
                loadingShow.value = false;
            });
        },
        onNegativeClick: () => {}
    });
};

// 复制计划
let copyDate = ref<any>(null);

let onCopyPlan = () => {
    window.$dialog.warning({
        title: "选择日期",
        positiveText: "确认",
        negativeText: "取消",
        content: () => {
            return h("div", { class: "py-10px" }, [
                h("div", { class: "text-16px mb" }, "确认后将复制目标当天的用量、人力资金项。"),
                h(NDatePicker, {
                    formattedValue: copyDate.value,
                    class: "w-100%",
                    clearable: true,
                    placeholder: "请选择日期",
                    type: "date",
                    valueFormat: "yyyy-MM-dd",
                    onUpdateFormattedValue: (v: any) => {
                        copyDate.value = v;
                    }
                })
            ]);
        },
        onPositiveClick: async () => {
            loadingShow.value = true;
            let planId = null;
            await GET_PLAN_AMOUNT_LIST({ ...searchForm.value, planDate: copyDate.value }).then((res) => {
                if (res.data.code === 0) planId = res.data.data.id ?? null;
            });
            if (!planId) {
                loadingShow.value = false;
                return window.$message.error("目标日期没有计划！");
            }
            COPY_DAILY_PLAN({
                fromDailyPlanId: planId,
                toPlanDate: searchForm.value.planDate
            }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("复制成功");
                    onSearch();
                }
                loadingShow.value = false;
            });
        },
        onNegativeClick: () => {
            copyDate.value = null;
        }
    });
};

onBeforeRouteLeave((to, from, next) => {
    window.$dialog.warning({
        title: "确认信息",
        content: "即将离开车间填报功能，需要再检查一下吗？",
        positiveText: "需要",
        negativeText: "不需要",
        onPositiveClick: () => {
            next(false);
        },
        onNegativeClick: () => {
            next();
        }
    });
});
</script>
