<template>
    <div class="w-100%">
        <n-data-table :columns="tableColumns" :data="tableData" :single-line="false" bordered striped />
        <div class="flex-x-center mt">
            <n-space>
                <n-button type="primary" @click="addTableItem">新增一行</n-button>
                <n-button type="success" @click="onSubmit">保存全部</n-button>
            </n-space>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed, h, onMounted, ref, watchEffect } from "vue";
import type { DataTableColumns } from "naive-ui";
import { NInput, NInputGroup, NInputGroupLabel, NSelect } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { GET_SEMI_MANUFACTURE_PAGE_LIST } from "@/api/application/reporting";
import { TableActions } from "@/components/TableActions";
import { SpecSelector, UnitSelector } from "@/views/Container/Application/Reporting/components";
import { useDicts } from "@/hooks";

let emits = defineEmits(["confirm", "update:value"]);

// 字典操作
let { dictLibs, getDictLibs } = useDicts();

let setDictLibs = async () => {
    let dictName = ["common_units"];
    await getDictLibs(dictName);
};

watchEffect(async () => {
    if (props.companyId) {
        await setDictLibs();
        await getSemiMaterialSpecIdOptions();
    }
});

// 获取原材料规格
let semiMaterialSpecIdOptions = ref<any[]>([]);

let getSemiMaterialSpecIdOptions = async () => {
    await GET_SEMI_MANUFACTURE_PAGE_LIST({ current: 1, size: 9999, companyId: props.companyId }).then((res) => {
        semiMaterialSpecIdOptions.value = (res.data.data.records ?? []).map((item: any) => {
            return {
                ...item,
                label: `${item.productModel}-${item.cementSuffix ?? ""}`,
                value: item.id
            };
        });
    });
};

// 表单数据
interface RowProps {
    semiManufactureId: Nullable<number>; // 半成品ID
    semiManufactureProportion: string; // 半成品占比

    [key: string]: any;
}
// 2023年9月8日单位变更需要对接数据-已处理
let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        title: "半成品",
        key: "semiManufactureId",
        align: "center",
        render: (row) => {
            return h(SpecSelector, {
                value: row.semiManufactureId,
                productGenre: 3,
                onSubmit: async (v: any, o: any[]) => {
                    row.semiManufactureId = v;
                    console.log(1111, o);
                    if (o?.length > 0) row.semiUnit = o[0].semiUnit;
                }
            });
        }
    },
    {
        title: "原材料占比",
        key: "semiManufactureProportion",
        align: "center",
        render: (row) => {
            return h(NInputGroup, {}, () => [
                h(NInput, {
                    value: row.semiManufactureProportion,
                    onUpdateValue: (v) => (row.semiManufactureProportion = v)
                }),
                h(NInputGroupLabel, {}, () => [
                    h(UnitSelector, {
                        type: "text",
                        value: row.semiUnit,
                        options: dictLibs["common_units"] ?? [],
                        onSubmit: (v: any) => (row.semiUnit = v)
                    })
                ])
            ]);
        }
    },
    {
        title: "操作",
        key: "action",
        align: "center",
        width: 80,
        render(row, index) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "删除",
                        tertiary: true,
                        type: "error",
                        onClick: () => deleteItem(index)
                    }
                ]
            });
        }
    }
]);

// 可编辑表单配置
let tableItem: RowProps = {
    semiManufactureId: null,
    semiManufactureProportion: "1"
};

let addTableItem = () => {
    if (!props.companyId) {
        window.$message.error("请先选择公司");
        return;
    }
    tableData.value.push(cloneDeep(tableItem));
};

let deleteItem = (index: number) => {
    tableData.value.splice(index, 1);
};

let props = withDefaults(defineProps<{ value: RowProps[]; companyId: Nullable<string | number> }>(), {
    value: () => []
});

let tableData = computed({ get: () => props.value, set: (val) => emits("update:value", val) });

// 提交
let onSubmit = () => {
    emits("confirm", tableData.value);
};
</script>
