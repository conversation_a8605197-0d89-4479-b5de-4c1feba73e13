import { defineComponent, onMounted, reactive, ref, watchEffect } from "vue";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import {
    ATTENDANCE_EXPORT_DOWNLOAD,
    DELETE_ATTENDANCE_DATA_QUERY,
    GET_ATTENDANCE_QUERY_EXPORT,
    GET_ATTENDANCE_QUERY_LIST
} from "@/api/permission";
import AttendanceDataQueryEdit from "@/views/Container/Permission/Attendance/Data/AttendanceDataQuery/AttendanceDataQueryEdit";
import dayjs from "dayjs";
import { TableActions } from "@/components/TableActions";
import AttendanceDataQueryDayDetail from "@/views/Container/Permission/Attendance/Data/AttendanceDataQuery/AttendanceDataQueryDayDetail";
import AttendanceDataQueryOtherDetail from "@/views/Container/Permission/Attendance/Data/AttendanceDataQuery/AttendanceDataQueryOtherDetail";
import { PUT_PUNISHMENT_CONFIG_USER } from "@/api/application/plasticMes";

export default defineComponent({
    name: "AttendanceDataQuery",
    props: {
        corpId: { type: String as PropType<string | null>, default: null }
    },
    setup(props, { emit }) {
        // 搜索项
        const searchConfig = ref<TableSearchbarConfig>([]);

        const searchOptions = ref<TableSearchbarOptions>({});

        const getSearchOptions = async () => {};

        const searchForm = ref<TableSearchbarData>({});

        const onSearch = () => {
            getTableData();
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            {
                title: "查询类型",
                key: "queryType",
                align: "center",
                render: (row) => {
                    switch (row.queryType) {
                        case 1:
                            return <n-text type="info">每日报表</n-text>;
                        case 2:
                            return <n-text type="info">每周报表</n-text>;
                        case 3:
                            return <n-text type="info">每月报表</n-text>;
                        case 4:
                            return <n-text type="info">自定义时间段</n-text>;
                        default:
                            return <n-text type="info">/</n-text>;
                    }
                }
            },
            {
                title: "数据查询日期",
                key: "startTime",
                align: "center",
                render(row) {
                    const startTime = dayjs(row.startTime).format("YYYY-MM-DD");
                    if (!row.endTime || startTime === dayjs(row.endTime).format("YYYY-MM-DD")) {
                        return startTime;
                    }

                    return startTime + " ~ " + dayjs(row.endTime).format("YYYY-MM-DD");
                }
            },
            {
                title: "操作人",
                key: "createByName",
                align: "center"
            },
            {
                title: "查询状态",
                key: "queryStatus",
                align: "center",
                render: (row) => {
                    const queryStatus = String(row.queryStatus);
                    switch (queryStatus) {
                        case "1":
                            return <n-text type="success">成功</n-text>;
                        case "2":
                            return <n-text type="info">部分成功</n-text>;
                        case "3":
                            return <n-text type="warning">同步中</n-text>;
                        case "4":
                            return <n-text type="error">失败</n-text>;
                        default:
                            return "/";
                    }
                }
            },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 220,
                render: (row) => {
                    return (
                        <TableActions
                            type="button"
                            buttonActions={[
                                {
                                    label: "查看",
                                    tertiary: true,
                                    type: "primary",
                                    onClick: () => {
                                        if (row.queryType === 1) {
                                            openDayDetailModal(row);
                                        } else {
                                            openOtherDetailModal(row);
                                        }
                                    }
                                },
                                { label: "导出", tertiary: true, type: "success", onClick: () => onExport(row.id) },
                                { label: "删除", tertiary: true, type: "error", onClick: () => onDelete(row) }
                            ]}
                        />
                    );
                }
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_ATTENDANCE_QUERY_LIST({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                corpId: props.corpId,
                ...searchForm.value
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        // 新增功能
        const editModal = ref<{ show: boolean; configData: RowProps }>({ show: false, configData: {} });

        const openEditModal = (row?: RowProps) => {
            editModal.value.show = true;
            editModal.value.configData = {
                ...(row ?? {}),
                corpId: props.corpId
            };
        };

        // 详情弹窗
        const dayDetailModal = ref<{ show: boolean; configData: RowProps }>({ show: false, configData: {} });

        const openDayDetailModal = (row?: RowProps) => {
            dayDetailModal.value.show = true;
            dayDetailModal.value.configData = {
                ...(row ?? {}),
                corpId: props.corpId
            };
        };

        const otherDetailModal = ref<{ show: boolean; configData: RowProps }>({ show: false, configData: {} });

        const openOtherDetailModal = (row?: RowProps) => {
            otherDetailModal.value.show = true;
            otherDetailModal.value.configData = {
                ...(row ?? {}),
                corpId: props.corpId
            };
        };

        // 导出功能
        const onExport = (id: string | number) => {
            window.$dialog.warning({
                title: "提示",
                content: "确定导出吗？",
                positiveText: "确定",
                negativeText: "取消",
                onPositiveClick: () => {
                    GET_ATTENDANCE_QUERY_EXPORT({
                        id
                    }).then((res) => {
                        ATTENDANCE_EXPORT_DOWNLOAD({
                            fileName: res.data.data
                        }).then((cres) => {
                            const blob = new Blob([cres.data]);
                            const a = document.createElement("a");
                            a.href = URL.createObjectURL(blob);
                            a.download = res.data.data;
                            a.style.display = "none";
                            document.body.appendChild(a);
                            a.click();
                            a.remove();
                        });
                    });
                }
            });
        };

        const onDelete = (row: RowProps) => {
            window.$dialog.warning({
                title: "警告",
                content: "确定要删除该条数据吗？",
                positiveText: "确定",
                negativeText: "取消",
                onPositiveClick: () => {
                    DELETE_ATTENDANCE_DATA_QUERY({
                        id: row.id
                    }).then((res: { data: RowProps }) => {
                        if (res.data.code === 0) {
                            window.$message.success("删除成功");
                            getTableData();
                        }
                    });
                }
            });
        };

        watchEffect(() => {
            if (props.corpId) getTableData();
        });

        onMounted(async () => {
            await getSearchOptions();
        });

        return () => (
            <div class="attendance-page">
                <n-card>
                    <TableSearchbar
                        form={searchForm.value}
                        config={searchConfig.value}
                        options={searchOptions.value}
                        onSearch={onSearch}
                    />
                </n-card>
                <n-card class="mt">
                    <n-space class="mb">
                        <n-button type="primary" onClick={() => openEditModal()}>
                            新增考勤查询
                        </n-button>
                    </n-space>
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        pagination={tablePagination}
                        row-key={tableRowKey}
                        single-line={false}
                        bordered
                        remote
                        striped
                        onUpdate:checked-row-keys={changeTableSelection}
                    />
                </n-card>
                <AttendanceDataQueryEdit
                    v-model:show={editModal.value.show}
                    v-model:configData={editModal.value.configData}
                    onRefresh={getTableData}
                />
                <AttendanceDataQueryDayDetail
                    v-model:show={dayDetailModal.value.show}
                    v-model:configData={dayDetailModal.value.configData}
                />
                <AttendanceDataQueryOtherDetail
                    v-model:show={otherDetailModal.value.show}
                    v-model:configData={otherDetailModal.value.configData}
                />
            </div>
        );
    }
});
