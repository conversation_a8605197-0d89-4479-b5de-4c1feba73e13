import { defineComponent, ref, computed } from "vue";
import { type FormInst } from "naive-ui";
import { PUSH_IRON_PRODUCTION_PLAN } from "@/api/application/TowerScan";
// import { UserSelector } from "@/components/UserSelector";

export default defineComponent({
    name: "TowerScanProductionPush",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => {
            emit("update:show", show);
            // if (!show) {
            //     clearForm();
            // }
        };

        // 表单数据 - 注释掉，不再需要选择执行人
        // const formRef = ref<FormInst | null>(null);
        // const formData = ref({
        //     implementorList: ""
        // });

        // const clearForm = () => {
        //     formData.value = {
        //         implementorList: ""
        //     };
        // };

        // 表单校验规则 - 注释掉
        // const formRules = {
        //     implementorList: {
        //         required: true,
        //         message: "请选择计划执行人",
        //         trigger: ["blur", "change"]
        //     }
        // };

        const onSubmit = async () => {
            // 注释掉冗余的二次确认dialog
            // window.$dialog.info({
            //     title: "确认推送",
            //     content: `确定要推送计划"${props.configData.planName}"吗？`,
            //     positiveText: "确定推送",
            //     negativeText: "取消",
            //     onPositiveClick: async () => {
            // 不再需要表单验证
            // const validateError = await formRef.value?.validate((errors) => !!errors);
            // if (validateError) return false;

            const submitData = {
                id: props.configData.id
                // 注释掉不再需要的implementorList
                // implementorList: formData.value.implementorList.split(",").map(username => ({
                //     planImplementor: username
                // }))
            };

            await PUSH_IRON_PRODUCTION_PLAN(submitData).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("推送成功");
                    changeModalShow(false);
                    emit("refresh");
                }
            });
            //     }
            // });
        };

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card title="推送计划" class="w-600px" closable onClose={() => changeModalShow(false)}>
                    {/* 注释掉原来的表单，改为确认信息 */}
                    {/* <n-form
                        ref={formRef}
                        model={formData.value}
                        rules={formRules}
                        label-placement="left"
                        label-width="auto"
                    >
                        <n-form-item label="计划执行人" path="implementorList" required>
                            <UserSelector
                                v-model:value={formData.value.implementorList}
                                class="w-100%"
                                key-name="username"
                                placeholder="请选择计划执行人"
                                multiple={true}
                            />
                        </n-form-item>

                        <n-form-item class="mt-4">
                            <n-space>
                                <n-button type="primary" onClick={onSubmit}>
                                    确定
                                </n-button>
                                <n-button onClick={() => changeModalShow(false)}>取消</n-button>
                            </n-space>
                        </n-form-item>
                    </n-form> */}

                    <div class="mb-4">
                        <n-text>确定要推送计划：</n-text>
                        <n-text strong type="primary">
                            {props.configData.planName}
                        </n-text>
                        <n-text> 吗？</n-text>
                    </div>

                    <n-space>
                        <n-button type="primary" onClick={onSubmit}>
                            确定推送
                        </n-button>
                        <n-button onClick={() => changeModalShow(false)}>取消</n-button>
                    </n-space>
                </n-card>
            </n-modal>
        );
    }
});
