import { defineComponent, ref } from "vue";
import PlasticMesProductionPlanMonthQuantity from "./PlasticMesProductionPlanMonthQuantity";
import PlasticMesProductionPlanMonthRepairCost from "./PlasticMesProductionPlanMonthRepairCost";
import PlasticMesProductionPlanMonthWages from "./PlasticMesProductionPlanMonthWages";
import PlasticMesProductionPlanMonthEnergy from "./PlasticMesProductionPlanMonthEnergy";
import PlasticMesProductionPlanMonthMaterial from "./PlasticMesProductionPlanMonthMaterial";

export default defineComponent({
    name: "PlasticMesProductionPlanMonth",
    setup() {
        const tabActive = ref(1);

        const tabList = [
            { label: "产量计划", value: 1 },
            { label: "修理费计划", value: 2 },
            { label: "工资计划", value: 3 },
            { label: "原辅材料计划", value: 4 },
            { label: "能耗计划", value: 5 }
        ];

        return () => (
            <div class="plastic-mes-production-plan-month">
                <n-card hoverable>
                    <n-tabs v-model:value={tabActive.value} animated type="bar">
                        {tabList.map((item) => (
                            <n-tab-pane name={item.value} tab={item.label}>
                                {tabActive.value === 1 && <PlasticMesProductionPlanMonthQuantity />}
                                {tabActive.value === 2 && <PlasticMesProductionPlanMonthRepairCost />}
                                {tabActive.value === 3 && <PlasticMesProductionPlanMonthWages />}
                                {tabActive.value === 4 && <PlasticMesProductionPlanMonthMaterial />}
                                {tabActive.value === 5 && <PlasticMesProductionPlanMonthEnergy />}
                            </n-tab-pane>
                        ))}
                    </n-tabs>
                </n-card>
            </div>
        );
    }
});
