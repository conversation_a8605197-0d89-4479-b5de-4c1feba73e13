<template>
    <div>
        <n-drawer
            v-model:show="show"
            :close-on-esc="false"
            :height="600"
            :mask-closable="false"
            placement="bottom"
            @update:show="changeModalShow(false)"
        >
            <n-drawer-content :title="props.configData.id ? '修改配方' : '新增配方'" closable>
                <n-form
                    ref="formRef"
                    :model="formData"
                    :rules="formRules"
                    class="w-600px mx-a"
                    label-placement="left"
                    label-width="auto"
                >
                    <n-grid :cols="12" x-gap="16">
                        <n-form-item-gi :span="12" label="所属公司" path="companyId">
                            <n-cascader
                                :disabled="!!configData.id"
                                v-model:value="formData.companyId"
                                :options="companyIdOptions"
                                class="w-100%"
                                clearable
                                filterable
                                label-field="companyName"
                                placeholder="请选择所属公司"
                                value-field="id"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="配方分类" path="categoryId">
                            <n-tree-select
                                v-model:value="formData.categoryId"
                                :options="categoryIdOptions"
                                children-field="childrenList"
                                class="w-100%"
                                clearable
                                default-expand-all
                                key-field="id"
                                label-field="categoryName"
                                placeholder="请选择配方分类"
                                @update:value="changeCategoryId"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="8" label="配方名称" path="formulaName">
                            <n-input
                                v-model:value="formData.formulaName"
                                class="w-100%"
                                clearable
                                placeholder="请输入配方名称"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="4" label="是否绑定产品" path="isBind">
                            <n-switch
                                v-model:value="formData.isBind"
                                :checked-value="1"
                                :unchecked-value="0"
                                @update:value="changeIsBind"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="绑定产品" path="bindContentId" v-if="formData.isBind === 1">
                            <SpecSelector class="w-100%" v-model:array-value="formData.bindContentId" multiple />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="备注" path="remark">
                            <n-input
                                v-model:value="formData.remark"
                                class="w-100%"
                                clearable
                                placeholder="请输入备注"
                            />
                        </n-form-item-gi>
                    </n-grid>
                </n-form>
                <n-tabs pane-class="pt-20px!" type="line">
                    <n-tab-pane display-directive="show" name="原材料列表">
                        <TableListRaw
                            v-model:value="rawTableList"
                            :companyId="formData.companyId"
                            @confirm="onSubmit"
                        />
                    </n-tab-pane>
                    <n-tab-pane display-directive="show" name="半成品列表">
                        <TableListSemi
                            v-model:value="semiTableList"
                            :companyId="formData.companyId"
                            @confirm="onSubmit"
                        />
                    </n-tab-pane>
                </n-tabs>
            </n-drawer-content>
        </n-drawer>
    </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref, watchEffect } from "vue";
import type { FormInst } from "naive-ui";
import { NInput } from "naive-ui";
import { cloneDeep } from "lodash-es";
import {
    ADD_FORMULA,
    GET_CONFIG_COMPANY_LIST,
    GET_FORMULA_CATEGORY_TREE_LIST,
    GET_FORMULA_DETAIL_BY_ID,
    GET_MANUFACTURE_PAGE_LIST,
    GET_SEMI_MANUFACTURE_PAGE_LIST,
    UPDATE_FORMULA
} from "@/api/application/reporting";
import { TableListRaw, TableListSemi } from "../components";
import { SpecSelector } from "@/views/Container/Application/Reporting/components";

let props = withDefaults(defineProps<{ show: boolean; configData: UnKnownObject }>(), { show: () => false });

let emits = defineEmits(["update:show", "refresh"]);

let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

let changeModalShow = (show: boolean) => {
    emits("update:show", show);
    if (!show) clearFrom();
};

onMounted(async () => {
    await getCategoryIdOptions();
    await getCompanyIdOptions();
});

// 获取公司选项-填报专属修改2023年8月9日
let companyIdOptions = ref<any[]>([]);

let getCompanyIdOptions = async () => {
    await GET_CONFIG_COMPANY_LIST({ needFill: 1 }).then((res) => {
        companyIdOptions.value = res.data.data || [];
    });
};

// 获取分类
let categoryIdOptions = ref<any[]>([]);

let getCategoryIdOptions = async () => {
    await GET_FORMULA_CATEGORY_TREE_LIST({}).then((res) => {
        if (res.data.code === 0) categoryIdOptions.value = res.data.data;
    });
};

let changeCategoryId = () => {
    formData.value.bindContentId = null;
};

// 是否绑定操作
let changeIsBind = () => {
    formData.value.bindContentId = null;
};

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {
    companyId: { required: true, message: "请选择所属公司", trigger: ["blur", "change"] },
    categoryId: { required: true, message: "请选择配方分类", trigger: ["blur", "change"] },
    formulaName: { required: false, message: "请输入配方名称", trigger: ["input", "blur"] },
    isBind: { required: true, type: "number", message: "请选择是否绑定产品", trigger: ["blur", "change"] },
    bindContentId: { required: true, type: "array", message: "请选择绑定产品", trigger: ["blur", "change"] }
};

// 表单数据
interface FormDataProps {
    [key: string]: any;
}

let initFormData: FormDataProps = {
    companyId: null,
    categoryId: null,
    formulaName: "",
    remark: "",
    isBind: 0,
    bindContentId: null
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
    rawTableList.value = [];
    semiTableList.value = [];
};

let getDetail = () => {
    GET_FORMULA_DETAIL_BY_ID({ id: props.configData.id }).then((res) => {
        if (res.data.code === 0) {
            let resData: any = res.data.data;
            formData.value = {
                companyId: resData.companyId,
                categoryId: resData.categoryId,
                formulaName: resData.formulaName,
                remark: resData.remark,
                isBind: resData.isBind,
                bindContentId: resData.bindContentId
            };
            rawTableList.value = resData.formulaItemList;
            semiTableList.value = resData.formulaSemiItemList;
        }
    });
};

watchEffect(() => {
    if (props.show && props.configData.categoryId) {
        formData.value.categoryId = props.configData.categoryId;
    }
});

watchEffect(() => {
    if (props.show && props.configData.id) getDetail();
});

// 获取产成品/办成品列表
let productList = ref<any[]>([]);

let getProductList = () => {
    let infinitePage = { current: 1, size: 9999, companyId: formData.value.companyId };
    let categoryItem = categoryIdOptions.value.find((item: any) => item.id === formData.value.categoryId);
    if (categoryItem.productGenre === 2) {
        GET_MANUFACTURE_PAGE_LIST(infinitePage).then((res) => {
            productList.value = res.data.data.records.map((item: any) => {
                return {
                    ...item,
                    label: item.productModel,
                    value: item.id
                };
            });
        });
    } else if (categoryItem.productGenre === 3) {
        GET_SEMI_MANUFACTURE_PAGE_LIST(infinitePage).then((res) => {
            productList.value = res.data.data.records.map((item: any) => {
                return {
                    ...item,
                    label: `${item.productModel}-${item.cementSuffix ?? ""}`,
                    value: item.id
                };
            });
        });
    }
};

watchEffect(() => {
    if (formData.value.categoryId && formData.value.companyId) {
        getProductList();
    } else {
        formData.value.bindContentId = null;
        productList.value = [];
    }
});

let rawTableList = ref<any[]>([]);
let semiTableList = ref<any[]>([]);

// 提交
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;

    // 2023年8月6日16:53:44
    // 配方名称取消保存前校验必填（分未绑定产品、绑定一个产品、绑定多个产品）
    if (!formData.value.formulaName) {
        if (formData.value.isBind === 1) {
            if (formData.value.bindContentId.length === 1) {
                let categoryItem = categoryIdOptions.value.find((item: any) => item.id === formData.value.categoryId);
                let productItem = productList.value.find((item: any) => item.id === formData.value.bindContentId[0]);
                formData.value.formulaName = productItem.productModel;
            } else {
                window.$message.error("请输入配方名称");
                return false;
            }
        } else {
            window.$message.error("请输入配方名称");
            return false;
        }
    }

    let params = {
        ...formData.value,
        formulaItemList: rawTableList.value,
        formulaSemiItemList: semiTableList.value
    };

    if (props.configData.id) {
        UPDATE_FORMULA({ id: props.configData.id, ...params }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("修改成功");
                changeModalShow(false);
                emits("refresh");
            } else window.$message.error(res.data.message);
        });
    } else {
        ADD_FORMULA({ ...params }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("新增成功");
                changeModalShow(false);
                emits("refresh");
            } else window.$message.error(res.data.message);
        });
    }
};
</script>
