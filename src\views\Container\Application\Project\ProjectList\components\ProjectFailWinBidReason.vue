<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-600px" closable title="未中标原因" @close="closeModal">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-form-item label="未中标原因" path="reason">
                        <n-input
                            v-model:value="formData.reason"
                            :autosize="{ minRows: 3 }"
                            class="w-100%"
                            clearable
                            placeholder="请输入未中标原因"
                            type="textarea"
                        />
                    </n-form-item>
                    <!--<n-form-item label="责任人" path="dutyUsers">-->
                    <!--    <n-select-->
                    <!--        v-model:value="formData.dutyUsers"-->
                    <!--        filterable-->
                    <!--        :options="dutyUsersOptions"-->
                    <!--        label-field="trueName"-->
                    <!--        multiple-->
                    <!--        placeholder="请选择责任人"-->
                    <!--        value-field="username"-->
                    <!--    />-->
                    <!--</n-form-item>-->
                    <n-form-item>
                        <n-space>
                            <n-button type="primary" @click="onSubmit">提交</n-button>
                            <n-button @click="closeModal">取消</n-button>
                        </n-space>
                    </n-form-item>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { GET_PROJECT_DUTY_ASSIGNEE_LIST, SUBMIT_FAIL_WIN_BID_REASON_FORM } from "@/api/application/project";

let props = defineProps({
    show: { type: Boolean, default: false },
    configData: { type: Object as PropType<any> }
});

let emits = defineEmits(["update:show", "refresh"]);

// 表单实例
let formRef = ref<FormInst | null>(null);

// 获取选项
let dutyUsersOptions = ref<any[]>([]);

let getOptions = () => {
    GET_PROJECT_DUTY_ASSIGNEE_LIST({ projectId: props.configData.projectId }).then((res: any) => {
        dutyUsersOptions.value = res.data.data;
    });
};

// 表单校验
let formRules = {
    reason: { required: true, message: "请输入未中标原因", trigger: ["input", "blur"] },
    dutyUsers: { required: true, message: "请选择责任人", type: "array", trigger: ["blur", "change"] }
};

// 表单数据
interface FormDataProps<T = string | null> {
    reason: T;
    dutyUsers: T;
}

let initFormData: FormDataProps = {
    reason: null,
    dutyUsers: null
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 监听
watch(
    () => ({ configData: props.configData, show: props.show }),
    (newVal) => {
        if (newVal.show) getOptions();
    },
    { deep: true }
);

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    emits("update:show", false);
};

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    SUBMIT_FAIL_WIN_BID_REASON_FORM({
        projectId: props.configData.projectId,
        nodeKey: props.configData.nextNodeKey,
        reason: formData.value.reason,
        dutyUsers: dutyUsersOptions.value.map((item) => item.username).join(",") // 在座的各位都有责任
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("提交成功");
            closeModal();
            emits("refresh");
        }
    });
};
</script>
