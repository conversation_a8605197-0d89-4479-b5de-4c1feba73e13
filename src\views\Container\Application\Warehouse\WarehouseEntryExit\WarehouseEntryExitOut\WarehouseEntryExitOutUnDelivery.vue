<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <!--审批单-->
        <ProcessDetail v-model:show="processDetailModal.show" :config-data="processDetailModal.configData" />
        <!--确认出库-->
        <ConfirmExit v-model:show="confirmModal.show" :config-data="confirmModal.configData" />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from "vue";
import { DataTableColumns, NButton, NText } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import { GET_WAREHOUSE_OUT_APPLY_LIST, REFUSE_WAREHOUSE_OUT_APPLY } from "@/api/application/warehouse";
import { ProcessDetail } from "@/views/Container/Application/Process/components";
import { GET_OA_INSTANCE_FORM } from "@/api/application/oa";
import { TableActions } from "@/components/TableActions";
import { ConfirmExit } from "@/views/Container/Application/Warehouse/components";

interface RowProps {
    [key: string]: any;
}

onMounted(async () => {
    await getSearchOptions();
    getTableData();
});

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([]);

let searchOptions = ref<TableSearchbarOptions>({});

let searchForm = ref<TableSearchbarData>({});

let getSearchOptions = async () => {};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    /*
     * NEED
     * 发货申请单
     */
    {
        title: "出库类型",
        key: "applyCategory",
        align: "center",
        render: () => {
            return h(NText, { type: "info" }, () => "产成品发货");
        }
    },
    {
        title: "申请部门",
        key: "applyDeptName",
        align: "center",
        render: (row) => {
            return row.applyDeptName || "/";
        }
    },
    {
        title: "申请人",
        key: "applyByName",
        align: "center",
        render: (row) => {
            return row.applyByName || "/";
        }
    },
    /*
     * NEED
     * 物料明细
     */
    {
        title: "相关单据",
        key: "processInstanceId",
        align: "center",
        render(row) {
            return h(
                NButton,
                {
                    type: "primary",
                    text: true,
                    onClick: () => openProcessDetailModal(row.processInstanceId)
                },
                () => "点击查看"
            );
        }
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: 200,
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "确认出库",
                        tertiary: true,
                        type: "primary",
                        onClick: () => openConfirmModal(row)
                    },
                    {
                        label: "拒绝出库",
                        tertiary: true,
                        type: "error",
                        onClick: () => onRefuse(row)
                    }
                ]
            });
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_WAREHOUSE_OUT_APPLY_LIST({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        applyState: 1,
        applyCategory: 1,
        ...searchForm.value
    }).then((res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data.records;
            tablePagination.itemCount = res.data.data.total;
            tableLoading.value = false;
        }
    });
};

// 搜索
let onSearch = () => {
    tablePagination.page = 1;
    tablePagination.pageSize = 10;
    getTableData();
};

// 查看审批单
let processDetailModal = ref<{ show: boolean; configData: Record<string, any> }>({
    show: false,
    configData: {}
});

let openProcessDetailModal = (id: string | number) => {
    GET_OA_INSTANCE_FORM({ processInstId: id }).then((res) => {
        if (res.data.code === 0) {
            processDetailModal.value.show = true;
            processDetailModal.value.configData = res.data.data;
        } else {
            window.$message.error("该审批单不存在");
        }
    });
};

// 确认出库
let confirmModal = ref<{ show: boolean; configData: Record<string, any> }>({ show: false, configData: {} });

let openConfirmModal = (row: RowProps) => {
    confirmModal.value.show = true;
    confirmModal.value.configData = row;
};

// 拒绝出库
let onRefuse = (row: RowProps) => {
    window.$dialog.warning({
        title: "提示",
        content: "确定拒绝出库吗？",
        positiveText: "确认",
        negativeText: "取消",
        onPositiveClick: () => {
            REFUSE_WAREHOUSE_OUT_APPLY({ id: row.id }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("操作成功");
                    getTableData();
                }
            });
        }
    });
};
</script>
