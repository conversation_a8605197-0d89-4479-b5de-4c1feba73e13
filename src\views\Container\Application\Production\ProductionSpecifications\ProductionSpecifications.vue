<template>
    <div class="user-list">
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-space class="mb">
                <n-button secondary type="primary" @click="openEditModal()">新增</n-button>
                <!--<n-button secondary type="error" @click="onDelete()">批量删除</n-button>-->
            </n-space>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <ProductionSpecificationsEditModal
            v-model:id="editModal.id"
            v-model:show="editModal.show"
            @refresh="getTableData"
        />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from "vue";
import type { DataTableColumns } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { TableActions } from "@/components/TableActions";
import { useCommonTable, useDicts } from "@/hooks";
import { DELETE_PRODUCTION_TYPE, GET_PRODUCTION_TYPE_LIST } from "@/api/application/production";
import ProductionSpecificationsEditModal from "./ProductionSpecificationsEditModal.vue";

interface RowProps {
    id: Nullable<string | number>;
    type: Nullable<string>;
    specification: Nullable<string>;
    externalDiameter: Nullable<string | number>;
    wallThickness: Nullable<string | number>;
    meterWeight: Nullable<string | number>;
    minimumMeterWeight: Nullable<string | number>;
}

onMounted(async () => {
    await setDictLibs();
    getSearchOptions();
    getTableData();
});

// 字典操作
let { dictLibs, getDictLibs, dictValueToLabel } = useDicts();

let setDictLibs = async () => {
    let dictName = ["product_type"];
    await getDictLibs(dictName);
};

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    {
        prop: "specification",
        type: "input",
        label: "规格名称"
    }
]);

let searchOptions = ref<TableSearchbarOptions>({});

let searchForm = ref<TableSearchbarData>({
    specification: null
});

let onSearch = () => {
    getTableData();
};

let getSearchOptions = () => {};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "规格名称",
        key: "specification",
        align: "center"
    },
    {
        title: "外径",
        key: "externalDiameter",
        align: "center"
    },
    {
        title: "壁厚",
        key: "wallThickness",
        align: "center"
    },
    {
        title: "米重",
        key: "meterWeight",
        align: "center"
    },
    {
        title: "最小米重",
        key: "minimumMeterWeight",
        align: "center"
    },
    {
        title: "产品类型",
        key: "type",
        align: "center",
        render: (row: RowProps) => {
            return dictValueToLabel(row.type, "product_type") || "未知";
        }
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: 150,
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "编辑",
                        tertiary: true,
                        onClick: () => {
                            openEditModal(row.id);
                        }
                    },
                    {
                        label: "删除",
                        type: "error",
                        tertiary: true,
                        onClick: () => {
                            if (row.id) onDelete(row.id);
                        }
                    }
                ]
            });
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_PRODUCTION_TYPE_LIST({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        ...searchForm.value
    }).then((res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data.records;
            tablePagination.itemCount = res.data.data.total;
            tableLoading.value = false;
        }
    });
};

// 新增编辑
let editModal = ref<{ show: boolean; id: Nullable<string | number> }>({
    show: false,
    id: null
});

let openEditModal = (id?: Nullable<string | number>) => {
    editModal.value.show = true;
    editModal.value.id = id || null;
};

// 删除
let onDelete = (id?: string | number) => {
    if (!id && tableSelection.value.length < 1) {
        window.$message.error("请选择要删除的数据");
        return false;
    }
    window.$dialog.warning({
        title: "警告",
        content: `确定删除${id ? "该" : "选中"}规格型号吗？`,
        positiveText: "删除",
        negativeText: "取消",
        onPositiveClick: () => {
            let ids: (string | number)[];
            DELETE_PRODUCTION_TYPE({ id }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("删除成功");
                    onSearch();
                }
            });
        }
    });
};
</script>
