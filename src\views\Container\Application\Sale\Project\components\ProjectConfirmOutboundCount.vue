<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-600px" closable title="提交发货数量确认申请" @close="closeModal">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-grid :cols="24" :x-gap="16">
                        <n-form-item-gi :span="24" label="申请数量" path="applyQuantity">
                            <n-input-number
                                v-model:value="formData.applyQuantity"
                                class="w-100%"
                                clearable
                                placeholder="请输入申请数量"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="24">
                            <n-space>
                                <n-button type="primary" @click="onSubmit">提交</n-button>
                                <n-button @click="closeModal">取消</n-button>
                            </n-space>
                        </n-form-item-gi>
                    </n-grid>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { POST_CHILD_OUT_QUANTITY_CONFIRM_APPLY } from "@/api/application/sale";

let props = defineProps({
    show: { type: Boolean, default: false },
    configData: { type: Object as PropType<any> }
});

let emits = defineEmits(["update:show", "refresh"]);

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {
    applyQuantity: [{ required: true, message: "请输入申请数量", trigger: ["input", "blur"], type: "number" }]
};

// 表单数据
interface FormDataProps {
    [key: string]: any;
}

let initFormData: FormDataProps = {
    applyQuantity: 0
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    emits("update:show", false);
};

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    if (props.configData.childProjectId) {
        POST_CHILD_OUT_QUANTITY_CONFIRM_APPLY({
            ...formData.value,
            childProjectId: props.configData.childProjectId
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("提交成功");
                closeModal();
                emits("refresh");
            }
        });
    }
};
</script>
