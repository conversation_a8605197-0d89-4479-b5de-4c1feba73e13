<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-tabs v-model:value="tabActive" animated class="mb" type="line">
                <n-tab-pane name="未完成任务">
                    <TaskUncompleted :search-form="searchForm" />
                </n-tab-pane>
                <n-tab-pane name="无需生产">
                    <TaskUnwanted :search-form="searchForm" />
                </n-tab-pane>
                <n-tab-pane name="生产完成">
                    <TaskCompleted :search-form="searchForm" />
                </n-tab-pane>
            </n-tabs>
        </n-card>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { TaskCompleted, TaskUncompleted, TaskUnwanted } from "./Task";

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([]);

let searchOptions = ref<TableSearchbarOptions>({});

let searchForm = ref<TableSearchbarData>({});

let onSearch = () => {};

// tab切换
let tabActive = ref("未完成任务");
</script>
