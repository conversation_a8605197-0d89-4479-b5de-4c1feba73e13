import { defineComponent, onMounted, reactive, ref, watch } from "vue";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable, useDicts } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import { NText } from "naive-ui";
import { TableActions } from "@/components/TableActions";
import { GET_IRON_TECHNIQUE_TREE_LIST } from "@/api/application/TowerScan";
import TowerScanTechniqueEdit from "./TowerScanTechniqueEdit";

export default defineComponent({
    name: "TowerScanTechnique",
    setup() {
        // 字典操作
        const { dictLibs, getDictLibs } = useDicts();

        const setDictLibs = async () => {
            const dictName = ["TechniqueProcessType"];
            await getDictLibs(dictName);
            dictLibs["TechniqueProcessType"] =
                dictLibs["TechniqueProcessType"]?.filter((item) => item.value !== "0" && item.value !== "1") || [];
        };

        // 工艺分类tab
        const workmanshipTabActive = ref<string>("");

        // 搜索项
        const searchConfig = ref<TableSearchbarConfig>([]);
        const searchOptions = ref<TableSearchbarOptions>({});
        const getSearchOptions = async () => {};
        const searchForm = ref<TableSearchbarData>({});
        const onSearch = () => {
            getTableData();
        };

        // 工艺类型选项
        const techniqueTypeOptions = [
            { label: "制孔工艺", value: 1 },
            { label: "计件工艺", value: 3 },
            { label: "按量计算工艺", value: 4 }
        ];

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            {
                title: "工艺名称",
                key: "techniqueName"
            },
            {
                title: "最小电压（KV）",
                key: "minPowerLevel",
                align: "center",
                render: (row) => row.minPowerLevel || "/"
            },
            {
                title: "最大电压（KV）",
                key: "maxPowerLevel",
                align: "center",
                render: (row) => row.maxPowerLevel || "/"
            },
            {
                title: "工艺类型",
                key: "techniqueType",
                align: "center",
                render: (row) => (
                    <NText type="info">
                        {techniqueTypeOptions.find((item) => item.value === row.techniqueType)?.label || "/"}
                    </NText>
                )
            },
            {
                title: "分类",
                key: "materialClassify",
                align: "center",
                render: (row) => {
                    if (String(row.materialClassify) === "1") {
                        return <NText type="info">角钢</NText>;
                    } else if (String(row.materialClassify) === "2") {
                        return <NText type="info">钢板</NText>;
                    } else if (String(row.materialClassify) === "3") {
                        return <NText type="info">圆钢</NText>;
                    } else if (String(row.materialClassify) === "4") {
                        return <NText type="info">圆管</NText>;
                    } else if (String(row.materialClassify) === "5") {
                        return <NText type="info">槽钢</NText>;
                    } else {
                        return <NText type="info">/</NText>;
                    }
                }
            },
            {
                title: "绑定工艺分类",
                key: "lineId",
                align: "center",
                render: (row) => {
                    if (row.lineId) {
                        return <NText type="success">{row.lineId}</NText>;
                    } else {
                        return <NText type="error">否</NText>;
                    }
                }
            },
            {
                title: "是否显示",
                key: "displayFlag",
                align: "center",
                render: (row) => {
                    if (row.displayFlag === 1) {
                        return <NText type="success">是</NText>;
                    } else if (row.displayFlag === 0) {
                        return <NText type="error">否</NText>;
                    } else {
                        return <NText type="info">/</NText>;
                    }
                }
            },
            {
                title: "电压等级计算",
                key: "lineFlag",
                align: "center",
                render: (row) => {
                    if (row.lineFlag === 1) {
                        return <NText type="success">是</NText>;
                    } else if (row.lineFlag === 0) {
                        return <NText type="error">否</NText>;
                    } else {
                        return <NText type="info">/</NText>;
                    }
                }
            },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 80,
                render: (row) => {
                    return (
                        <TableActions
                            type="button"
                            buttonActions={[
                                {
                                    label: "编辑",
                                    tertiary: true,
                                    type: "primary",
                                    onClick: () => openEditModal(row)
                                }
                            ]}
                        />
                    );
                }
            }
        ]);

        const getTableData = () => {
            tableLoading.value = true;
            GET_IRON_TECHNIQUE_TREE_LIST({
                processType: workmanshipTabActive.value,
                ...searchForm.value
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data || [];
                    tableLoading.value = false;
                } else {
                    tableLoading.value = false;
                }
            });
        };

        // 监听tab变化
        watch(
            () => workmanshipTabActive.value,
            (val) => {
                if (val) getTableData();
            },
            { immediate: true }
        );

        // 新增编辑弹窗
        const editModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const openEditModal = (row?: RowProps) => {
            editModal.value = {
                show: true,
                configData: row
                    ? { ...row, processType: workmanshipTabActive.value }
                    : { processType: workmanshipTabActive.value }
            };
        };

        onMounted(async () => {
            await setDictLibs();
            if (dictLibs?.["TechniqueProcessType"]?.length > 0) {
                workmanshipTabActive.value = dictLibs["TechniqueProcessType"][0].value as string;
            }
            await getSearchOptions();
        });

        return () => (
            <div class="tower-scan-technique">
                <n-card hoverable>
                    <n-tabs v-model:value={workmanshipTabActive.value} animated class="mb-1" type="bar">
                        {dictLibs["TechniqueProcessType"]?.map((item, index) => (
                            <n-tab-pane key={index} name={item.value} tab={item.label} />
                        ))}
                    </n-tabs>
                    <n-space class="mb">
                        <n-button secondary type="primary" onClick={() => openEditModal()}>
                            新增工艺
                        </n-button>
                    </n-space>
                    {/*<n-card class="mb">*/}
                    {/*    <TableSearchbar*/}
                    {/*        form={searchForm.value}*/}
                    {/*        config={searchConfig.value}*/}
                    {/*        options={searchOptions.value}*/}
                    {/*        onSearch={onSearch}*/}
                    {/*    />*/}
                    {/*</n-card>*/}
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        row-key={tableRowKey}
                        single-line={false}
                        bordered
                        children-key="childrenList"
                        remote
                        striped
                        onUpdate:checked-row-keys={changeTableSelection}
                    />
                </n-card>
                <TowerScanTechniqueEdit
                    v-model:show={editModal.value.show}
                    config-data={editModal.value.configData}
                    onRefresh={getTableData}
                />
            </div>
        );
    }
});
