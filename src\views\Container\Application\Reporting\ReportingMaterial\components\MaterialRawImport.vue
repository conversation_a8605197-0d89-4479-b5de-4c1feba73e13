<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-500px" closable title="原材料调价表导入" @close="changeModalShow(false)">
                <n-upload v-model:file-list="fileList" directory-dnd @before-upload="onBeforeUpload">
                    <n-upload-dragger>
                        <div class="mb-4">
                            <dynamic-icon icon="UploadOutlined" size="40" />
                        </div>
                        <n-p class="text-16px">点击或拖动文件到该区域上传</n-p>
                    </n-upload-dragger>
                </n-upload>
                <n-button class="mt-10px" text type="primary" @click="onDownloadTemplate">
                    点击此处下载批量导入模板
                </n-button>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref } from "vue";
import type { UploadFileInfo } from "naive-ui";
import { DynamicIcon } from "@/components/DynamicIcon";
import { IMPORT_MATERIAL_SPEC } from "@/api/application/reporting";

let props = withDefaults(defineProps<{ show: boolean; configData: UnKnownObject }>(), {
    show: () => false
});

let emits = defineEmits(["update:show", "refresh"]);

let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

let changeModalShow = (show: boolean) => emits("update:show", show);

let fileList = ref<UploadFileInfo[]>([]);

let onBeforeUpload = async (fileData: { file: UploadFileInfo; fileList: UploadFileInfo[] }) => {
    let formData = new FormData();
    formData.append("file", fileData.file.file as any);
    IMPORT_MATERIAL_SPEC(formData).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("导入成功");
            changeModalShow(false);
            emits("refresh");
        } else {
            window.$message.error("导入失败，请检查后重试！");
        }
    });
};

let onDownloadTemplate = () => {
    // window.location.href = import.meta.env.VITE_API_URL + "/admin/sys-file/local/towerMaterial.xlsx";
};
</script>
