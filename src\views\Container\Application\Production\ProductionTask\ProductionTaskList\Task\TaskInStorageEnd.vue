<template>
    <div>
        <n-data-table
            :columns="tableColumns"
            :data="tableData"
            :loading="tableLoading"
            :pagination="tablePagination"
            :row-key="tableRowKey"
            :single-line="false"
            bordered
            remote
            striped
            @update:checked-row-keys="changeTableSelection"
        />
        <!--查看详情-->
        <DetailModal v-model:show="detailModal.show" :config-data="detailModal.configData" @refresh="getTableData" />
    </div>
</template>

<script lang="ts" setup>
import { useCommonTable } from "@/hooks";
import { h, onMounted, reactive, ref } from "vue";
import type { DataTableColumns } from "naive-ui";
import { GET_PRODUCTION_TASK_FINISH_STORAGE } from "@/api/application/production";
import { NText } from "naive-ui";
import { DetailModal } from "@/views/Container/Application/Production/ProductionTask/ProductionTaskList/Modal";
import { TableActions } from "@/components/TableActions";

let props = withDefaults(
    defineProps<{
        searchForm?: UnKnownObject;
    }>(),
    {}
);

interface RowProps {
    poId?: string | number;
    pomNumber?: string | number;
    id?: string | number;
    specification?: string;
    prodLineName?: string;
    prodCount?: string | number;
    prodScheDate?: string;
    waterBy?: string;
    fullStorageState?: number;
}

onMounted(() => {
    getTableData();
});

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("poId");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "订单号",
        key: "pomNumber",
        align: "center",
        render: (row) => {
            return row.pomNumber || "/";
        }
    },
    {
        title: "生产任务单号",
        key: "id",
        align: "center"
    },
    {
        title: "生产规格型号",
        key: "specification",
        align: "center"
    },
    {
        title: "生产线路",
        key: "prodLineName",
        align: "center"
    },
    {
        title: "生产数量",
        key: "prodCount",
        align: "center"
    },
    {
        title: "提交人",
        key: "waterBy",
        align: "center",
        render: (row: RowProps) => row.waterBy ?? "未知"
    },
    {
        title: "状态",
        key: "fullStorageState",
        align: "center",
        render: (row) => {
            if (row.fullStorageState === 1) {
                return h(NText, { type: "warning" }, () => "二次确认中");
            } else if (row.fullStorageState === 2) {
                return h(NText, { type: "primary" }, () => "技术已知晓");
            } else if (row.fullStorageState === 3) {
                return h(NText, { type: "error" }, () => "技术已拒绝");
            } else if (row.fullStorageState === 4) {
                return h(NText, { type: "success" }, () => "检验核对通过");
            } else if (row.fullStorageState === 5) {
                return h(NText, { type: "error" }, () => "检验核对拒绝");
            } else if (row.fullStorageState === 6) {
                return h(NText, { type: "success" }, () => "完成完全入库");
            } else {
                return "未知状态";
            }
        }
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: 120,
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "查看详情",
                        tertiary: true,
                        type: "primary",
                        onClick: () => openDetailModal(row)
                    }
                ]
            });
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_PRODUCTION_TASK_FINISH_STORAGE({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        ...props.searchForm
    }).then(async (res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data.records;
            tablePagination.itemCount = res.data.data.total;
            tableLoading.value = false;
        }
    });
};

// 查看详情
let detailModal = ref<{ show: boolean; configData: Record<string, any> }>({ show: false, configData: {} });

let openDetailModal = (row: RowProps) => {
    detailModal.value = {
        show: true,
        configData: row
    };
    console.log(111, detailModal.value);
};
</script>
