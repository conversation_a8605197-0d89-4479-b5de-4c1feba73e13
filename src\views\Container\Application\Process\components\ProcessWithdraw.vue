<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-800px" closable title="撤回审批" @close="closeModal">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-grid cols="12" x-gap="16">
                        <n-form-item-gi :span="12" label="撤回原因" path="deleteReason">
                            <n-input
                                v-model:value="formData.deleteReason"
                                :autosize="{ minRows: 3 }"
                                class="w-100%"
                                clearable
                                placeholder="请输入撤回原因"
                                type="textarea"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12">
                            <n-space>
                                <n-button type="primary" @click="onSubmit">提交</n-button>
                                <n-button @click="closeModal">取消</n-button>
                            </n-space>
                        </n-form-item-gi>
                    </n-grid>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { useStoreUser } from "@/store";
import { WITHDRAW_OA_INSTANCE } from "@/api/application/oa";

let storeUser = useStoreUser();

let props = defineProps({
    show: { type: Boolean, default: false },
    configData: { type: Object as PropType<any> }
});

let emits = defineEmits(["update:show", "refresh"]);

watch(
    () => ({ configData: props.configData, show: props.show }),
    (newVal) => {
        if (newVal.show) getOptions();
    },
    { deep: true }
);

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {
    deleteReason: [{ required: true, message: "请输入撤回原因", trigger: ["input", "blur"] }]
};

// 表单数据
interface FormDataProps<T = string | null> {
    deleteReason: T;
}

let initFormData: FormDataProps = {
    deleteReason: null
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

let getOptions = async () => {};

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    emits("update:show", false);
};

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    WITHDRAW_OA_INSTANCE({
        ...formData.value,
        processInstId: props.configData.processInstanceId
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("撤回成功");
            closeModal();
            emits("refresh");
        }
    });
};
</script>
