<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-800px" closable title="确认开票" @close="closeModal">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-grid :cols="24" :x-gap="16">
                        <n-form-item-gi :span="12" label="开票时间" path="invoicingTime">
                            <n-date-picker
                                v-model:formatted-value="formData.invoicingTime"
                                class="w-100%"
                                clearable
                                placeholder="请选择开票时间"
                                type="datetime"
                                value-format="yyyy-MM-dd HH:mm:ss"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="开票金额" path="invoicingAmount">
                            <n-input-number
                                v-model:value="formData.invoicingAmount"
                                class="w-100%"
                                placeholder="请输入开票金额"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="24" label="附件" path="fileIds">
                            <FileValueUploader v-model:value="formData.fileIds" value-key="fileId" />
                        </n-form-item-gi>
                        <n-form-item-gi :span="24">
                            <n-space>
                                <n-button type="primary" @click="onSubmit">提交</n-button>
                                <n-button @click="closeModal">取消</n-button>
                            </n-space>
                        </n-form-item-gi>
                    </n-grid>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { FileValueUploader } from "@/components/Uploader";
import { CONFIRM_INVOICING_APPLY } from "@/api/application/power";

let props = defineProps({
    show: { type: Boolean, default: false },
    configData: { type: Object as PropType<any> }
});

let emits = defineEmits(["update:show", "refresh"]);

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {
    invoicingTime: [{ required: true, message: "请选择开票时间", trigger: ["blur", "change"] }],
    invoicingAmount: [{ required: true, message: "请输入开票金额", trigger: ["blur", "change"], type: "number" }]
};

// 表单数据
interface FormDataProps {
    [key: string]: any;
}

let initFormData: FormDataProps = {
    invoicingTime: null,
    invoicingAmount: null,
    fileIds: null
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    emits("update:show", false);
};

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    CONFIRM_INVOICING_APPLY({
        applyId: props.configData.id,
        ...formData.value
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("操作成功");
            closeModal();
            emits("refresh");
        }
    });
};
</script>
