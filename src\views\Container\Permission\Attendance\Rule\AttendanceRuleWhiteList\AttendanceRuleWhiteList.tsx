import { defineComponent, reactive, ref, watchEffect } from "vue";
import type { DataTableColumns } from "naive-ui";
import {
    DELETE_ATTENDANCE_WHITE_LIST,
    DELETE_SPOT_CHECK_ATTENDANCE_CONFIGURATION,
    GET_ATTENDANCE_WHITE_LIST
} from "@/api/permission";
import { useCommonTable } from "@/hooks";
import { TableActions } from "@/components/TableActions";
import AttendanceRuleWhiteListEdit from "@/views/Container/Permission/Attendance/Rule/AttendanceRuleWhiteList/AttendanceRuleWhiteListEdit";

export default defineComponent({
    name: "AttendanceRuleWhiteList",
    props: {
        corpId: { type: String as PropType<string | null>, default: null }
    },
    setup(props, { emit }) {
        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { title: "姓名", key: "trueName", align: "center" },
            { title: "手机号", key: "phone", align: "center" },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 100,
                render: (row) => {
                    return (
                        <TableActions
                            type="button"
                            buttonActions={[
                                { label: "删除", tertiary: true, type: "error", onClick: () => onDelete(row) }
                            ]}
                        />
                    );
                }
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_ATTENDANCE_WHITE_LIST({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                companyId: props.corpId
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        // 新增功能
        const editModal = ref<{ show: boolean; configData: RowProps }>({ show: false, configData: {} });

        const openEditModal = (row?: RowProps) => {
            editModal.value.show = true;
            editModal.value.configData = row ? { ...row, corpId: props.corpId } : { corpId: props.corpId };
        };

        // 删除功能
        const onDelete = (row: RowProps) => {
            window.$dialog.warning({
                title: "警告",
                content: "确认删除该条数据？该操作不可逆",
                positiveText: "确认删除",
                negativeText: "我再想想",
                onPositiveClick: () => {
                    DELETE_ATTENDANCE_WHITE_LIST({ userId: row.userId }).then((res) => {
                        if (res.data.code === 0) {
                            window.$message.success("删除成功");
                            getTableData();
                        }
                    });
                }
            });
        };

        watchEffect(() => {
            if (props.corpId) {
                getTableData();
            }
        });

        return () => (
            <div>
                <n-space class="mb">
                    <n-button type="primary" onClick={() => openEditModal()}>
                        新增白名单用户
                    </n-button>
                </n-space>
                <n-data-table
                    columns={tableColumns.value}
                    data={tableData.value}
                    loading={tableLoading.value}
                    pagination={tablePagination}
                    row-key={tableRowKey}
                    single-line={false}
                    bordered
                    remote
                    striped
                    onUpdate:checked-row-keys={changeTableSelection}
                />
                <AttendanceRuleWhiteListEdit
                    v-model:show={editModal.value.show}
                    v-model:configData={editModal.value.configData}
                    onRefresh={getTableData}
                />
            </div>
        );
    }
});
