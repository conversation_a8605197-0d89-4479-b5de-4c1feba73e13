<template>
    <div>
        <n-data-table
            :columns="tableColumns"
            :data="tableData"
            :loading="tableLoading"
            :pagination="tablePagination"
            :row-key="tableRowKey"
            :single-line="false"
            bordered
            remote
            striped
            @update:checked-row-keys="changeTableSelection"
        />
        <!--领料生产-->
        <MaterialProduction
            v-model:show="materialProductionModal.show"
            :config-data="materialProductionModal.configData"
        />
        <!--需求反馈详情-->
        <Requirements v-model:show="requirementsModal.show" :config-data="requirementsModal.configData" />
        <!--无需生产确认-->
        <UnwantedProduction
            v-model:show="unwantedModal.show"
            :config-data="unwantedModal.configData"
            @refresh="getTableData"
        />
        <!--订单需求详情-->
        <OrderDetail v-model:show="orderDetailModal.show" :config-data="orderDetailModal.configData" />
    </div>
</template>

<script lang="ts" setup>
import { useCommonTable } from "@/hooks";
import { h, onMounted, reactive, ref } from "vue";
import type { DataTableColumns } from "naive-ui";
import { NButton, NText } from "naive-ui";
import { GET_PRODUCTION_ARRANGE_UNCOMPLETED, PRODUCTION_ORDER_FINISH } from "@/api/application/production";
import { TableActions } from "@/components/TableActions";
import { OrderDetail, Requirements, UnwantedProduction, MaterialProduction } from "../Modal";
import { GET_STAFF_BY_USERNAMES } from "@/api/permission";

let props = withDefaults(
    defineProps<{
        searchForm?: UnKnownObject;
    }>(),
    {}
);

interface RowProps {
    poId?: string | number;
    pomNumber?: string | number;
    projectName?: string; // 项目名称
    deliveryDate?: string; // 交货日期
    salesPerson?: string; // 销售联系人
    salesPersonName?: string; // 销售联系人名称
    stockCheckState?: string | number; // 仓库反馈状态
    techCheckState?: string | number; // 技术配方状态
    formulaCheckState?: string | number; // 配方反馈状态
    finishedProdCount?: string | number; // 已生产数量
    underWayProdCount?: string | number; // 生产中数量
    modelFormulaList?: {
        specification: string;
        needCount: string | number;
        customerRemark: string;
        stockCount: string | number;
        ignoreCount: string | number;
    }[]; // 配方详情
}

onMounted(() => {
    getTableData();
});

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("poId");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "订单号",
        key: "pomNumber",
        align: "center",
        render: (row) => {
            return row.pomNumber || "/";
        }
    },
    {
        title: "项目名称",
        key: "projectName",
        align: "center"
    },
    {
        title: "需求详情",
        key: "poId",
        align: "center",
        render(row) {
            return h(
                NButton,
                {
                    type: "primary",
                    text: true,
                    onClick: () => openRequirementsModal(row)
                },
                () => "点击查看"
            );
        }
    },
    {
        title: "技术配方",
        key: "formulaCheckState",
        align: "center",
        render(row) {
            if (String(row.formulaCheckState) === "1") {
                return h(
                    NButton,
                    {
                        type: "primary",
                        text: true,
                        onClick: () => openRequirementsModal(row)
                    },
                    () => "点击查看"
                );
            } else {
                return h(NText, { type: "error" }, () => "未反馈");
            }
        }
    },
    {
        title: "仓库反馈",
        key: "stockCheckState",
        align: "center",
        render(row) {
            if (String(row.stockCheckState) === "1") {
                return h(
                    NButton,
                    {
                        type: "primary",
                        text: true,
                        onClick: () => openRequirementsModal(row)
                    },
                    () => "点击查看"
                );
            } else {
                return h(NText, { type: "error" }, () => "未反馈");
            }
        }
    },
    {
        title: "交货日期",
        key: "deliveryDate",
        align: "center"
    },
    {
        title: "销售联系人",
        key: "salesPersonName",
        align: "center",
        render(row) {
            return row.salesPersonName || "/";
        }
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: 380,
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "领料生产",
                        tertiary: true,
                        type: "primary",
                        onClick: () => openMaterialProductionModal(row)
                    },
                    {
                        label: "无需生产",
                        tertiary: true,
                        disabled: () => (row.modelFormulaList ?? []).some((item) => item.stockCount ?? 0 > 0),
                        type: "success",
                        onClick: () => openUnwantedModal(row)
                    },
                    {
                        label: "完成订单",
                        tertiary: true,
                        disabled: () => {
                            let haveIgnoreCount = (row.modelFormulaList ?? []).some(
                                (item) => item.ignoreCount && item.ignoreCount > 0
                            );
                            let haveFinishedProdCount = (row.finishedProdCount ?? 0) > 0;
                            let haveUnderWayProdCount = (row.underWayProdCount ?? 0) > 0;
                            return !haveIgnoreCount && !haveFinishedProdCount && !haveUnderWayProdCount;
                        },
                        type: "error",
                        onClick: () => onCompleteOrder(row)
                    },
                    {
                        label: "查看详情",
                        tertiary: true,
                        type: "warning",
                        onClick: () => openOrderDetailModal(row)
                    }
                ]
            });
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_PRODUCTION_ARRANGE_UNCOMPLETED({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        ...props.searchForm
    }).then(async (res) => {
        if (res.data.code === 0) {
            tableData.value = await Promise.all(
                res.data.data.records.map(async (item: RowProps) => {
                    // item.salesPersonName = "未知";
                    // if (item.salesPerson) {
                    //     await GET_STAFF_BY_USERNAMES({ usernames: item.salesPerson }).then((res) => {
                    //         if (res.data.code === 0 && res.data.data?.[0]?.trueName) {
                    //             item.salesPersonName = res.data.data[0].trueName;
                    //         }
                    //     });
                    // }
                    return item;
                })
            );
            tablePagination.itemCount = res.data.data.total;
            tableLoading.value = false;
        }
    });
};

// 需求反馈详情
let requirementsModal = ref<{ show: boolean; configData: Record<string, any> }>({
    show: false,
    configData: {}
});

let openRequirementsModal = (row: RowProps) => {
    requirementsModal.value = {
        show: true,
        configData: row
    };
    console.log(111, row);
};

// 领料生产
let materialProductionModal = ref<{ show: boolean; configData: Record<string, any> }>({
    show: false,
    configData: {}
});

let openMaterialProductionModal = (row: RowProps) => {
    materialProductionModal.value = {
        show: true,
        configData: row
    };
};

// 无需生产
let unwantedModal = ref<{ show: boolean; configData: Record<string, any> }>({
    show: false,
    configData: {}
});

let openUnwantedModal = (row: RowProps) => {
    unwantedModal.value = {
        show: true,
        configData: row
    };
};

// 订单详情
let orderDetailModal = ref<{ show: boolean; configData: Record<string, any> }>({
    show: false,
    configData: {}
});

let openOrderDetailModal = (row: RowProps) => {
    orderDetailModal.value = {
        show: true,
        configData: row
    };
};

// 完成订单
let onCompleteOrder = (row: RowProps) => {
    window.$dialog.warning({
        title: "提示",
        content: "确定完成该订单吗？",
        positiveText: "完成",
        negativeText: "取消",
        onPositiveClick: () => {
            PRODUCTION_ORDER_FINISH({ poId: row.poId }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("操作成功");
                    getTableData();
                } else window.$message.error(res.data.msg);
            });
        }
    });
};
</script>
