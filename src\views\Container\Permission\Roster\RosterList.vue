<template>
    <div class="flex">
        <n-card class="flex-fixed-300" hoverable>
            <div class="flex-y-center mb">
                <n-input v-model:value="deptTreePattern" placeholder="搜索组织架构关键词" />
                <n-button class="ml-2" type="default" @click="cancelTreeSelect">查看全部</n-button>
            </div>
            <n-tree
                v-model:selected-keys="deptSelectKeys"
                :cancelable="false"
                :accordion="false"
                :animated="true"
                :default-expanded-keys="expandedKeys"
                :data="deptTree"
                :pattern="deptTreePattern"
                :show-irrelevant-nodes="false"
                block-line
                key-field="id"
                label-field="name"
                selectable
                @update:selected-keys="selectDeptNode"
            />
        </n-card>
        <div class="flex-1 ml-4">
            <n-card hoverable>
                <table-searchbar
                    v-model:form="searchForm"
                    :config="searchConfig"
                    :options="searchOptions"
                    @search="onSearch"
                />
            </n-card>
            <n-card class="mt" hoverable>
                <n-space class="mb">
                    <n-button secondary type="primary" @click="openAddModal()">新增</n-button>
                    <n-button secondary type="success" @click="openImportModal()">批量导入</n-button>
                    <n-button secondary type="warning" @click="openExportModal()">批量导出</n-button>
                    <n-button secondary type="error" @click="onDelete()">批量删除</n-button>
                    <n-button secondary type="info" @click="openChangeDeptModal()">批量修改部门</n-button>
                    <n-button secondary type="default" @click="openResetPassword()">批量重置密码</n-button>
                    <n-button secondary type="primary" @click="openUpdateModal()">同步钉钉用户</n-button>
                </n-space>
                <n-data-table
                    :columns="tableColumns"
                    :data="tableData"
                    :loading="tableLoading"
                    :pagination="tablePagination"
                    :row-key="tableRowKey"
                    :single-line="false"
                    bordered
                    remote
                    striped
                    @update:checked-row-keys="changeTableSelection"
                />
            </n-card>
        </div>
        <!--新增-->
        <RosterAddModal v-model:show="addModal.show" @refresh="onSearch" />
        <!--详情-->
        <RosterDetailModal v-model:id="detailModal.id" v-model:show="detailModal.show" @refresh="getTableData" />
        <!--批量导入-->
        <n-modal v-model:show="importModal.show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-500px" closable title="批量导入" @close="closeImportModal">
                <n-upload v-model:file-list="fileList" directory-dnd @before-upload="onBeforeUpload">
                    <n-upload-dragger>
                        <div class="mb-4">
                            <dynamic-icon icon="UploadOutlined" size="40" />
                        </div>
                        <n-p class="text-16px">点击或拖动文件到该区域上传</n-p>
                    </n-upload-dragger>
                </n-upload>
                <n-button class="mt-10px" text type="primary" @click="onDownloadTemplate">
                    点击此处下载批量导入模板
                </n-button>
            </n-card>
        </n-modal>
        <!--批量修改部门-->
        <ChangeUsersDept v-model:show="changeDeptModal.show" :user-ids="tableSelection" @refresh="getTableData" />
        <!--钉钉同步-->
        <AttendanceSyncUpdate
            v-model:show="updateModal.show"
            :config-data="updateModal.configData"
            @refresh="getTableData"
        />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from "vue";
import type { DataTableColumns, UploadFileInfo } from "naive-ui";
import { NSpace } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { TableActions } from "@/components/TableActions";
import { DynamicIcon } from "@/components/DynamicIcon";
import {
    DELETE_ARCHIVES,
    DELETE_ARCHIVES_BATCH,
    EXPORT_ARCHIVES,
    GET_ARCHIVES_LIST,
    GET_DEPT_TREE,
    IMPORT_ARCHIVES,
    RESET_PASSWORD
} from "@/api/permission";
import { useCommonTable } from "@/hooks";
import RosterAddModal from "./RosterAddModal.vue";
import RosterDetailModal from "./RosterDetailModal.vue";
import { ChangeUsersDept } from "@/components/Business";
import { useStoreUser } from "@/store";
import dayjs from "dayjs";
import AttendanceSyncUpdate from "@/views/Container/Permission/Attendance/Sync/AttendanceSyncUpdate";

let storeUser = useStoreUser();

type RowProps = {
    archivesId: string | number;
    userId: string | number;
    nickname: string | null;
    postName: string | null;
    rankPostName: string | null;
    typeOfJobPostName: string | null;
    timeOfEntry: string | null;
    bindStatus: string | number | null;
    roleList: any[];
};

onMounted(() => {
    getDeptTree();
    getSearchOptions();
    getTableData();
});

// 该功能后期可以封装为组件
// 获取组织树
let deptTree = ref<any[]>([]);

let deptTreePattern = ref("");

let getDeptTree = () => {
    GET_DEPT_TREE({ deptName: "" }).then((res) => {
        deptTree.value = res.data.data || [];
        expandedKeys.value = deptTree.value.map((item) => item.id);
    });
};

// 新增状态expandedKeys用于控制树的展开项
let expandedKeys = ref<(string | number)[]>([]);

// 组织树选中
let deptSelectKeys = ref<(string | number)[]>([]);

let selectDeptNode = (keys: (string | number)[]) => {
    deptSelectKeys.value = keys;
    onSearch();
};

let cancelTreeSelect = () => {
    deptSelectKeys.value = [];
    onSearch();
};

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { prop: "trueName", type: "input", label: "真实姓名" },
    { prop: "username", type: "input", label: "工号" },
    { prop: "phone", type: "input", label: "手机号" }
]);

let searchOptions = ref<TableSearchbarOptions>({});

let searchForm = ref<TableSearchbarData>({
    trueName: null,
    username: null
});

let getSearchOptions = () => {};

let onSearch = () => {
    getTableData();
};

// 数据列表
// 此处用userId作用为批量修改部门时，可以根据userId去获取对应的用户信息
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("userId");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "姓名",
        key: "trueName",
        align: "center"
    },
    {
        title: "昵称",
        key: "nickname",
        align: "center",
        render(row) {
            return row.nickname || "暂无";
        }
    },
    {
        title: "所在公司",
        key: "company",
        align: "center"
    },
    {
        title: "所在部门",
        key: "deptName",
        align: "center"
    },
    {
        title: "钉钉同步状态",
        key: "bindStatus",
        align: "center",
        render: (row) => {
            const bindStatus = String(row.bindStatus);
            if (bindStatus === "0") {
                return "未绑定";
            } else if (bindStatus === "1") {
                return "已绑定";
            } else {
                return "暂无";
            }
        }
    },
    {
        title: "职级",
        key: "postName",
        align: "center",
        render(row) {
            return row.postName || "暂无";
        }
    },
    {
        title: "薪资级别",
        key: "rankPostName",
        align: "center",
        render(row) {
            return row.rankPostName || "暂无";
        }
    },
    {
        title: "岗位类型",
        key: "typeOfJobPostName",
        align: "center",
        render(row) {
            return row.typeOfJobPostName || "暂无";
        }
    },
    {
        title: "手机号",
        key: "phone",
        align: "center"
    },
    {
        title: "入职时间",
        key: "timeOfEntry",
        align: "center",
        render(row) {
            return row.timeOfEntry ? dayjs(row.timeOfEntry).format("YYYY-MM-DD") : "暂无";
        }
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: 180,
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "查看详情",
                        tertiary: true,
                        onClick: () => openDetailModal(row.archivesId)
                    },
                    {
                        label: "删除",
                        tertiary: true,
                        type: "error",
                        onClick: () => onDelete(row.archivesId)
                    }
                ]
            });
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_ARCHIVES_LIST({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        ...searchForm.value,
        deptId: deptSelectKeys.value.length > 0 ? deptSelectKeys.value[0] : null
    }).then((res) => {
        if (res.data.code === 0) {
            let userData = storeUser.getUserData;
            let postType: any = userData.postList?.find((item: any) => item.postGroup === "positiontype");
            let lowFilter = ["非管理岗", "基层", "中层"];
            let highFilter = ["总裁办/董事会", "高层"];
            tableData.value = res.data.data.records.map((item: any) => {
                if (postType && lowFilter.includes(postType.postName)) {
                    if (highFilter.includes(item.typeOfJobPostName)) {
                        item.phone = "***********";
                    }
                }
                return item;
            });
            // 隐藏角色为超级管理员的人员，2023年4月16日17:11:38新增
            tableData.value = tableData.value.filter((item) => {
                let isSuperAdmin = (item.roleList as any).some((citem: any) => citem.roleId === "1");
                return !isSuperAdmin;
            });
            tablePagination.itemCount = res.data.data.total;
            tableLoading.value = false;
        }
    });
};

// 删除
let onDelete = (id?: string | number) => {
    if (!id && tableSelection.value.length < 1) {
        window.$message.error("请选择要删除的数据");
        return false;
    }
    window.$dialog.warning({
        title: "警告",
        content: `确定删除${id ? "该" : "选中"}用户吗？`,
        positiveText: "删除",
        negativeText: "取消",
        onPositiveClick: () => {
            if (id) {
                DELETE_ARCHIVES({ id: id }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("删除成功");
                        onSearch();
                    }
                });
            } else {
                let ids = tableData.value
                    .filter((item) => tableSelection.value.includes(item.userId as string | number))
                    .map((item) => item.archivesId)
                    .join(",");
                DELETE_ARCHIVES_BATCH({ archivesIds: ids }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("删除成功");
                        onSearch();
                    }
                });
            }
        }
    });
};

// 新增
let addModal = ref<{ show: boolean }>({ show: false });

let openAddModal = () => (addModal.value.show = true);

// 查看详情
let detailModal = ref<{ show: boolean; id: string | number | null }>({
    show: false,
    id: null
});

let openDetailModal = (id: string | number | null) => {
    detailModal.value.show = true;
    detailModal.value.id = id;
};

// 批量导入
let importModal = ref<{ show: boolean }>({
    show: false
});

let openImportModal = () => {
    importModal.value.show = true;
};

let closeImportModal = () => {
    importModal.value.show = false;
    fileList.value = [];
};

let fileList = ref<UploadFileInfo[]>([]);

let onBeforeUpload = async (fileData: { file: UploadFileInfo; fileList: UploadFileInfo[] }) => {
    let formData = new FormData();
    formData.append("file", fileData.file.file as any);
    IMPORT_ARCHIVES(formData).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("导入成功");
            closeImportModal();
            onSearch();
        } else {
            window.$message.error("导入失败，请检查后重试！");
        }
    });
};

let onDownloadTemplate = () => {
    window.location.href = import.meta.env.VITE_API_URL + "/admin/sys-file/local/archives.xlsx";
};

// 批量导出
let openExportModal = () => {
    EXPORT_ARCHIVES().then((res) => {
        let blob = new Blob([res.data]);
        let a = document.createElement("a");
        a.href = URL.createObjectURL(blob);
        a.download = "花名册.xlsx";
        a.style.display = "none";
        document.body.appendChild(a);
        a.click();
        a.remove();
    });
};

// 批量修改部门
let changeDeptModal = ref<{ show: boolean }>({
    show: false
});

let openChangeDeptModal = () => {
    if (tableSelection.value.length < 1) {
        window.$message.error("请选择要变更部门的人员");
        return false;
    }
    changeDeptModal.value.show = true;
};

// 批量重置默认密码
let openResetPassword = () => {
    if (tableSelection.value.length < 1) {
        window.$message.error("请选择要重置密码的人员");
        return false;
    }
    window.$dialog.warning({
        title: "警告",
        content: `确定重置选中人员的密码吗？`,
        positiveText: "重置",
        negativeText: "取消",
        onPositiveClick: () => {
            RESET_PASSWORD({
                userIds: tableSelection.value.join(",")
            }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("重置成功");
                    onSearch();
                }
            });
        }
    });
};

// 同步功能
const updateModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

const openUpdateModal = (row?: RowProps) => {
    updateModal.value.show = true;
    updateModal.value.configData = row ?? {};
};
</script>
