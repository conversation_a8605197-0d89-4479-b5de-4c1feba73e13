<template>
    <n-card content-style="padding-top:10px" hoverable>
        <n-tabs v-model:value="tabActive" type="bar" animated>
            <n-tab-pane name="填报公司设定">
                <ReportingSettingsCompany />
            </n-tab-pane>
            <n-tab-pane name="其他设置">
                <ReportingSettingsOther />
            </n-tab-pane>
        </n-tabs>
    </n-card>
</template>

<script setup lang="ts">
import { ref } from "vue";
import ReportingSettingsCompany from "./ReportingSettingsCompany.vue";
import ReportingSettingsOther from "./ReportingSettingsOther/ReportingSettingsOther.vue";

let tabActive = ref("填报公司设定");
</script>
