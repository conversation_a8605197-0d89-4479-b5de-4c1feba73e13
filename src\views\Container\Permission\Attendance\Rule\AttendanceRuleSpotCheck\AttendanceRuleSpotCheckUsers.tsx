import { computed, defineComponent, reactive, ref, watchEffect } from "vue";
import { type DataTableColumns } from "naive-ui";
import { useCommonTable } from "@/hooks";
import { GET_SPOT_CHECK_ATTENDANCE_CONFIGURATION_LIST } from "@/api/permission";

interface RowProps {
    [key: string]: any;
}

export default defineComponent({
    name: "AttendanceRuleSpotCheckUsers",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        interface RowProps {
            [key: string]: any;
        }

        const tableColumns = ref<DataTableColumns<any>>([
            { title: "姓名", key: "trueName", align: "center" },
            { title: "所在部门", key: "depName", align: "center" }
        ]);

        const { tableData, tablePaginationPreset, tableLoading } = useCommonTable<RowProps>("id");

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_SPOT_CHECK_ATTENDANCE_CONFIGURATION_LIST({
                id: props.configData?.id,
                current: tablePagination.page,
                size: tablePagination.pageSize
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records ?? [];
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        // 交互按钮
        const onClose = () => {
            changeModalShow(false);
            emit("refresh");
        };

        watchEffect(() => {
            if (show.value && props.configData?.id) {
                getTableData();
            }
        });

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card title="考勤抽查名单" class="w-800px" closable onClose={onClose}>
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        pagination={tablePagination}
                        single-line={false}
                        bordered
                        striped
                    />
                </n-card>
            </n-modal>
        );
    }
});
