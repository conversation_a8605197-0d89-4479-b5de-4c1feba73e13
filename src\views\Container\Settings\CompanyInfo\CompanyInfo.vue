<template>
    <div class="company-info">
        <n-card class="pt" hoverable>
            <n-form
                ref="formRef"
                :model="formData"
                :rules="formRules"
                class="max-w-1000px"
                label-placement="left"
                label-width="auto"
            >
                <n-grid :cols="24" :x-gap="24">
                    <n-form-item-gi :span="12" label="集团名称" path="ciName">
                        <n-input
                            v-model:value="formData.ciName"
                            class="w-100%"
                            clearable
                            placeholder="请输入集团名称"
                        />
                    </n-form-item-gi>
                    <n-form-item-gi :span="12" label="集团英文名称" path="ciNameEn">
                        <n-input
                            v-model:value="formData.ciNameEn"
                            class="w-100%"
                            clearable
                            placeholder="请输入集团英文名称"
                        />
                    </n-form-item-gi>
                    <n-form-item-gi :span="12" label="集团地址" path="ciAddress">
                        <n-input
                            v-model:value="formData.ciAddress"
                            class="w-100%"
                            clearable
                            placeholder="请输入集团地址"
                        />
                    </n-form-item-gi>
                    <n-form-item-gi :span="12" label="集团英文地址" path="ciAddressEn">
                        <n-input
                            v-model:value="formData.ciAddressEn"
                            class="w-100%"
                            clearable
                            placeholder="请输入集团英文地址"
                        />
                    </n-form-item-gi>
                    <n-form-item-gi :span="12" label="集团电话" path="ciPhone">
                        <n-input
                            v-model:value="formData.ciPhone"
                            class="w-100%"
                            clearable
                            placeholder="请输入集团电话"
                        />
                    </n-form-item-gi>
                    <n-form-item-gi :span="12" label="集团简称" path="ciAbbreviation">
                        <n-input
                            v-model:value="formData.ciAbbreviation"
                            class="w-100%"
                            clearable
                            placeholder="请输入集团简称"
                        />
                    </n-form-item-gi>
                    <n-form-item-gi :span="12" label="统一社会信用代码" path="ciUsci">
                        <n-input
                            v-model:value="formData.ciUsci"
                            class="w-100%"
                            clearable
                            placeholder="请输入统一社会信用代码"
                        />
                    </n-form-item-gi>
                    <n-form-item-gi :span="12" label="邮政编码" path="ciPostcode">
                        <n-input
                            v-model:value="formData.ciPostcode"
                            class="w-100%"
                            clearable
                            placeholder="请输入邮政编码"
                        />
                    </n-form-item-gi>
                    <n-form-item-gi :span="12" label="法人代表" path="ciLegalPerson">
                        <n-input
                            v-model:value="formData.ciLegalPerson"
                            class="w-100%"
                            clearable
                            placeholder="请输入法人代表"
                        />
                    </n-form-item-gi>
                    <n-form-item-gi :span="12" label="公司邮箱" path="ciEmail">
                        <n-input
                            v-model:value="formData.ciEmail"
                            class="w-100%"
                            clearable
                            placeholder="请输入公司邮箱"
                        />
                    </n-form-item-gi>
                    <n-form-item-gi :span="12">
                        <n-space>
                            <n-button type="primary" @click="onSubmit()">保存</n-button>
                            <n-button type="error" @click="clearFrom()">重置</n-button>
                        </n-space>
                    </n-form-item-gi>
                </n-grid>
            </n-form>
        </n-card>
    </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { cloneDeep } from "lodash-es";
import type { FormInst } from "naive-ui";
import { GET_COMPANY_INFO, UPDATE_COMPANY_INFO } from "@/api/settings";

onMounted(() => {
    getCompanyInfo();
});

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {
    ciName: {
        required: true,
        message: "请输入集团名称",
        trigger: ["input", "blur"]
    },
    ciPhone: {
        required: true,
        message: "请输入集团电话",
        trigger: ["input", "blur"]
    },
    ciUsci: {
        required: true,
        message: "请输入统一社会信用代码",
        trigger: ["input", "blur"]
    },
    ciLegalPerson: {
        required: true,
        message: "请输入法人代表",
        trigger: ["input", "blur"]
    }
};

// 表单数据
interface FormDataProps<T = string | null> {
    ciName: T;
    ciNameEn: T;
    ciAddress: T;
    ciAddressEn: T;
    ciPhone: T;
    ciAbbreviation: T;
    ciUsci: T;
    ciPostcode: T;
    ciLegalPerson: T;
    ciEmail: T;
}

let initFormData: FormDataProps = {
    ciName: null,
    ciNameEn: null,
    ciAddress: null,
    ciAddressEn: null,
    ciPhone: null,
    ciAbbreviation: null,
    ciUsci: null,
    ciPostcode: null,
    ciLegalPerson: null,
    ciEmail: null
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    UPDATE_COMPANY_INFO(formData.value).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("编辑成功");
            getCompanyInfo();
        }
    });
};

let getCompanyInfo = () => {
    GET_COMPANY_INFO({}).then((res) => {
        if (res.data.code === 0) {
            let resData = res.data.data;
            formData.value = {
                ciName: resData.ciName,
                ciNameEn: resData.ciNameEn,
                ciAddress: resData.ciAddress,
                ciAddressEn: resData.ciAddressEn,
                ciPhone: resData.ciPhone,
                ciAbbreviation: resData.ciAbbreviation,
                ciUsci: resData.ciUsci,
                ciPostcode: resData.ciPostcode,
                ciLegalPerson: resData.ciLegalPerson,
                ciEmail: resData.ciEmail
            };
        }
    });
};
</script>
