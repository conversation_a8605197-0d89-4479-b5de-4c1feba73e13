import { computed, defineComponent, h, ref, watchEffect } from "vue";
import { type DataTableColumns, type FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import {
    GET_ALL_USER_LIST,
    GET_ATTENDANCE_RULE_CONFIG_CHECK_THE_CLOCK_IN_TIME_PERIOD,
    GET_ATTENDANCE_RULE_CONFIG_DETAIL,
    GET_STAFF_BY_USERNAMES,
    SAVE_ATTENDANCE_RULE_CONFIG
} from "@/api/permission";
import { TableActions } from "@/components/TableActions";
import { UserSelector } from "@/components/UserSelector";

export default defineComponent({
    name: "AttendanceRuleConfigTimeSlot",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        const tableColumns = ref<DataTableColumns<any>>([
            { title: "上班打卡时间段", key: "workTimeRange", align: "center" },
            { title: "下班打卡时间段", key: "offTimeRange", align: "center" }
        ]);

        const tableData = ref<any[]>([]);

        const getTableData = () => {
            GET_ATTENDANCE_RULE_CONFIG_CHECK_THE_CLOCK_IN_TIME_PERIOD({
                ruleId: props.configData?.id
            }).then((res) => {
                tableData.value = res.data.data;
            });
        };

        // 交互按钮
        const onClose = () => {
            changeModalShow(false);
            emit("refresh");
        };

        watchEffect(() => {
            if (show.value && props.configData?.id) {
                getTableData();
            }
        });

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card title="打卡时间段" class="w-800px" closable onClose={onClose}>
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        single-line={false}
                        bordered
                        striped
                    />
                </n-card>
            </n-modal>
        );
    }
});
