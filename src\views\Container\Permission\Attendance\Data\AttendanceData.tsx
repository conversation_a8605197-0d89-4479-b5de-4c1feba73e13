import { defineComponent, onMounted, ref } from "vue";
import { GET_ATTENDANCE_MODULE_SETTING_LIST } from "@/api/permission";
import AttendanceDataQuery from "@/views/Container/Permission/Attendance/Data/AttendanceDataQuery/AttendanceDataQuery";
import AttendanceDataAbnormalHandle from "@/views/Container/Permission/Attendance/Data/AttendanceDataAbnormal/AttendanceDataAbnormalHandle";
import AttendanceDataAbnormalRecord from "@/views/Container/Permission/Attendance/Data/AttendanceDataAbnormal/AttendanceDataAbnormalRecord";

export default defineComponent({
    name: "AttendanceData",
    setup(props, { emit }) {
        const tabActive = ref<string | null>(null);

        const tabOptions = ref<any[]>([]);

        const getTabOptions = async () => {
            await GET_ATTENDANCE_MODULE_SETTING_LIST({ current: 1, size: 999 }).then((res) => {
                tabOptions.value = (res.data.data.records ?? []).map((item: any) => {
                    return {
                        label: item.companyName,
                        key: item.corpId
                    };
                });
                tabActive.value = tabOptions.value[0].key ?? null;
            });
        };

        const changeTabActive = (key: string) => {
            tabActive.value = key;
        };

        onMounted(async () => {
            await getTabOptions();
        });

        return () => (
            <div class="attendance-page">
                <n-card hoverable>
                    <div class="flex">
                        <n-menu
                            v-model:value={tabActive.value}
                            indent={20}
                            options={tabOptions.value}
                            class={"common-left-menu flex-fixed-235 border-r-1px border-[#E5E5E5]"}
                            mode="vertical"
                            onUpdate:value={changeTabActive}
                        />
                        <div class="flex-1 ml-8">
                            <n-tabs animated type="bar" defaultValue={2}>
                                <n-tab-pane name={1} tab="考勤查询">
                                    <AttendanceDataQuery corpId={tabActive.value} />
                                </n-tab-pane>
                                <n-tab-pane name={2} tab="异常考勤处理">
                                    <AttendanceDataAbnormalHandle corpId={tabActive.value} />
                                </n-tab-pane>
                                <n-tab-pane name={3} tab="异常处理记录">
                                    <AttendanceDataAbnormalRecord corpId={tabActive.value} />
                                </n-tab-pane>
                                {/*<n-tab-pane name={4} tab="用户考勤轨迹"></n-tab-pane>*/}
                            </n-tabs>
                        </div>
                    </div>
                </n-card>
            </div>
        );
    }
});
