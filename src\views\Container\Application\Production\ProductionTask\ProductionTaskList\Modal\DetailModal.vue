<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-800px" closable title="排产任务详情" @close="changeModalShow(false)">
                <div class="flex-y-center text-16px font-bold mb">
                    <div>排产单号：</div>
                    <n-element class="color-[var(--primary-color)]" tag="div">{{ taskDetail.id }}</n-element>
                </div>
                <n-grid :col="24" :x-gap="16" :y-gap="16">
                    <n-grid-item :span="12">
                        <span>所属项目：</span>
                        <n-element tag="span">
                            {{ taskDetail.projectName }}
                        </n-element>
                    </n-grid-item>
                    <n-grid-item :span="12">
                        <span>排产状态：</span>
                        <n-element v-if="taskDetail.prodState === 0" tag="span">待生产</n-element>
                        <n-element v-if="taskDetail.prodState === 1" class="color-[var(--warning-color)]" tag="span">
                            生产中
                        </n-element>
                        <n-element v-if="taskDetail.prodState === 2" class="color-[var(--success-color)]" tag="span">
                            生产完成
                        </n-element>
                        <n-element v-if="taskDetail.prodState === 3" class="color-[var(--success-color)]" tag="span">
                            准入库
                        </n-element>
                        <n-element v-if="taskDetail.prodState === 4" class="color-[var(--success-color)]" tag="span">
                            已入库
                        </n-element>
                    </n-grid-item>
                    <n-grid-item :span="12">
                        <span>排产类型：</span>
                        <n-element class="color-[var(--primary-color)]" tag="span"> 订单排产</n-element>
                    </n-grid-item>
                    <n-grid-item :span="12">
                        <span>交货日期：</span>
                        <n-element tag="span">
                            {{ taskDetail.deliveryDate }}
                        </n-element>
                    </n-grid-item>
                    <n-grid-item :span="12">
                        <span>所属订单号：</span>
                        <n-element class="color-[var(--primary-color)]" tag="span">
                            {{ taskDetail.pomNumber }}
                        </n-element>
                    </n-grid-item>
                    <n-grid-item :span="12">
                        <span>生产规格型号：</span>
                        <n-element tag="span">
                            {{ taskDetail.specification }}
                        </n-element>
                    </n-grid-item>
                    <n-grid-item :span="12">
                        <span>生产数量：</span>
                        <n-element tag="span">
                            {{ taskDetail.prodCount }}
                        </n-element>
                    </n-grid-item>
                    <n-grid-item :span="12">
                        <span>当前生产线路：</span>
                        <n-element class="color-[var(--primary-color)]" tag="span">
                            {{ taskDetail.prodLineName }}
                        </n-element>
                    </n-grid-item>
                </n-grid>
                <n-tabs v-model:value="tabActive" class="mt-10px" type="line">
                    <n-tab-pane :name="0" tab="生产单据填写情况">
                        <n-table :single-line="false" class="mt text-center">
                            <thead>
                                <tr>
                                    <th>表单类型</th>
                                    <th>填写时间</th>
                                    <th>表单详情</th>
                                    <th>填写人</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="item in taskDetail.productSheetList">
                                    <td>{{ item.sheetTitle }}</td>
                                    <td>
                                        <template v-if="item.sheetFillStatus === 3 && item.fillTime">
                                            {{ item.fillTime }}
                                        </template>
                                        <template v-else>/</template>
                                    </td>
                                    <td>
                                        <n-element
                                            v-if="item.sheetFillStatus === 3"
                                            class="cursor-pointer color-[var(--primary-color)]"
                                            @click="openProcessDetailModal(item)"
                                        >
                                            点击查看
                                        </n-element>
                                        <div v-else>暂未填写</div>
                                    </td>
                                    <td>
                                        <UserSelector
                                            v-model:value="item.updateBy"
                                            :show-input="false"
                                            class="w-100% flex-center"
                                            key-name="username"
                                        >
                                            <template #input="scope">{{ scope.data }}</template>
                                        </UserSelector>
                                    </td>
                                </tr>
                            </tbody>
                        </n-table>
                    </n-tab-pane>
                    <n-tab-pane :name="1" tab="检验单据填写情况">
                        <n-table :single-line="false" class="mt text-center">
                            <thead>
                                <tr>
                                    <th>表单类型</th>
                                    <th>填写时间</th>
                                    <th>表单详情</th>
                                    <th>填写人</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="item in taskDetail.checkSheetList">
                                    <td>{{ item.sheetTitle }}</td>
                                    <td>
                                        <template v-if="item.sheetFillStatus === 3 && item.fillTime">
                                            {{ item.fillTime }}
                                        </template>
                                        <template v-else>/</template>
                                    </td>
                                    <td>
                                        <n-element
                                            v-if="item.sheetFillStatus === 3"
                                            class="cursor-pointer color-[var(--primary-color)]"
                                            @click="openProcessDetailModal(item)"
                                        >
                                            点击查看
                                        </n-element>
                                        <div v-else>暂未填写</div>
                                    </td>
                                    <td>
                                        <UserSelector
                                            v-model:value="item.updateBy"
                                            :show-input="false"
                                            class="w-100% flex-center"
                                            key-name="username"
                                        >
                                            <template #input="scope">{{ scope.data }}</template>
                                        </UserSelector>
                                    </td>
                                </tr>
                            </tbody>
                        </n-table>
                    </n-tab-pane>
                    <n-tab-pane :name="2" tab="相关领料单">
                        <n-table :single-line="false" class="mt text-center">
                            <thead>
                                <tr>
                                    <th>领料审批单</th>
                                    <th>领料数量</th>
                                    <th>领料时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <n-element
                                            class="cursor-pointer color-[var(--primary-color)]"
                                            @click="openProcessDetailModal(taskDetail.productMaterialAppl, '领料单')"
                                        >
                                            点击查看
                                        </n-element>
                                    </td>
                                    <td>{{ taskDetail.productMaterialAppl?.applyCount || "未知" }}</td>
                                    <td>{{ taskDetail.productMaterialAppl?.starterTime }}</td>
                                </tr>
                            </tbody>
                        </n-table>
                    </n-tab-pane>
                </n-tabs>
            </n-card>
        </n-modal>
        <!--普通表单详情弹窗-->
        <CommonFormDetail v-model:show="commonForm.show" :config-data="commonForm.configData" />
        <!--流程详情弹窗-->
        <ProcessDetail v-model:show="processDetailModal.show" :config-data="processDetailModal.configData" />
    </div>
</template>

<script lang="ts" setup>
import { computed, ref, watchEffect } from "vue";
import { GET_PRODUCTION_TASK_DETAIL } from "@/api/application/production";
import { ProcessDetail } from "@/views/Container/Application/Process/components";
import { UserSelector } from "@/components/UserSelector";
import { CommonFormDetail } from "@/views/Container/Application/Production/ProductionForm/components";

let props = withDefaults(
    defineProps<{
        show: boolean;
        configData: UnKnownObject;
    }>(),
    {
        show: () => false
    }
);

let emits = defineEmits(["update:show", "refresh"]);

let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

let changeModalShow = (show: boolean) => emits("update:show", show);

interface TaskProps {
    [key: string]: any;
}

let taskDetail = ref<TaskProps>({});

watchEffect(() => {
    if (props.show && props.configData?.id) {
        GET_PRODUCTION_TASK_DETAIL({ id: props.configData.id }).then((res) => {
            taskDetail.value = res.data.data;
        });
    }
});

// tab切换
let tabActive = ref(0);

// 普通表单
let commonForm = ref<{ show: boolean; configData: Record<string, any> }>({ show: false, configData: {} });

let openCommonFormDetail = (row: Record<string, any>) => {
    commonForm.value.show = true;
    commonForm.value.configData = { ...row, sheetKey: row.sheetKey };
};

// 流程详情弹窗
let processDetailModal = ref<{ show: boolean; configData: Record<string, any> }>({ show: false, configData: {} });

let openProcessDetailModal = (row: Record<string, any>, type?: string) => {
    if (type === "领料单") {
        processDetailModal.value.show = true;
        processDetailModal.value.configData = { ...row, processInstanceId: row.processInstanceId };
    } else {
        if (row.sheetType === 2) {
            if (row.processInstanceId) {
                processDetailModal.value.show = true;
                processDetailModal.value.configData = { ...row, processInstanceId: row.processInstanceId };
            } else {
                window.$message.error("暂未发起流程");
            }
        } else {
            openCommonFormDetail(row);
        }
    }
};
</script>
