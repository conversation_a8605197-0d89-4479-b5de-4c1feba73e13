<template>
    <div>
        <n-card content-style="padding-top:16px;padding-bottom:8px" hoverable>
            <n-tabs v-model:value="tabActive" animated type="bar">
                <n-tab-pane :name="1" tab="未完结合同项目" />
                <n-tab-pane :name="2" tab="未完结无合同项目" />
            </n-tabs>
        </n-card>
        <div class="mt">
            <FinancePaymentListUnfinishedHave v-if="tabActive === 1" />
            <FinancePaymentListUnfinishedNon v-else-if="tabActive === 2" />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import FinancePaymentListUnfinishedHave from "./FinancePaymentListUnfinishedHave.vue";
import FinancePaymentListUnfinishedNon from "./FinancePaymentListUnfinishedNon.vue";

let tabActive = ref(1);
</script>
