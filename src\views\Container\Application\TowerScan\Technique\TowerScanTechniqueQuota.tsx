import { defineComponent, onMounted, reactive, ref, watch } from "vue";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable, useDicts } from "@/hooks";
import type { DataTableColumns, PaginationProps } from "naive-ui";
import { NText } from "naive-ui";
import { TableActions } from "@/components/TableActions";
import { DynamicTableEditor } from "@/components/Dynamic";
import { GET_IRON_TECHNIQUE_PAGE_LIST, UPDATE_IRON_TECHNIQUE_WAGE } from "@/api/application/TowerScan";
import TowerScanTechniqueQuotaHistory from "./TowerScanTechniqueQuotaHistory";

export default defineComponent({
    name: "TowerScanTechniqueQuota",
    setup() {
        // 字典操作
        const { dictLibs, getDictLibs } = useDicts();

        const setDictLibs = async () => {
            const dictName = ["common_units", "TechniqueProcessType"];
            await getDictLibs(dictName);
            // 去掉第0项和第1项
            dictLibs["TechniqueProcessType"] =
                dictLibs["TechniqueProcessType"]?.filter((item) => item.value !== "0" && item.value !== "1") || [];
        };

        // 工艺分类tab
        const workmanshipTabActive = ref<string>("");

        // 搜索项
        const searchConfig = ref<TableSearchbarConfig>([{ label: "工艺名称", prop: "techniqueName", type: "input" }]);
        const searchOptions = ref<TableSearchbarOptions>({});
        const searchForm = ref<TableSearchbarData>({
            techniqueName: ""
        });

        const getSearchOptions = () => {};

        const onSearch = () => {
            getTableData();
        };

        // 工艺类型选项
        const techniqueTypeOptions = [
            { label: "制孔工艺", value: 1 },
            { label: "计件工艺", value: 3 },
            { label: "按量计算工艺", value: 4 }
        ];

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            {
                title: "工艺名称",
                key: "treeTechniqueName",
                align: "center"
            },
            {
                title: "工艺类型",
                key: "techniqueType",
                align: "center",
                render: (row) => (
                    <NText type="info">
                        {techniqueTypeOptions.find((item) => item.value === row.techniqueType)?.label || "/"}
                    </NText>
                )
            },
            {
                title: "定价金额（元）",
                key: "techniqueAmount",
                align: "center",
                render: (row) => (
                    <DynamicTableEditor
                        type="input"
                        value={row.techniqueAmount}
                        onUpdateValue={(v: any) => (row.techniqueAmount = v)}
                    />
                )
            },
            {
                title: "定价单位",
                key: "techniqueUnit",
                align: "center",
                render: (row) => (
                    <DynamicTableEditor
                        type="treeSelect"
                        value={row.techniqueUnit}
                        readonlyValue={row.techniqueUnitName}
                        onUpdateValue={(v: any) => (row.techniqueUnit = v)}
                        onUpdateReadonlyValue={(o: any) => (row.techniqueUnitName = o.label)}
                        componentConfig={{
                            options: dictLibs["common_units"],
                            clearable: true,
                            filterable: true,
                            keyField: "value",
                            labelField: "label",
                            placeholder: "请选择定价单位"
                        }}
                    />
                )
            },
            {
                title: "最小电压（KV）",
                key: "minPowerLevel",
                align: "center",
                render: (row) => row.minPowerLevel || "/"
            },
            {
                title: "最大电压（KV）",
                key: "maxPowerLevel",
                align: "center",
                render: (row) => row.maxPowerLevel || "/"
            },
            {
                title: "操作",
                key: "actions",
                align: "center",
                width: 200,
                render: (row: RowProps) => (
                    <TableActions
                        type="button"
                        buttonActions={[
                            {
                                label: "保存修改",
                                tertiary: true,
                                type: "success",
                                onClick: () => updateTableItem(row)
                            },
                            {
                                label: "修改历史",
                                tertiary: true,
                                type: "info",
                                onClick: () => openHistoryModal(row)
                            }
                        ]}
                    />
                )
            }
        ]);

        const tablePagination = reactive<PaginationProps>({
            page: 1,
            pageSize: 10,
            itemCount: 0,
            pageSizes: [10, 50, 100],
            showSizePicker: true,
            showQuickJumper: true,
            displayOrder: ["size-picker", "pages", "quick-jumper"],
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_IRON_TECHNIQUE_PAGE_LIST({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                processType: workmanshipTabActive.value,
                ...searchForm.value
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records || [];
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                } else {
                    tableLoading.value = false;
                }
            });
        };

        // 更新定额
        const updateTableItem = async (row: RowProps) => {
            const res = await UPDATE_IRON_TECHNIQUE_WAGE({
                id: row.id,
                techniqueAmount: row.techniqueAmount,
                techniqueUnit: row.techniqueUnit
            });
            if (res.data.code === 0) {
                window.$message.success("保存成功");
                onSearch();
            } else {
                window.$message.error(res.data.msg);
            }
        };

        // 历史记录弹窗
        const historyModal = ref<{ show: boolean; configData: UnKnownObject }>({
            show: false,
            configData: {}
        });

        const openHistoryModal = (row: RowProps) => {
            historyModal.value = {
                show: true,
                configData: { ironTechniqueId: row.id, techniqueName: row.treeTechniqueName }
            };
        };

        // 监听tab变化
        watch(
            () => workmanshipTabActive.value,
            () => {
                getTableData();
            },
            { immediate: true }
        );

        onMounted(async () => {
            await setDictLibs();
            if (dictLibs?.["TechniqueProcessType"]?.length > 0) {
                workmanshipTabActive.value = dictLibs["TechniqueProcessType"][0].value as string;
            }
            getSearchOptions();
        });

        return () => (
            <div class="tower-scan-technique-quota">
                <n-card hoverable>
                    <n-tabs v-model:value={workmanshipTabActive.value} animated class="mb-1" type="bar">
                        {dictLibs["TechniqueProcessType"]?.map((item, index) => (
                            <n-tab-pane key={index} name={item.value} tab={item.label} />
                        ))}
                    </n-tabs>
                    <n-card class="mb">
                        <TableSearchbar
                            form={searchForm.value}
                            config={searchConfig.value}
                            options={searchOptions.value}
                            onSearch={onSearch}
                        />
                    </n-card>
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        pagination={tablePagination}
                        row-key={tableRowKey}
                        single-line={false}
                        bordered
                        children-key="childrenList"
                        remote
                        striped
                        onUpdate:checked-row-keys={changeTableSelection}
                    />
                </n-card>
                <TowerScanTechniqueQuotaHistory
                    v-model:show={historyModal.value.show}
                    config-data={historyModal.value.configData}
                />
            </div>
        );
    }
});
