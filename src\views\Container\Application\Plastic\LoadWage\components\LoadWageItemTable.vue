<template>
    <div class="w-100%">
        <n-data-table :columns="tableColumns" :data="tableData" :single-line="false" bordered striped />
        <div class="flex-x-center mt">
            <n-space>
                <n-button type="primary" @click="addTableItem">新增一行</n-button>
                <n-button type="success" @click="onSubmit">保存全部</n-button>
            </n-space>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed, h, ref } from "vue";
import type { DataTableColumns } from "naive-ui";
import { NInput} from "naive-ui";
import { cloneDeep } from "lodash-es";
import { TableActions } from "@/components/TableActions";
import { PlasticProductTypeSelector } from "@/components/PlasticProductTypeSelector";

let emits = defineEmits(["confirm", "update:value"]);

// 表单数据
interface RowProps {
    [key: string]: any;
}

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        title: "规格型号",
        align: "center",
        key: "ptId",
        render: (row) => {
            return h(PlasticProductTypeSelector, {
                value: row.ptId,
                keyName: "id",
                onSubmit: (v: number)=>{
                    row.ptId = v;
                }
            });
        }
    },
    {
        title: "米数",
        key: "meterNum",
        align: "center",
        render: (row) => {
            return h(NInput, { value: row.meterNum, onUpdateValue: (v) => (row.meterNum = v) })
        }
    },
    {
        title: "车间定额",
        key: "quotaNew",
        align: "center",
        render: (row) => {
            return h(NInput, { value: row.quotaNew, onUpdateValue: (v) => (row.quotaNew = v) })
        }
    },
    {
        title: "操作",
        key: "action",
        align: "center",
        width: 80,
        render(row, index) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "删除",
                        tertiary: true,
                        type: "error",
                        onClick: () => deleteItem(index)
                    }
                ]
            });
        }
    }
]);

// 可编辑表单配置
let tableItem: RowProps = {
    ptId: null,
    ptName: "",
    type: null,
    degree: "",
    series: "",
    specification: "",
    meterNum: "",
    quotaNew: ""
};

let addTableItem = () => {
    tableData.value.push(cloneDeep(tableItem));
};

let deleteItem = (index: number) => {
    tableData.value.splice(index, 1);
};

let props = withDefaults(defineProps<{ value: RowProps[] }>(), {
    value: () => []
});

let tableData = computed({ get: () => props.value, set: (val) => emits("update:value", val) });

// 提交
let onSubmit = () => {
    emits("confirm", tableData.value);
};
</script>
