<template>
    <div>
        <n-table :single-line="false" class="text-center" v-if="(formData.invoicingApplyList || []).length">
            <thead>
                <tr>
                    <th>开票金额</th>
                    <th>开票数量</th>
                    <!--<th>开票信息</th>-->
                    <th>开票时间</th>
                    <th>关联OA审批单</th>
                    <!--                    <th>操作</th>-->
                </tr>
            </thead>
            <tbody>
                <tr v-for="(item, index) in formData.invoicingApplyList || []" :key="index">
                    <td>{{ item.invoicingAmount }}</td>
                    <td>1</td>
                    <!--<td></td>-->
                    <td>{{ item.invoicingTime }}</td>
                    <td>
                        <n-text class="cursor-pointer" type="info">点击查看</n-text>
                    </td>
                    <!--<td></td>-->
                </tr>
            </tbody>
        </n-table>
        <n-result v-else class="py-50px" size="large" status="404" title="暂无开票记录" />
    </div>
</template>

<script lang="ts" setup>
interface FormDataProps {
    [key: string]: any;
}

let props = withDefaults(defineProps<{ formData: FormDataProps }>(), {});

let emits = defineEmits(["refresh"]);

let onRefresh = () => emits("refresh");
</script>
