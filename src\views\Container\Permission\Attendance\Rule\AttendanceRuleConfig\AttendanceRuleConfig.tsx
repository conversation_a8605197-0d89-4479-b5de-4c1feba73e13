import { defineComponent, reactive, ref, watchEffect } from "vue";
import type { DataTableColumns } from "naive-ui";
import { DELETE_ATTENDANCE_RULE_CONFIG, GET_ATTENDANCE_RULE_CONFIG } from "@/api/permission";
import { useCommonTable } from "@/hooks";
import { TableActions } from "@/components/TableActions";
import AttendanceRuleConfigEdit from "./AttendanceRuleConfigEdit";
import AttendanceRuleConfigTimeSlot from "@/views/Container/Permission/Attendance/Rule/AttendanceRuleConfig/AttendanceRuleConfigTimeSlot";

export default defineComponent({
    name: "AttendanceRuleConfig",
    props: {
        corpId: { type: String as PropType<string | null>, default: null }
    },
    setup(props, { emit }) {
        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { title: "规格名称", key: "ruleName", align: "center" },
            { title: "打卡时间", key: "printTime", align: "center" },
            {
                title: "打卡时间段",
                key: "id",
                align: "center",
                width: 120,
                render: (row) => {
                    return (
                        <n-button type="primary" onClick={() => openTimeSlotModal(row)}>
                            点击查看
                        </n-button>
                    );
                }
            },
            {
                title: "考勤类型",
                key: "ruleType",
                align: "center",
                render: (row) => {
                    switch (row.ruleType) {
                        case 1:
                            return <n-text type="info">固定制</n-text>;
                        case 2:
                            return <n-text type="info">排班</n-text>;
                        case 3:
                            return <n-text type="info">自由时间</n-text>;
                        default:
                            return <n-text type="info">/</n-text>;
                    }
                }
            },
            {
                title: "考勤工作日",
                key: "workday",
                align: "center",
                render: (row) => {
                    return <n-text type="info">{row.workday}</n-text>;
                }
            },
            {
                title: "启用状态",
                key: "ruleStatus",
                align: "center",
                width: 100,
                render: (row) => {
                    switch (row.ruleStatus) {
                        case 1:
                            return <n-text type="success">启用</n-text>;
                        case 0:
                            return <n-text type="error">禁用</n-text>;
                        default:
                            return <n-text type="info">/</n-text>;
                    }
                }
            },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 160,
                render: (row) => {
                    return (
                        <TableActions
                            type="button"
                            buttonActions={[
                                { label: "编辑", tertiary: true, type: "primary", onClick: () => openEditModal(row) },
                                { label: "删除", tertiary: true, type: "error", onClick: () => onDelete(row) }
                            ]}
                        />
                    );
                }
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_ATTENDANCE_RULE_CONFIG({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                corpId: props.corpId
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        // 新增功能
        const editModal = ref<{ show: boolean; configData: RowProps }>({ show: false, configData: {} });

        const openEditModal = (row?: RowProps) => {
            editModal.value.show = true;
            editModal.value.configData = {
                ...(row ?? {}),
                corpId: props.corpId
            };
        };

        // 打卡时间段弹窗
        const timeSlotModal = ref<{ show: boolean; configData: RowProps }>({ show: false, configData: {} });

        const openTimeSlotModal = (row: RowProps) => {
            timeSlotModal.value = { show: true, configData: row };
        };

        // 删除功能
        const onDelete = (row: RowProps) => {
            window.$dialog.warning({
                title: "警告",
                content: "确认删除该条数据？该操作不可逆",
                positiveText: "确认删除",
                negativeText: "我再想想",
                onPositiveClick: () => {
                    DELETE_ATTENDANCE_RULE_CONFIG({ id: row.id }).then((res) => {
                        if (res.data.code === 0) {
                            window.$message.success("删除成功");
                            getTableData();
                        }
                    });
                }
            });
        };

        watchEffect(() => {
            if (props.corpId) {
                getTableData();
            }
        });

        return () => (
            <div>
                <n-space class="mb">
                    <n-button type="primary" onClick={() => openEditModal()}>
                        新增时间段规则
                    </n-button>
                </n-space>
                <n-data-table
                    columns={tableColumns.value}
                    data={tableData.value}
                    loading={tableLoading.value}
                    pagination={tablePagination}
                    row-key={tableRowKey}
                    single-line={false}
                    bordered
                    remote
                    striped
                    onUpdate:checked-row-keys={changeTableSelection}
                />
                <AttendanceRuleConfigEdit
                    v-model:show={editModal.value.show}
                    v-model:configData={editModal.value.configData}
                    onRefresh={getTableData}
                />
                <AttendanceRuleConfigTimeSlot
                    v-model:show={timeSlotModal.value.show}
                    v-model:configData={timeSlotModal.value.configData}
                    onRefresh={getTableData}
                />
            </div>
        );
    }
});
