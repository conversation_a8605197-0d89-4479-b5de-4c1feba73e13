<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card
                class="w-800px"
                closable
                :title="`${props.configData.formulaName}的历史版本`"
                @close="changeModalShow(false)"
            >
                <n-data-table
                    :columns="tableColumns as any"
                    :data="tableData"
                    :loading="tableLoading"
                    :row-key="tableRowKey"
                    :single-line="false"
                    bordered
                    remote
                    striped
                />
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { computed, h, ref, watchEffect } from "vue";
import { GET_FORMULA_HIS_LIST } from "@/api/application/reporting";
import type { DataTableColumns } from "naive-ui";
import { NCollapse, NCollapseItem, NDataTable, NText } from "naive-ui";
import { useCommonTable } from "@/hooks";

let props = withDefaults(defineProps<{ show: boolean; configData: UnKnownObject }>(), {
    show: () => false
});

let emits = defineEmits(["update:show", "refresh"]);

let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

let changeModalShow = (show: boolean) => emits("update:show", show);

// 数据列表
interface RowProps {
    [key: string]: any;
}

let { tableRowKey, tableData, tableLoading } = useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "expand",
        expandable: (row) => row.formulaItemList && row.formulaItemList.length > 0,
        renderExpand: (row) => {
            return h(NCollapse, { defaultExpandedNames: ["1", "2"] }, () => [
                h(NCollapseItem, { title: "原材料列表", name: "1" }, () =>
                    h(NDataTable, {
                        bordered: true,
                        striped: true,
                        singleLine: true,
                        data: row.formulaItemList ?? [],
                        columns: [
                            {
                                title: "原材料",
                                key: "materialName",
                                align: "center",
                                render: (row) => `【${row.materialName}】${row.spec}`
                            },
                            { title: "原材料占比", key: "materialProportion", align: "center" },
                            { title: "原材料价格", key: "unitPrice", align: "center" },
                            { title: "原材料成本", key: "materialCost", align: "center" },
                            {
                                title: "状态",
                                align: "center",
                                key: "specDelFlag",
                                width: "100",
                                render: (row) => {
                                    if (row.specDelFlag === 1) {
                                        return h(NText, { type: "error" }, () => "已删除");
                                    } else {
                                        return h(NText, { type: "primary" }, () => "正常");
                                    }
                                }
                            }
                        ]
                    })
                ),
                h(NCollapseItem, { title: "半成品列表", name: "2" }, () =>
                    h(NDataTable, {
                        bordered: true,
                        striped: true,
                        singleLine: true,
                        data: row.formulaSemiItemList ?? [],
                        columns: [
                            { title: "半成品", key: "semiProductModel", align: "center" },
                            { title: "半成品占比", key: "semiManufactureProportion", align: "center" },
                            {
                                title: "状态",
                                align: "center",
                                key: "specDelFlag",
                                width: "100",
                                render: (row) => {
                                    if (row.specDelFlag === 1) {
                                        return h(NText, { type: "error" }, () => "已删除");
                                    } else {
                                        return h(NText, { type: "primary" }, () => "正常");
                                    }
                                }
                            }
                        ]
                    })
                )
            ]);
        }
    },
    {
        title: "配方名称",
        key: "formulaName",
        align: "center"
    },
    // {
    //     title: "原材料累计占比",
    //     key: "totalAmount",
    //     align: "center"
    // },
    {
        title: "原材料累计成本（元）",
        key: "totalCost",
        align: "center"
    },
    {
        title: "修改人",
        key: "createByName",
        align: "center"
    }
]);

let getTableData = () => {
    GET_FORMULA_HIS_LIST({
        formulaId: props.configData.id
    }).then((res) => {
        tableData.value = res.data.data ?? [];
        tableLoading.value = false;
    });
};

watchEffect(() => {
    if (props.show && props.configData.id) {
        getTableData();
    }
});
</script>
