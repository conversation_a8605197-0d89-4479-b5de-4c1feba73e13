<template>
    <div>
        <n-data-table
            :columns="tableColumns"
            :data="tableData"
            :loading="tableLoading"
            :pagination="tablePagination"
            :row-key="tableRowKey"
            :single-line="false"
            bordered
            remote
            striped
            @update:checked-row-keys="changeTableSelection"
        />
        <!--需求反馈详情-->
        <Requirements v-model:show="requirementsModal.show" :config-data="requirementsModal.configData" />
    </div>
</template>

<script lang="ts" setup>
import { useCommonTable } from "@/hooks";
import { h, onMounted, reactive, ref } from "vue";
import { DataTableColumns, NButton, NText } from "naive-ui";
import { GET_PRODUCTION_ARRANGE_UNWANT } from "@/api/application/production";
import { Requirements } from "../Modal";
import { GET_STAFF_BY_USERNAMES } from "@/api/permission";

let props = withDefaults(
    defineProps<{
        searchForm?: UnKnownObject;
    }>(),
    {}
);

interface RowProps {
    poId?: string | number;
    pomNumber?: string | number;
    projectName?: string; // 项目名称
    deliveryDate?: string; // 交货日期
    createBy?: string; // 销售联系人
    createTime: string; // 确认时间
    salesPersonName?: string; // 销售联系人名称
    stockCheckState?: string | number; // 仓库反馈状态
    ignoreCount?: string | number; // 无需生产数量
    specification?: string; // 无需生产规格
}

onMounted(() => {
    getTableData();
});

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("poId");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "订单号",
        key: "pomNumber",
        align: "center",
        render: (row) => {
            return row.pomNumber || "/";
        }
    },
    {
        title: "项目名称",
        key: "projectName",
        align: "center"
    },
    {
        title: "需求详情",
        key: "poId",
        align: "center",
        render(row) {
            return h(
                NButton,
                {
                    type: "primary",
                    text: true,
                    onClick: () => openRequirementsModal(row)
                },
                () => "点击查看"
            );
        }
    },
    {
        title: "仓库反馈",
        key: "stockCheckState",
        align: "center",
        render(row) {
            if (String(row.stockCheckState) === "1") {
                return h(
                    NButton,
                    {
                        type: "primary",
                        text: true,
                        onClick: () => openRequirementsModal(row)
                    },
                    () => "点击查看"
                );
            } else {
                return h(NText, { type: "error" }, () => "未反馈");
            }
        }
    },
    {
        title: "交货日期",
        key: "deliveryDate",
        align: "center"
    },
    {
        title: "无需生产规格",
        key: "specification",
        align: "center"
    },
    {
        title: "无需生产数量",
        key: "ignoreCount",
        align: "center"
    },
    {
        title: "销售联系人",
        key: "salesPersonName",
        align: "center"
    },
    {
        title: "确认时间",
        key: "createTime",
        align: "center"
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_PRODUCTION_ARRANGE_UNWANT({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        ...props.searchForm
    }).then(async (res) => {
        if (res.data.code === 0) {
            tableData.value = await Promise.all(
                res.data.data.records.map(async (item: RowProps) => {
                    item.salesPersonName = "未知";
                    await GET_STAFF_BY_USERNAMES({ usernames: item.createBy }).then((res) => {
                        if (res.data.code === 0 && res.data.data?.[0]?.trueName) {
                            item.salesPersonName = res.data.data[0].trueName;
                        }
                    });
                    return item;
                })
            );
            tablePagination.itemCount = res.data.data.total;
            tableLoading.value = false;
            console.log(111, tableData.value);
        }
    });
};

// 需求反馈详情
let requirementsModal = ref<{ show: boolean; configData: Record<string, any> }>({
    show: false,
    configData: {}
});

let openRequirementsModal = (row: RowProps) => {
    requirementsModal.value = {
        show: true,
        configData: row
    };
};
</script>
