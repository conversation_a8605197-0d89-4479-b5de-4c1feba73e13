import { computed, defineComponent, ref, watch } from "vue";
import { GET_IRON_COMPONENT_DETAIL } from "@/api/application/TowerScan";
import type { DataTableColumns } from "naive-ui";

interface MaterialItem {
    id: string;
    materialCode: string;
    composeQuantity: string;
    composeWeight: string;
    mainFlag: number;
    [key: string]: any;
}

interface DetailData {
    projectName: string;
    contractNumber: string;
    typeName: string;
    mainMaterialCode: string;
    composeQuantity: string;
    materialList: MaterialItem[];
    [key: string]: any;
}

export default defineComponent({
    name: "TowerScanEngineeringProcessesComponentItem",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        const onClose = () => {
            changeModalShow(false);
        };

        const loading = ref(false);
        const detailData = ref<DetailData>({
            projectName: "",
            contractNumber: "",
            typeName: "",
            mainMaterialCode: "",
            composeQuantity: "",
            materialList: []
        });

        // 获取配方详情
        const getDetailData = () => {
            if (!props.configData.id) return;

            loading.value = true;
            GET_IRON_COMPONENT_DETAIL({ id: props.configData.id })
                .then((res) => {
                    loading.value = false;
                    if (res.data.code === 0) {
                        detailData.value = res.data.data;
                    }
                })
                .catch(() => {
                    loading.value = false;
                });
        };

        // 表格列配置
        const tableColumns = ref<DataTableColumns<MaterialItem>>([
            {
                title: "零件号",
                key: "materialCode",
                align: "center",
                render: (row) => row.materialCode ?? "/"
            },
            {
                title: "材质",
                key: "materialQuality",
                align: "center",
                render: (row) => row.materialQuality ?? "/"
            },
            {
                title: "单基端数量",
                key: "singleBaseQuantity",
                align: "center",
                render: (row) => row.singleBaseQuantity ?? "/"
            },
            {
                title: "多基段数量",
                key: "multiBaseQuantity",
                align: "center",
                render: (row) => row.multiBaseQuantity ?? "/"
            },
            {
                title: "需求数量",
                key: "composeQuantity",
                align: "center",
                render: (row) => row.composeQuantity ?? "/"
            },
            {
                title: "是否主件",
                key: "mainFlag",
                align: "center",
                render: (row) => {
                    if (row.mainFlag === 1) {
                        return <n-text type="info">是</n-text>;
                    } else {
                        return <n-text>否</n-text>;
                    }
                }
            }
        ]);

        watch(
            () => show.value,
            (val) => {
                if (val) {
                    getDetailData();
                }
            }
        );

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card
                    title={`主件${detailData.value.mainMaterialCode ?? "/"}配方清单`}
                    class="w-1200px"
                    closable
                    onClose={onClose}
                >
                    <n-spin show={loading.value}>
                        <n-form label-placement="left" label-width="auto">
                            <n-grid cols={12} x-gap={16}>
                                <n-form-item-gi required span={4} label="工程名称：">
                                    {detailData.value.projectName ?? "/"}
                                </n-form-item-gi>
                                <n-form-item-gi required span={4} label="工程简称：">
                                    {detailData.value.projectAs ?? "/"}
                                </n-form-item-gi>
                                <n-form-item-gi required span={4} label="合同号：">
                                    {detailData.value.contractNumber ?? "/"}
                                </n-form-item-gi>
                                <n-form-item-gi required span={4} label="塔型：">
                                    {detailData.value.typeName ?? "/"}
                                </n-form-item-gi>
                                <n-form-item-gi required span={4} label="主件号：">
                                    <n-text type="info">{detailData.value.mainMaterialCode ?? "/"}</n-text>
                                </n-form-item-gi>
                                <n-form-item-gi required span={4} label="组装需求数：">
                                    {detailData.value.composeQuantity ?? "/"}
                                </n-form-item-gi>
                            </n-grid>
                        </n-form>

                        <n-data-table
                            columns={tableColumns.value}
                            data={detailData.value.materialList}
                            loading={loading.value}
                            row-key={(row: MaterialItem) => row.id}
                            single-line={false}
                            bordered
                            striped
                        />
                    </n-spin>
                </n-card>
            </n-modal>
        );
    }
});
