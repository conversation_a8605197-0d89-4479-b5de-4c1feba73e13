import { computed, defineComponent, ref, watchEffect } from "vue";
import { GET_REPOSITORY_VIRTUAL_STOREROOM_DETAIL } from "@/api/application/repository";
import type { DataTableColumns } from "naive-ui";

export default defineComponent({
    name: "RepositoryVirtualDetail",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        const tableData = ref<any[]>([]);
        const tableColumns = ref<DataTableColumns<any>>([
            { title: "物品名称", key: "goodsName", align: "center" },
            { title: "规格型号", key: "goodsSpec", align: "center" },
            { title: "出库数量", key: "actualQuality", align: "center" },
            // 根据SYM-81从unitName改为goodsUnitName
            { title: "物品单位", key: "goodsUnitName", align: "center" },
            { title: "备注", key: "remark", align: "center" }
        ]);

        // 获取详情
        const detailData = ref<any>(null);

        const getDetail = async () => {
            await GET_REPOSITORY_VIRTUAL_STOREROOM_DETAIL({ id: props.configData.id }).then((res) => {
                if (res.data.code === 0) {
                    detailData.value = res.data.data;
                    tableData.value = detailData.value.enterRecord?.recordItemList ?? [];
                }
            });
        };

        const onClose = () => {
            changeModalShow(false);
            emit("refresh");
        };

        watchEffect(async () => {
            if (show.value) {
                if (props.configData.id) await getDetail();
            }
        });

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card title="查看在场虚拟库详情" class="w-800px" closable onClose={onClose}>
                    <n-form label-placement="left" label-width="auto">
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi required span={6} label="入库单号">
                                {detailData.value?.enterRecordId ?? "/"}
                            </n-form-item-gi>
                            <n-form-item-gi required span={6} label="申请人">
                                {detailData.value?.enterRecord?.applyByName ?? "/"}
                            </n-form-item-gi>
                            <n-form-item-gi required span={6} label="申请时间">
                                {detailData.value?.enterRecord?.applyTime ?? "/"}
                            </n-form-item-gi>
                            <n-form-item-gi required span={6} label="核对人">
                                {detailData.value?.enterRecord?.confirmByName ?? "/"}
                            </n-form-item-gi>
                            <n-form-item-gi required span={6} label="核对时间">
                                {detailData.value?.enterRecord?.confirmTime ?? "/"}
                            </n-form-item-gi>
                            <n-form-item-gi span={12}>
                                <div class="w-100%">
                                    <div class="text-18px">物品清单</div>
                                    <div class="mt">
                                        <n-data-table
                                            columns={tableColumns.value}
                                            data={tableData.value}
                                            single-line={false}
                                            bordered
                                            striped
                                        />
                                    </div>
                                </div>
                            </n-form-item-gi>
                        </n-grid>
                    </n-form>
                </n-card>
            </n-modal>
        );
    }
});
