<script lang="ts" setup>
import { computed, ref, watch } from "vue";
import { cloneDeep } from "lodash-es";
import VueDraggable from "vuedraggable";
import type { FormGeneratorProps } from "@/components/FormGenerator";
import { FormComponents, FormItem, FormPreview, FormProps } from "@/components/FormGenerator/src/components";
import { DynamicIcon } from "@/components/DynamicIcon";
import { useStoreFormGenerator } from "@/store";
import { useFormOptions } from "@/hooks";

let storeFormGenerator = useStoreFormGenerator();
let { getDynamicTableDataSource } = useFormOptions();

let props = withDefaults(defineProps<{ value: FormGeneratorProps[] }>(), {});
let emit = defineEmits(["update:value", "submit"]);

// 组件列表
let componentList = computed({
    get: () => props.value,
    set: (val) => emit("update:value", val)
});

// 设置NForm属性
let setNFormProps = () => ({ labelPlacement: "left" });

// 选中组件
let currentIndex = ref<number | null>(null);

// 监听组件列表变化，为新的dynamicTable设置数据源
watch(
    componentList,
    async (newList, oldList) => {
        if (newList.length > (oldList?.length || 0)) {
            // 有新组件添加
            let newComponents = newList.slice(oldList?.length || 0);
            for (let component of newComponents) {
                if (
                    component.type === "dynamicTable" &&
                    (!component.dynamicTableDataSource || component.dynamicTableDataSource.length === 0)
                ) {
                    try {
                        let dynamicTableDataSource = await getDynamicTableDataSource();
                        component.dynamicTableDataSource = dynamicTableDataSource;
                    } catch (error) {
                        console.error("设置dynamicTable数据源失败:", error);
                    }
                }
            }
        }
    },
    { deep: true }
);

// 实时赋值
watch(
    () => storeFormGenerator.getComponentProps,
    (val) => {
        if (currentIndex.value || currentIndex.value === 0) componentList.value[currentIndex.value] = val;
    },
    { deep: true }
);

// 设置当前选中组件index和props
let onSelectComponent = (index: number) => {
    currentIndex.value = index;
    storeFormGenerator.setComponentProps(cloneDeep(componentList.value[index]));
};

// 选择组件
let onChooseComponent = (e: any) => onSelectComponent(e.oldIndex);

// 组件移动结束
let onMovedComponent = (e: any) => onSelectComponent(e.newIndex);

// 新增组件
let onAddComponent = async (e: any) => {
    onSelectComponent(e.newIndex);

    // 如果是动态表格组件，自动设置数据源
    let newComponent = componentList.value[e.newIndex];
    if (newComponent.type === "dynamicTable" && !newComponent.dynamicTableDataSource?.length) {
        let dynamicTableDataSource = await getDynamicTableDataSource();
        newComponent.dynamicTableDataSource = dynamicTableDataSource;
    }
};

// 复制组件
let onCopyComponent = async (element: any, index: number) => {
    let nowTime = new Date().getTime();
    let copiedElement = cloneDeep({ ...element, id: "fv" + nowTime, modelName: "fv" + nowTime });

    // 如果是动态表格组件，确保数据源正确设置
    if (copiedElement.type === "dynamicTable" && !copiedElement.dynamicTableDataSource?.length) {
        let dynamicTableDataSource = await getDynamicTableDataSource();
        copiedElement.dynamicTableDataSource = dynamicTableDataSource;
    }

    componentList.value.splice(index + 1, 0, copiedElement);
};

// 删除单个组件或者清空所有组件
let onDeleteComponent = (element?: any) => {
    currentIndex.value = null;
    storeFormGenerator.setComponentProps();
    if (element) {
        componentList.value = componentList.value.filter((item: any) => item.id !== element.id);
    } else {
        storeFormGenerator.setFormConfigs();
        componentList.value = [];
    }
};

// 预览表单
let previewShow = ref(false);

let onPreview = () => {
    if (Object.keys(componentList.value).length === 0) return window.$message.error("请先添加组件");
    previewShow.value = true;
};

// 清空组件
let onClearAll = () => {
    if (componentList.value && componentList.value.length > 0) {
        window.$dialog.warning({
            title: "提示",
            content: "确定清空全部组件吗？",
            positiveText: "清空",
            negativeText: "取消",
            onPositiveClick: () => onDeleteComponent()
        });
    } else window.$message.warning("当前没有组件，无需清空!");
};

let onSubmit = () => {
    emit("submit");
};

defineExpose({
    onDeleteComponent
});
</script>

<template>
    <n-element class="form-generator p-20px" tag="div">
        <div class="flex">
            <form-components class="flex-fixed-300" />
            <n-card class="ml mr" content-style="padding:0" hoverable>
                <div>
                    <div
                        class="flex-y-center box-border h-60px pl-20px pr-20px border-b-1px border-[var(--n-border-color)]"
                    >
                        <div class="text-20px">排版</div>
                        <n-space class="ml-a">
                            <n-button type="success" @click="onSubmit">下一步</n-button>
                            <n-button type="primary" @click="onPreview">预览</n-button>
                            <n-button type="error" @click="onClearAll">清空</n-button>
                        </n-space>
                    </div>
                    <div class="pb-20px" style="height: calc(95vh - 183px)">
                        <n-scrollbar>
                            <div class="p-20px pb-0">
                                <VueDraggable
                                    v-model="componentList"
                                    :animation="100"
                                    :component-data="setNFormProps()"
                                    :forceFallback="true"
                                    chosen-class="draggable-chosen"
                                    class="draggable-default grid grid-cols-12 gap-16px pb-50px"
                                    clone-class="draggable-clone"
                                    drag-class="draggable-drag"
                                    fallbackTolerance="5"
                                    ghost-class="draggable-ghost"
                                    group="generator"
                                    item-key="index"
                                    tag="n-form"
                                    @add="onAddComponent"
                                    @choose="onChooseComponent"
                                    @end="onMovedComponent"
                                >
                                    <template #item="{ element, index }">
                                        <div
                                            :class="index === currentIndex ? 'b-solid' : 'b-dashed'"
                                            :style="`grid-column: span ${element.row} / span 12`"
                                            class="relative p-2 pb-0 b-1px b-color-[var(--primary-color)]"
                                        >
                                            <div
                                                :class="index !== currentIndex && 'hidden'"
                                                class="flex-center absolute bottom--11px right--11px"
                                            >
                                                <n-button
                                                    class="rd-0"
                                                    size="tiny"
                                                    type="primary"
                                                    @click="onCopyComponent(element, index)"
                                                >
                                                    <template #icon>
                                                        <dynamic-icon icon="CopyOutlined" size="18" />
                                                    </template>
                                                </n-button>
                                                <n-button
                                                    class="ml-1 rd-0"
                                                    size="tiny"
                                                    type="error"
                                                    @click="onDeleteComponent(element)"
                                                >
                                                    <template #icon>
                                                        <dynamic-icon icon="DeleteOutlined" size="18" />
                                                    </template>
                                                </n-button>
                                            </div>
                                            <n-form-item
                                                :label="element.label"
                                                :label-width="element.labelWidth"
                                                :show-label="element.showLabel"
                                                :show-require-mark="element.showRequireMark"
                                            >
                                                <form-item
                                                    v-model:element="componentList[index]"
                                                    form-key="formGenerator"
                                                />
                                            </n-form-item>
                                        </div>
                                    </template>
                                </VueDraggable>
                            </div>
                        </n-scrollbar>
                    </div>
                </div>
                <form-preview v-model="componentList" v-model:show="previewShow" />
            </n-card>
            <form-props class="flex-fixed-300" />
        </div>
    </n-element>
</template>
