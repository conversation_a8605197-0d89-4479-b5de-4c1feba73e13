import { defineComponent, h, onMounted, reactive, ref } from "vue";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import { GET_REPOSITORY_INVENTORY_TASK_LIST } from "@/api/application/repository";
import { TableActions } from "@/components/TableActions";
import RepositoryCheckMoveCheckEdit from "./RepositoryCheckMoveCheckEdit";

export default defineComponent({
    name: "RepositoryCheckMoveCheckList",
    setup(props, { expose }) {
        // 搜索项
        const searchConfig = ref<TableSearchbarConfig>([{ label: "盘库状态", prop: "inventoryState", type: "select" }]);
        const searchOptions = ref<TableSearchbarOptions>({
            inventoryState: [
                { label: "未开始", value: 1 },
                { label: "进行中", value: 2 },
                { label: "已暂停", value: 3 },
                { label: "已完成", value: 4 }
            ]
        });
        const getSearchOptions = async () => {};
        const searchForm = ref<TableSearchbarData>({
            inventoryState: null
        });
        const onSearch = () => {
            tablePagination.page = 1;
            tablePagination.pageSize = 10;
            getTableData();
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            { title: "盘库任务单", key: "id", align: "center" },
            { title: "执行人", key: "inventoryByName", align: "center" },
            { title: "计划盘点完成时间", key: "planTime", align: "center" },
            { title: "实际盘点完成时间", key: "finishTime", align: "center" },
            { title: "备注", key: "remark", align: "center" },
            {
                title: "操作",
                key: "actions",
                align: "center",
                width: 120,
                render(row) {
                    return h(TableActions, {
                        type: "button",
                        buttonActions: [
                            {
                                label: "编辑盘点任务",
                                tertiary: true,
                                onClick: () => openEditModal(row)
                            }
                        ]
                    });
                }
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_REPOSITORY_INVENTORY_TASK_LIST({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                ...searchForm.value
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        // 编辑弹窗
        const editModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const openEditModal = (row: RowProps) => {
            editModal.value.show = true;
            editModal.value.configData = row;
        };

        onMounted(async () => {
            await getSearchOptions();
            getTableData();
        });

        expose({
            refresh: getTableData
        });

        return () => (
            <div class="repository-page">
                <n-card>
                    <TableSearchbar
                        form={searchForm.value}
                        config={searchConfig.value}
                        options={searchOptions.value}
                        onSearch={onSearch}
                    />
                </n-card>
                <n-card class="mt">
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        pagination={tablePagination}
                        row-key={tableRowKey}
                        single-line={false}
                        bordered
                        remote
                        striped
                        onUpdate:checked-row-keys={changeTableSelection}
                    />
                </n-card>
                <RepositoryCheckMoveCheckEdit
                    v-model:show={editModal.value.show}
                    config-data={editModal.value.configData}
                    onRefresh={getTableData}
                />
            </div>
        );
    }
});
