import { defineComponent, reactive, ref, watchEffect } from "vue";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import { GET_INJECTION_QUANTITY_HIS_PAGE_LIST } from "@/api/application/plasticMes";
import { TableActions } from "@/components/TableActions";
import PlasticMesQualityTestingSecondaryVerificationDetail from "@/views/Container/Application/PlasticMes/QualityTesting/IMSecondaryVerification/PlasticMesQualityTestingIMSecondaryVerificationDetail";

export default defineComponent({
    name: "PlasticMesIMProductionScheduleSecondaryVerificationRecord",
    props: {
        scheduleDetailId: { type: String }
    },
    setup(props) {
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { title: "订单号", key: "orderCode", align: "center" },
            { title: "排产单号", key: "scheduleId", align: "center" },
            {
                title: "生产机器",
                key: "machineName",
                align: "center",
                render: (row) => {
                    return <n-text type="info">{row.machineName}</n-text>;
                }
            },
            {
                title: "生产班组",
                key: "workGroupName",
                align: "center",
                render: (row) => {
                    return <n-text type="info">{row.workGroupName}</n-text>;
                }
            },
            {
                title: "生产规格",
                key: "moldingSpec",
                align: "center",
                render: (row) => {
                    return <n-text type="info">{row.moldingSpec}</n-text>;
                }
            },
            { title: "上报质检工", key: "createByName", align: "center" },
            { title: "二次核对人", key: "secondCheckByName", align: "center" },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 120,
                render: (row) => {
                    return (
                        <TableActions
                            type="button"
                            buttonActions={[
                                {
                                    label: "查看详情",
                                    tertiary: true,
                                    type: "primary",
                                    onClick: () => openDetailModal(row)
                                }
                            ]}
                        />
                    );
                }
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_INJECTION_QUANTITY_HIS_PAGE_LIST({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                scheduleDetailId: props.scheduleDetailId,
                secondCheckState: 1 // 只查询已核对的记录
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        // 详情弹窗
        const detailModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const openDetailModal = (row: RowProps) => {
            detailModal.value = { show: true, configData: row };
        };

        watchEffect(async () => {
            if (props.scheduleDetailId) getTableData();
        });

        return () => (
            <div>
                <n-data-table
                    columns={tableColumns.value}
                    data={tableData.value}
                    loading={tableLoading.value}
                    pagination={tablePagination}
                    row-key={tableRowKey}
                    single-line={false}
                    bordered
                    remote
                    striped
                    max-height={250}
                    onUpdate:checked-row-keys={changeTableSelection}
                />
                <PlasticMesQualityTestingSecondaryVerificationDetail
                    v-model:show={detailModal.value.show}
                    configData={detailModal.value.configData}
                />
            </div>
        );
    }
});
