<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-800px" closable title="延期到款" @close="closeModal">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-grid :cols="24" :x-gap="16">
                        <n-form-item-gi :span="12" label="所属订单">
                            <div>{{ configData.projectNumber }}</div>
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="预计到款时间">
                            <div>{{ configData.predelayTime }}</div>
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="预计到款金额">
                            <div>{{ configData.preReturnAmount }}</div>
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="延期到款时间" path="delayTime">
                            <n-date-picker
                                v-model:formatted-value="formData.delayTime"
                                class="w-100%"
                                clearable
                                placeholder="请选择延期到款时间"
                                type="datetime"
                                value-format="yyyy-MM-dd HH:mm:ss"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="24">
                            <n-space>
                                <n-button type="primary" @click="onSubmit">提交</n-button>
                                <n-button @click="closeModal">取消</n-button>
                            </n-space>
                        </n-form-item-gi>
                    </n-grid>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { DELAY_PAYMENT_RETURN } from "@/api/application/power";

let props = defineProps({
    show: { type: Boolean, default: false },
    configData: { type: Object as PropType<any> }
});

let emits = defineEmits(["update:show", "refresh"]);

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = computed(() => {
    return {
        delayTime: [{ required: true, message: "请选择延期到款时间", trigger: ["blur", "change"] }]
    };
});

// 表单数据
interface FormDataProps {
    [key: string]: any;
}

let initFormData: FormDataProps = {
    delayTime: null
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    emits("update:show", false);
};

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    DELAY_PAYMENT_RETURN({
        id: props.configData.id,
        ...formData.value
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("操作成功");
            closeModal();
            emits("refresh");
        }
    });
};
</script>
