<script lang="ts" setup>
import { computed, ref } from "vue";
import { UserSelector } from "@/components/UserSelector";

let props = withDefaults(
    defineProps<{
        value: Record<string, any>;
    }>(),
    {}
);

let emits = defineEmits(["update:value", "submit"]);

// 表单实例
let formRef = ref<any>(null);

// 表单校验
let formRules = {
    ccPersons: [{ required: false, message: "请输入抄送人", trigger: ["blur", "change"] }]
};

// 表单数据
interface FormDataProps {
    [key: string]: any;
}

let formData = computed<Record<string, any>>({
    get: () => props.value,
    set: (val) => emits("update:value", val)
});

let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors: any) => !!errors);
    if (validateError) return false;
    emits("submit");
};
</script>

<template>
    <div class="w-500px py-20px mx-a">
        <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="140px">
            <n-grid cols="12" x-gap="16">
                <n-form-item-gi :span="12" label="抄送人：" path="ccPersons">
                    <UserSelector
                        v-model:value="formData.ccPersons"
                        class="w-100%"
                        key-name="username"
                        placeholder="请选择抄送人"
                    />
                </n-form-item-gi>
            </n-grid>
        </n-form>
    </div>
</template>
