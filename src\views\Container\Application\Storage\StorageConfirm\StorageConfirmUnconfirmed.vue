<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <!--审批单-->
        <ProcessDetail v-model:show="processDetailModal.show" :config-data="processDetailModal.configData" />
        <!--确认单据-->
        <StorageConfirmModal
            v-model:show="confirmModal.show"
            :config-data="confirmModal.configData"
            @refresh="getTableData"
        />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from "vue";
import type { DataTableColumns } from "naive-ui";
import { NButton } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import { GET_STOCK_CHECK_TO_CONFIRM_PAGE_LIST } from "@/api/application/production";
import { TableActions } from "@/components/TableActions";
import { ProcessDetail } from "@/views/Container/Application/Process/components";
import { GET_OA_INSTANCE_FORM } from "@/api/application/oa";
import { StorageConfirmModal } from "../components";

interface RowProps {
    [key: string]: any;
}

onMounted(() => {
    getSearchOptions();
    getTableData();
});

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    {
        prop: "projectName",
        type: "input",
        label: "项目名称"
    }
]);

let searchOptions = ref<TableSearchbarOptions>({});

let searchForm = ref<TableSearchbarData>({
    projectName: ""
});

let getSearchOptions = () => {};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("poId");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "订单号",
        key: "pomNumber",
        align: "center"
    },
    {
        title: "项目名称",
        key: "projectName",
        align: "center"
    },
    {
        title: "交货日期",
        key: "deliveryDate",
        align: "center"
    },
    // {
    //     title: "审批单",
    //     key: "processInstanceId",
    //     align: "center",
    //     render(row) {
    //         return h(
    //             NButton,
    //             {
    //                 type: "primary",
    //                 text: true,
    //                 onClick: () => openProcessDetailModal(row.processInstanceId)
    //             },
    //             () => "点击查看"
    //         );
    //     }
    // },
    {
        title: "操作",
        key: "action",
        align: "center",
        width: 120,
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "确认单据",
                        tertiary: true,
                        type: "primary",
                        onClick: () => openConfirmModal(row)
                    }
                ]
            });
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_STOCK_CHECK_TO_CONFIRM_PAGE_LIST({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        ...searchForm.value
    }).then((res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data.records;
            tablePagination.itemCount = res.data.data.total;
            tableLoading.value = false;
        }
    });
};

// 搜索
let onSearch = () => {
    tablePagination.page = 1;
    tablePagination.pageSize = 10;
    getTableData();
};

// 查看审批单
let processDetailModal = ref<{ show: boolean; configData: Record<string, any> }>({
    show: false,
    configData: {}
});

let openProcessDetailModal = (id: string | number) => {
    GET_OA_INSTANCE_FORM({ processInstId: id }).then((res) => {
        if (res.data.code === 0) {
            processDetailModal.value.show = true;
            processDetailModal.value.configData = res.data.data;
        } else {
            window.$message.error("该审批单不存在");
        }
    });
};

// 确认单据
let confirmModal = ref<{ show: boolean; configData: UnKnownObject }>({
    show: false,
    configData: {}
});

let openConfirmModal = (row: RowProps) => {
    confirmModal.value = {
        show: true,
        configData: row
    };
};
</script>
