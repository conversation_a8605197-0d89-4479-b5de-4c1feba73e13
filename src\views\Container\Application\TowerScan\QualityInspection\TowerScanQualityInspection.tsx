import { defineComponent, ref } from "vue";
import TowerScanQualityInspectionPartList from "./TowerScanQualityInspectionPartList";
import TowerScanQualityInspectionComponentList from "./TowerScanQualityInspectionComponentList";

export default defineComponent({
    name: "TowerScanQualityInspection",
    setup() {
        // tab切换的当前值
        const activeTab = ref<string>("parts");

        return () => (
            <div class="tower-scan-quality-inspection">
                <n-card>
                    <n-tabs
                        animated
                        v-model:value={activeTab.value}
                        onUpdate:value={(value: string) => {
                            activeTab.value = value;
                        }}
                    >
                        <n-tab-pane name="parts" tab="零件质检记录">
                            <TowerScanQualityInspectionPartList />
                        </n-tab-pane>
                        <n-tab-pane name="assembly" tab="组装电焊质检记录">
                            <TowerScanQualityInspectionComponentList />
                        </n-tab-pane>
                    </n-tabs>
                </n-card>
            </div>
        );
    }
});
