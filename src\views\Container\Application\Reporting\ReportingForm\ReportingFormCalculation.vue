<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-space class="mb">
                <n-button secondary type="primary" @click="exportSorterAndFilterCsv">导出</n-button>
            </n-space>
            <n-data-table
                ref="tableRef"
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
            />
        </n-card>
    </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import type { DataTableColumns, DataTableInst } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import { GET_FINAL_ACCOUNTS_LIST, GET_FINAL_ACCOUNTS_PERSONS } from "@/api/application/reporting";

onMounted(() => {
    // getTableData();
    getSearchOptions();
});

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { prop: "startTime", type: "date", label: "开始日期", dateFormat: "yyyy-MM-dd" },
    { prop: "endTime", type: "date", label: "结束日期", dateFormat: "yyyy-MM-dd" },
    { prop: "userId", type: "select", label: "所属人员", span: 2, }
]);

let searchOptions = ref<TableSearchbarOptions>({
    userId: []
});

let searchForm = ref<TableSearchbarData>({
    startTime: null,
    endTime: null,
    userId: null
});

let getSearchOptions = () => {
    GET_FINAL_ACCOUNTS_PERSONS({}).then((res) => {
        searchOptions.value.userId = (res.data.data || []).map((item: any) => {
            return {
                label: (item.user_information?.true_name || "/") + " - " + (item.user_information?.company_name || "/"),
                value: item.name || ""
            };
        });
    });
};

// 数据列表
interface RowProps {
    [key: string]: any;
}

let { tableRowKey, tableData, tableLoading } = useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
  { title: "物料名称规格", key: "name", align: "center" },
  { title: "累计价格（元）", key: "actual_amount", align: "center" },
  { title: "实际累计用料", key: "actual_usage", align: "center" },
  { title: "物料单价（元）", key: "unit_price", align: "center" },
  { title: "用量/计价单位", key: "unit", align: "center" },
    {
        title: "所属人员",
        key: "ui_true_name",
        align: "center"
    },
    {
        title: "所属公司",
          key: "ui_company_name",
        align: "center"
    }
]);

let getTableData = () => {
    tableLoading.value = true;
    GET_FINAL_ACCOUNTS_LIST({
        ...searchForm.value,
        userId: searchForm.value.userId || ""
    }).then((res) => {
        tableData.value = (res.data.data || []).map((item: any) => {
            return {
                ...item,
                ui_true_name: item.user_information?.true_name || "",
                ui_company_name: item.user_information?.company_name || ""
            };
        });
        tableLoading.value = false;
    });
};

let tableRef = ref<DataTableInst>();

// 导出csv
let downloadCsv = (columns: any, dataList: any, fileName: any) => {
    let title = columns.map((item: any) => item.title);
    let keyArray = columns.map((item: any) => item.key);
    let str = [];
    str.push(title.join(",") + "\n");
    for (let i = 0; i < dataList.length; i++) {
        const temp = [];
        for (let j = 0; j < keyArray.length; j++) {
            temp.push(dataList[i][keyArray[j]]);
        }
        str.push(temp.join(",") + "\n");
    }
    let uri = "data:text/csv;charset=utf-8,\ufeff" + encodeURIComponent(str.join(""));
    let downloadLink = document.createElement("a");
    downloadLink.href = uri;
    downloadLink.download = fileName;
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
};

let exportSorterAndFilterCsv = () => {
    downloadCsv(tableColumns.value, tableData.value, "决算表.csv");
};

// 搜索
let onSearch = () => {
    if (searchForm.value.startTime && searchForm.value.endTime && searchForm.value.userId) {
        getTableData();
    } else {
        window.$message.error("请选择开始日期和结束日期以及所属人员");
    }
};
</script>
