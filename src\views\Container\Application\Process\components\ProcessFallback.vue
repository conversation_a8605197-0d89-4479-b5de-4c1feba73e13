<template>
    <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
        <n-card class="w-600px" closable title="回退审批节点" @close="closeModal">
            <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                <n-grid cols="12" x-gap="16">
                    <n-form-item-gi :span="12" label="当前节点">
                        {{ props.configData.name }}
                    </n-form-item-gi>
                    <n-form-item-gi :span="12" label="驳回节点" path="tarTaskId">
                        <n-select
                            v-model:value="formData.tarTaskId"
                            :options="tarTaskIdOptions"
                            clearable
                            filterable
                            label-field="name"
                            placeholder="请选择要驳回的节点"
                            value-field="id"
                        />
                    </n-form-item-gi>
                    <n-form-item-gi :span="12" label="驳回原因" path="rejectionReasons">
                        <n-input
                            v-model:value="formData.rejectionReasons"
                            class="w-100%"
                            clearable
                            placeholder="请输入驳回原因"
                        />
                    </n-form-item-gi>
                    <n-form-item-gi :span="12">
                        <n-space>
                            <n-button type="primary" @click="onSubmit">提交</n-button>
                            <n-button @click="closeModal">取消</n-button>
                        </n-space>
                    </n-form-item-gi>
                </n-grid>
            </n-form>
        </n-card>
    </n-modal>
</template>

<script lang="ts" setup>
import { computed, ref, watchEffect } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { GET_OA_INSTANCE_BY_BUSINESS_NUMBER, GET_OA_INSTANCE_FORM, WITHDRAW_TO_APPROVER } from "@/api/application/oa";

const props = withDefaults(defineProps<{ show: boolean; configData: UnKnownObject }>(), { show: () => false });

const emits = defineEmits(["update:show", "refresh"]);

const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

const changeModalShow = (show: boolean) => emits("update:show", show);

const closeModal = () => {
    changeModalShow(false);
    clearFrom();
};

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
const formRules = {
    tarTaskId: { required: true, message: "请选择要驳回的节点", trigger: ["blur", "change"] },
    rejectionReasons: { required: true, message: "请输入驳回原因", trigger: ["input", "blur"] }
};

// 表单数据
interface FormDataProps {
    [key: string]: any;
}

const initFormData: FormDataProps = {
    tarTaskId: null,
    rejectionReasons: null
};

const formData = ref(cloneDeep(initFormData));

const clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

const tarTaskIdOptions = ref<any[]>([]);

const getFormOptions = async () => {
    /*
     * 2025年6月10日19:44:08
     * 后台让businessNumber改成processInstId
     * 之前是通的
     */
    await GET_OA_INSTANCE_FORM({ processInstId: props.configData.processInstanceId }).then((res) => {
        tarTaskIdOptions.value = (res.data.data?.processHistoryVoList ?? []).filter(
            (item: any) => item.id !== props.configData.id
        );
    });
};

const onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;

    WITHDRAW_TO_APPROVER({
        taskId: props.configData.id,
        ...formData.value
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("操作成功");
            closeModal();
            emits("refresh");
        }
    });
};

watchEffect(async () => {
    if (show.value) {
        await getFormOptions();
    }
});
</script>
