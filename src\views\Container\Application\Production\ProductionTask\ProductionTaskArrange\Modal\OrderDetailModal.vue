<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-1000px" closable title="订单任务生产详情" @close="closeModal">
                <n-spin :show="loadingShow">
                    <n-element class="flex-y-center" tag="div">
                        <div class="text-16px font-bold">订单号：</div>
                        <div class="text-16px font-bold color-[var(--primary-color)]">{{ orderDetail.pomNumber }}</div>
                    </n-element>
                    <div class="flex-y-center mt-20px pb-20px b-b-1px b-b-solid b-[var(--n-border-color)]">
                        <n-grid :cols="24" :x-gap="16" :y-gap="16">
                            <n-grid-item :span="12" class="flex-y-center text-14px">
                                <div>项目名称：</div>
                                <div>{{ orderDetail.projectName }}</div>
                            </n-grid-item>
                            <n-grid-item :span="12" class="flex-y-center text-14px">
                                <div>订单生产状态：</div>
                                <n-text v-if="String(orderDetail.orderProductState) === '0'" type="error">
                                    未完成
                                </n-text>
                                <n-text v-else-if="String(orderDetail.orderProductState) === '1'" type="warning">
                                    生产中
                                </n-text>
                                <n-text v-else-if="String(orderDetail.orderProductState) === '2'" type="success">
                                    已完成
                                </n-text>
                                <n-text v-else type="default">未知</n-text>
                            </n-grid-item>
                            <n-grid-item :span="12" class="flex-y-center text-14px">
                                <div>交货日期：</div>
                                <n-text type="error">
                                    {{ orderDetail.deliveryDate }}
                                </n-text>
                            </n-grid-item>
                            <n-grid-item :span="12" class="flex-y-center text-14px">
                                <div>负责销售：</div>
                                <div>{{ orderDetail.salesPersonName }}</div>
                            </n-grid-item>
                            <n-grid-item :span="24" class="flex-y-center text-14px">
                                <div>客户备注：</div>
                                <div>{{ orderDetail.customerRemarks || "暂无" }}</div>
                            </n-grid-item>
                        </n-grid>
                        <n-grid :cols="24" :x-gap="16" :y-gap="16">
                            <n-grid-item :span="6" class="text-center">
                                <n-element class="text-20px color-[var(--primary-color)]" tag="div">
                                    {{ orderDetail.materialCount ?? 0 }}
                                </n-element>
                                <div>累计领料数量</div>
                            </n-grid-item>
                            <n-grid-item :span="6" class="text-center">
                                <n-element class="text-20px color-[var(--primary-color)]" tag="div">
                                    {{ orderDetail.underWayProdCount ?? 0 }}
                                </n-element>
                                <div>进行中的生产</div>
                            </n-grid-item>
                            <n-grid-item :span="6" class="text-center">
                                <n-element class="text-20px color-[var(--primary-color)]" tag="div">
                                    {{ orderDetail.finishedProdCount ?? 0 }}
                                </n-element>
                                <div>完成的生产</div>
                            </n-grid-item>
                            <n-grid-item :span="6" class="text-center">
                                <n-element class="text-20px color-[var(--primary-color)]" tag="div">
                                    {{ orderDetail.returnOfMaterialCount ?? 0 }}
                                </n-element>
                                <div>退料数量</div>
                            </n-grid-item>
                        </n-grid>
                    </div>
                    <div>
                        <n-element class="flex-y-center gap-10px mt-20px" tag="div">
                            <div
                                v-for="(item, index) in orderDetail.porderContractTypeList"
                                :class="[
                                    'px-10px py-5px cursor-pointer rd-5px',
                                    specificationActive === index && 'color-[#fff]! bg-[var(--primary-color)]!'
                                ]"
                                @click="changeSpecificationActive(index)"
                            >
                                {{ item.specification }}
                            </div>
                        </n-element>
                        <template v-for="(item, index) in orderDetail.porderContractTypeList">
                            <n-tabs
                                v-if="specificationActive === index"
                                v-model:value="infoTabActive"
                                class="mt-10px"
                                type="line"
                            >
                                <n-tab-pane name="概述">
                                    <n-grid class="py-20px" cols="24" x-gap="20" y-gap="20">
                                        <n-grid-item :span="8" class="text-14px">
                                            客户需求数量：
                                            <n-text type="default">{{ item.needCount ?? 0 }}</n-text>
                                        </n-grid-item>
                                        <n-grid-item :span="8" class="text-14px">
                                            生产中的数量：
                                            <n-text type="warning">{{ item.inProdCount ?? 0 }}</n-text>
                                        </n-grid-item>
                                        <n-grid-item :span="8" class="text-14px">
                                            无需生产数量：
                                            <n-text type="default">{{ item.noProdCount ?? 0 }}</n-text>
                                        </n-grid-item>
                                        <n-grid-item :span="8" class="text-14px">
                                            已生产总数：
                                            <n-text type="default">{{ item.alreadyProdCount ?? 0 }}</n-text>
                                        </n-grid-item>
                                        <n-grid-item :span="8" class="text-14px">
                                            交付产品数量：
                                            <n-text type="success">{{ item.deliveredProdCount ?? 0 }}</n-text>
                                        </n-grid-item>
                                        <n-grid-item :span="8" class="text-14px">
                                            已生产废品数量：
                                            <n-text type="error">{{ item.gunkProdCount ?? 0 }}</n-text>
                                        </n-grid-item>
                                        <n-grid-item :span="8" class="text-14px">
                                            让步接收数量：
                                            <n-text type="default">{{ item.concessionsReceivedCount ?? 0 }}</n-text>
                                        </n-grid-item>
                                    </n-grid>
                                </n-tab-pane>
                                <n-tab-pane name="技术配方">
                                    <div v-if="item.porderFormula">
                                        <n-grid class="pt-10px pb-20px" cols="24" x-gap="20" y-gap="20">
                                            <n-grid-item :span="8" class="text-14px">
                                                反馈人：
                                                <n-element class="color-[var(--primary-color)]" tag="span">
                                                    {{ item.porderFormula.formulaCheckBy || "暂无" }}
                                                </n-element>
                                            </n-grid-item>
                                            <n-grid-item :span="8" class="text-14px">
                                                反馈时间：
                                                <n-text type="default">
                                                    {{ item.porderFormula.formulaCheckTime || "暂无" }}
                                                </n-text>
                                            </n-grid-item>
                                        </n-grid>
                                        <DynamicTable
                                            v-model:header="getHeaderAndValue(item.porderFormula).header"
                                            v-model:value="getHeaderAndValue(item.porderFormula).value"
                                            :addable="false"
                                            :data-source="dynamicTableDataSource"
                                            :deletable="false"
                                            :headerConfigurable="false"
                                            class="w-100%"
                                            disabled
                                        />
                                    </div>
                                </n-tab-pane>
                                <n-tab-pane name="仓库反馈记录">
                                    <n-table
                                        v-if="item.pstoreCheckHisList.length > 0"
                                        :single-line="false"
                                        class="mt text-center"
                                    >
                                        <thead>
                                            <tr>
                                                <th>仓库成品数</th>
                                                <th>原材料满足情况</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="citem in item.pstoreCheckHisList">
                                                <td>{{ citem.stockCount }}</td>
                                                <td>
                                                    <n-text v-if="String(item.materialIsEnough) === '1'" type="success">
                                                        满足
                                                    </n-text>
                                                    <template v-else>
                                                        <n-text type="error">不满足</n-text>
                                                    </template>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </n-table>
                                    <n-result v-else class="py-50px" status="404" title="暂无数据" />
                                </n-tab-pane>
                                <n-tab-pane name="购买申请记录">
                                    <n-table
                                        v-if="item.procurementMaterialList.length > 0"
                                        :single-line="false"
                                        class="mt text-center"
                                    >
                                        <thead>
                                            <tr>
                                                <th>采购类型</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="citem in item.procurementMaterialList">
                                                <td>
                                                    {{ citem.procurementType === 1 ? "原辅材料采购" : "委外加工采购" }}
                                                </td>
                                            </tr>
                                        </tbody>
                                    </n-table>
                                    <n-result v-else class="py-50px" status="404" title="暂无数据" />
                                </n-tab-pane>
                                <n-tab-pane name="领料记录">
                                    <n-table
                                        v-if="item.pmaterialApplList.length > 0"
                                        :single-line="false"
                                        class="mt text-center"
                                    >
                                        <thead>
                                            <tr>
                                                <th>发生时间</th>
                                                <th>审批单</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="citem in item.pmaterialApplList">
                                                <td>{{ citem.createTime }}</td>
                                                <td>
                                                    <n-element
                                                        class="cursor-pointer color-[var(--primary-color)]"
                                                        @click="openProcessDetailModal(citem)"
                                                    >
                                                        点击查看
                                                    </n-element>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </n-table>
                                    <n-result v-else class="py-50px" status="404" title="暂无数据" />
                                </n-tab-pane>
                                <n-tab-pane name="生产排单记录">
                                    <n-table
                                        v-if="item.productReqDetailList.length > 0"
                                        :single-line="false"
                                        class="mt text-center"
                                    >
                                        <thead>
                                            <tr>
                                                <th>生产任务单号</th>
                                                <th>排产时间</th>
                                                <th>排产数量</th>
                                                <th>排产线路</th>
                                                <th>详情</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="citem in item.productReqDetailList">
                                                <td>{{ citem.id }}</td>
                                                <td>{{ citem.prodScheDate }}</td>
                                                <td>{{ citem.prodCount }}</td>
                                                <td>{{ citem.prodLineName }}</td>
                                                <td>
                                                    <n-button text type="primary" @click="openDetailModal(citem)">
                                                        点击查看
                                                    </n-button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </n-table>
                                    <n-result v-else class="py-50px" status="404" title="暂无数据" />
                                </n-tab-pane>
                            </n-tabs>
                        </template>
                    </div>
                </n-spin>
            </n-card>
        </n-modal>
        <!--领料流程表单-->
        <ProcessDetail v-model:show="processDetailModal.show" :config-data="processDetailModal.configData" />
        <!--查看详情-->
        <DetailModal v-model:show="detailModal.show" :config-data="detailModal.configData" />
    </div>
</template>

<script lang="ts" setup>
import { ref, watchEffect } from "vue";
import { GET_PRODUCTION_ORDER_DETAIL } from "@/api/application/production";
import { DynamicTable } from "@/components/Dynamic";
import { useFormulaTable } from "@/hooks";
import { GET_STAFF_BY_USERNAMES } from "@/api/permission";
import { isJSON } from "@/utils/tools";
import { ProcessDetail } from "@/views/Container/Application/Process/components";
import { DetailModal } from "@/views/Container/Application/Production/ProductionTask/ProductionTaskList/Modal";

let props = withDefaults(defineProps<{ show: Boolean; configData: UnKnownObject }>(), {
    show: () => false
});

let emits = defineEmits(["update:show", "refresh"]);

// 动态表格数据
let { dynamicTableDataSource } = useFormulaTable();

// 订单详情
let orderDetail = ref<Record<string, any>>({});

let getOrderDetail = async () => {
    await GET_PRODUCTION_ORDER_DETAIL({ poId: props.configData.poId }).then((res) => {
        if (res.data.code === 0) {
            orderDetail.value = res.data.data;
            if (orderDetail.value.salesPerson) {
                GET_STAFF_BY_USERNAMES({ usernames: orderDetail.value.salesPerson }).then((res) => {
                    if (res.data.code === 0 && res.data.data?.[0]?.trueName) {
                        orderDetail.value.salesPersonName = res.data.data[0].trueName;
                    }
                });
            }
        }
    });
};

// 剥离header和value
let getHeaderAndValue = (i: any) => (isJSON(i.chargerSheet) ? JSON.parse(i.chargerSheet) : { header: [], value: [] });

// 规格型号tab选中
let specificationActive = ref<string | number>(0);

let changeSpecificationActive = (index: string | number) => (specificationActive.value = index);

// 信息tab
let infoTabActive = ref("概述");

let loadingShow = ref(false);

watchEffect(() => {
    if (props.show && props.configData) {
        loadingShow.value = true;
        infoTabActive.value = "概述";
        specificationActive.value = 0;
        getOrderDetail();
        loadingShow.value = false;
    }
});

// 关闭弹窗
let closeModal = () => emits("update:show", false);

// 领料流程流程弹窗
let processDetailModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });
let openProcessDetailModal = (row: UnKnownObject) => (processDetailModal.value = { show: true, configData: row });

// 查看详情
let detailModal = ref<{ show: boolean; configData: Record<string, any> }>({ show: false, configData: {} });

let openDetailModal = (row: any) => {
    detailModal.value = {
        show: true,
        configData: row
    };
    console.log(111, detailModal.value);
};
</script>
