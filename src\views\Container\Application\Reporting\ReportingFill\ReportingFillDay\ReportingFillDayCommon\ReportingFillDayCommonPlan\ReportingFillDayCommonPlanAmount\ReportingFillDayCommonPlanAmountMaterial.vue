<template>
    <div>
        <n-card>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                auto-search
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt">
            <n-space class="mb">
                <n-button secondary type="warning" @click="onExpand()">一键展开/收缩</n-button>
            </n-space>
            <n-tabs animated type="bar">
                <n-tab-pane name="原材料清单">
                    <n-card
                        v-if="
                            !materialData.manufactureFormulaSpecAmountList ||
                            materialData.manufactureFormulaSpecAmountList <= 0
                        "
                    >
                        <n-result class="py-50px" status="404" title="暂无数据" />
                    </n-card>
                    <n-collapse v-else v-model:expanded-names="expandedData.manufactureFormulaSpecAmountList">
                        <n-collapse-item
                            v-for="(item, index) in materialData.manufactureFormulaSpecAmountList"
                            :name="index"
                            :title="item.materialName"
                        >
                            <n-data-table
                                :columns="tableColumns"
                                :data="item.specAmountList"
                                :single-line="false"
                                bordered
                                remote
                                striped
                            />
                        </n-collapse-item>
                    </n-collapse>
                </n-tab-pane>
                <n-tab-pane name="独立原材料清单">
                    <n-card
                        v-if="!materialData.manufactureSpecAmountList || materialData.manufactureSpecAmountList <= 0"
                    >
                        <n-result class="py-50px" status="404" title="暂无数据" />
                    </n-card>
                    <n-collapse v-else v-model:expanded-names="expandedData.manufactureSpecAmountList">
                        <n-collapse-item
                            v-for="(item, index) in materialData.manufactureSpecAmountList"
                            :name="index"
                            :title="item.materialName"
                        >
                            <n-data-table
                                :columns="tableColumns"
                                :data="item.specAmountList"
                                :single-line="false"
                                bordered
                                remote
                                striped
                            />
                        </n-collapse-item>
                    </n-collapse>
                </n-tab-pane>
                <n-tab-pane name="半成品清单">
                    <n-data-table
                        :columns="semiTableColumns"
                        :data="materialData.manufactueSemiAmountList"
                        :single-line="false"
                        bordered
                        remote
                        striped
                    />
                </n-tab-pane>
            </n-tabs>
        </n-card>
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, ref } from "vue";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { GET_CONFIG_WORK_GROUP_LIST, GET_DAILY_PLAN_QUOTA_SPEC_AMOUNT_LIST } from "@/api/application/reporting";
import dayjs from "dayjs";
import type { DataTableColumns } from "naive-ui";
import { NButton } from "naive-ui";
import { useStoreReportingSearch } from "@/store";
import { UnitSelector } from "@/views/Container/Application/Reporting/components";
import { useDicts } from "@/hooks";
import { cloneDeep } from "lodash-es";

let storeReportingSearch = useStoreReportingSearch();

// 字典操作
let { dictLibs, getDictLibs } = useDicts();

let setDictLibs = async () => {
    let dictName = ["common_units"];
    await getDictLibs(dictName);
};

onMounted(async () => {
    await getWorkGroupIdOptions();
    await setDictLibs();
    getTableData();
});

let getWorkGroupIdOptions = async () => {
    await GET_CONFIG_WORK_GROUP_LIST({}).then((res) => {
        searchOptions.value.workGroupId = (res.data.data ?? []).map((item: any) => ({
            label: item.companyName + "-" + item.workshopName + "-" + item.groupName,
            value: item.id
        }));
        searchForm.value.workGroupId = searchOptions.value.workGroupId[0].value;
        searchForm.value.planDate = dayjs().format("YYYY-MM-DD");
        if (storeReportingSearch.getSearchForm.workGroupId) {
            searchForm.value.workGroupId = storeReportingSearch.getSearchForm.workGroupId;
        }
        if (storeReportingSearch.getSearchForm.planDate) {
            searchForm.value.planDate = storeReportingSearch.getSearchForm.planDate;
        }
    });
};

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { label: "班组", type: "select", prop: "workGroupId", span: 2 },
    { label: "日期筛选", type: "date", dateFormat: "yyyy-MM-dd", prop: "planDate" }
]);

let searchOptions = ref<TableSearchbarOptions>({ workGroupId: [] });

let searchForm = ref<TableSearchbarData>({ workGroupId: null, planDate: null });

let onSearch = () => {
    storeReportingSearch.setSearchForm({
        workGroupId: searchForm.value.workGroupId ?? null,
        planDate: searchForm.value.planDate ?? null
    });
    getTableData();
};

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        title: "规格型号",
        align: "center",
        key: "spec",
        render: (row) => {
            return `${row.materialName} - ${row.spec}`;
        }
    },
    // 2023年9月8日单位变更需要对接数据-已处理
    {
        title: "定额用量",
        align: "center",
        key: "planQuotaAmount",
        render: (row) => {
            return h("div", { class: "flex-center" }, [
                h("span", {}, row.planQuotaAmount),
                h(UnitSelector, {
                    type: "text",
                    value: row.amountUnit,
                    options: dictLibs["common_units"] ?? []
                })
            ]);
        }
    }
]);

let semiTableColumns = ref<DataTableColumns<RowProps>>([
    { title: "名称", align: "center", key: "semiPoleName" },
    { title: "规格型号", align: "center", key: "semiProductModel" },
    {
        title: "定额用量",
        align: "center",
        key: "amount",
        render: (row) => {
            return h("div", { class: "flex-center" }, [
                h("span", {}, row.amount),
                h(UnitSelector, {
                    type: "text",
                    value: row.semiUnit,
                    options: dictLibs["common_units"] ?? []
                })
            ]);
        }
    }
]);

let expandedData = ref<any>({
    manufactureFormulaSpecAmountList: [],
    manufactureSpecAmountList: []
});

let getTableData = () => {
    GET_DAILY_PLAN_QUOTA_SPEC_AMOUNT_LIST({
        ...searchForm.value
    }).then((res) => {
        materialData.value = res.data.data ?? {
            manufactureFormulaSpecAmountList: [],
            manufactureSpecAmountList: [],
            manufactueSemiAmountList: []
        };
        // 默认展开
        materialData.value.manufactureFormulaSpecAmountList.forEach((_: any, i: number) =>
            expandedData.value.manufactureFormulaSpecAmountList.push(i)
        );
        materialData.value.manufactureSpecAmountList.forEach((_: any, i: number) =>
            expandedData.value.manufactureSpecAmountList.push(i)
        );
        isExpand.value = true;
    });
};

// 展开/收缩
let isExpand = ref(false);

let onExpand = () => {
    if (isExpand.value) {
        expandedData.value.manufactureFormulaSpecAmountList = [];
        expandedData.value.manufactureSpecAmountList = [];
    } else {
        getTableData();
    }
    isExpand.value = !isExpand.value;
};

interface RowProps {
    [key: string]: any;
}

let materialData = ref<any>({
    manufactureFormulaSpecAmountList: [],
    manufactureSpecAmountList: [],
    manufactueSemiAmountList: []
});
</script>
