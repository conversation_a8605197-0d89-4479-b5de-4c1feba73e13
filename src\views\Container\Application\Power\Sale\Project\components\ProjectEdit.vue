<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card
                :title="configData.id ? '编辑项目' : '新增项目'"
                class="w-800px"
                closable
                @close="changeModalShow(false)"
            >
                <n-tabs type="line">
                    <n-tab-pane name="基础信息">
                        <n-form
                            ref="formRef"
                            :model="formData"
                            :rules="formRules"
                            label-placement="left"
                            label-width="auto"
                        >
                            <n-grid :cols="24" x-gap="16">
                                <n-form-item-gi :span="12" label="项目名称" path="projectName">
                                    <n-input
                                        v-model:value="formData.projectName"
                                        class="w-100%"
                                        clearable
                                        placeholder="请输入项目名称"
                                    />
                                </n-form-item-gi>
                                <n-form-item-gi :span="12" label="招投标项目编号" path="projectCode">
                                    <n-input
                                        v-model:value="formData.projectCode"
                                        :disabled="!!configData.id"
                                        class="w-100%"
                                        clearable
                                        placeholder="请输入招投标项目编号"
                                    />
                                </n-form-item-gi>
                                <n-form-item-gi :span="12" label="项目负责人" path="belongUsername">
                                    <UserSelector
                                        v-model:value="formData.projectLeader"
                                        :multiple="false"
                                        class="w-100%"
                                        clearable
                                        key-name="username"
                                        placeholder="请选择项目负责人"
                                    />
                                </n-form-item-gi>
                                <n-form-item-gi :span="12" label="项目责任单位" path="responsibleDeptId">
                                    <n-select
                                        v-model:value="formData.responsibleDeptId"
                                        :options="companyIdOptions"
                                        class="w-100%"
                                        clearable
                                        filterable
                                        label-field="company"
                                        placeholder="请选择项目责任单位"
                                        value-field="id"
                                    />
                                </n-form-item-gi>
                                <n-form-item-gi :span="12" label="信息首报人" path="firstRecorder">
                                    <UserSelector
                                        v-model:value="formData.firstRecorder"
                                        :multiple="false"
                                        class="w-100%"
                                        clearable
                                        key-name="username"
                                        placeholder="请选择信息首报人"
                                    />
                                </n-form-item-gi>
                                <n-form-item-gi :span="12" label="项目相关人" path="focusedUsers">
                                    <UserSelector
                                        v-model:value="formData.focusedUsers"
                                        :multiple="true"
                                        class="w-100%"
                                        clearable
                                        key-name="username"
                                        placeholder="请选择项目相关人"
                                    />
                                </n-form-item-gi>
                                <n-form-item-gi :span="12" label="所属客户" path="customerId">
                                    <n-select
                                        v-model:value="formData.customerId"
                                        :disabled="!!configData.id && hasCustomer"
                                        :options="customerOptions"
                                        clearable
                                        filterable
                                        label-field="customerName"
                                        placeholder="请选择所属客户"
                                        value-field="customerId"
                                    />
                                </n-form-item-gi>
                                <n-form-item-gi :span="12" label="无合同项目" path="nonContractFlag">
                                    <n-radio-group
                                        v-model:value="formData.nonContractFlag"
                                        @update:value="getNodeDirectorList"
                                    >
                                        <n-space>
                                            <n-radio :value="1">是</n-radio>
                                            <n-radio :value="0">否</n-radio>
                                        </n-space>
                                    </n-radio-group>
                                </n-form-item-gi>
                                <n-form-item-gi
                                    v-if="formData.nonContractFlag === 0"
                                    :span="12"
                                    label="需要投标"
                                    path="winBidFlag"
                                >
                                    <n-radio-group
                                        v-model:value="formData.winBidFlag"
                                        @update:value="getNodeDirectorList"
                                    >
                                        <n-space>
                                            <n-radio :value="1">是</n-radio>
                                            <n-radio :value="0">否</n-radio>
                                        </n-space>
                                    </n-radio-group>
                                </n-form-item-gi>
                                <n-form-item-gi :span="24" label="项目类型" path="projectType">
                                    <n-radio-group
                                        v-model:value="formData.projectType"
                                        :disabled="!!configData.id"
                                        name="projectType"
                                    >
                                        <n-radio
                                            v-for="item in dictLibs.project_type"
                                            :key="item.value"
                                            :value="item.value"
                                        >
                                            {{ item.label }}
                                        </n-radio>
                                    </n-radio-group>
                                </n-form-item-gi>
                                <n-form-item-gi :span="24">
                                    <n-space>
                                        <n-button type="primary" @click="onSubmit">提交</n-button>
                                        <n-button @click="changeModalShow(false)">取消</n-button>
                                    </n-space>
                                </n-form-item-gi>
                            </n-grid>
                        </n-form>
                    </n-tab-pane>
                    <n-tab-pane name="节点负责人设置">
                        <n-form v-if="nodeDirectorList.length" label-placement="left" label-width="auto">
                            <n-grid :cols="24" x-gap="16">
                                <template v-for="(item, index) in nodeDirectorList" :key="index">
                                    <n-form-item-gi
                                        v-if="item.nodeKey !== 'MatchContractOrderList'"
                                        :label="item.nodeName"
                                        :span="12"
                                    >
                                        <UserSelector
                                            v-model:value="item.nodeDirector"
                                            :multiple="false"
                                            class="w-100%"
                                            key-name="username"
                                            placeholder="请选择节点负责人"
                                        />
                                    </n-form-item-gi>
                                </template>

                                <n-form-item-gi :span="24">
                                    <n-space>
                                        <n-button type="primary" @click="onSubmit">提交</n-button>
                                        <n-button @click="changeModalShow(false)">取消</n-button>
                                    </n-space>
                                </n-form-item-gi>
                            </n-grid>
                        </n-form>
                        <n-result v-else class="py-66px" status="404" title="请先选择项目类型" />
                    </n-tab-pane>
                </n-tabs>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref, watchEffect } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import {
    GET_CUSTOMER_LIST,
    ADD_PROJECT,
    GET_NODES_BY_TYPE,
    GET_PROJECT_BY_ID,
    UPDATE_PROJECT
} from "@/api/application/power";
import { useDicts } from "@/hooks";
import { UserSelector } from "@/components/UserSelector";
import { GET_YD_COMPANY_LIST } from "@/api/permission";
import { useStoreBusiness } from "@/store";

let props = withDefaults(defineProps<{ show: boolean; configData: UnKnownObject }>(), {
    show: () => false
});

let emits = defineEmits(["update:show", "refresh"]);

let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

let changeModalShow = (show: boolean) => {
    emits("update:show", show);
    if (!show) {
        clearFrom();
    }
};

let { dictLibs, getDictLibs, dictValueToLabel } = useDicts();

let storeBusiness = useStoreBusiness();

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {
    projectName: { required: true, message: "请输入项目名称", trigger: ["input", "blur"] },
    customerId: { required: true, message: "请选择所属客户", trigger: ["blur", "change"] },
    firstRecorder: { required: true, message: "请选择信息首报人", trigger: ["blur", "change"] }
};

// 表单数据
interface FormDataProps {
    [key: string]: any;
}

let initFormData: FormDataProps = {
    customerId: null,
    projectName: null,
    projectCode: null,
    projectType: null,
    projectLeader: null,
    responsibleDeptId: null,
    firstRecorder: null,
    focusedUsers: null,
    nonContractFlag: 0,
    winBidFlag: 0
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
    nodeDirectorList.value = [];
};

let setDictLibs = async () => {
    let dictName = ["project_type"];
    await getDictLibs(dictName);
};

// 编辑时获取详情
watchEffect(async () => {
    if (props.show) {
        await getOptions();
        await getNodeDirectorList();
        props.configData.id && getDetail();
    }
});

// 是否存在客户
let hasCustomer = ref(false);

// 获取详情
let getDetail = () => {
    GET_PROJECT_BY_ID({ id: props.configData.id }).then((res) => {
        let rowItem: FormDataProps = res.data.data;
        hasCustomer.value = !!rowItem.customerId;
        formData.value = {
            customerId: rowItem.customerId,
            projectName: rowItem.projectName,
            projectCode: rowItem.projectCode,
            projectLeader: rowItem.projectLeader,
            responsibleDeptId: rowItem.responsibleDeptId,
            firstRecorder: rowItem.firstRecorder,
            focusedUsers: rowItem.focusedUsers,
            nonContractFlag: rowItem.nonContractFlag,
            winBidFlag: rowItem.winBidFlag,
            projectType: String(rowItem.projectType)
        };
        nodeDirectorList.value = rowItem.nodeDirectorList || [];
    });
};

// 表单选项
let customerOptions = ref<UnKnownObject[]>([]);
let companyIdOptions = ref<UnKnownObject[]>([]);

let getOptions = async () => {
    await setDictLibs();
    await GET_CUSTOMER_LIST({ current: 1, size: 1000 }).then(
        (res) => (customerOptions.value = res.data.data.records || [])
    );
    await GET_YD_COMPANY_LIST({}).then((res) => {
        companyIdOptions.value = res.data.data || [];
    });
    // 新增的时候默认选中第一个项目类型
    if (!props.configData.id) formData.value.projectType = dictLibs["project_type"][0].value || null;
};

/*
 * 根据项目类型获取流程节点列表
 */
interface NodeDirectorProps {
    nodeId: string | number;
    nodeName: string;
    nodeKey: string;
    formKey: string;
    nodeDirector: Nullable<string>;
}

let nodeDirectorList = ref<NodeDirectorProps[]>([]);

let getNodeDirectorList = async () => {
    await GET_NODES_BY_TYPE({
        nonContractFlag: formData.value.nonContractFlag,
        winBidFlag: formData.value.winBidFlag
    }).then((res) => {
        nodeDirectorList.value = (res.data.data || []).map((i: NodeDirectorProps) => ({ ...i, nodeDirector: null }));
    });
    autoFillNodeDirector();
};

// 自动填充节点负责人
let autoFillNodeDirector = () => {
    if (storeBusiness.projectNodeDirectors.length) {
        (storeBusiness.projectNodeDirectors || [])
            .filter((item) => item.nodeKey !== "MatchContractOrderList")
            .forEach((item) => {
                let node = nodeDirectorList.value.find((i) => i.nodeKey === item.nodeKey);
                if (node) node.nodeDirector = item.nodeDirector;
            });
    }
};

/*
 * 提交表单
 */
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    if (!props.configData.id) {
        ADD_PROJECT({
            ...formData.value,
            nodeDirectorList: nodeDirectorList.value
        }).then((res) => {
            if (res.data.code === 0) {
                storeBusiness.setProjectNodeDirectors(nodeDirectorList.value || []);
                window.$message.success("新增成功");
                changeModalShow(false);
                emits("refresh");
            }
        });
    } else {
        UPDATE_PROJECT({
            projectId: props.configData.id,
            ...formData.value,
            nodeDirectorList: nodeDirectorList.value
        }).then((res) => {
            if (res.data.code === 0) {
                storeBusiness.setProjectNodeDirectors(nodeDirectorList.value || []);
                window.$message.success("编辑成功");
                changeModalShow(false);
                emits("refresh");
            }
        });
    }
};
</script>
