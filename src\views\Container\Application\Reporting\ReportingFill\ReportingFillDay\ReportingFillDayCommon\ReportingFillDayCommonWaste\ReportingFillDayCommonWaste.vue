<template>
    <n-spin :show="loadingShow">
        <template #description>正在处理中，请耐心等候</template>
        <n-card>
            <table-searchbar
                auto-search
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
                @componentClick="onComponentClick"
            />
        </n-card>
        <n-card class="mt">
            <n-space class="mb">
                <n-button secondary type="success" @click="saveTableData">保存填写</n-button>
            </n-space>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
    </n-spin>
</template>

<script lang="ts" setup>
import { h, onMounted, ref } from "vue";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import { NButton, NDatePicker, NInput, NTooltip } from "naive-ui";
import { GET_CONFIG_WORK_GROUP_LIST, GET_WASTE_AMOUNT_LIST, SAVE_WASTE_AMOUNT_LIST } from "@/api/application/reporting";
import dayjs from "dayjs";
import { useStoreReportingSearch } from "@/store";
import { useThrottleFn } from "@vueuse/core";

let storeReportingSearch = useStoreReportingSearch();

let loadingShow = ref(false);

interface RowProps {
    [key: string]: any;
}

onMounted(async () => {
    await getWorkGroupIdOptions();
    getTableData();
});

let getWorkGroupIdOptions = async () => {
    await GET_CONFIG_WORK_GROUP_LIST({}).then((res) => {
        searchOptions.value.workGroupId = (res.data.data ?? []).map((item: any) => {
            return { label: item.companyName + "-" + item.workshopName + "-" + item.groupName, value: item.id };
        });
    });
    searchForm.value.workGroupId = searchOptions.value.workGroupId[0].value;
    searchForm.value.planDate = dayjs().format("YYYY-MM-DD");
    if (storeReportingSearch.getSearchForm.workGroupId) {
        searchForm.value.workGroupId = storeReportingSearch.getSearchForm.workGroupId;
    }
    if (storeReportingSearch.getSearchForm.planDate) {
        searchForm.value.planDate = storeReportingSearch.getSearchForm.planDate;
    }
};

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { label: "班组", type: "select", prop: "workGroupId", span: 2 },
    { label: "日期筛选", type: "date", dateFormat: "yyyy-MM-dd", prop: "planDate" }
]);

let searchOptions = ref<TableSearchbarOptions>({ workGroupId: [] });

let searchForm = ref<TableSearchbarData>({ workGroupId: null, planDate: null });

let onSearch = () => {
    storeReportingSearch.setSearchForm({
        workGroupId: searchForm.value.workGroupId ?? null,
        planDate: searchForm.value.planDate ?? null
    });
    getTableData();
};

// 搜索栏自动保存逻辑
let autoSave = useThrottleFn(async () => {
    let totalWasteAmount = 0;
    tableData.value.forEach((item: any) => (totalWasteAmount += Number(item.wasteAmount)));
    let params = {
        ...searchForm.value,
        id: planId.value,
        totalWasteAmount: totalWasteAmount,
        dailyAmountList: tableData.value
    };
    await SAVE_WASTE_AMOUNT_LIST({ ...params }).then((res) => {
        if (res.data.code === 0) window.$message.success("自动保存成功");
    });
}, 1000);

let onComponentClick = async (val: TableSearchbarData) => {
    // await autoSave();
};

let planId = ref<Nullable<string | number>>(null);

// 数据列表
let { tableRowKey, tableData, tableLoading, changeTableSelection } = useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    { type: "selection" },
    { title: "名称", align: "center", key: "poleName" },
    { title: "规格型号", align: "center", key: "productModel" },
    { title: "计划数量", align: "center", key: "planAmount" },
    { title: "完成数量", align: "center", key: "productAmount" },
    {
        title: "合格入库数",
        align: "center",
        key: "qualifiedAmount",
        render: (row) => {
            return row.qualifiedAmount ?? "暂无";
        }
    },
    {
        title: "废品数量",
        align: "center",
        key: "wasteAmount",
        render: (row) => {
            return h(
                NTooltip,
                {},
                {
                    trigger: () => {
                        return h(NInput, {
                            value: row.wasteAmount,
                            onUpdateValue: (v) => (row.wasteAmount = v),
                            onFocus: () => {
                                if (row.wasteAmount === "0") row.wasteAmount = "";
                            },
                            onBlur: () => {
                                if (!row.wasteAmount) row.wasteAmount = "0";
                            }
                        });
                    },
                    default: () => {
                        return row.wasteAmount;
                    }
                }
            );
        }
    },
    {
        title: "废品生产时间",
        align: "center",
        key: "wasteDate",
        render: (row) => {
            return h(
                NTooltip,
                {},
                {
                    trigger: () => {
                        return h(NDatePicker, {
                            formattedValue: row.wasteDate,
                            class: "w-100%",
                            clearable: true,
                            placeholder: "请选择废品生产时间",
                            type: "date",
                            valueFormat: "yyyy-MM-dd",
                            onUpdateFormattedValue: (v: any) => {
                                row.wasteDate = v;
                            }
                        });
                    },
                    default: () => {
                        return row.wasteDate;
                    }
                }
            );
        }
    },
    {
        title: "备注",
        align: "center",
        key: "remark",
        render: (row) => {
            return h(
                NTooltip,
                {},
                {
                    trigger: () => {
                        return h(NInput, { value: row.remark, onUpdateValue: (v) => (row.remark = v) });
                    },
                    default: () => {
                        return row.remark;
                    }
                }
            );
        }
    }
]);

let getTableData = () => {
    tableLoading.value = true;
    GET_WASTE_AMOUNT_LIST({
        ...searchForm.value
    }).then((res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data.dailyAmountList ?? [];
            planId.value = res.data.data.id ?? null;
        }
        tableLoading.value = false;
    });
};

//保存填写
let saveTableData = async () => {
    loadingShow.value = true;
    let totalWasteAmount = 0;
    tableData.value.forEach((item: any) => {
        totalWasteAmount += Number(item.wasteAmount);
    });
    let params = {
        ...searchForm.value,
        id: planId.value,
        totalWasteAmount: totalWasteAmount,
        dailyAmountList: tableData.value
    };
    if (!searchForm.value.workGroupId) {
        window.$message.error("请先选择班组！");
        return false;
    }
    if (!searchForm.value.planDate) {
        window.$message.error("请先选择日期！");
        return false;
    }
    await SAVE_WASTE_AMOUNT_LIST({ ...params }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("保存成功");
            onSearch();
        }
    });
    loadingShow.value = false;
};
</script>
