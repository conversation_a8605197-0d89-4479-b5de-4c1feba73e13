<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <ProcessDetail v-model:show="processDetailModal.show" :config-data="processDetailModal.configData" />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from "vue";
import type { DataTableColumns } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable, useDicts } from "@/hooks";
import { GET_OA_MODEL_LIST, GET_PROCESS_INSTANCE_MY_INVOLVED, ADD_IMPORTANT_OA_INSTANCE } from "@/api/application/oa";
import { TableActions } from "@/components/TableActions";
import { ProcessDetail } from "../components";

interface RowProps<T = string | null> {
    id: T | number; // 添加id字段
    processInstanceId: T | number;
    processDefinitionName: T;
    name: T;
    endTime: T;
    processBusinessStatus: any;
    status: T | number;
    businessName: T;
    starterUserTrueName: T;
    businessNumber: T;
    businessKey: T;
    elements: any[];
    significance?: number; // 添加关注状态字段：0不重要,1重要

    [key: string]: any;
}

onMounted(async () => {
    await setDictLibs();
    getSearchOptions();
    getTableData();
});

let { dictLibs, getDictLibs, dictValueToLabel, dictValueToAll } = useDicts();

// 维护本地关注状态
let importantStatus = ref<Record<number, number>>({});

let setDictLibs = async () => {
    let dictName = ["process_business_status"];
    await getDictLibs(dictName);
};

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { prop: "modelId", type: "select", label: "流程名称", span: 2 },
    {
        prop: "status",
        type: "select",
        label: "当前状态"
    },
    {
        prop: "significance",
        type: "select",
        label: "关注状态"
    },
    // {
    //     prop: "processName",
    //     type: "input",
    //     label: "流程名称"
    // },
    { prop: "paramProjectName", type: "input", label: "项目名称/主题", labelWidth: "120" },
    {
        prop: "modelLabel",
        type: "input",
        label: "关键词"
    },
    {
        prop: "businessNumber",
        type: "input",
        label: "审批单号"
    },
    {
        prop: "beginStartTime",
        type: "datetime",
        label: "发起开始时间",
        dateFormat: "yyyy-MM-dd HH:mm:ss",
        labelWidth: "100"
    },
    {
        prop: "endStartTime",
        type: "datetime",
        label: "发起截止时间",
        dateFormat: "yyyy-MM-dd HH:mm:ss",
        labelWidth: "100"
    },
    {
        prop: "starterUser",
        type: "userSelector",
        label: "发起人员"
    }
]);

let searchOptions = ref<TableSearchbarOptions>({
    status: [],
    modelId: [],
    significance: []
});

let searchForm = ref<TableSearchbarData>({
    status: null,
    processName: null,
    paramProjectName: null,
    modelLabel: null,
    businessNumber: null,
    beginStartTime: null,
    endStartTime: null,
    significance: null
});

let getSearchOptions = () => {
    searchOptions.value.status = dictLibs.process_business_status;
    // 关注状态选项
    searchOptions.value.significance = [
        { label: "全部", value: null },
        { label: "已关注", value: 1 },
        { label: "未关注", value: 0 }
    ];
    /*
     * 时间：2025年6月5日16:28:53
     * 来源：上海公司研发群
     * 内容：modelState: 1 后台让删这个
     */
    GET_OA_MODEL_LIST({ current: 1, size: 1000 }).then((res) => {
        if (res.data.code === 0) {
            searchOptions.value.modelId = res.data.data.records.map((item: any) => {
                return {
                    label: item.name,
                    value: item.id
                };
            });
        }
    });
};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("processInstanceId");

// 关注/移出关注功能
let toggleImportant = async (row: RowProps) => {
    const id = row.id as number; // 改用id字段
    // 优先使用后端返回的significance值
    const currentStatus = row.significance ?? 0;
    const newStatus = currentStatus === 1 ? 0 : 1;
    const actionText = newStatus === 1 ? "添加关注" : "移出关注";

    // 二次确认
    const confirmed = await new Promise<boolean>((resolve) => {
        window.$dialog?.warning({
            title: "确认操作",
            content: `确定要${actionText}此流程吗？`,
            positiveText: "确定",
            negativeText: "取消",
            onPositiveClick: () => resolve(true),
            onNegativeClick: () => resolve(false),
            onClose: () => resolve(false)
        });
    });

    if (!confirmed) return;

    try {
        const res = await ADD_IMPORTANT_OA_INSTANCE({
            id: id, // 直接使用id
            significance: newStatus
        });

        if (res.data.code === 0) {
            // 更新本地状态和数据，使用id作为索引
            importantStatus.value[id] = newStatus;
            // 更新tableData中的significance值
            const targetRow = tableData.value.find((item) => item.id === id);
            if (targetRow) {
                targetRow.significance = newStatus;
            }
            window.$message?.success(`${actionText}成功`);
        } else {
            window.$message?.error(res.data.msg || `${actionText}失败`);
        }
    } catch (error) {
        console.error(`${actionText}失败:`, error);
        window.$message?.error(`${actionText}失败，请稍后重试`);
    }
};

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "审批单号",
        key: "businessNumber",
        align: "center",
        render: (row: RowProps) => {
            if (row.businessNumber != null) {
                return row.businessNumber;
            } else {
                return row.businessKey;
            }
        }
    },
    {
        title: "流程名称",
        align: "center",
        key: "name",
        render: (row: RowProps) => {
            if (row.businessName) {
                return `【项目名称:${row.businessName}】${row.starterUserTrueName}提交的${row.name}`;
            } else {
                return `${row.starterUserTrueName}提交的${row.name}`;
            }
        }
    },
    /*
     * 2024年5月25日
     * 铁塔新需求，需要展示散单项目名称
     * reloading
     */
    {
        title: "项目名称/主题",
        align: "center",
        key: "paramProjectName",
        render: (row: RowProps) => {
            return row.paramProjectName ?? "/";
            // return (row.elements || []).find((item) => item.modelName === "projectName")?.value || "暂无";
        }
    },
    {
        title: "发起人",
        key: "starterUserTrueName",
        align: "center"
    },
    {
        title: "审批时间",
        key: "endTime",
        align: "center",
        render: (row: RowProps) => {
            return row.endTime || "暂无";
        }
    },
    {
        title: "审批状态",
        key: "processBusinessStatus",
        align: "center",
        render: (row: RowProps) => {
            return h(
                "span",
                {
                    style: `color:${dictValueToAll(row.status, "process_business_status").color || ""}`
                },
                dictValueToLabel(row.status, "process_business_status")
            );
        }
    },
    {
        title: "关注状态",
        key: "significance",
        align: "center",
        render: (row: RowProps) => {
            const significance = row.significance ?? 0;
            const isImportant = significance === 1;
            return h(
                "span",
                {
                    style: `color: ${isImportant ? "#f0a020" : "#909399"}`
                },
                isImportant ? "已关注" : "未关注"
            );
        }
    },
    {
        title: "操作",
        key: "action",
        align: "center",
        width: 200,
        render: (row: RowProps) => {
            const id = row.id as number; // 改用id字段
            // 优先使用后端返回的significance值
            const currentStatus = row.significance ?? 0;
            const isImportant = currentStatus === 1;

            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "查看单据",
                        tertiary: true,
                        onClick: () => {
                            openProcessDetailModal(row);
                        }
                    },
                    {
                        label: isImportant ? "移出关注" : "添加关注",
                        tertiary: true,
                        type: isImportant ? "warning" : "info",
                        onClick: () => {
                            toggleImportant(row);
                        }
                    }
                ]
            });
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_PROCESS_INSTANCE_MY_INVOLVED({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        ...searchForm.value
    }).then((res) => {
        if (res.data.code === 0) {
            console.log("流程实例", res.data.data);
            tableData.value = res.data.data.records;
            tablePagination.itemCount = res.data.data.total;
            tableLoading.value = false;
        }
    });
};

// 搜索
let onSearch = () => {
    tablePagination.page = 1;
    tablePagination.pageSize = 10;
    getTableData();
};

// 单据详情
let processDetailModal = ref<any>({
    show: false,
    configData: {}
});

let openProcessDetailModal = (row: any) => {
    processDetailModal.value.show = true;
    processDetailModal.value.configData = row;
};
</script>
