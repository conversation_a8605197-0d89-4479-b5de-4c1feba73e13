import { defineComponent, h, onMounted, reactive, ref } from "vue";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import { TableActions } from "@/components/TableActions";
import { GET_DINGDING_SYNCHRONIZED_RECORDS } from "@/api/permission";
import AttendanceSyncUpdate from "./AttendanceSyncUpdate";

export default defineComponent({
    name: "AttendanceSyncList",
    setup(props, { emit }) {
        // 搜索项
        const searchConfig = ref<TableSearchbarConfig>([]);

        const searchOptions = ref<TableSearchbarOptions>({});

        const getSearchOptions = async () => {};

        const searchForm = ref<TableSearchbarData>({});

        const onSearch = () => {
            getTableData();
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            { title: "同步时间", key: "createTime", align: "center" },
            { title: "操作人", key: "operator", align: "center" },
            {
                title: "同步类型",
                key: "recordType",
                align: "center",
                render: (row) => {
                    const recordType = String(row.recordType);
                    switch (recordType) {
                        case "1":
                            return "全量覆盖";
                        case "2":
                            return "按组织同步";
                        case "3":
                            return "按用户同步";
                        default:
                            return "/";
                    }
                }
            },
            {
                title: "同步状态",
                key: "recordStatus",
                align: "center",
                render: (row) => {
                    const recordStatus = String(row.recordStatus);
                    switch (recordStatus) {
                        case "1":
                            return <n-text type="success">成功</n-text>;
                        case "2":
                            return <n-text type="info">部分成功</n-text>;
                        case "3":
                            return <n-text type="warning">同步中</n-text>;
                        case "4":
                            return <n-text type="error">失败</n-text>;
                        default:
                            return "/";
                    }
                }
            },
            {
                title: "同步结果",
                key: "actions",
                align: "center",
                width: 120,
                render(row) {
                    return h(TableActions, {
                        type: "button",
                        buttonActions: [
                            {
                                label: "点击查看",
                                tertiary: true,
                                onClick: () => openUpdateModal(row)
                            }
                        ]
                    });
                }
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_DINGDING_SYNCHRONIZED_RECORDS({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                ...searchForm.value
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        // 同步功能
        const updateModal = ref<{ show: boolean; configData: RowProps }>({ show: false, configData: {} });

        const openUpdateModal = (row?: RowProps) => {
            updateModal.value.show = true;
            updateModal.value.configData = row ?? {};
        };

        onMounted(async () => {
            await getSearchOptions();
            getTableData();
        });

        return () => (
            <div class="attendance-sync-page">
                <n-card>
                    <TableSearchbar
                        form={searchForm.value}
                        config={searchConfig.value}
                        options={searchOptions.value}
                        onSearch={onSearch}
                    />
                </n-card>
                <n-card class="mt">
                    <n-space class="mb">
                        <n-button type="primary" onClick={() => openUpdateModal()}>
                            同步钉钉用户
                        </n-button>
                    </n-space>
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        pagination={tablePagination}
                        row-key={tableRowKey}
                        single-line={false}
                        bordered
                        remote
                        striped
                        onUpdate:checked-row-keys={changeTableSelection}
                    />
                </n-card>
                <AttendanceSyncUpdate
                    v-model:show={updateModal.value.show}
                    v-model:configData={updateModal.value.configData}
                    onRefresh={getTableData}
                />
            </div>
        );
    }
});
