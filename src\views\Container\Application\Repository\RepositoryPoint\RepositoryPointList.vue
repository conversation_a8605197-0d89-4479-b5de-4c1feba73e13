<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-space class="mb">
                <n-button secondary type="primary" @click="openEditModal()">新增</n-button>
            </n-space>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <!--新增编辑-->
        <RepositoryPointEdit
            v-model:show="editModal.show"
            :configData="editModal.configData"
            @refresh="getTableData()"
        />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from "vue";
import type { DataTableColumns } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { TableActions } from "@/components/TableActions";
import { useCommonTable } from "@/hooks";
import { DELETE_REPOSITORY_POINT, GET_REPOSITORY_POINT_LIST } from "@/api/application/repository";
import RepositoryPointEdit from "./RepositoryPointEdit.vue";
import { GET_YD_COMPANY_LIST } from "@/api/permission";

interface RowProps {
    [key: string]: any;
}

onMounted(async () => {
    await getSearchOptions();
    getTableData();
});

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { label: "库点名称", prop: "pointName", type: "input" },
    { label: "库点编号", prop: "pointCode", type: "input" },
    { label: "所属公司", prop: "companyId", type: "select" },
    { label: "负责人", prop: "pointManager", type: "userSelector" },
    { label: "运行状态", prop: "lockFlag", type: "radio" }
]);

let searchOptions = ref<TableSearchbarOptions>({
    companyId: [],
    lockFlag: [
        { label: "运行中", value: 0 },
        { label: "已停用", value: 1 }
    ]
});

let searchForm = ref<TableSearchbarData>({
    pointName: null,
    pointCode: null,
    companyId: null,
    pointManager: null,
    lockFlag: null
});

let getSearchOptions = async () => {
    await GET_YD_COMPANY_LIST({}).then((res) => {
        searchOptions.value.companyId = (res.data.data || []).map((i: any) => ({ label: i.company, value: i.id }));
    });
};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "库点名称",
        key: "pointName",
        align: "center"
    },
    {
        title: "所属公司",
        key: "companyName",
        align: "center"
    },
    {
        title: "库点编号",
        key: "pointCode",
        align: "center"
    },
    {
        title: "负责人",
        key: "pointManagerName",
        align: "center"
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: "150",
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "编辑",
                        tertiary: true,
                        onClick: () => {
                            openEditModal(row);
                        }
                    },
                    {
                        label: "删除",
                        type: "error",
                        tertiary: true,
                        onClick: () => {
                            onDelete(row);
                        }
                    }
                ]
            });
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_REPOSITORY_POINT_LIST({
        current: tablePagination.page,
        size: tablePagination.pageSize,
        ...searchForm.value
    }).then((res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data.records;
            tablePagination.itemCount = res.data.data.total;
            tableLoading.value = false;
        }
    });
};

// 搜索
let onSearch = () => {
    tablePagination.page = 1;
    tablePagination.pageSize = 10;
    getTableData();
};

// 新增编辑
let editModal = ref<{ show: boolean; configData: UnKnownObject }>({
    show: false,
    configData: {}
});

let openEditModal = (row?: RowProps) => {
    editModal.value = {
        show: true,
        configData: row ?? {}
    };
};

// 删除
let onDelete = (row: RowProps) => {
    window.$dialog.warning({
        title: "警告",
        content: "确定删除该库点吗？",
        positiveText: "删除",
        negativeText: "取消",
        onPositiveClick: () => {
            DELETE_REPOSITORY_POINT({ id: row.id }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("删除成功");
                    onSearch();
                }
            });
        }
    });
};
</script>
