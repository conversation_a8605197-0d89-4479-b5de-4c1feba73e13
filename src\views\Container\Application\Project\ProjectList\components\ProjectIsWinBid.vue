<template>
    <div>
        <n-modal v-model:show="show" :cloformData-on-esc="false" :mask-closable="false">
            <n-card class="w-600px" closable title="是否中标" @close="closeModal">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-form-item label="是否中标" path="isWinBid">
                        <n-radio-group v-model:value="formData.isWinBid">
                            <n-space>
                                <n-radio :value="1" label="已中标" />
                                <n-radio :value="0" label="未中标" />
                            </n-space>
                        </n-radio-group>
                    </n-form-item>
                    <n-form-item label="所属销售人员" path="saleUsername">
                        <UserSelector
                            v-model:value="formData.saleUsername"
                            class="w-100%"
                            clearable
                            key-name="username"
                            placeholder="请选择所属销售人员"
                        />
                    </n-form-item>
                    <n-form-item>
                        <n-space>
                            <n-button type="primary" @click="onSubmit">提交</n-button>
                            <n-button @click="closeModal">取消</n-button>
                        </n-space>
                    </n-form-item>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { UserSelector } from "@/components/UserSelector";
import { SET_BIND_STATUS } from "@/api/application/project";

let props = defineProps({
    show: { type: Boolean, default: false },
    configData: { type: Object as PropType<any> }
});

let emits = defineEmits(["update:show", "refresh"]);

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = computed(() => {
    return {
        isWinBid: [{ required: true, message: "请选择是否中标" }],
        saleUsername: [{ required: formData.value.isWinBid === 1, message: "请选择所属销售人员" }]
    };
});

// 表单数据
interface FormDataProps<T = string | null> {
    isWinBid: T | number;
    saleUsername: T;
}

let initFormData: FormDataProps = {
    isWinBid: null,
    saleUsername: null
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    emits("update:show", false);
};

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    SET_BIND_STATUS({
        ...formData.value,
        projectId: props.configData.projectId,
        nodeKey: props.configData.nextNodeKey
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("中标信息已提交");
            closeModal();
            emits("refresh");
        }
    });
};
</script>
