<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-95vw" closable content-style="padding:0" title="项目详情" @close="closeModal">
                <div class="h-80vh">
                    <n-scrollbar trigger="hover">
                        <div class="p-20px pt-0">
                            <n-card class="pb-20px" hoverable>
                                <n-breadcrumb>
                                    <n-breadcrumb-item v-for="item in $route.meta.breadcrumb">
                                        {{ item }}
                                    </n-breadcrumb-item>
                                    <n-breadcrumb-item>{{ formData.projectName }}</n-breadcrumb-item>
                                </n-breadcrumb>
                                <div class="flex-y-center mt-20px">
                                    <div class="flex-1">
                                        <div class="flex-y-center">
                                            <div class="text-25px">
                                                <b>项目名称：{{ formData.projectName }}</b>
                                            </div>
                                        </div>
                                        <n-grid :cols="24" :x-gap="16" :y-gap="16" class="mt-20px">
                                            <n-grid-item :span="8" class="text-14px">
                                                项目编号：{{ formData.projectId }}
                                            </n-grid-item>
                                            <n-grid-item :span="8" class="text-14px color-[var(--primary-color)]">
                                                项目类型：
                                                {{ dictValueToLabel(formData.projectType, "project_type") }}
                                            </n-grid-item>
                                            <n-grid-item :span="8" class="text-14px">
                                                相关合同编号：{{ formData.contractNumber }}
                                            </n-grid-item>
                                            <n-grid-item :span="8" class="text-14px">
                                                创建时间：{{ formData.createTime }}
                                            </n-grid-item>
                                            <n-grid-item :span="8" class="text-14px">
                                                <span>所属客户：</span>
                                                <span class="color-[var(--primary-color)]">
                                                    {{ formData.customer?.customerCompanyName || "未知" }}
                                                </span>
                                            </n-grid-item>
                                            <n-grid-item v-if="formData.saleUsername" :span="8" class="text-14px">
                                                项目负责人：{{ saleUserData.trueName }}
                                            </n-grid-item>
                                            <n-grid-item v-if="formData.isWinBid" :span="8" class="text-14px">
                                                是否中标：
                                                <n-text v-if="formData.isWinBid === 0" type="error">未中标</n-text>
                                                <n-text v-if="formData.isWinBid === 1" type="success">已中标</n-text>
                                            </n-grid-item>
                                            <n-grid-item v-if="formData.bidUploadType" :span="8" class="text-14px">
                                                是否上传/邮寄标书：
                                                <n-text v-if="formData.bidUploadType === 1" type="success">
                                                    线下邮寄
                                                </n-text>
                                                <n-text v-if="formData.bidUploadType === 2" type="success">
                                                    线上上传
                                                </n-text>
                                            </n-grid-item>
                                            <!--<n-grid-item v-if="formData.bidCosts" :span="8" class="text-14px">-->
                                            <!--    中标费用：{{ bidCosts }}-->
                                            <!--</n-grid-item>-->
                                        </n-grid>
                                    </div>
                                    <div class="ml-a flex-y-center flex-fixed-600">
                                        <div class="flex-1 text-center">
                                            <div class="text-14px">订单数量</div>
                                            <div class="mt-5px text-30px color-[#f60]">
                                                <b>{{ formData?.orderNum }}</b>
                                            </div>
                                        </div>
                                        <div class="flex-1 text-center">
                                            <div class="text-14px">最新进度</div>
                                            <div class="mt-5px text-30px color-[#FF8D1A]">
                                                <b>{{ formData.nodeName }}</b>
                                            </div>
                                        </div>
                                        <div class="flex-1 text-center">
                                            <div class="text-14px">当前状态</div>
                                            <div
                                                :style="`color:${
                                                    dictValueToAll(formData.projectStatus, 'project_status').color || ''
                                                }`"
                                                class="mt-5px text-30px"
                                            >
                                                <b>{{ dictValueToLabel(formData.projectStatus, "project_status") }}</b>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </n-card>
                            <n-card class="w-100% mt-15px" content-style="padding:0" hoverable title="项目进度展示">
                                <n-scrollbar x-scrollable>
                                    <div class="p-20px pt-0 flex-y-center">
                                        <div v-for="(item, index) in formData.projectNodeConfigVoList" class="step-box">
                                            <div class="flex-y-center">
                                                <div class="round">{{ index + 1 }}</div>
                                                <div
                                                    v-if="
                                                        index + 1 !== formData.projectNodeConfigVoList.length ||
                                                        formData.projectNodeConfigVoList?.length === 1
                                                    "
                                                    class="line"
                                                />
                                            </div>
                                            <div class="name">{{ item.nodeName }}</div>
                                            <div>
                                                状态：
                                                <template v-if="item.nodeKey === 'BidUpload'">
                                                    <n-text v-if="formData.bidUploadType === 1" type="success">
                                                        线下邮寄
                                                    </n-text>
                                                    <n-text v-else-if="formData.bidUploadType === 2" type="success">
                                                        线上上传
                                                    </n-text>
                                                    <n-text v-else>未知</n-text>
                                                </template>
                                                <template v-else-if="item.nodeKey === 'IsWinBid'">
                                                    <n-text v-if="formData.isWinBid === 0" type="error">
                                                        未中标
                                                    </n-text>
                                                    <n-text v-else-if="formData.isWinBid === 1" type="success">
                                                        已中标
                                                    </n-text>
                                                    <n-text v-else>未知</n-text>
                                                </template>
                                                <template v-else>
                                                    <span
                                                        :style="`color:${
                                                            dictValueToAll(item.nodeStatus, 'node_status').color || ''
                                                        }`"
                                                    >
                                                        {{ item.nodeStatusName }}
                                                    </span>
                                                </template>
                                            </div>
                                            <div>
                                                <template v-if="item.processInstanceId">
                                                    审批单：
                                                    <span
                                                        v-if="item.formKey"
                                                        class="name cursor-pointer"
                                                        @click="openProcessDetailModal(item)"
                                                    >
                                                        查看详情
                                                    </span>
                                                    <span v-else>暂无</span>
                                                </template>
                                                <div v-else class="nodeTime op0">占位符</div>
                                            </div>
                                            <div class="nodeTime">
                                                {{ item.createTime }}
                                            </div>
                                        </div>
                                        <div v-if="formData.projectNodeConfigVoList?.length === 1" class="step-box">
                                            <div class="flex-y-center">
                                                <div class="round roundready">2</div>
                                            </div>
                                            <div class="name">暂无反馈</div>
                                            <div>
                                                状态：
                                                <span class="ready">待反馈</span>
                                            </div>
                                            <div>审批单：暂无</div>
                                            <div class="nodeTime op0">占位符</div>
                                        </div>
                                    </div>
                                </n-scrollbar>
                            </n-card>
                            <n-card class="w-100% mt-15px" hoverable>
                                <n-tabs pane-class="pt-20px!" type="line">
                                    <n-tab-pane name="标书附件">
                                        <n-space v-if="bidFiles && bidFiles.length > 0" vertical>
                                            <n-space v-for="item in bidFiles">
                                                <n-text>{{ item.original }}</n-text>
                                                <n-text
                                                    class="cursor-pointer"
                                                    type="info"
                                                    @click="downloadFile(item.url)"
                                                >
                                                    点击下载
                                                </n-text>
                                            </n-space>
                                        </n-space>
                                        <n-result v-else class="py-50px" size="large" status="404" title="暂无附件" />
                                    </n-tab-pane>
                                    <n-tab-pane name="合同附件">
                                        <n-space v-if="contractFiles && contractFiles.length > 0" vertical>
                                            <n-space v-for="item in contractFiles">
                                                <n-text>{{ item.original }}</n-text>
                                                <n-text
                                                    class="cursor-pointer"
                                                    type="info"
                                                    @click="downloadFile(item.url)"
                                                >
                                                    点击下载
                                                </n-text>
                                            </n-space>
                                        </n-space>
                                        <n-result v-else class="py-50px" size="large" status="404" title="暂无附件" />
                                    </n-tab-pane>
                                    <n-tab-pane v-if="formData.unBidFeedbackList?.length > 0" name="未中标原因反馈">
                                        <n-table :single-line="false" class="text-center">
                                            <thead>
                                                <tr>
                                                    <th>反馈人</th>
                                                    <th>反馈原因</th>
                                                    <th>反馈时间</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr v-for="item in formData.unBidFeedbackList">
                                                    <td>{{ item.createBy }}</td>
                                                    <td>{{ item.createTime }}</td>
                                                    <td>{{ item.feedbackMessage }}</td>
                                                </tr>
                                            </tbody>
                                        </n-table>
                                    </n-tab-pane>
                                    <n-tab-pane v-if="formData.finishFeedbackList?.length > 0" name="项目完结反馈">
                                        <n-table :single-line="false" class="text-center">
                                            <thead>
                                                <tr>
                                                    <th>反馈人</th>
                                                    <th>反馈原因</th>
                                                    <th>反馈时间</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr v-for="item in formData.finishFeedbackList">
                                                    <td>{{ item.createBy }}</td>
                                                    <td>{{ item.feedbackMessage }}</td>
                                                    <td>{{ item.createTime }}</td>
                                                </tr>
                                            </tbody>
                                        </n-table>
                                    </n-tab-pane>
                                    <n-tab-pane name="节点负责人">
                                        <n-scrollbar x-scrollable>
                                            <div class="px-10px">
                                                <n-table :single-line="false" class="text-center">
                                                    <thead>
                                                        <tr>
                                                            <th
                                                                v-for="(item, index) in formData.nodeDirectorList || []"
                                                                :key="index"
                                                            >
                                                                {{ item.nodeName }}
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td
                                                                v-for="(item, index) in formData.nodeDirectorList || []"
                                                                :key="index"
                                                            >
                                                                {{ item.nodeDirectorName }}
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </n-table>
                                            </div>
                                        </n-scrollbar>
                                    </n-tab-pane>
                                    <n-tab-pane name="中标费用" v-if="formData.bidCosts">
                                        {{ bidCosts }}
                                    </n-tab-pane>
                                </n-tabs>
                            </n-card>
                        </div>
                    </n-scrollbar>
                </div>
            </n-card>
        </n-modal>
        <ProcessDetail v-model:show="processDetailModal.show" :config-data="processDetailModal.configData" />
    </div>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import { GET_PROJECT_DETAIL, GET_PROJECT_FILE_LIST } from "@/api/application/project";
import { useDicts, usePublic } from "@/hooks";
import { ProcessDetail } from "@/views/Container/Application/Process/components";
import { GET_STAFF_BY_USERNAMES } from "@/api/permission";

let { $route } = usePublic();

let props = defineProps({
    show: { type: Boolean, default: false },
    id: { type: [String, Number] as PropType<any> }
});

let emits = defineEmits(["update:show", "refresh"]);

// 字典操作
let { dictLibs, getDictLibs, dictValueToLabel, dictValueToAll } = useDicts();

let setDictLibs = async () => {
    let dictName = ["project_status", "node_status", "project_type"];
    await getDictLibs(dictName);
};

// 获取详情
watch(
    () => ({ id: props.id, show: props.show }),
    async (newVal) => {
        await setDictLibs();
        if (newVal.show && newVal.id) getDetail();
    },
    { deep: true }
);

// 表单数据
let formData = ref<any>({});

// 获取详情
let getDetail = () => {
    GET_PROJECT_DETAIL({ id: props.id }).then((res) => {
        formData.value = res.data.data;
        if (formData.value.saleUsername) getSaleUserData(formData.value.saleUsername);
        if (formData.value.bidCosts) getBidCosts();
        getProjectFiles();
    });
};

// 关闭弹窗
let closeModal = () => {
    emits("update:show", false);
};

// 单据详情
let processDetailModal = ref<any>({
    show: false,
    configData: {}
});

let openProcessDetailModal = (row: any) => {
    processDetailModal.value.show = true;
    processDetailModal.value.configData = row;
};

// 获取项目负责人信息
let saleUserData = ref<any>({});

let getSaleUserData = (usernames: string) => {
    GET_STAFF_BY_USERNAMES({ usernames }).then((res) => {
        saleUserData.value = res.data.data[0] || {};
    });
};

// 获取费用信息
let bidCosts = ref("");

let getBidCosts = () => {
    let costArray = JSON.parse(decodeURIComponent(formData.value.bidCosts));
    let newArray = costArray.map((item: any) => {
        return `${Object.values(item)}${Object.keys(item)}元`;
    });
    bidCosts.value = newArray.join("、");
};

// 获取项目文件
let bidFiles = ref<any[]>([]);

let contractFiles = ref<any[]>([]);

let getProjectFiles = () => {
    // 标书
    GET_PROJECT_FILE_LIST({ projectId: props.id, fileType: 2 }).then((res) => {
        bidFiles.value = res.data.data || [];
    });
    // 合同
    GET_PROJECT_FILE_LIST({ projectId: props.id, fileType: 1 }).then((res) => {
        contractFiles.value = res.data.data || [];
    });
};

let downloadFile = (url: any) => {
    window.open(import.meta.env.VITE_API_URL + url, "_blank");
};
</script>

<style lang="scss" scoped>
.ready {
    color: orange;
}

.step-box {
    width: 18%;
    min-width: 180px;
    margin-right: 5px;

    .line {
        width: 80%;
        height: 1px;
        border-bottom: 1px solid #cecece;
        margin-left: 10px;
    }

    .round {
        width: 20px;
        height: 20px;
        text-align: center;
        line-height: 20px;
        background: green;
        border-radius: 50%;
        color: #fff;
    }

    .roundready {
        background: orange;
    }

    .name {
        color: #7faadf;
        margin-top: 10px;
    }

    .nodeTime {
        color: #cecece;
    }
}
</style>
