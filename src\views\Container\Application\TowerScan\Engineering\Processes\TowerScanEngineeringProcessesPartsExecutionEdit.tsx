import { computed, defineComponent, ref, watchEffect } from "vue";
import { type FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import {
    UPDATE_MATERIAL_FILL,
    GET_MATERIAL_FILL_DETAIL,
    GET_MATERIAL_FILL_QUANTITY_CHECK
} from "@/api/application/TowerScan";
import UserSelector from "@/components/UserSelector/src/UserSelector.vue";

export default defineComponent({
    name: "TowerScanEngineeringProcessesPartsExecutionEdit",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => {
            emit("update:show", show);
            if (!show) {
                clearForm();
            }
        };

        // 表单数据
        interface FormDataProps {
            [key: string]: any;
        }

        const formRef = ref<FormInst | null>(null);

        // 表单校验规则
        const formRules = computed(() => {
            const rules: any = {
                fillQuantity: [
                    { required: true, message: "请输入质检数量", trigger: ["input", "blur"] },
                    { pattern: /^\d+(\.\d+)?$/, message: "请输入有效的数量", trigger: ["input", "blur"] }
                ],
                belongUsers: [{ required: true, message: "请输入操作工", trigger: ["input", "blur"] }],
                // inFlag: [{ required: true, message: "请选择是否入库", trigger: ["blur", "change"], type: "number" }]
            };

            // 制孔工艺(processType===3)时，工艺填报数量为必填
            if (formData.value.processType === 3) {
                // 这里简化处理，因为编辑时暂时不支持单重自动计算
                rules.fillTechniqueQuantity = [
                    { required: true, message: "请输入工艺填报数量", trigger: ["input", "blur"] },
                    { pattern: /^\d+(\.\d+)?$/, message: "请输入有效的数量", trigger: ["input", "blur"] }
                ];
            }

            return rules;
        });

        const initFormData: FormDataProps = {
            id: null,
            fillQuantity: "",
            belongUsers: "",
            // inFlag: 0,
            // 以下字段从详情获取，不可编辑，原样返回
            fillLineId: null,
            fillTechniqueId: "",
            fillTechniqueQuantity: "",
            materialId: "",
            planMaterialId: "",
            planTechniqueId: "",
            processFillFrom: 1,
            processType: 2,
            qualityTaskMaterialId: ""
        };

        const formData = ref(cloneDeep(initFormData));

        const clearForm = () => {
            formData.value = cloneDeep(initFormData);
        };

        // 获取详情
        const getDetail = () => {
            GET_MATERIAL_FILL_DETAIL({ id: props.configData.id }).then((res) => {
                if (res.data.code === 0) {
                    const detail = res.data.data;
                    formData.value = {
                        id: detail.id,
                        fillQuantity: detail.fillQuantity?.toString() || "",
                        belongUsers: detail.belongUsers || "",
                        // inFlag: detail.inFlag ?? 0,
                        // 以下字段原样返回
                        fillLineId: detail.fillLineId,
                        fillTechniqueId: detail.fillTechniqueId || "",
                        fillTechniqueQuantity: detail.fillTechniqueQuantity?.toString() || "",
                        materialId: detail.materialId || "",
                        planMaterialId: detail.planMaterialId || "",
                        planTechniqueId: detail.planTechniqueId || "",
                        processFillFrom: detail.processFillFrom ?? 1,
                        processType: detail.processType ?? 2,
                        qualityTaskMaterialId: detail.qualityTaskMaterialId || ""
                    };
                }
            });
        };

        watchEffect(() => {
            if (show.value) {
                if (props.configData.id) {
                    getDetail();
                }
            }
        });

        const onClose = () => {
            changeModalShow(false);
            emit("refresh");
        };

        const onSubmit = async () => {
            const validateError = await formRef.value?.validate((errors) => !!errors);
            if (validateError) return false;

            // 先调用校验接口
            const checkResult = await GET_MATERIAL_FILL_QUANTITY_CHECK({
                ...formData.value
            });

            if (checkResult.data.code !== 0) {
                // 校验失败，显示错误信息
                window.$message.error(checkResult.data.msg);
                return false;
            }

            // 校验通过，执行更新操作
            await UPDATE_MATERIAL_FILL({
                ...formData.value
            }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("修改成功");
                    onClose();
                }
            });
        };

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card
                    title={`修改零件填报记录 - ${props.configData?.materialCode ?? "/"}`}
                    class="w-1200px"
                    closable
                    onClose={() => changeModalShow(false)}
                >
                    <n-form
                        ref={formRef}
                        model={formData.value}
                        rules={formRules.value}
                        label-placement="left"
                        label-width="auto"
                    >
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi span={6} label="质检数量" path="fillQuantity">
                                <n-input
                                    v-model:value={formData.value.fillQuantity}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入质检数量"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="操作工" path="belongUsers">
                                <UserSelector
                                    v-model:value={formData.value.belongUsers}
                                    class="w-100%"
                                    key-name="username"
                                    placeholder="请选择操作工"
                                />
                            </n-form-item-gi>
                            {/* 制孔工艺时显示工艺填报数量字段 */}
                            {formData.value.processType === 3 && (
                                <n-form-item-gi span={6} label="工艺填报数量" path="fillTechniqueQuantity">
                                    <n-input
                                        v-model:value={formData.value.fillTechniqueQuantity}
                                        class="w-100%"
                                        clearable
                                        placeholder="请输入工艺填报数量"
                                    />
                                </n-form-item-gi>
                            )}
                            {/* <n-form-item-gi span={6} label="是否入库" path="inFlag">
                                <n-switch v-model:value={formData.value.inFlag} checked-value={1} unchecked-value={0} />
                            </n-form-item-gi> */}
                            <n-form-item-gi span={12}>
                                <n-space>
                                    <n-button type="primary" onClick={onSubmit}>
                                        提交
                                    </n-button>
                                    <n-button onClick={() => changeModalShow(false)}>取消</n-button>
                                </n-space>
                            </n-form-item-gi>
                        </n-grid>
                    </n-form>
                </n-card>
            </n-modal>
        );
    }
});
