<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card :title="id ? '编辑角色' : '新增角色'" class="w-600px" closable @close="closeModal">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-form-item label="角色名称" path="roleName">
                        <n-input
                            v-model:value="formData.roleName"
                            class="w-100%"
                            clearable
                            placeholder="请输入角色名称"
                        />
                    </n-form-item>
                    <n-form-item label="角色组" path="roleGroup">
                        <n-select
                            v-model:value="formData.roleGroup"
                            :options="dictLibs.role_group"
                            placeholder="请选择角色组"
                        />
                    </n-form-item>
                    <n-form-item label="角色标识" path="roleCode">
                        <n-input
                            v-model:value="formData.roleCode"
                            class="w-100%"
                            clearable
                            placeholder="请输入角色标识"
                        />
                    </n-form-item>
                    <n-form-item label="角色描述" path="roleDesc">
                        <n-input
                            v-model:value="formData.roleDesc"
                            :autosize="{ minRows: 1 }"
                            class="w-100%"
                            clearable
                            placeholder="请输入角色描述"
                            type="textarea"
                        />
                    </n-form-item>
                    <n-form-item label="管理范围" path="dataPeriod">
                        <n-radio-group
                            v-model:value="formData.dataPeriod"
                            name="dataPeriod"
                            @update:value="changeDataPeriod"
                        >
                            <n-space>
                                <n-radio v-for="item in dictLibs.data_period" :value="item.value" :label="item.label" />
                            </n-space>
                        </n-radio-group>
                    </n-form-item>
                    <n-form-item v-if="formData.dataPeriod === '3'" label="数据权限" path="dataPermission">
                        <n-tree
                            v-model:checked-keys="formData.dataPermission"
                            :data="dataPermissionTree"
                            block-line
                            checkable
                            key-field="id"
                            label-field="name"
                        />
                    </n-form-item>
                    <n-form-item label="人员选择" path="userIds">
                        <UserSelector
                            v-model:value="formData.userIds"
                            class="w-100%"
                            clearable
                            key-name="userId"
                            placeholder="请选择人员"
                        />
                    </n-form-item>
                    <n-form-item>
                        <n-space>
                            <n-button type="primary" @click="onSubmit">提交</n-button>
                            <n-button @click="closeModal">取消</n-button>
                        </n-space>
                    </n-form-item>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { ADD_ROLE, GET_DEPT_TREE, GET_ROLE_BY_ID, UPDATE_ROLE } from "@/api/permission";
import { UserSelector } from "@/components/UserSelector";
import { useDicts } from "@/hooks";

let props = defineProps({
    show: { type: Boolean, default: false },
    id: { type: [String, Number] as PropType<any> }
});

let emits = defineEmits(["update:show", "refresh"]);

onMounted(async () => {
    await setDictLibs();
    getOptions();
});

// 字典操作
let { dictLibs, getDictLibs } = useDicts();

let setDictLibs = async () => {
    let dictName = ["role_group", "data_period"];
    await getDictLibs(dictName);
};

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {
    roleName: {
        required: true,
        message: "请输入角色名称",
        trigger: ["input", "blur"]
    },
    roleGroup: {
        required: true,
        message: "请选择角色组",
        trigger: ["blur", "change"]
    },
    roleCode: {
        required: true,
        message: "请输入角色标识",
        trigger: ["input", "blur"]
    },
    roleDesc: {
        required: false,
        message: "请输入角色描述",
        trigger: ["input", "blur"]
    },
    dataPeriod: {
        required: true,
        message: "请选择管理范围",
        trigger: ["blur", "change"]
    },
    dataPermission: {
        type: "array",
        required: true,
        message: "请选择数据权限",
        trigger: ["blur", "change"]
    }
};

// 获取选项
let dataPermissionTree = ref([]);

let getOptions = () => {
    GET_DEPT_TREE({ deptName: "" }).then((res) => {
        dataPermissionTree.value = res.data.data;
    });
};

// 表单数据
interface FormDataProps<T = string | null> {
    roleName: T;
    roleGroup: T;
    roleCode: T;
    roleDesc: T;
    dataPeriod: T | number;
    dataPermission: T[];
    userIds: T;
}

let initFormData: FormDataProps = {
    roleName: null,
    roleGroup: null,
    roleCode: null,
    roleDesc: null,
    dataPeriod: "1",
    dataPermission: [],
    userIds: null
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 编辑时获取详情
watch(
    () => ({ id: props.id, show: props.show }),
    (newVal) => {
        if (newVal.show && newVal.id) getDetail();
    },
    { deep: true }
);

// 获取详情
let getDetail = () => {
    GET_ROLE_BY_ID({ id: props.id }).then((res) => {
        let rowItem: any = res.data.data;
        formData.value = {
            ...rowItem,
            dataPeriod: String(rowItem.dataPeriod),
            dataPermission: rowItem.dataPermission?.split(",")
        };
    });
};

// 权限范围变更
let changeDataPeriod = () => {
    formData.value.dataPermission = [];
};

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    emits("update:show", false);
};

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    if (!props.id) {
        ADD_ROLE({
            ...formData.value,
            dataPermission: formData.value.dataPermission.join(",")
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("新增成功");
                closeModal();
                emits("refresh");
            }
        });
    } else {
        UPDATE_ROLE({
            roleId: props.id,
            ...formData.value,
            dataPermission: formData.value.dataPermission.join(",")
        }).then((res) => {
            if (res.data.code === 0) {
                window.$message.success("编辑成功");
                closeModal();
                emits("refresh");
            }
        });
    }
};
</script>
