<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-800px" closable title="是否中标" @close="closeModal">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-grid :cols="24" :x-gap="16">
                        <n-form-item-gi :span="12" label="是否中标" path="isWinBid">
                            <n-radio-group v-model:value="formData.isWinBid">
                                <n-space>
                                    <n-radio :value="1" label="已中标" />
                                    <n-radio :value="0" label="未中标" />
                                </n-space>
                            </n-radio-group>
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="是否中标确认时间" path="winBidFinishTime">
                            <n-date-picker
                                v-model:formatted-value="formData.winBidFinishTime"
                                class="w-100%"
                                clearable
                                placeholder="请选择是否中标确认时间"
                                type="datetime"
                                value-format="yyyy-MM-dd HH:mm:ss"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="是否中标确认人" path="winBidConfirmUser">
                            <UserSelector
                                v-model:value="formData.winBidConfirmUser"
                                :multiple="false"
                                class="w-100%"
                                clearable
                                key-name="username"
                                placeholder="请选择是否中标确认人"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="24">
                            <n-space>
                                <n-button type="primary" @click="onSubmit">提交</n-button>
                                <n-button @click="closeModal">取消</n-button>
                            </n-space>
                        </n-form-item-gi>
                    </n-grid>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { POST_BIND_STATUS } from "@/api/application/power";
import { UserSelector } from "@/components/UserSelector";

let props = defineProps({
    show: { type: Boolean, default: false },
    configData: { type: Object as PropType<any> }
});

let emits = defineEmits(["update:show", "refresh"]);

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = computed(() => {
    return {
        isWinBid: [{ required: true, message: "请选择是否中标" }]
    };
});

// 表单数据
interface FormDataProps {
    [key: string]: any;
}

let initFormData: FormDataProps = {
    isWinBid: 1,
    winBidFinishTime: null,
    winBidConfirmUser: null
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    emits("update:show", false);
};

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    POST_BIND_STATUS({
        ...formData.value,
        projectId: props.configData.projectId,
        nodeKey: props.configData.nextNodeKey
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("中标信息已提交");
            closeModal();
            emits("refresh");
        }
    });
};
</script>
