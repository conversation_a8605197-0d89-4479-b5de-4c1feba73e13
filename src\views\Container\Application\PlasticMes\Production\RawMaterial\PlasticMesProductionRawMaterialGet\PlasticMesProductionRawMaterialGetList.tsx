import { defineComponent, h, onMounted, reactive, ref } from "vue";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import { DataTableColumns, NText } from "naive-ui";
import {
    GET_PRODUCTION_MATERIAL_OUT_RECORD_LIST,
    POST_PRODUCTION_MATERIAL_OUT_RECORD_CANCEL
} from "@/api/application/plasticMes";
import PlasticMesProductionRawMaterialGetItemList from "@/views/Container/Application/PlasticMes/Production/RawMaterial/PlasticMesProductionRawMaterialGet/PlasticMesProductionRawMaterialGetItemList";
import { TableActions } from "@/components/TableActions";

export default defineComponent({
    name: "PlasticMesProductionRawMaterialGetList",
    setup(props, { expose }) {
        // 搜索项
        const searchConfig = ref<TableSearchbarConfig>([
            { prop: "goodsId", type: "select", label: "原材料名称", labelWidth: "100" }
        ]);
        const searchOptions = ref<TableSearchbarOptions>({
            goodsId: []
        });
        const getSearchOptions = async () => {};
        const searchForm = ref<TableSearchbarData>({
            goodsId: null
        });
        const onSearch = () => {};

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            { title: "领料单号", key: "id", align: "center" },
            {
                title: "领料清单",
                key: "id",
                align: "center",
                width: 120,
                render: (row) => (
                    <n-button type="primary" onClick={() => openListModal(row)}>
                        点击查看
                    </n-button>
                )
            },
            { title: "领料人", key: "applyByName", align: "center" },
            { title: "领料时间", key: "applyTime", align: "center" },
            {
                title: "出库状态",
                key: "outState",
                align: "center",
                render: (row) => {
                    // 出库状态（1：待出库，2：已确认；3：已拒绝；4：已取消）
                    if (row.outState === 1) {
                        return h(NText, { type: "primary" }, () => "待出库");
                    } else if (row.outState === 2) {
                        return h(NText, { type: "success" }, () => "已确认");
                    } else if (row.outState === 3) {
                        return h(NText, { type: "error" }, () => "已拒绝");
                    } else if (row.outState === 4) {
                        return h(NText, { type: "warning" }, () => "已取消");
                    } else {
                        return h(NText, { type: "default" }, () => "/");
                    }
                }
            },
            {
                title: "用途类型",
                key: "purposeType",
                align: "center",
                render: (row) => {
                    const purposeTypeList = [
                        { label: "原材料", value: 1 },
                        { label: "维修配件", value: 2 },
                        { label: "辅料", value: 3 }
                    ];
                    const object = purposeTypeList.find((i: any) => i.value === row.purposeType);
                    return object?.label ?? "/";
                }
            },
            { title: "拒绝原因", key: "rejectReason", align: "center", render: (row) => row.rejectReason || "/" },
            {
                title: "操作",
                key: "actions",
                align: "center",
                width: 120,
                render: (row) => (
                    <TableActions
                        type="button"
                        buttonActions={[
                            {
                                label: "取消操作",
                                disabled: () => row.outState !== 1,
                                tertiary: true,
                                type: "error",
                                onClick: () => onCancel(row)
                            }
                        ]}
                    />
                )
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_PRODUCTION_MATERIAL_OUT_RECORD_LIST({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                outType: 3,
                ...searchForm.value
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        // 取消
        const rejectReason = ref<string>("");

        const onCancel = (row: RowProps) => {
            window.$dialog.warning({
                title: "提示",
                content: () => {
                    return (
                        <div class="py-10px flex-y-center">
                            <div class="flex-fixed-120">请输入取消原因：</div>
                            <n-input v-model:value={rejectReason.value} clearable placeholder="请输入取消原因" />
                        </div>
                    );
                },
                positiveText: "确认取消",
                negativeText: "我再想想",
                onPositiveClick: () => {
                    if (!rejectReason.value) return window.$message.error("请输入取消原因");
                    POST_PRODUCTION_MATERIAL_OUT_RECORD_CANCEL({
                        id: row.id,
                        cancleReason: rejectReason.value
                    }).then((res) => {
                        if (res.data.code === 0) {
                            window.$message.success("操作成功");
                            getTableData();
                        }
                    });
                }
            });
        };

        // 清单弹窗
        const listModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const openListModal = (row: RowProps) => {
            listModal.value.show = true;
            listModal.value.configData = row;
        };

        onMounted(async () => {
            await getSearchOptions();
            getTableData();
        });

        expose({
            refresh: getTableData
        });

        return () => (
            <div class="plastic-mes-raw-material">
                <n-card>
                    <TableSearchbar
                        form={searchForm.value}
                        config={searchConfig.value}
                        options={searchOptions.value}
                        onSearch={onSearch}
                    />
                </n-card>
                <n-card class="mt">
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        pagination={tablePagination}
                        row-key={tableRowKey}
                        single-line={false}
                        bordered
                        remote
                        striped
                        onUpdate:checked-row-keys={changeTableSelection}
                    />
                </n-card>
                <PlasticMesProductionRawMaterialGetItemList
                    v-model:show={listModal.value.show}
                    configData={listModal.value.configData}
                />
            </div>
        );
    }
});
