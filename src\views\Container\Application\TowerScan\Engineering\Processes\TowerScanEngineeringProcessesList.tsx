import { computed, defineComponent, reactive, ref, watch, watchEffect } from "vue";
import {
    TableSearchbar,
    type TableSearchbarConfig,
    type TableSearchbarData,
    type TableSearchbarOptions
} from "@/components/TableSearchbar";
import {
    GET_IRON_PROJECT_MATERIAL_PROCESS_LIST,
    GET_IRON_PROJECT_PROCESSES_TOP_DETAIL,
    GET_IRON_COMPONENT_PAGE_LIST
} from "@/api/application/TowerScan";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import TowerScanEngineeringProcessesImport from "./TowerScanEngineeringProcessesImport";
import TowerScanEngineeringProcessesPartsExecution from "./TowerScanEngineeringProcessesPartsExecution";
import TowerScanEngineeringProcessesComponentItem from "./TowerScanEngineeringProcessesComponentItem";
import TowerScanEngineeringProcessesComponentExecution from "./TowerScanEngineeringProcessesComponentExecution";
import { TableActions } from "@/components/TableActions";

export default defineComponent({
    name: "TowerScanEngineeringProcessesList",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        // 获取详情
        interface DetailDataProps {
            [key: string]: any;
        }

        const detailData = ref<DetailDataProps>({});

        const getDetail = async () => {
            await GET_IRON_PROJECT_PROCESSES_TOP_DETAIL({ typeId: props.configData.id }).then((res) => {
                if (res.data.code === 0) {
                    detailData.value = res.data.data;
                }
            });
        };

        const onClose = () => {
            changeModalShow(false);
            emit("refresh");
            // 清空数据
            detailData.value = {};
            tableData.value = [];
            tablePagination.itemCount = 0;
            materialClassifyActive.value = "";
            techniqueActive.value = "";
            techniqueOptions.value = [];
            currentProcessType.value = 5;
            resetSearchForm();
        };

        // 材料分类tab - 根据数据动态生成
        const materialClassifyOptions = computed(() => {
            const options: Array<{ label: string; value: string }> = [];

            // 角钢
            if (detailData.value?.jgTechniqueList && detailData.value.jgTechniqueList.length > 0) {
                options.push({ label: "角钢", value: "1" });
            }

            // 钢板
            if (detailData.value?.gbTechniqueList && detailData.value.gbTechniqueList.length > 0) {
                options.push({ label: "钢板", value: "2" });
            }

            // 圆钢
            if (detailData.value?.ygangTechniqueList && detailData.value.ygangTechniqueList.length > 0) {
                options.push({ label: "圆钢", value: "3" });
            }

            // 圆管
            if (detailData.value?.yguanTechniqueList && detailData.value.yguanTechniqueList.length > 0) {
                options.push({ label: "圆管", value: "4" });
            }

            // 槽钢
            if (detailData.value?.cgTechniqueList && detailData.value.cgTechniqueList.length > 0) {
                options.push({ label: "槽钢", value: "5" });
            }

            // 组装电焊 - 根据标志位判断
            if (String(detailData.value?.zzFlag) === "1" || String(detailData.value?.dhFlag) === "1") {
                options.push({ label: "组装电焊", value: "6" });
            }

            return options;
        });

        const materialClassifyActive = ref<string>("");

        // 当前是否为组装电焊tab
        const isComponentTab = computed(() => materialClassifyActive.value === "6");

        // 工艺ID tab（仅用于普通材料）
        const techniqueOptions = ref<any[]>([]);
        const techniqueActive = ref<string>("");

        // 组装电焊工序类型tab
        const currentProcessType = ref<number>(5); // 默认组装工序
        const processTypeOptions = [
            { label: "组装", value: 5 },
            { label: "电焊", value: 6 }
        ];

        const onProcessTypeChange = (value: number) => {
            currentProcessType.value = value;
            // 切换工序类型时重新获取数据
            onSearch();
        };

        // 根据材料分类获取对应的工艺选项
        const getTechniqueOptions = () => {
            let techniqueList: any[] = [];

            // 如果是组装电焊tab，不需要工艺选项
            if (isComponentTab.value) {
                techniqueOptions.value = [];
                techniqueActive.value = "";
                return;
            }

            // 如果没有选中的材料分类，清空工艺选项
            if (!materialClassifyActive.value) {
                techniqueOptions.value = [];
                techniqueActive.value = "";
                return;
            }

            switch (materialClassifyActive.value) {
                case "1": // 角钢
                    techniqueList = detailData.value?.jgTechniqueList || [];
                    break;
                case "2": // 钢板
                    techniqueList = detailData.value?.gbTechniqueList || [];
                    break;
                case "3": // 圆钢
                    techniqueList = detailData.value?.ygangTechniqueList || [];
                    break;
                case "4": // 圆管
                    techniqueList = detailData.value?.yguanTechniqueList || [];
                    break;
                case "5": // 槽钢
                    techniqueList = detailData.value?.cgTechniqueList || [];
                    break;
                default:
                    techniqueList = [];
            }

            // 格式化工艺选项数据
            techniqueOptions.value = techniqueList.map((item: any) => ({ ...item }));

            // 重置工艺选择为第一个选项，确保每次材料分类切换时都重新选择
            const newTechniqueActive = techniqueOptions.value.length > 0 ? String(techniqueOptions.value[0].id) : "";

            // 只有当工艺选择真正改变时才触发数据更新
            if (techniqueActive.value !== newTechniqueActive) {
                techniqueActive.value = newTechniqueActive;
                // 因为 watch 会监听 techniqueActive 的变化，所以不需要手动调用 handleTabChange
            } else {
                // 如果工艺选择没有改变，但工艺列表已经更新，仍需要刷新数据
                if (show.value) {
                    handleTabChange();
                }
            }
        };

        // 监听变化并重新查询
        const handleTabChange = () => {
            if (show.value && materialClassifyActive.value) {
                tablePagination.page = 1;
                getTableData();
            }
        };

        watch(
            () => materialClassifyActive.value,
            () => {
                if (show.value && detailData.value) {
                    // 材料分类切换时重新获取工艺选项，这会自动重置工艺选择
                    getTechniqueOptions();
                }
            }
        );

        watch(() => techniqueActive.value, handleTabChange);

        // 监听材料分类选项变化，确保当前选中的tab有效
        watch(
            () => materialClassifyOptions.value,
            (newOptions) => {
                if (newOptions.length > 0) {
                    // 检查当前选中的分类是否还存在
                    const isCurrentValid = newOptions.some((option) => option.value === materialClassifyActive.value);

                    if (!isCurrentValid) {
                        // 如果当前选中的分类不存在，选择第一个可用的
                        materialClassifyActive.value = newOptions[0].value;
                    }
                } else {
                    // 如果没有可用选项，清空选择
                    materialClassifyActive.value = "";
                }
            },
            { immediate: true }
        );

        // 搜索项 - 根据是否为组装电焊tab动态配置
        const searchConfig = computed<TableSearchbarConfig>(() => {
            if (isComponentTab.value) {
                return [
                    { label: "主件号", prop: "mainMaterialCode", type: "input" },
                    { label: "状态", prop: "fillStatus", type: "select" }
                ];
            } else {
                return [
                    { label: "零件号", prop: "materialCode", type: "input" },
                    { label: "规格", prop: "materialSpec", type: "input" },
                    { label: "尺寸", prop: "materialSize", type: "input" },
                    { label: "状态", prop: "fillStatus", type: "select" }
                ];
            }
        });

        const searchOptions = computed(() => {
            const baseOptions: TableSearchbarOptions = {};

            if (isComponentTab.value) {
                baseOptions.fillStatus = [
                    { label: "待完成", value: 0 },
                    { label: "进行中", value: 1 },
                    { label: "已完成", value: 2 }
                ];
            } else {
                baseOptions.fillStatus = [
                    { label: "待完成", value: 1 },
                    { label: "已完成", value: 2 },
                    { label: "进行中", value: 3 }
                ];
                baseOptions.materialClassify = [
                    { label: "角钢", value: 1 },
                    { label: "钢板", value: 2 },
                    { label: "圆钢", value: 3 },
                    { label: "圆管", value: 4 },
                    { label: "槽钢", value: 5 }
                ];
            }

            return baseOptions;
        });

        const searchForm = ref<TableSearchbarData>({
            materialCode: null,
            materialSpec: null,
            materialSize: null,
            mainMaterialCode: null,
            finishStatus: null,
            fillStatus: null
        });

        // 重置搜索表单
        const resetSearchForm = () => {
            if (isComponentTab.value) {
                searchForm.value = {
                    materialCode: null,
                    materialSpec: null,
                    materialSize: null,
                    mainMaterialCode: null,
                    finishStatus: null,
                    fillStatus: null
                };
                // 只保留组装电焊需要的字段
                searchForm.value.mainMaterialCode = null;
                searchForm.value.fillStatus = null;
                // 清空普通零件字段
                searchForm.value.materialCode = null;
                searchForm.value.materialSpec = null;
                searchForm.value.materialSize = null;
                searchForm.value.finishStatus = null;
            } else {
                searchForm.value = {
                    materialCode: null,
                    materialSpec: null,
                    materialSize: null,
                    mainMaterialCode: null,
                    finishStatus: null,
                    fillStatus: null
                };
                // 只保留普通零件需要的字段
                searchForm.value.materialCode = null;
                searchForm.value.materialSpec = null;
                searchForm.value.materialSize = null;
                searchForm.value.fillStatus = null;
                // 清空组装电焊字段
                searchForm.value.mainMaterialCode = null;
                searchForm.value.finishStatus = null;
            }
        };

        // 监听tab切换重置搜索表单
        watch(() => isComponentTab.value, resetSearchForm);

        // 判断当前选中工艺是否包含"入库"
        const isCurrentTechniqueStorage = computed(() => {
            if (!techniqueActive.value || techniqueOptions.value.length === 0) {
                return false;
            }
            const currentTechnique = techniqueOptions.value.find((item) => String(item.id) === techniqueActive.value);
            return currentTechnique?.treeTechniqueName?.includes("入库") || false;
        });

        const onSearch = () => {
            tablePagination.page = 1;
            tablePagination.pageSize = 10;
            getTableData();
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        // 表格列 - 根据是否为组装电焊tab动态配置
        const tableColumns = computed<DataTableColumns<RowProps>>(() => {
            if (isComponentTab.value) {
                return [
                    { type: "selection" },
                    {
                        title: "主件号",
                        key: "mainMaterialCode",
                        align: "center",
                        render: (row) => row.mainMaterialCode ?? "/"
                    },
                    {
                        title: "组装需求数",
                        key: "composeQuantity",
                        align: "center",
                        render: (row) => row.composeQuantity ?? "/"
                    },
                    {
                        title: "需求总重（KG）",
                        key: "composeWeight",
                        align: "center",
                        render: (row) => row.composeWeight ?? "/"
                    },
                    {
                        title: "工序完成状态",
                        key: "finishStatus",
                        align: "center",
                        render: (row) => {
                            if (row.finishStatus === 0) {
                                return <n-text type="error">待完成</n-text>;
                            } else if (row.finishStatus === 1) {
                                return <n-text type="info">进行中</n-text>;
                            } else if (row.finishStatus === 2) {
                                return <n-text type="success">已完成</n-text>;
                            } else {
                                return <n-text>/</n-text>;
                            }
                        }
                    },
                    {
                        title: "配方完成状态",
                        key: "composeFinishStatus",
                        align: "center",
                        render: (row) => {
                            if (row.composeFinishStatus === 1) {
                                return <n-text type="error">待完成</n-text>;
                            } else if (row.composeFinishStatus === 2) {
                                return <n-text type="success">已完成</n-text>;
                            } else if (row.composeFinishStatus === 3) {
                                return <n-text type="info">进行中</n-text>;
                            } else {
                                return <n-text>/</n-text>;
                            }
                        }
                    },
                    { title: "备注", key: "remark", align: "center", render: (row) => row.remark ?? "/" },
                    {
                        title: "组装零件清单",
                        key: "id",
                        align: "center",
                        width: 120,
                        render: (row) => (
                            <n-button
                                type="primary"
                                size="small"
                                tertiary
                                onClick={() => openComponentDetailModal(row)}
                            >
                                点击查看
                            </n-button>
                        )
                    },
                    {
                        title: "查看执行情况",
                        key: "action",
                        align: "center",
                        width: 110,
                        render: (row) => {
                            return (
                                <TableActions
                                    type="button"
                                    buttonActions={[
                                        {
                                            label: "点击查看",
                                            tertiary: true,
                                            type: "primary",
                                            onClick: () => openComponentExecutionModal(row)
                                        }
                                    ]}
                                />
                            );
                        }
                    }
                ];
            } else {
                return [
                    { type: "selection" },
                    { title: "零件编号", key: "materialCode", align: "center" },
                    {
                        title: "零件类别",
                        key: "materialClassify",
                        align: "center",
                        render: (row) => {
                            if (String(row.materialClassify) === "1") {
                                return <n-text type="info">角钢</n-text>;
                            } else if (String(row.materialClassify) === "2") {
                                return <n-text type="info">钢板</n-text>;
                            } else if (String(row.materialClassify) === "3") {
                                return <n-text type="info">圆钢</n-text>;
                            } else if (String(row.materialClassify) === "4") {
                                return <n-text type="info">圆管</n-text>;
                            } else if (String(row.materialClassify) === "5") {
                                return <n-text type="info">槽钢</n-text>;
                            } else {
                                return <n-text type="info">/</n-text>;
                            }
                        }
                    },
                    {
                        title: "工序完成状态",
                        key: "fillStatus",
                        align: "center",
                        render: (row) => {
                            if (row.fillStatus === 1) {
                                return <n-text type="error">待完成</n-text>;
                            } else if (row.fillStatus === 2) {
                                return <n-text type="success">已完成</n-text>;
                            } else if (row.fillStatus === 3) {
                                return <n-text type="info">进行中</n-text>;
                            } else {
                                return <n-text>/</n-text>;
                            }
                        }
                    },
                    // {
                    //     title: "零件入库状态",
                    //     key: "finishStatus",
                    //     align: "center",
                    //     render: (row) => {
                    //         if (row.finishStatus === 1) {
                    //             return <n-text type="error">待完成</n-text>;
                    //         } else if (row.finishStatus === 2) {
                    //             return <n-text type="success">已完成</n-text>;
                    //         } else if (row.finishStatus === 3) {
                    //             return <n-text type="info">进行中</n-text>;
                    //         } else {
                    //             return <n-text>/</n-text>;
                    //         }
                    //     }
                    // },
                    {
                        title: "材质",
                        key: "materialQuality",
                        align: "center",
                        render: (row) => row.materialQuality ?? "/"
                    },
                    { title: "规格", key: "materialSpec", align: "center", render: (row) => row.materialSpec ?? "/" },
                    { title: "尺寸", key: "materialSize", align: "center", render: (row) => row.materialSize ?? "/" },
                    {
                        title: "单基数量",
                        key: "singleBaseQuantity",
                        align: "center",
                        render: (row) => row.singleBaseQuantity ?? "/"
                    },
                    {
                        title: "多基数量",
                        key: "multiBaseQuantity",
                        align: "center",
                        render: (row) => row.multiBaseQuantity ?? "/"
                    },
                    { title: "单重", key: "singleWeight", align: "center", render: (row) => row.singleWeight ?? "/" },
                    { title: "多基总重", key: "multiWeight", align: "center", render: (row) => row.multiWeight ?? "/" },
                    {
                        title: "备注",
                        key: "remark",
                        align: "center",
                        render: (row) => (!!row.remark && row.remark !== "" ? row.remark : "/")
                    },
                    { title: "入库完成时间", key: "fillDate", align: "center", render: (row) => row.fillDate ?? "/" },
                    {
                        title: "入库完成人姓名",
                        key: "fillByName",
                        align: "center",
                        render: (row) => row.fillByName ?? "/"
                    },
                    {
                        title: "查看执行情况",
                        key: "action",
                        align: "center",
                        fixed: "right",
                        width: 110,
                        render: (row) => {
                            return (
                                <TableActions
                                    type="button"
                                    buttonActions={[
                                        {
                                            label: "点击查看",
                                            tertiary: true,
                                            type: "primary",
                                            disabled: () => isCurrentTechniqueStorage.value,
                                            onClick: () => openExecutionModal(row)
                                        }
                                    ]}
                                />
                            );
                        }
                    }
                ];
            }
        });

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            // 如果没有选中的材料分类，不加载数据
            if (!materialClassifyActive.value) {
                tableData.value = [];
                tablePagination.itemCount = 0;
                return;
            }

            tableLoading.value = true;

            const params: any = {
                current: tablePagination.page,
                size: tablePagination.pageSize,
                typeId: props.configData.id,
                ...searchForm.value
            };

            // 根据是否为组装电焊tab调用不同的API
            if (isComponentTab.value) {
                // 组装电焊API
                params.processType = currentProcessType.value;
                GET_IRON_COMPONENT_PAGE_LIST(params).then((res) => {
                    tableLoading.value = false;
                    if (res.data.code === 0) {
                        tableData.value = res.data.data.records;
                        tablePagination.itemCount = res.data.data.total;
                    } else {
                        tableData.value = [];
                        tablePagination.itemCount = 0;
                    }
                });
            } else {
                // 普通材料API
                params.materialClassify = materialClassifyActive.value;
                // 即使 techniqueActive 为空也传递参数，让后端处理
                if (techniqueActive.value !== undefined) params.techniqueId = techniqueActive.value;

                GET_IRON_PROJECT_MATERIAL_PROCESS_LIST(params).then((res) => {
                    tableLoading.value = false;
                    if (res.data.code === 0) {
                        tableData.value = res.data.data.records;
                        tablePagination.itemCount = res.data.data.total;
                    } else {
                        tableData.value = [];
                        tablePagination.itemCount = 0;
                    }
                });
            }
        };

        // 导入弹窗
        const importModal = ref<{ show: boolean; configData: any }>({ show: false, configData: {} });

        const openImportModal = () => {
            importModal.value = { show: true, configData: props.configData };
        };

        // 执行情况弹窗（普通材料）
        const executionModal = ref<{ show: boolean; configData: any }>({ show: false, configData: {} });

        const openExecutionModal = (row: RowProps) => {
            executionModal.value = {
                show: true,
                configData: {
                    materialId: row.materialId,
                    planTechniqueId: techniqueActive.value,
                    materialCode: row.materialCode,
                    typeName: detailData.value?.ironType?.typeName,
                    projectName: detailData.value?.ironProject?.projectName
                }
            };
        };

        // 组装零件清单弹窗
        const componentDetailModal = ref<{ show: boolean; configData: any }>({ show: false, configData: {} });

        const openComponentDetailModal = (row: RowProps) => {
            componentDetailModal.value = {
                show: true,
                configData: { id: row.componentId }
            };
        };

        // 组装电焊执行情况弹窗
        const componentExecutionModal = ref<{ show: boolean; configData: any }>({ show: false, configData: {} });

        const openComponentExecutionModal = (row: RowProps) => {
            componentExecutionModal.value = {
                show: true,
                configData: {
                    componentId: row.componentId,
                    processId: row.processId,
                    processType: currentProcessType.value,
                    mainMaterialCode: row.mainMaterialCode,
                    typeName: detailData.value?.ironType?.typeName,
                    projectName: detailData.value?.ironProject?.projectName
                }
            };
        };

        watchEffect(async () => {
            if (show.value && props.configData.id) {
                await getDetail();
                // 获取详情后再获取工艺选项
                getTechniqueOptions();
            }
        });

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card
                    title={`工序表详情 - ${detailData.value?.ironType?.typeName ?? "/"} - ${
                        detailData.value?.ironProject?.projectName ?? "/"
                    }`}
                    class={"w-1200px my-5vh"}
                    closable
                    onClose={onClose}
                >
                    <n-form label-placement="left" label-width="auto">
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi required span={4} label="工程名称">
                                {detailData.value?.ironProject?.projectName ?? "/"}
                            </n-form-item-gi>
                            <n-form-item-gi required span={4} label="工程简称">
                                {detailData.value?.ironProject?.projectAs ?? "/"}
                            </n-form-item-gi>
                            <n-form-item-gi required span={4} label="合同号">
                                {detailData.value?.ironProject?.contractNumber ?? "/"}
                            </n-form-item-gi>
                            <n-form-item-gi required span={4} label="塔型">
                                {detailData.value?.ironType?.typeName ?? "/"}
                            </n-form-item-gi>
                            <n-form-item-gi required span={4} label="电压等级">
                                {detailData.value?.ironProject?.powerLevel ?? "/"}
                            </n-form-item-gi>
                            <n-form-item-gi span={12}>
                                <div>
                                    <div class="text-18px font-bold">导入工序要求：</div>
                                </div>
                            </n-form-item-gi>
                            <n-form-item-gi required span={4} label="组装工序">
                                <n-text type="info">{String(detailData.value?.zzFlag) === "1" ? "有" : "无"}</n-text>
                            </n-form-item-gi>
                            <n-form-item-gi required span={4} label="电焊工序">
                                <n-text type="info">{String(detailData.value?.dhFlag) === "1" ? "有" : "无"}</n-text>
                            </n-form-item-gi>
                            {(detailData.value?.jgTechniqueList ?? []).length > 0 && (
                                <n-form-item-gi required span={12} label="角钢工序">
                                    <n-text type="info">
                                        {detailData.value.jgTechniqueList
                                            .map((item: any) => item.techniqueName)
                                            .join(",")}
                                    </n-text>
                                </n-form-item-gi>
                            )}
                            {(detailData.value?.gbTechniqueList ?? []).length > 0 && (
                                <n-form-item-gi required span={12} label="钢板工序">
                                    <n-text type="info">
                                        {detailData.value.gbTechniqueList
                                            .map((item: any) => item.techniqueName)
                                            .join(",")}
                                    </n-text>
                                </n-form-item-gi>
                            )}
                            {(detailData.value?.ygangTechniqueList ?? []).length > 0 && (
                                <n-form-item-gi required span={12} label="圆钢工序">
                                    <n-text type="info">
                                        {detailData.value.ygangTechniqueList
                                            .map((item: any) => item.techniqueName)
                                            .join(",")}
                                    </n-text>
                                </n-form-item-gi>
                            )}
                            {(detailData.value?.yguanTechniqueList ?? []).length > 0 && (
                                <n-form-item-gi required span={12} label="圆管工序">
                                    <n-text type="info">
                                        {detailData.value.yguanTechniqueList
                                            .map((item: any) => item.techniqueName)
                                            .join(",")}
                                    </n-text>
                                </n-form-item-gi>
                            )}
                            {(detailData.value?.cgTechniqueList ?? []).length > 0 && (
                                <n-form-item-gi required span={12} label="槽钢工序">
                                    <n-text type="info">
                                        {detailData.value.cgTechniqueList
                                            .map((item: any) => item.techniqueName)
                                            .join(",")}
                                    </n-text>
                                </n-form-item-gi>
                            )}
                        </n-grid>
                    </n-form>

                    {/* 材料分类tabs - 根据数据动态显示 */}
                    {materialClassifyOptions.value.length > 0 && (
                        <n-tabs
                            value={materialClassifyActive.value}
                            animated
                            class="flex-fixed-200"
                            type="bar"
                            onUpdate:value={(val: string) => {
                                materialClassifyActive.value = val;
                            }}
                        >
                            {materialClassifyOptions.value.map((item) => (
                                <n-tab-pane key={item.value} name={item.value} tab={item.label} />
                            ))}
                        </n-tabs>
                    )}

                    {/* 搜索表单始终显示 */}
                    <n-card class="mt-2">
                        <TableSearchbar
                            form={searchForm.value}
                            config={searchConfig.value}
                            options={searchOptions.value}
                            onSearch={onSearch}
                            buttonAlign="left"
                            v-slots={{
                                buttons: () => (
                                    <n-button type="warning" onClick={openImportModal}>
                                        导入工序表
                                    </n-button>
                                )
                            }}
                        />
                    </n-card>

                    {/* 组装电焊工序类型tabs - 仅在组装电焊tab时显示 */}
                    {isComponentTab.value && (
                        <n-tabs
                            value={currentProcessType.value}
                            onUpdate:value={onProcessTypeChange}
                            type="bar"
                            class="mt"
                        >
                            {processTypeOptions.map((option) => (
                                <n-tab-pane key={option.value} name={option.value} tab={option.label} />
                            ))}
                        </n-tabs>
                    )}

                    {/* 工艺ID tabs - 只有在有工艺选项时才显示（普通材料） */}
                    {!isComponentTab.value && techniqueOptions.value.length > 0 && (
                        <n-tabs
                            key={`technique-tabs-${materialClassifyActive.value}`}
                            class="mt-2"
                            v-model:value={techniqueActive.value}
                            animated
                            type="bar"
                            onUpdate:value={(val: string) => (techniqueActive.value = val)}
                        >
                            {techniqueOptions.value.map((item) => (
                                <n-tab-pane key={item.id} name={String(item.id)} tab={item.treeTechniqueName} />
                            ))}
                        </n-tabs>
                    )}

                    {/* 数据表格始终显示 */}
                    <n-card class="mt">
                        <n-data-table
                            columns={tableColumns.value}
                            data={tableData.value}
                            loading={tableLoading.value}
                            pagination={tablePagination}
                            row-key={tableRowKey}
                            single-line={false}
                            bordered
                            remote
                            striped
                            onUpdate:checked-row-keys={changeTableSelection}
                        />
                    </n-card>

                    <TowerScanEngineeringProcessesImport
                        v-model:show={importModal.value.show}
                        configData={importModal.value.configData}
                        onRefresh={() => {
                            getTableData();
                        }}
                    />

                    <TowerScanEngineeringProcessesPartsExecution
                        v-model:show={executionModal.value.show}
                        configData={executionModal.value.configData}
                        permissions={{
                            edit: true, // 工程模块保持原有功能
                            delete: true, // 工程模块保持原有功能
                            history: true // 工程模块保持原有功能
                        }}
                        onRefresh={() => {
                            getTableData();
                        }}
                    />

                    <TowerScanEngineeringProcessesComponentItem
                        v-model:show={componentDetailModal.value.show}
                        configData={componentDetailModal.value.configData}
                    />

                    <TowerScanEngineeringProcessesComponentExecution
                        v-model:show={componentExecutionModal.value.show}
                        configData={componentExecutionModal.value.configData}
                    />
                </n-card>
            </n-modal>
        );
    }
});
