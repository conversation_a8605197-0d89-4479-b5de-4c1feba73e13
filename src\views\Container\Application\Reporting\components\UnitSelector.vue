<template>
    <div>
        <n-select
            v-if="type === 'select'"
            v-model:value="value"
            :options="options"
            clearable
            filterable
            placeholder="请选择单位"
        />
        <div v-else-if="type === 'text'">{{ getUnitText(value) ?? "未知" }}</div>
    </div>
</template>

<script lang="ts" setup>
import { computed } from "vue";

let props = withDefaults(
    defineProps<{
        type: "select" | "text";
        options: any[];
        value: Nullable<string>;
    }>(),
    {}
);

let emits = defineEmits(["update:value", "submit"]);

let value = computed({
    get: () => props.value,
    set: (val: Nullable<string>) => {
        emits("update:value", val);
        emits("submit", val);
    }
});

let getUnitText = (value: Nullable<string>): string => {
    let object = props.options.find((item) => item.value === value);
    return object?.label ?? "未知";
};
</script>
