<template>
    <n-drawer v-model:show="show" :close-on-esc="false" :mask-closable="false" placement="right" width="600px">
        <n-card :title="`编辑配方：${configData.formulaName}`" closable @close="closeModal">
            <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                <n-form-item label="配方名称" path="formulaName">
                    <n-input
                        v-model:value="formData.formulaName"
                        class="w-100%"
                        clearable
                        placeholder="请输入配方名称"
                    />
                </n-form-item>
                <n-form-item label="配方表" path="chargerSheet" required>
                    <DynamicTable
                        v-model:header="formData.chargerSheet.header"
                        v-model:value="formData.chargerSheet.value"
                        :data-source="dynamicTableDataSource"
                        class="w-100%"
                    />
                </n-form-item>
                <n-form-item>
                    <n-space>
                        <n-button type="primary" @click="onSubmit">提交</n-button>
                        <n-button @click="closeModal">取消</n-button>
                    </n-space>
                </n-form-item>
            </n-form>
        </n-card>
    </n-drawer>
</template>

<script lang="ts" setup>
import { ref, watchEffect } from "vue";
import { useStoreUser } from "@/store";
import { useDicts } from "@/hooks";
import {
    GET_PRODUCTION_FORMULA_DETAIL,
    GET_PRODUCTION_TYPE_LIST,
    UPDATE_PRODUCTION_FORMULA
} from "@/api/application/production";
import { DynamicTable } from "@/components/Dynamic";
import type {
    DynamicTableDataSourceProps,
    DynamicTableHeaderProps,
    DynamicTableRowProps
} from "@/components/Dynamic/DynamicTable";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { isJSON } from "@/utils/tools";

let storeUser = useStoreUser();

let props = withDefaults(
    defineProps<{
        show: Boolean;
        configData: UnKnownObject;
    }>(),
    {
        show: () => false
    }
);

let emits = defineEmits(["update:show", "refresh"]);

// 监听
watchEffect(async () => {
    if (props.show) {
        await getDetail();
        await setDictLibs();
        await setDynamicTableDataSource();
    }
});

// 字典操作
let { dictLibs, getDictLibs } = useDicts();

let setDictLibs = async () => {
    let dictName = ["product_type"];
    await getDictLibs(dictName);
};

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {
    formulaName: {
        required: true,
        message: "请输入配方名称",
        trigger: ["input", "blur"]
    }
};

// 表单数据
interface FormDataProps {
    formulaName: string;
    chargerSheet: {
        header: DynamicTableHeaderProps[];
        value: DynamicTableRowProps[];
    };
}

let initFormData: FormDataProps = {
    formulaName: "",
    chargerSheet: {
        header: [],
        value: []
    }
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

let dynamicTableDataSource = ref<DynamicTableDataSourceProps[]>([]);

let setDynamicTableDataSource = async () => {
    // 规格型号
    let specifications = await GET_PRODUCTION_TYPE_LIST({ current: 1, size: 10000 }).then((res) => {
        if (res.data.code === 0) {
            return res.data.data.records.map((item: any) => ({
                label: item.specification,
                value: item.id
            }));
        }
    });

    let tubeClassification = dictLibs.product_type;

    dynamicTableDataSource.value = [
        { label: "管材分类", value: "tubeClassification", defaultOptions: tubeClassification },
        { label: "规格型号", value: "specifications", defaultOptions: specifications }
    ];
};

// 获取详情
let getDetail = async () => {
    await GET_PRODUCTION_FORMULA_DETAIL({ formulaId: props.configData.formulaId }).then((res) => {
        formData.value = {
            ...res.data.data,
            chargerSheet: isJSON(res.data.data.chargerSheet)
                ? JSON.parse(res.data.data.chargerSheet)
                : {
                      header: "",
                      value: ""
                  }
        };
    });
    console.log(23333, formData.value);
};

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    emits("refresh");
    emits("update:show", false);
};

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    UPDATE_PRODUCTION_FORMULA({
        ...formData.value,
        chargerSheet: formData.value.chargerSheet ? JSON.stringify(formData.value.chargerSheet) : ""
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("编辑成功");
            closeModal();
            emits("refresh");
        }
    });
};
</script>
