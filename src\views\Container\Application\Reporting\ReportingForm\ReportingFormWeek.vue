<template>
    <div class="flex-center">
        <n-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            class="w-500px"
            label-placement="left"
            label-width="auto"
        >
            <n-form-item label="所属公司" path="companyId">
                <n-select
                    v-model:value="formData.companyId"
                    :options="companyIdOptions"
                    placeholder="请选择所属公司"
                    @update:value="changeCompanyIdOptions"
                />
            </n-form-item>
            <n-form-item label="所属班组" path="workGroupId">
                <n-select
                    v-model:value="formData.workGroupId"
                    :options="workGroupIdOptions"
                    placeholder="请选择所属班组"
                    clearable
                />
            </n-form-item>
            <n-form-item label="计划开始时间" path="planBeginDate">
                <n-date-picker
                    v-model:formatted-value="formData.planBeginDate"
                    class="w-100%"
                    clearable
                    placeholder="请选择计划开始时间"
                    type="date"
                    value-format="yyyy-MM-dd"
                />
            </n-form-item>
            <n-form-item label="计划结束时间" path="planEndDate">
                <n-date-picker
                    v-model:formatted-value="formData.planEndDate"
                    class="w-100%"
                    clearable
                    placeholder="请选择计划结束时间"
                    type="date"
                    value-format="yyyy-MM-dd"
                />
            </n-form-item>
            <n-form-item class="flex-center">
                <n-space>
                    <n-button type="primary" @click="getWeekForm">导出</n-button>
                    <n-button @click="clearFrom">取消</n-button>
                </n-space>
            </n-form-item>
        </n-form>
    </div>
</template>

<script lang="ts" setup>
import { DOWNLOAD_REPORT, EXPORT_WEEK_REPORT, GET_CONFIG_COMPANY_LIST } from "@/api/application/reporting";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { onMounted, ref } from "vue";
import { GET_WORK_GROUP_PAGE_LIST } from "@/api/application/production";

onMounted(async () => {
    await getCompanyIdOptions();
});

let companyIdOptions = ref<any[]>([]);
let workGroupIdOptions = ref<any[]>([]);

let getCompanyIdOptions = async () => {
    await GET_CONFIG_COMPANY_LIST({ needFill: 1 }).then((res) => {
        companyIdOptions.value = (res.data.data || []).map((item: any) => ({
            label: item.companyName,
            value: item.id
        }));
    });
};

let changeCompanyIdOptions = async (value: any) => {
    await GET_WORK_GROUP_PAGE_LIST({
        current: 1,
        size: 9999,
        companyId: value
    }).then((res) => {
        console.log(res.data.data.records);
        workGroupIdOptions.value = (res.data.data.records ?? []).map((item: any) => ({
            label: item.companyName + "-" + item.workshopName + "-" + item.groupName,
            value: item.id
        }));
    });
};

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = {
    companyId: [{ required: true, message: "请选择所属公司", trigger: ["blur", "change"] }],
    workGroupId: [{ required: false, message: "请选择所属班组", trigger: ["blur", "change"] }],
    planBeginDate: [{ required: true, message: "请选择计划开始时间", trigger: ["blur", "change"] }],
    planEndDate: [{ required: true, message: "请选择计划结束时间", trigger: ["blur", "change"] }]
};

// 表单数据
type FormDataProps = Record<string, any>;

let initFormData: FormDataProps = {
    companyId: null,
    workGroupId: null,
    planBeginDate: null,
    planEndDate: null
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

onMounted(() => {});

let get;

let getWeekForm = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    EXPORT_WEEK_REPORT({ ...formData.value }).then((res) => {
        console.log(res.data.data);
        DOWNLOAD_REPORT({
            fileName: res.data.data
        }).then((cres) => {
            let blob = new Blob([cres.data]);
            let a = document.createElement("a");
            a.href = URL.createObjectURL(blob);
            a.download = res.data.data ?? "周报表";
            a.style.display = "none";
            document.body.appendChild(a);
            a.click();
            a.remove();
        });
    });
};
</script>
