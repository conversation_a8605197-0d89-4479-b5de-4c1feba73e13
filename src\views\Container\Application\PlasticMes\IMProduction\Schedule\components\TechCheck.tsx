import { computed, defineComponent } from "vue";

export default defineComponent({
    name: "PlasticMesIMProductionScheduleTechCheck",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        const onClose = () => {
            changeModalShow(false);
            emit("refresh");
        };

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card title="查看技术核对内容" class="w-800px" closable onClose={onClose}>
                    <n-form label-placement="left" label-width="auto">
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi required span={6} label="反馈人">
                                {props.configData?.techCheckByName ?? "/"}
                            </n-form-item-gi>
                            <n-form-item-gi required span={6} label="反馈时间">
                                {props.configData?.techCheckTime ?? "/"}
                            </n-form-item-gi>
                            <n-form-item-gi required span={6} label="反馈结果">
                                {props.configData?.techCheckState === 1 ? "满足" : "不满足"}
                            </n-form-item-gi>
                            <n-form-item-gi required span={6} label="反馈内容">
                                {props.configData?.techCheckRemark ?? "/"}
                            </n-form-item-gi>
                        </n-grid>
                    </n-form>
                </n-card>
            </n-modal>
        );
    }
});
