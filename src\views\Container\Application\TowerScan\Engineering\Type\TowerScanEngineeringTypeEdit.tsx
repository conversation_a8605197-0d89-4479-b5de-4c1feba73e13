import { computed, defineComponent, h, ref, watchEffect } from "vue";
import { type FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { useStoreUser } from "@/store";
import {
    ADD_IRON_PROJECT_TYPE,
    GET_IRON_PROJECT_TYPE_DETAIL,
    UPDATE_IRON_PROJECT_TYPE
} from "@/api/application/TowerScan";
import { UserSelector } from "@/components/UserSelector";
import dayjs from "dayjs";
import { FileValueUploader } from "@/components/Uploader";

export default defineComponent({
    name: "TowerScanEngineeringTypeEdit",
    props: {
        show: { type: Boolean, default: false },
        configData: { type: Object, default: () => ({}) }
    },
    emits: ["update:show", "refresh"],
    setup(props, { emit }) {
        const show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });
        const changeModalShow = (show: boolean) => emit("update:show", show);

        const storeUser = useStoreUser();

        // 获取详情
        const getDetail = () => {
            GET_IRON_PROJECT_TYPE_DETAIL({ typeId: props.configData.id }).then((res) => {
                if (res.data.code === 0) {
                    const typeDrawingList = res.data.data.typeDrawingList ?? [];
                    formData.value = {
                        operBy: res.data.data.operBy,
                        operTime: res.data.data.operTime,
                        typeName: res.data.data.typeName,
                        materialNo: res.data.data.materialNo,
                        towerSegment: res.data.data.towerSegment,
                        towerBase: res.data.data.towerBase,
                        remark: res.data.data.remark,
                        typeDrawingId: typeDrawingList?.[0]?.typeDrawingId ?? null,
                        // 保存原始的typeDrawingList信息，用于编辑时保留id
                        originalTypeDrawingList: typeDrawingList
                    };
                }
            });
        };

        // 表单数据
        interface FormDataProps {
            [key: string]: any;
        }

        const formRef = ref<FormInst | null>(null);

        const initFormData: FormDataProps = {
            operBy: storeUser.getUserData.sysUser?.username,
            operTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
            typeName: null,
            materialNo: null,
            towerSegment: null,
            towerBase: null,
            remark: null,
            typeDrawingId: null,
            originalTypeDrawingList: []
        };

        const formRules = computed(() => ({
            operTime: [{ required: true, message: "请选择操作时间", trigger: ["blur", "change"] }],
            typeName: [{ required: true, message: "请输入塔型", trigger: ["input", "blur"] }],
            towerSegment: [{ required: true, message: "请输入对应端次", trigger: ["input", "blur"] }],
            towerBase: [{ required: true, message: "请输入加工基数", trigger: ["input", "blur"] }]
        }));

        const formData = ref(cloneDeep(initFormData));

        const clearForm = () => {
            formData.value = cloneDeep(initFormData);
        };

        const onClose = () => {
            clearForm();
            changeModalShow(false);
            emit("refresh");
        };

        const onSubmit = async () => {
            let validateError = await formRef.value?.validate((errors) => !!errors);
            if (validateError) return false;

            const submitData = {
                ...formData.value
            };

            // 构建typeDrawingList
            const buildTypeDrawingList = () => {
                if (props.configData.id) {
                    // 编辑模式：保留原有id，更新typeDrawingId
                    const originalItem = submitData.originalTypeDrawingList?.[0];
                    if (originalItem && submitData.typeDrawingId) {
                        return [
                            {
                                id: originalItem.id,
                                typeDrawingId: submitData.typeDrawingId,
                                delFlag: 0
                            }
                        ];
                    } else if (submitData.typeDrawingId) {
                        // 原来没有图纸，现在新增
                        return [
                            {
                                typeDrawingId: submitData.typeDrawingId,
                                delFlag: 0
                            }
                        ];
                    } else if (originalItem) {
                        // 删除原有图纸
                        return [
                            {
                                id: originalItem.id,
                                typeDrawingId: originalItem.typeDrawingId,
                                delFlag: 1
                            }
                        ];
                    }
                    return [];
                } else {
                    // 新增模式：只有typeDrawingId时才添加
                    return submitData.typeDrawingId
                        ? [
                              {
                                  typeDrawingId: submitData.typeDrawingId,
                                  delFlag: 0
                              }
                          ]
                        : [];
                }
            };

            const typeDrawingList = buildTypeDrawingList();

            if (props.configData.id) {
                await UPDATE_IRON_PROJECT_TYPE({
                    id: props.configData.id,
                    projectId: props.configData.projectId,
                    ...submitData,
                    typeDrawingList
                }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("编辑成功");
                        onClose();
                    }
                });
            } else {
                await ADD_IRON_PROJECT_TYPE({
                    projectId: props.configData.projectId,
                    ...submitData,
                    typeDrawingList
                }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("新增成功");
                        onClose();
                    }
                });
            }
        };

        watchEffect(async () => {
            if (show.value) {
                if (props.configData.id) getDetail();
            }
        });

        return () => (
            <n-modal v-model:show={show.value} close-on-esc={false} mask-closable={false}>
                <n-card
                    title={props.configData.id ? "编辑塔型" : "新增塔型"}
                    class="w-1200px"
                    closable
                    onClose={onClose}
                >
                    <n-form
                        ref={formRef}
                        model={formData.value}
                        rules={formRules.value}
                        label-placement="left"
                        label-width="auto"
                    >
                        <n-grid cols={12} x-gap={16}>
                            <n-form-item-gi span={6} label="操作人" path="operBy" required>
                                <UserSelector
                                    value={formData.value.operBy}
                                    class="w-100%"
                                    disabled
                                    key-name="username"
                                    placeholder="请选择操作人"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="操作时间" path="operTime">
                                <n-date-picker
                                    v-model:formatted-value={formData.value.operTime}
                                    class="w-100%"
                                    clearable
                                    placeholder="请选择操作时间"
                                    type="datetime"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="塔型" path="typeName">
                                <n-input
                                    v-model:value={formData.value.typeName}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入塔型"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="材料表号" path="materialNo">
                                <n-input
                                    v-model:value={formData.value.materialNo}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入材料表号"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="对应端次" path="towerSegment">
                                <n-input
                                    v-model:value={formData.value.towerSegment}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入对应端次"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={6} label="加工基数" path="towerBase">
                                <n-input
                                    v-model:value={formData.value.towerBase}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入加工基数"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={12} label="备注" path="remark">
                                <n-input
                                    v-model:value={formData.value.remark}
                                    class="w-100%"
                                    clearable
                                    placeholder="请输入备注"
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={12} label="上传塔型图纸" path="typeDrawingId">
                                <FileValueUploader
                                    v-model:value={formData.value.typeDrawingId}
                                    value-key="fileId"
                                    multiple={false}
                                    max-files={1}
                                />
                            </n-form-item-gi>
                            <n-form-item-gi span={12}>
                                <n-space>
                                    <n-button type="primary" onClick={onSubmit}>
                                        提交
                                    </n-button>
                                    <n-button onClick={onClose}>取消</n-button>
                                </n-space>
                            </n-form-item-gi>
                        </n-grid>
                    </n-form>
                </n-card>
            </n-modal>
        );
    }
});
