import { defineComponent, onMounted, reactive, ref, watchEffect } from "vue";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import {
    ATTENDANCE_CARD_REPLACEMENT,
    ATTENDANCE_EXCEPTION_HANDLING,
    DELETE_ABNORMAL_ATTENDANCE,
    GET_ABNORMAL_ATTENDANCE,
    GET_USER_AVAILABLE_REPLENISH_COUNT
} from "@/api/permission";
import dayjs from "dayjs";
import { TableActions } from "@/components/TableActions";

export default defineComponent({
    name: "AttendanceDataAbnormalHandle",
    props: {
        corpId: { type: String as PropType<string | null>, default: null }
    },
    setup(props, { emit }) {
        // 搜索项
        const searchConfig = ref<TableSearchbarConfig>([]);

        const searchOptions = ref<TableSearchbarOptions>({});

        const getSearchOptions = async () => {};

        const searchForm = ref<TableSearchbarData>({});

        const onSearch = () => {
            getTableData();
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            {
                title: "人员名称",
                key: "personName",
                align: "center",
                render: (row) => <n-text type="info">{row.personName ?? "/"}</n-text>
            },
            {
                title: "异常日期",
                key: "workDate",
                align: "center",
                render: (row) => <n-text>{dayjs(row.workDate).format("YYYY-MM-DD") ?? "/"}</n-text>
            },
            {
                title: "触发考勤规则",
                key: "ruleName",
                align: "center",
                render: (row) => <n-text type="info">{row.ruleName ?? "/"}</n-text>
            },
            {
                title: "异常类型",
                key: "dstatus",
                align: "center",
                render: (row) => {
                    switch (row.dstatus) {
                        case 2:
                            return <n-text type="error">缺卡</n-text>;
                        case 3:
                            return <n-text type="warning">早退</n-text>;
                        case 4:
                            return <n-text type="warning">迟到</n-text>;
                        default:
                            return <n-text type="info">正常</n-text>;
                    }
                }
            },
            {
                title: "异常详情",
                key: "statusDescription",
                align: "center",
                render: (row) => <n-text>{row.statusDescription ?? "/"}</n-text>
            },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 150,
                render: (row) => {
                    return (
                        <TableActions
                            type="button"
                            buttonActions={[
                                {
                                    label: "处理",
                                    tertiary: true,
                                    type: "primary",
                                    onClick: () => {
                                        if (row.dstatus === 2) {
                                            onCardReplacement(row);
                                        } else {
                                            onHandleAbnormal(row);
                                        }
                                    }
                                },
                                {
                                    label: "删除",
                                    tertiary: true,
                                    type: "error",
                                    onClick: () => onDelete(row)
                                }
                            ]}
                        />
                    );
                }
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_ABNORMAL_ATTENDANCE({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                corpId: props.corpId,
                ...searchForm.value
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        // 异常操作
        const handleAbnormalRemark = ref("");

        const onHandleAbnormal = (row: RowProps) => {
            window.$dialog.info({
                title: "温馨提示",
                content: () => (
                    <div>
                        <div>该操作将把异常考勤进行正常化处理，是否继续？</div>
                        <div class="mt-4 flex-y-center">
                            <div class="flex-fixed-60">备注：</div>
                            <n-input v-model:value={cardReplacementRemark.value} placeholder="请输入备注" />
                        </div>
                    </div>
                ),
                positiveText: "确认",
                negativeText: "取消",
                onPositiveClick: () => {
                    ATTENDANCE_EXCEPTION_HANDLING({
                        attendanceDayId: row.id,
                        reason: handleAbnormalRemark.value
                    }).then((res: { data: RowProps }) => {
                        if (res.data.code === 0) {
                            window.$message.success("处理成功");
                            getTableData();
                        }
                    });
                }
            });
        };

        // 补卡操作
        const cardReplacementRemark = ref("");

        const onCardReplacement = (row: RowProps) => {
            // 先查询用户剩余补卡次数
            GET_USER_AVAILABLE_REPLENISH_COUNT({
                userId: row.userId
            }).then((countRes: { data: any }) => {
                if (countRes.data.code === 0) {
                    const remainingCount = countRes.data.data || 0;
                    
                    window.$dialog.info({
                        title: "温馨提示",
                        content: () => (
                            <div>
                                <div>
                                    {row.personName}本月剩余可补卡次数
                                    <n-text type="info">{remainingCount}</n-text>次
                                </div>
                                <div class="mt-2">本操作将扣除该用户本月补卡次数，是否继续？</div>
                                <div class="mt-4 flex-y-center">
                                    <div class="flex-fixed-60">备注：</div>
                                    <n-input v-model:value={cardReplacementRemark.value} placeholder="请输入备注" />
                                </div>
                            </div>
                        ),
                        positiveText: "确认",
                        negativeText: "取消",
                        onPositiveClick: () => {
                            ATTENDANCE_CARD_REPLACEMENT({
                                attendanceDayId: row.id,
                                reason: cardReplacementRemark.value
                            }).then((res: { data: RowProps }) => {
                                if (res.data.code === 0) {
                                    window.$message.success("处理成功");
                                    getTableData();
                                }
                            });
                        }
                    });
                } else {
                    window.$message.error("查询用户补卡次数失败");
                }
            }).catch(() => {
                window.$message.error("查询用户补卡次数失败");
            });
        };

        const onDelete = (row: RowProps) => {
            window.$dialog.warning({
                title: "警告",
                content: "确定要删除该条数据吗？",
                positiveText: "确定",
                negativeText: "取消",
                onPositiveClick: () => {
                    DELETE_ABNORMAL_ATTENDANCE({
                        id: row.id
                    }).then((res: { data: RowProps }) => {
                        if (res.data.code === 0) {
                            window.$message.success("删除成功");
                            getTableData();
                        }
                    });
                }
            });
        };

        watchEffect(() => {
            if (props.corpId) getTableData();
        });

        onMounted(async () => {
            await getSearchOptions();
        });

        return () => (
            <div class="attendance-page">
                <n-card>
                    <TableSearchbar
                        form={searchForm.value}
                        config={searchConfig.value}
                        options={searchOptions.value}
                        onSearch={onSearch}
                    />
                </n-card>
                <n-card class="mt">
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        pagination={tablePagination}
                        row-key={tableRowKey}
                        single-line={false}
                        bordered
                        remote
                        striped
                        onUpdate:checked-row-keys={changeTableSelection}
                    />
                </n-card>
            </div>
        );
    }
});
