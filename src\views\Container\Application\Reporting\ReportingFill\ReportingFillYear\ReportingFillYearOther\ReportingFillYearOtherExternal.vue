<template>
    <n-spin :show="loadingShow">
        <template #description>正在处理中，请耐心等候</template>
        <div class="flex">
            <n-card class="flex-fixed-200">
                <div class="flex-y-center mb">
                    <n-input v-model:value="treePattern" clearable placeholder="搜索" />
                </div>
                <n-tree
                    v-model:selected-keys="treeSelectKeys"
                    :cancelable="false"
                    :data="treeData"
                    :pattern="treePattern"
                    :show-irrelevant-nodes="false"
                    block-line
                    children-field="childrenList"
                    default-expand-all
                    key-field="id"
                    label-field="categoryName"
                    selectable
                    @update:selected-keys="selectTreeNode"
                />
            </n-card>
            <div class="flex-1 ml">
                <n-card>
                    <n-space class="mb">
                        <n-button secondary type="primary" @click="addTableItem">新增记录</n-button>
                        <n-button secondary type="success" @click="saveTableData">保存填写</n-button>
                    </n-space>
                    <n-data-table
                        :columns="tableColumns"
                        :data="tableData"
                        :loading="tableLoading"
                        :row-key="tableRowKey"
                        :single-line="false"
                        bordered
                        remote
                        striped
                        @update:checked-row-keys="changeTableSelection"
                    />
                </n-card>
            </div>
        </div>
    </n-spin>
</template>

<script lang="ts" setup>
import { h, onMounted, ref, watchEffect } from "vue";
import {
    GET_MANUFACTURE_PAGE_LIST,
    GET_YEAR_OUTSOURCE_COST_PLAN_COST_LIST,
    GET_OTHER_CATEGORY_TREE_BY_TYPE,
    GET_SEMI_MANUFACTURE_PAGE_LIST,
    SAVE_YEAR_OUTSOURCE_COST_PLAN_COST_LIST
} from "@/api/application/reporting";
import type { DataTableColumns } from "naive-ui";
import { NButton, NInput, NTooltip } from "naive-ui";
import { useCommonTable, useReportingTree } from "@/hooks";
import { SpecSelector, SupplierSelector } from "@/views/Container/Application/Reporting/components";
import { cloneDeep } from "lodash-es";
import { TableActions } from "@/components/TableActions";
import { GET_SUPPLIER_LIST } from "@/api/application/purchase";

let props = withDefaults(
    defineProps<{
        planYear: string;
    }>(),
    {}
);

interface RowProps {
    [key: string]: any;
}

let loadingShow = ref(false);

onMounted(async () => {
    await getTreeData();
    await getSupplierOptions();
});

// 获取供应商列表
let supplierOptions = ref<any[]>([]);

let getSupplierOptions = async () => {
    await GET_SUPPLIER_LIST({
        current: 1,
        size: 9999
    }).then((res) => {
        if (res.data.code === 0) {
            supplierOptions.value = res.data.data.records ?? [];
        }
    });
};

// 树相关操作
let { treeData, treePattern, treeSelectKeys, treeAddDisabled, findLastLevel } = useReportingTree();

let getTreeData = async () => {
    await GET_OTHER_CATEGORY_TREE_BY_TYPE({
        wageType: 1
    }).then((res) => {
        if (res.data.code === 0) {
            treeData.value = treeAddDisabled(res.data.data ?? []);
            treeSelectKeys.value = [findLastLevel(treeData.value)[0]?.id];
        }
    });
};

let selectTreeNode = (keys?: (string | number)[], option?: any[]) => {
    treeSelectKeys.value = keys ?? [];
    // onSearch();
};

let onSearch = () => {
    getTableData();
};

// 数据列表
let { tableRowKey, tableData, tableLoading, changeTableSelection } = useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    { type: "selection" },
    {
        title: "规格型号",
        align: "center",
        key: "contentId",
        render: (row) => {
            return h(SpecSelector, {
                value: row.contentId,
                onSubmit: async (v: unknown) => {
                    row.contentId = v;
                    let selectArray: any[] = [];
                    let [semiManufacture, manufacture] = await Promise.all([
                        GET_SEMI_MANUFACTURE_PAGE_LIST({ ids: v }),
                        GET_MANUFACTURE_PAGE_LIST({ ids: v })
                    ]);
                    let semiManufactureData = (semiManufacture.data.data.records ?? []).map((item: any) => {
                        return { ...item, contentType: 2 };
                    });
                    let manufactureData = (manufacture.data.data.records ?? []).map((item: any) => {
                        return { ...item, contentType: 1 };
                    });
                    selectArray = [...semiManufactureData, ...manufactureData];
                    row.contentType = selectArray[0].contentType;
                }
            });
        }
    },
    {
        title: "计划金额（元）",
        align: "center",
        key: "planCost",
        render: (row) => {
            return h(
                NTooltip,
                {},
                {
                    trigger: () => {
                        return h(NInput, {
                            value: row.planCost,
                            onUpdateValue: (v) => (row.planCost = v),
                            onFocus: () => {
                                if (row.planCost === "0") row.planCost = "";
                            },
                            onBlur: () => {
                                if (!row.planCost) row.planCost = "0";
                            }
                        });
                    },
                    default: () => {
                        return row.planCost;
                    }
                }
            );
        }
    },
    {
        title: "供应商",
        align: "center",
        key: "supplyIds",
        render: (row) => {
            return h(SupplierSelector, {
                type: "select",
                value: row.supplyIds,
                options: supplierOptions.value,
                onSubmit: (v: any) => (row.supplyIds = v.join(","))
            });
        }
    },
    // {
    //     title: "销售订单",
    //     align: "center",
    //     key: "saleOrderIds",
    //     render: (row) => {
    //         return h(NInput, {
    //             value: row.saleOrderIds,
    //             placeholder: "请输入销售订单",
    //             onUpdateValue: (v) => (row.saleOrderIds = v)
    //         });
    //     }
    // },
    {
        title: "备注",
        align: "center",
        key: "remark",
        render: (row) => {
            return h(
                NTooltip,
                {},
                {
                    trigger: () => {
                        return h(NInput, { value: row.remark, onUpdateValue: (v) => (row.remark = v) });
                    },
                    default: () => {
                        return row.remark;
                    }
                }
            );
        }
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: "80",
        render(row, index) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "删除",
                        tertiary: true,
                        type: "error",
                        onClick: () => deleteItem(index)
                    }
                ]
            });
        }
    }
]);

let getTableData = () => {
    tableLoading.value = true;
    GET_YEAR_OUTSOURCE_COST_PLAN_COST_LIST({
        categoryId: treeSelectKeys.value[0],
        planYear: props.planYear
    }).then((res) => {
        if (res.data.code === 0) {
            tableData.value = res.data.data ?? [];
        }
        tableLoading.value = false;
    });
};

watchEffect(() => {
    if (props.planYear) {
        getTableData();
    }
});

// 可编辑表单配置
let tableItem: RowProps = {
    id: "",
    contentId: "",
    contentType: "",
    planCost: "",
    remark: ""
};

//新增计划
let addTableItem = () => {
    tableData.value.push(cloneDeep(tableItem));
};

let deleteItem = (index: number) => {
    tableData.value.splice(index, 1);
};

//保存填写
let saveTableData = async () => {
    loadingShow.value = true;
    let params = {
        planYear: props.planYear,
        categoryId: treeSelectKeys.value[0],
        outsourceCostList: tableData.value
    };
    await SAVE_YEAR_OUTSOURCE_COST_PLAN_COST_LIST({ ...params }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("保存成功");
            onSearch();
        }
    });
    loadingShow.value = false;
};
</script>
