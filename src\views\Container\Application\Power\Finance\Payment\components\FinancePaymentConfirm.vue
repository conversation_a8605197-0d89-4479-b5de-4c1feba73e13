<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-800px" closable title="确认到款" @close="closeModal">
                <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto">
                    <n-grid :cols="24" :x-gap="16">
                        <n-form-item-gi :span="12" label="所属订单">
                            <div>{{ configData.projectNumber }}</div>
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="预计到款时间">
                            <div>{{ configData.preReturnTime }}</div>
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="预计到款金额">
                            <div>{{ configData.preReturnAmount }}</div>
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="实际到款时间" path="returnTime">
                            <n-date-picker
                                v-model:formatted-value="formData.returnTime"
                                class="w-100%"
                                clearable
                                placeholder="请选择实际到款时间"
                                type="datetime"
                                value-format="yyyy-MM-dd HH:mm:ss"
                            />
                        </n-form-item-gi>
                        <n-form-item-gi :span="12" label="实际到款金额" path="returnAmount">
                            <n-input-number v-model:value="formData.returnAmount" class="w-100%" clearable />
                        </n-form-item-gi>
                        <n-form-item-gi :span="24" label="附件凭证" path="fileIds">
                            <FileValueUploader v-model:value="formData.fileIds" value-key="fileId" />
                        </n-form-item-gi>
                        <n-form-item-gi :span="24">
                            <n-space>
                                <n-button type="primary" @click="onSubmit">提交</n-button>
                                <n-button @click="closeModal">取消</n-button>
                            </n-space>
                        </n-form-item-gi>
                    </n-grid>
                </n-form>
            </n-card>
        </n-modal>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref } from "vue";
import type { FormInst } from "naive-ui";
import { cloneDeep } from "lodash-es";
import { CONFIRM_PAYMENT_RETURN_APPLY } from "@/api/application/power";
import { FileValueUploader } from "@/components/Uploader";

let props = defineProps({
    show: { type: Boolean, default: false },
    configData: { type: Object as PropType<any> }
});

let emits = defineEmits(["update:show", "refresh"]);

// 表单实例
let formRef = ref<FormInst | null>(null);

// 表单校验
let formRules = computed(() => {
    return {
        returnTime: [{ required: true, message: "请选择实际到款时间", trigger: ["blur", "change"] }],
        returnAmount: [{ required: true, message: "请输入实际到款金额", trigger: ["input", "blur"], type: "number" }]
    };
});

// 表单数据
interface FormDataProps {
    [key: string]: any;
}

let initFormData: FormDataProps = {
    returnTime: null,
    returnAmount: 0
};

let formData = ref(cloneDeep(initFormData));

let clearFrom = () => {
    formData.value = cloneDeep(initFormData);
};

// 关闭弹窗
let closeModal = () => {
    clearFrom();
    emits("update:show", false);
};

// 提交表单
let onSubmit = async () => {
    let validateError = await formRef.value?.validate((errors) => !!errors);
    if (validateError) return false;
    CONFIRM_PAYMENT_RETURN_APPLY({
        id: props.configData.id,
        ...formData.value
    }).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("操作成功");
            closeModal();
            emits("refresh");
        }
    });
};
</script>
