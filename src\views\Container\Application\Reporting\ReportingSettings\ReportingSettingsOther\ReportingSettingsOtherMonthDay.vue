<template>
    <div>
        <n-card>
            <n-grid :cols="24" x-gap="16">
                <n-grid-item :span="12">
                    <div class="flex-y-center">
                        <div class="flex-fixed-80">选择班组：</div>
                        <n-select
                            v-model:value="workGroupId"
                            :options="workGroupIdOptions"
                            placeholder="请选择班组"
                            @update:value="getPlanDayList"
                        />
                    </div>
                </n-grid-item>
                <n-grid-item :span="12">
                    <div class="flex-y-center">
                        <div class="flex-fixed-80">当前年份：</div>
                        <n-date-picker
                            v-model:value="currentYear"
                            class="flex-1"
                            placeholder="请选择当前年份"
                            type="year"
                            @update:value="getPlanDayList"
                        />
                    </div>
                </n-grid-item>
            </n-grid>
        </n-card>
        <n-card class="mt" v-if="planDayList && planDayList.length > 0">
            <n-tabs v-model:value="tabActive" type="bar">
                <n-tab-pane name="年度计划天数">
                    <n-grid :col="24" :x-gap="16" :y-gap="16" item-responsive>
                        <n-grid-item v-for="(item, index) in planDayList" span="12 600:8 800:6 1000:4">
                            <n-input
                                v-model:value="item.yearDayAmount"
                                class="w-100% text-center"
                                placeholder="请输入天数"
                            >
                                <template #suffix>天</template>
                            </n-input>
                            <div class="text-center mt-2">
                                <n-text type="primary">{{ item.calMonthName }}</n-text>
                            </div>
                        </n-grid-item>
                    </n-grid>
                </n-tab-pane>
                <n-tab-pane name="每月计划天数">
                    <n-grid :col="24" :x-gap="16" :y-gap="16" item-responsive>
                        <n-grid-item v-for="(item, index) in planDayList" span="12 600:8 800:6 1000:4">
                            <n-input v-model:value="item.dayAmount" class="w-100% text-center" placeholder="请输入天数">
                                <template #suffix>天</template>
                            </n-input>
                            <div class="text-center mt-2">
                                <n-text type="primary">{{ item.calMonthName }}</n-text>
                            </div>
                        </n-grid-item>
                    </n-grid>
                </n-tab-pane>
            </n-tabs>
            <n-space class="mt">
                <n-button type="success" @click="onSave()">确认保存</n-button>
                <n-button type="error" @click="onClear()">清空内容</n-button>
            </n-space>
        </n-card>
        <n-card class="mt" v-else>
            <n-result class="pt-50px pb-50px" status="404" title="暂无数据" />
        </n-card>
    </div>
</template>

<script lang="ts" setup>
import { ref, watchEffect } from "vue";
import { NInput } from "naive-ui";
import { GET_MONTH_DAY_CONFIG_LIST, SAVE_MONTH_DAY_CONFIG_LIST } from "@/api/application/reporting";
import dayjs from "dayjs";
import { GET_WORK_GROUP_PAGE_LIST } from "@/api/application/production";

let props = withDefaults(defineProps<{ treeSelectKeys: any[] }>(), {});

let tabActive = ref<string>("年度计划天数");

// 班组
let workGroupId = ref<Nullable<string | number>>(null);

let workGroupIdOptions = ref<any[]>([]);

let getWorkGroupIdOptions = async () => {
    await GET_WORK_GROUP_PAGE_LIST({
        current: 1,
        size: 9999,
        companyId: props.treeSelectKeys[0]
    }).then((res) => {
        workGroupIdOptions.value = (res.data.data.records ?? []).map((item: any) => {
            return { label: item.companyName + "-" + item.workshopName + "-" + item.groupName, value: item.id };
        });
        workGroupId.value = workGroupIdOptions.value[0].value ?? null;
    });
};

// 月份天数
let currentYear = ref<number>(dayjs().startOf("year").valueOf());
let planDayList = ref<Record<string, any>[]>([]);

let getPlanDayList = () => {
    GET_MONTH_DAY_CONFIG_LIST({
        calYear: dayjs(currentYear.value).format("YYYY"),
        workGroupId: workGroupId.value
    }).then((res) => {
        if (res.data.code === 0) planDayList.value = res.data.data ?? [];
    });
};

watchEffect(async () => {
    if (props.treeSelectKeys[0]) {
        workGroupId.value = null;
        planDayList.value = [];
        await getWorkGroupIdOptions();
        getPlanDayList();
    }
});

// 清除选择
let onClear = () => {
    (planDayList.value ?? []).forEach((item) => {
        if (tabActive.value === "年度计划天数") {
            item.yearDayAmount = 0;
        } else if (tabActive.value === "每月计划天数") {
            item.dayAmount = 0;
        }
    });
};

// 保存选择
let onSave = () => {
    SAVE_MONTH_DAY_CONFIG_LIST(planDayList.value).then((res) => {
        if (res.data.code === 0) {
            window.$message.success("保存成功");
        } else {
            window.$message.error(res.data.msg);
        }
    });
};
</script>
