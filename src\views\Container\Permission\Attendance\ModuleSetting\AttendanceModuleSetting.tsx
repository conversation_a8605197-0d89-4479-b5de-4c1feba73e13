import { defineComponent, onMounted, reactive, ref } from "vue";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import { GET_ATTENDANCE_MODULE_SETTING_LIST } from "@/api/permission";
import AttendanceModuleSettingEdit from "@/views/Container/Permission/Attendance/ModuleSetting/AttendanceModuleSettingEdit";

export default defineComponent({
    name: "AttendanceModuleSetting",
    setup(props, { emit }) {
        // 搜索项
        const searchConfig = ref<TableSearchbarConfig>([]);

        const searchOptions = ref<TableSearchbarOptions>({});

        const getSearchOptions = async () => {};

        const searchForm = ref<TableSearchbarData>({});

        const onSearch = () => {
            getTableData();
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            { title: "公司名称", key: "companyName", align: "center" },
            { title: "考勤管理员", key: "userTrueNames", align: "center" },
            {
                title: "考勤同步规则",
                key: "comRule",
                align: "center",
                render: (row) => {
                    return <n-text type="info">{row.comRule}</n-text>;
                }
            },
            {
                title: "最新同步状态",
                key: "syncStatus",
                align: "center",
                render: (row) => {
                    const syncStatus = String(row.syncStatus);
                    switch (syncStatus) {
                        case "1":
                            return <n-text type="success">成功</n-text>;
                        case "2":
                            return <n-text type="info">部分成功</n-text>;
                        case "3":
                            return <n-text type="warning">同步中</n-text>;
                        case "4":
                            return <n-text type="error">失败</n-text>;
                        default:
                            return "/";
                    }
                }
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_ATTENDANCE_MODULE_SETTING_LIST({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                ...searchForm.value
            }).then((res) => {
                if (res.data.code === 0) {
                    tableData.value = res.data.data.records;
                    tablePagination.itemCount = res.data.data.total;
                    tableLoading.value = false;
                }
            });
        };

        // 新增功能
        const editModal = ref<{ show: boolean; configData: RowProps }>({ show: false, configData: {} });

        const openEditModal = (row?: RowProps) => {
            editModal.value.show = true;
            editModal.value.configData = row ?? {};
        };

        onMounted(async () => {
            await getSearchOptions();
            getTableData();
        });

        return () => (
            <div class="attendance-page">
                <n-card>
                    <TableSearchbar
                        form={searchForm.value}
                        config={searchConfig.value}
                        options={searchOptions.value}
                        onSearch={onSearch}
                    />
                </n-card>
                <n-card class="mt">
                    <n-space class="mb">
                        <n-button type="primary" onClick={() => openEditModal()}>
                            新增考勤公司
                        </n-button>
                    </n-space>
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        pagination={tablePagination}
                        row-key={tableRowKey}
                        single-line={false}
                        bordered
                        remote
                        striped
                        onUpdate:checked-row-keys={changeTableSelection}
                    />
                </n-card>
                <AttendanceModuleSettingEdit
                    v-model:show={editModal.value.show}
                    v-model:configData={editModal.value.configData}
                    onRefresh={getTableData}
                />
            </div>
        );
    }
});
