<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, ref } from "vue";
import { DataTableColumns, NText, PaginationProps } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { useCommonTable } from "@/hooks";
import { TableActions } from "@/components/TableActions";

import { GET_PROJECT_PAYMENT_RETURN_APPLY_LIST, GET_UNFINISH_PROJECT_LIST } from "@/api/application/power";

interface RowProps {
    [key: string]: any;
}

onMounted(async () => {
    getSearchOptions();
    getTableData();
});

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([]);

let searchOptions = ref<TableSearchbarOptions>({});

let searchForm = ref<TableSearchbarData>({});

let getSearchOptions = () => {};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, changeTableSelection } =
    useCommonTable<RowProps>("projectId");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "项目编号",
        key: "projectNumber",
        align: "center"
    },
    {
        title: "项目名称",
        key: "projectName",
        align: "center",
        render: (row) => {
            return h(NText, { type: "primary" }, () => row.projectName || "暂无");
        }
    },
    {
        title: "项目负责人",
        key: "projectLeaderName",
        align: "center",
        render: (row) => {
            return h(NText, { type: "primary" }, () => row.projectLeaderName || "暂无");
        }
    },
    {
        title: "项目总金额",
        key: "contractAmount",
        align: "center",
        render: (row) => (row.contractAmount ? h(NText, { type: "error" }, () => row.contractAmount) : "暂无")
    },
    {
        title: "已回款金额",
        key: "returnAmount",
        align: "center",
        render: (row) => (row.returnAmount ? h(NText, { type: "primary" }, () => row.returnAmount) : "暂无")
    }
    // {
    //     title: "开票总金额",
    //     key: "invoicingAmount",
    //     align: "center",
    //     render: (row) => (row.invoicingAmount ? h(NText, { type: "warning" }, () => row.invoicingAmount) : "暂无")
    // }
]);

let tablePagination = ref<PaginationProps>({
    page: 1,
    pageSize: 10,
    itemCount: 0,
    pageSizes: [10, 50, 100],
    showSizePicker: true,
    showQuickJumper: true,
    displayOrder: ["size-picker", "pages", "quick-jumper"],
    onChange: (page: number) => {
        tablePagination.value.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.value.pageSize = pageSize;
        tablePagination.value.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    GET_UNFINISH_PROJECT_LIST({
        current: tablePagination.value.page,
        size: tablePagination.value.pageSize,
        ...searchForm.value,
        applyStatus: 1,
        nonContractFlag: 1
    }).then((res) => {
        tableData.value = res.data.data.records || [];
        tablePagination.value.itemCount = res.data.data.total;
        tableLoading.value = false;
    });
};

let onSearch = () => {
    getTableData();
};
</script>
