import { defineComponent, h, onMounted, reactive, ref, watch } from "vue";
import {
    TableSearchbar,
    type TableSearchbarConfig,
    type TableSearchbarData,
    type TableSearchbarOptions
} from "@/components/TableSearchbar";
import { useCommonTable, useDicts } from "@/hooks";
import type { DataTableColumns } from "naive-ui";
import { TableActions } from "@/components/TableActions";
import { GET_INSPECT_PAGE_LIST, GET_IRON_TECHNIQUE_TREE_LIST } from "@/api/application/TowerScan";
import dayjs from "dayjs";
import TowerScanQualityInspectionPartDetail from "./TowerScanQualityInspectionPartDetail";

export default defineComponent({
    name: "TowerScanQualityInspectionPartList",
    setup(props) {
        // 完成状态tab
        const activeStatusTab = ref<string>("all");

        // 完成状态选项
        const statusOptions = [
            { label: "全部", value: "all" },
            { label: "未开始", value: "1" },
            { label: "进行中", value: "2" },
            { label: "已完成", value: "3" },
            { label: "已结束", value: "4" }
        ];

        // 搜索项
        const searchConfig = ref<TableSearchbarConfig>([
            { prop: "planName", label: "计划名称", type: "input", labelWidth: "100" },
            {
                prop: "createBeginDate",
                label: "下发开始时间",
                type: "date",
                labelWidth: "100",
                dateFormat: "yyyy-MM-dd"
            },
            { prop: "createEndDate", label: "下发结束时间", type: "date", labelWidth: "100", dateFormat: "yyyy-MM-dd" },
            {
                prop: "finishBeginDate",
                label: "完成开始时间",
                type: "date",
                labelWidth: "100",
                dateFormat: "yyyy-MM-dd"
            },
            { prop: "finishEndDate", label: "完成结束时间", type: "date", labelWidth: "100", dateFormat: "yyyy-MM-dd" },
            { prop: "techniqueId", label: "工艺环节", type: "treeSelect", labelWidth: "100" },
            { prop: "processFillFrom", label: "工序类型", type: "select", labelWidth: "100" }
        ]);

        // 字典操作
        const { dictLibs, getDictLibs } = useDicts();

        const setDictLibs = async () => {
            const dictName = ["TechniqueProcessType"];
            await getDictLibs(dictName);
            dictLibs["TechniqueProcessType"] =
                dictLibs["TechniqueProcessType"]?.filter((item) => item.value !== "0" && item.value !== "1") || [];
        };

        // 工艺树数据
        const techniqueTreeData = ref<any[]>([]);
        const processTypeOptions = ref<any[]>([]);

        // 递归构建树节点
        const buildTreeNode = (item: any): any => {
            const node = {
                label: item.techniqueName,
                key: item.id,
                ...item
            };

            if (item.childrenList && item.childrenList.length > 0) {
                node.children = item.childrenList.map((child: any) => buildTreeNode(child));
            } else {
                node.isLeaf = true;
            }

            return node;
        };

        // 获取表单选项
        const getFormOptions = async () => {
            await setDictLibs();
            processTypeOptions.value = dictLibs["TechniqueProcessType"] || [];

            // 获取所有工艺分类的工艺数据
            const allTechniqueData: any[] = [];
            for (const processType of processTypeOptions.value) {
                const res = await GET_IRON_TECHNIQUE_TREE_LIST({
                    processType: processType.value
                });
                if (res.data.code === 0) {
                    const categoryData = {
                        label: processType.label,
                        key: `category_${processType.value}`,
                        disabled: true, // 第一层级不可选择
                        children: (res.data.data || []).map((item: any) => buildTreeNode(item))
                    };
                    allTechniqueData.push(categoryData);
                }
            }
            techniqueTreeData.value = allTechniqueData;
        };

        const searchOptions = ref<TableSearchbarOptions>({
            techniqueId: [],
            processFillFrom: [
                { label: "标准工序", value: "1" },
                { label: "非标准工序", value: "2" }
            ]
        });

        const getSearchOptions = async () => {
            await getFormOptions();
            // 将工艺树数据直接用于搜索选项
            searchOptions.value.techniqueId = techniqueTreeData.value;
        };

        // 监听工艺选择变化，禁止选择第一层级
        const handleTechniqueChange = () => {
            if (
                searchForm.value.techniqueId &&
                typeof searchForm.value.techniqueId === "string" &&
                searchForm.value.techniqueId.startsWith("category_")
            ) {
                searchForm.value.techniqueId = null;
                window.$message.warning("请选择具体的工艺，不能只选择工艺分类");
            }
        };

        const searchForm = ref<TableSearchbarData>({
            planName: null,
            createBeginDate: null,
            createEndDate: null,
            finishBeginDate: null,
            finishEndDate: null,
            techniqueId: null,
            processFillFrom: "1"
        });

        // 获取当前完成状态值
        const getCurrentFinishStatus = () => {
            return activeStatusTab.value === "all" ? null : activeStatusTab.value;
        };

        const onSearch = () => {
            tablePagination.page = 1;
            tablePagination.pageSize = 10;
            getTableData();
        };

        // 监听状态tab变化时触发搜索
        const handleStatusTabChange = (value: string) => {
            activeStatusTab.value = value;
            onSearch();
        };

        // 数据列表
        interface RowProps {
            [key: string]: any;
        }

        const { tableRowKey, tableData, tablePaginationPreset, tableLoading, tableSelection, changeTableSelection } =
            useCommonTable<RowProps>("id");

        const tableColumns = ref<DataTableColumns<RowProps>>([
            { type: "selection" },
            {
                title: "生产计划名称",
                key: "planName",
                align: "center",
                width: 180,
                ellipsis: true
            },
            {
                title: "工程名称",
                key: "projectName",
                align: "center",
                width: 150,
                ellipsis: true
            },
            {
                title: "塔型名称",
                key: "typeName",
                align: "center",
                width: 120,
                ellipsis: true
            },
            {
                title: "计划下发人",
                key: "issuedByName",
                align: "center",
                width: 100
            },
            {
                title: "下发时间",
                key: "issuedDate",
                align: "center",
                width: 160,
                render: (row) => (row.issuedDate ? dayjs(row.issuedDate).format("YYYY-MM-DD HH:mm:ss") : "/")
            },
            {
                title: "质检工艺环节",
                key: "techniqueName",
                align: "center",
                width: 120,
                ellipsis: true,
                render: (row) => {
                    return <n-text type="info">{row.techniqueName}</n-text>;
                }
            },
            {
                title: "质检完成状态",
                key: "finishStatus",
                align: "center",
                width: 100,
                render: (row) => {
                    // 非标准件直接显示已完成
                    if (row.processFillFrom === 2) {
                        return <n-text type="success">已完成</n-text>;
                    }
                    
                    switch (row.finishStatus) {
                        case 1:
                            return <n-text type="error">未开始</n-text>;
                        case 2:
                            return <n-text type="info">进行中</n-text>;
                        case 3:
                            return <n-text type="success">已完成</n-text>;
                        case 4:
                            return <n-text type="warning">已结束</n-text>;
                        default:
                            return <n-text>/</n-text>;
                    }
                }
            },
            {
                title: "待检总量",
                key: "pendingWeightTotal",
                align: "center",
                width: 120,
                render: (row) => {
                    // 非标准件不显示待检总量
                    if (row.processFillFrom === 2) {
                        return <n-text>/</n-text>;
                    }
                    return <n-text type="info">{row.pendingWeightTotal ? `${row.pendingWeightTotal}kg` : "/"}</n-text>;
                }
            },
            {
                title: "已完成总量",
                key: "totalFinishWeight",
                align: "center",
                width: 120,
                render: (row) => {
                    return <n-text type="success">{row.totalFinishWeight ? `${row.totalFinishWeight}kg` : "/"}</n-text>;
                }
            },
            {
                title: "操作",
                key: "action",
                align: "center",
                width: 100,
                fixed: "right",
                render: (row) => {
                    return (
                        <TableActions
                            type="button"
                            buttonActions={[
                                {
                                    label: "查看详情",
                                    tertiary: true,
                                    type: "primary",
                                    onClick: () => openQualityDetailModal(row)
                                }
                            ]}
                        />
                    );
                }
            }
        ]);

        const tablePagination = reactive({
            ...tablePaginationPreset,
            onChange: (page: number) => {
                tablePagination.page = page;
                getTableData();
            },
            onUpdatePageSize: (pageSize: number) => {
                tablePagination.pageSize = pageSize;
                tablePagination.page = 1;
                getTableData();
            }
        });

        const getTableData = () => {
            tableLoading.value = true;
            GET_INSPECT_PAGE_LIST({
                current: tablePagination.page,
                size: tablePagination.pageSize,
                ...searchForm.value,
                finishStatus: getCurrentFinishStatus() // 添加完成状态到请求参数
            })
                .then((res) => {
                    if (res.data.code === 0) {
                        tableData.value = res.data.data.records;
                        tablePagination.itemCount = res.data.data.total;
                        tableLoading.value = false;
                    } else {
                        window.$message.error(res.data.msg || "获取数据失败");
                        tableLoading.value = false;
                    }
                })
                .catch(() => {
                    window.$message.error("获取数据失败");
                    tableLoading.value = false;
                });
        };

        // 质检详情弹窗
        const qualityDetailModal = ref<{ show: boolean; configData: UnKnownObject }>({ show: false, configData: {} });

        const openQualityDetailModal = (row?: RowProps) => {
            qualityDetailModal.value = {
                show: true,
                configData: {
                    ...row
                }
            };
        };

        // 监听工艺选择变化
        watch(
            () => searchForm.value.techniqueId,
            () => {
                handleTechniqueChange();
            }
        );

        onMounted(async () => {
            await getSearchOptions();
            getTableData();
        });

        return () => (
            <div class="tower-scan-quality-inspection-list">
                <n-tabs v-model:value={activeStatusTab.value} onUpdate:value={handleStatusTabChange}>
                    {statusOptions.map((option) => (
                        <n-tab-pane key={option.value} name={option.value} tab={option.label + "质检任务"} />
                    ))}
                </n-tabs>

                <n-card>
                    <TableSearchbar
                        form={searchForm.value}
                        config={searchConfig.value}
                        options={searchOptions.value}
                        onSearch={onSearch}
                    />
                </n-card>
                <n-card class="mt">
                    <n-data-table
                        columns={tableColumns.value}
                        data={tableData.value}
                        loading={tableLoading.value}
                        pagination={tablePagination}
                        row-key={tableRowKey}
                        single-line={false}
                        bordered
                        remote
                        striped
                        onUpdate:checked-row-keys={changeTableSelection}
                        scroll-x={1500}
                    />
                </n-card>
                <TowerScanQualityInspectionPartDetail
                    v-model:show={qualityDetailModal.value.show}
                    config-data={qualityDetailModal.value.configData}
                    onRefresh={() => {
                        getTableData();
                    }}
                />
            </div>
        );
    }
});
