<template>
    <div>
        <n-card content-style="padding-top:16px;padding-bottom:8px" hoverable>
            <n-tabs v-model:value="tabActive" animated type="bar">
                <n-tab-pane :name="1" tab="待处理项目节点" />
                <n-tab-pane :name="2" tab="已处理项目节点" />
            </n-tabs>
        </n-card>
        <div class="mt">
            <ProjectListHandlePending v-if="tabActive === 1" />
            <ProjectListHandleFinish v-if="tabActive === 2" />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import ProjectListHandleFinish from "./ProjectListHandleFinish.vue";
import ProjectListHandlePending from "./ProjectListHandlePending.vue";

let tabActive = ref(1);
</script>
