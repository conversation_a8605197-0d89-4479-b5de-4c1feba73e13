<template>
    <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
        <n-card class="w-90vw" closable content-style="padding: 0" title="查看生产表单" @close="changeModalShow(false)">
            <div class="h-80vh">
                <n-scrollbar trigger="hover">
                    <div class="relative p-20px pt-0">
                        <div class="absolute top-0 left-0 right-0 bottom-0 z-999" />
                        <dynamic-form v-model="formData.elements" :options="formData.wcfOptions" />
                    </div>
                </n-scrollbar>
            </div>
        </n-card>
    </n-modal>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from "vue";
import { DynamicForm } from "@/components/DynamicForm";
import { PRODUCTION_GET_COMMON_FORM_BY_ID } from "@/api/application/production";
import { isJSON } from "@/utils/tools";
import type { FormGeneratorProps } from "@/components/FormGenerator";

let props = withDefaults(
    defineProps<{
        show: boolean;
        configData: UnKnownObject;
    }>(),
    {
        show: () => false
    }
);

let emits = defineEmits(["update:show", "refresh"]);

let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

let changeModalShow = (show: boolean) => emits("update:show", show);

// 表单数据
let formData = ref<any>({
    elements: [],
    wcfOptions: {}
});

watch(
    () => ({ show: props.show, configData: props.configData }),
    (newVal) => {
        if (newVal.show && props.configData?.id) {
            PRODUCTION_GET_COMMON_FORM_BY_ID({ id: props.configData?.id }).then((res) => {
                formData.value.elements =
                    (res.data.data.elements || []).map((item: FormGeneratorProps) => {
                        let newModelValue: any = null;
                        if (item.modelValue) {
                            if (item.modelValue === "null") {
                                newModelValue = null;
                            } else {
                                newModelValue = isJSON(item.modelValue) ? JSON.parse(item.modelValue) : item.modelValue;
                            }
                        }
                        return { ...item, modelValue: newModelValue };
                    }) || [];
            });
        }
    },
    { deep: true }
);
</script>
