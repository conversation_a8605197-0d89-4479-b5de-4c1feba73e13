<template>
    <div>
        <n-modal v-model:show="show" :close-on-esc="false" :mask-closable="false">
            <n-card class="w-90vw" closable content-style="padding: 0" title="任务处理" @close="closeModal">
                <template #header-extra>
                    <n-button @click="toggleProcessSteps">
                        {{ showProcessSteps ? "收起流程" : "展开流程" }}
                        <template #icon>
                            <DynamicIcon :icon="showProcessSteps ? 'LeftOutlined' : 'RightOutlined'" />
                        </template>
                    </n-button>
                </template>
                <div class="flex">
                    <div class="flex-1 h-80vh">
                        <n-scrollbar trigger="hover">
                            <div class="p-20px">
                                <n-form
                                    ref="ccPersonsRef"
                                    :model="ccPersonsData"
                                    :rules="ccPersonsRules"
                                    label-placement="left"
                                    label-width="auto"
                                >
                                    <n-grid cols="12" x-gap="16">
                                        <n-form-item-gi class="mt-0" :span="12" label="选择抄送人：" path="ccPersons">
                                            <UserSelector
                                                v-model:value="ccPersonsData.ccPersons"
                                                class="w-100%"
                                                key-name="username"
                                                placeholder="请选择抄送人"
                                            />
                                        </n-form-item-gi>
                                    </n-grid>
                                </n-form>
                                <dynamic-form v-model="formData.elements" :options="formData.wcfOptions" />
                                <div class="flex-center mt-24px">
                                    <n-space>
                                        <n-button type="primary" @click="onSubmit(true)">审批通过</n-button>
                                        <n-button type="error" @click="onSubmit(false)">完全驳回</n-button>
                                        <n-button type="warning" @click="openFallbackModal()">驳回到节点</n-button>
                                        <n-button type="success" @click="onSubmit(true)">已查阅</n-button>
                                    </n-space>
                                </div>
                            </div>
                        </n-scrollbar>
                    </div>
                    <div v-if="showProcessSteps" class="w-350px p-20px pr-0 transition-all">
                        <div class="flex justify-between mb-4">
                            <span class="text-16px font-medium">流程进度</span>
                        </div>
                        <n-steps vertical>
                            <template v-for="(item, index) in processDetail.processHistoryVoList">
                                <n-step v-if="item.status !== '已处理'" :title="item.name" status="wait">
                                    <template #icon>
                                        <DynamicIcon icon="EditOutlined" />
                                    </template>
                                    <div class="mt-1">未处理</div>
                                    <div v-if="index !== 0" class="mt-1">审批人：{{ item.assignee }}</div>
                                    <div v-else class="mt-1">发起人：{{ item.starter ?? item.assignee }}</div>
                                </n-step>
                                <n-step v-else :title="item.name" status="process">
                                    <template #icon>
                                        <DynamicIcon icon="CheckOutlined" />
                                    </template>
                                    <div class="mt-1">{{ item.comment }}</div>
                                    <div v-if="index !== 0" class="mt-1">审批人：{{ item.assignee }}</div>
                                    <div v-else class="mt-1">发起人：{{ item.starter ?? item.assignee }}</div>
                                    <div class="mt-1">审批时间：{{ item.endTime }}</div>
                                </n-step>
                            </template>
                            <template #error-icon>
                                <DynamicIcon icon="CloseOutlined" />
                            </template>
                            <template #finish-icon>
                                <DynamicIcon icon="CopyOutlined" />
                            </template>
                            <n-step v-if="processDetail.status === 5" status="error" title="发起人已撤回">
                                <div class="mt-1">撤回人：{{ processDetail.starterTrueName }}</div>
                                <div class="mt-1">撤回时间：{{ processDetail.updateTime }}</div>
                            </n-step>
                            <n-step v-if="!!processDetail.ccPersons" status="finish" title="">
                                <template #title>
                                    <n-element class="color-[var(--primary-color)]" tag="div"> 抄送</n-element>
                                </template>
                                <n-element class="mt-1 color-[var(--primary-color)]" tag="div">
                                    抄送人：{{ processDetail.ccPersonsNames }}
                                </n-element>
                            </n-step>
                        </n-steps>
                    </div>
                </div>
            </n-card>
        </n-modal>
        <!--选择下一节点审批人-->
        <ProcessApproverSelector
            v-model:show="approverSelector.show"
            :config-data="approverSelector.configData"
            @submit="onApproverSubmit"
        />
        <!--驳回到节点-->
        <ProcessFallback
            v-model:show="fallbackModal.show"
            :config-data="fallbackModal.configData"
            @refresh="closeModal"
        />
    </div>
</template>

<script lang="ts" setup>
import { h, ref, watch } from "vue";
import { useStoreUser } from "@/store";
import { usePublic } from "@/hooks";
import { DynamicForm } from "@/components/DynamicForm";
import { FormGeneratorProps } from "@/components/FormGenerator";
import { isJSON } from "@/utils/tools";
import {
    GET_OA_INSTANCE_FORM,
    GET_OA_NEXT_NODE,
    GET_OA_TASK_FORM,
    OA_TASK_PASS,
    OA_TASK_REJECT,
    UPDATE_OA_PROCESS_BUSINESS_FORM
} from "@/api/application/oa";
import { ProcessApproverSelector, ProcessFallback } from "@/views/Container/Application/Process/components/index";
import { NInput } from "naive-ui";
import { UserSelector } from "@/components/UserSelector";
import { useDebounceFn } from "@vueuse/core";
import { DynamicIcon } from "@/components/DynamicIcon";

let storeUser = useStoreUser();
let { $router } = usePublic();

let props = defineProps({
    show: { type: Boolean, default: false },
    configData: { type: Object as PropType<any> }
});

let emits = defineEmits(["update:show", "refresh"]);

let formData = ref<any>({
    wcfhId: null,
    businessFrom: null,
    businessKey: null,
    elements: [],
    wcfOptions: {}
});

// 控制流程节点展示的状态
const showProcessSteps = ref(true);

// 切换流程节点展示
const toggleProcessSteps = () => {
    showProcessSteps.value = !showProcessSteps.value;
};

watch(
    () => ({ show: props.show, configData: props.configData }),
    (newVal) => {
        if (newVal.show) {
            GET_OA_TASK_FORM({
                taskId: newVal.configData.id
            }).then((res) => {
                formData.value.wcfhId = res.data.data.wcfhId;
                formData.value.businessFrom = res.data.data.businessFrom;
                formData.value.businessKey = res.data.data.businessKey;
                formData.value.wcfOptions = res.data.data.wcfOptions || {};
                formData.value.elements =
                    res.data.data.elements.map((item: FormGeneratorProps) => {
                        let newModelValue: any = null;
                        if (item.modelValue && item.modelValue !== "null") {
                            if (isJSON(item.modelValue)) {
                                let parsedValue = JSON.parse(item.modelValue);
                                newModelValue = typeof parsedValue === "number" ? item.modelValue : parsedValue;
                            } else {
                                newModelValue = item.modelValue;
                            }
                        }

                        // 为特定组件类型提供正确的默认值
                        if (newModelValue === null) {
                            if (
                                item.type === "dynamicTable" ||
                                item.type === "checkbox" ||
                                item.type === "ProcessInstanceSelector"
                            ) {
                                newModelValue = [];
                            } else if (item.type === "dynamicInput") {
                                newModelValue = [];
                            }
                        }

                        return {
                            ...item,
                            modelValue: newModelValue
                        };
                    }) || [];
            });

            getProcessProgress();
        }
    },
    { deep: true }
);

// 关闭弹窗
let closeModal = () => {
    emits("update:show", false);
    formData.value = {
        wcfhId: null,
        businessFrom: null,
        businessKey: null,
        elements: [],
        wcfOptions: {}
    };
    approveMessage.value = "";
};

// 审批人选择器
let approverSelector = ref<{ show: boolean; configData: UnKnownObject }>({
    show: false,
    configData: {}
});

let approveMessage = ref("");

/*
 *抄送人
 */

// 表单实例
let ccPersonsRef = ref<any>(null);

let ccPersonsData = ref<any>({ ccPersons: null });

let ccPersonsRules = {
    ccPersons: [{ required: false, message: "请输入抄送人", trigger: ["blur", "change"] }]
};

// 提交
let onSubmit = useDebounceFn(async (status: boolean) => {
    let formElements = await Promise.all(
        formData.value.elements.map((item: any) => {
            return new Promise((resolve, reject) => {
                // 2023年2月5日19:38:32 表单新增必填项校验
                if (
                    item.isHide !== 1 &&
                    item.isRead === 1 &&
                    !item.modelValue &&
                    item.type !== "text" &&
                    !!item.showRequireMark
                ) {
                    window.$message.error(`${item.label}不能为空`);
                    reject("表单必填项不能为空");
                }
                // 2022年11月28日23:44:07 后台不想要null所以处理成空字符串
                let modelValue = item.modelValue;
                if (item.modelValue === null || item.modelValue === undefined) modelValue = "";
                let newModelValue = typeof modelValue === "string" ? modelValue : JSON.stringify(modelValue);
                resolve({
                    wcfdId: item.wcfdId,
                    id: item.id,
                    modelName: item.modelName,
                    modelLabel: item.modelLabel,
                    modelValue: newModelValue
                });
            });
        })
    );
    // 更新业务表单
    let formRes = await UPDATE_OA_PROCESS_BUSINESS_FORM({
        businessKey: formData.value.businessKey,
        businessFrom: formData.value.businessFrom,
        wcfhId: formData.value.wcfhId,
        formKey: formData.value.wcfOptions.wcfCode,
        formParamList: formElements
    });
    if (formRes.data.code !== 0) return window.$message.error(formRes.data.msg ?? "表单提交失败");

    // 获取下一个节点信息
    let nextNodeRes = await GET_OA_NEXT_NODE({ taskId: props.configData.id });
    if (nextNodeRes.data.code !== 0) return window.$message.error(nextNodeRes.data.msg ?? "获取下一个节点失败");
    let nextNodes = nextNodeRes.data.data.nextProcessNodeList;
    let nextNodeConfig = nextNodeRes.data.data.processNodeConfig;

    // 判断是否需要选择审批人
    let isNeedApprover = nextNodes.some((item: any) => item.approveChooseWay === 2);
    if (isNeedApprover && status) {
        approverSelector.value = {
            show: true,
            configData: {
                taskId: props.configData.id,
                type: "approval",
                nextNodes,
                status,
                processNodeConfig: nextNodeRes.data.data.processNodeConfig
            }
        };
    } else {
        let nextAssigneeList = nextNodes.map((item: any) => ({
            nodeId: item.nodeId,
            nodeName: item.nodeName,
            multipleColumn: item.multipleColumn,
            assignees: item.approveChooseWay === 2 ? item.userSelectedAssignees : item.optionAssignees
        }));
        if (nextNodeConfig.approveFormId) {
            window.$dialog.warning({
                title: `【${nextNodeConfig.nodeName}节点】审批意见`,
                content: () => {
                    return h(NInput as any, {
                        class: "my-2",
                        type: "textarea",
                        autosize: { minRows: 3 },
                        placeholder: "请输入审批意见",
                        clearable: true,
                        value: approveMessage.value,
                        onUpdateValue: (v: string) => (approveMessage.value = v)
                    }) as any;
                },
                positiveText: "提交",
                negativeText: "取消",
                onPositiveClick: () => {
                    if (status) {
                        OA_TASK_PASS({
                            taskId: props.configData.id,
                            nextAssigneeList,
                            formDetailId: nextNodeConfig.approveFormId,
                            approveMessage: approveMessage.value,
                            ccPersons: ccPersonsData.value.ccPersons
                        }).then((res) => {
                            if (res.data.code === 0) {
                                window.$message.success("审批通过成功");
                                closeModal();
                                emits("refresh");
                            }
                        });
                    } else {
                        OA_TASK_REJECT({
                            taskId: props.configData.id,
                            formDetailId: nextNodeConfig.approveFormId,
                            approveMessage: approveMessage.value,
                            ccPersons: ccPersonsData.value.ccPersons
                        }).then((res) => {
                            if (res.data.code === 0) {
                                window.$message.success("审批驳回成功");
                                closeModal();
                                emits("refresh");
                            }
                        });
                    }
                }
            });
        } else {
            if (status) {
                OA_TASK_PASS({
                    taskId: props.configData.id,
                    nextAssigneeList,
                    formDetailId: nextNodeConfig.approveFormId,
                    approveMessage: "",
                    ccPersons: ccPersonsData.value.ccPersons
                }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("审批通过成功");
                        closeModal();
                        emits("refresh");
                    }
                });
            } else {
                OA_TASK_REJECT({
                    taskId: props.configData.id,
                    formDetailId: nextNodeConfig.approveFormId,
                    approveMessage: "",
                    ccPersons: ccPersonsData.value.ccPersons
                }).then((res) => {
                    if (res.data.code === 0) {
                        window.$message.success("审批驳回成功");
                        closeModal();
                        emits("refresh");
                    }
                });
            }
        }
    }
}, 1000);

// 发起人自选的审批人提交
let onApproverSubmit = async (taskRes: any) => {
    let nextAssigneeList = taskRes.nextNodes.map((item: any) => ({
        nodeId: item.nodeId,
        nodeName: item.nodeName,
        multipleColumn: item.multipleColumn,
        assignees: item.approveChooseWay === 2 ? item.userSelectedAssignees : item.optionAssignees
    }));
    if (taskRes.processNodeConfig.approveFormId) {
        window.$dialog.warning({
            title: `【${taskRes.processNodeConfig.nodeName}节点】审批意见`,
            content: () => {
                return h(NInput as any, {
                    class: "my-2",
                    type: "textarea",
                    autosize: { minRows: 3 },
                    placeholder: "请输入审批意见",
                    clearable: true,
                    value: approveMessage.value,
                    onUpdateValue: (v: string) => (approveMessage.value = v)
                }) as any;
            },
            positiveText: "提交",
            negativeText: "取消",
            onPositiveClick: () => {
                if (taskRes.status) {
                    OA_TASK_PASS({
                        taskId: taskRes.taskId,
                        nextAssigneeList,
                        formDetailId: taskRes.processNodeConfig.approveFormId,
                        approveMessage: approveMessage.value,
                        ccPersons: ccPersonsData.value.ccPersons
                    }).then((res) => {
                        if (res.data.code === 0) {
                            window.$message.success("审批通过成功");
                            closeModal();
                            emits("refresh");
                        }
                    });
                } else {
                    OA_TASK_REJECT({
                        taskId: taskRes.taskId,
                        formDetailId: taskRes.processNodeConfig.approveFormId,
                        approveMessage: approveMessage.value,
                        ccPersons: ccPersonsData.value.ccPersons
                    }).then((res) => {
                        if (res.data.code === 0) {
                            window.$message.success("审批驳回成功");
                            closeModal();
                            emits("refresh");
                        }
                    });
                }
            }
        });
    } else {
        if (taskRes.status) {
            OA_TASK_PASS({
                taskId: taskRes.taskId,
                nextAssigneeList,
                formDetailId: taskRes.processNodeConfig.approveFormId,
                approveMessage: "",
                ccPersons: ccPersonsData.value.ccPersons
            }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("审批通过成功");
                    closeModal();
                    emits("refresh");
                }
            });
        } else {
            OA_TASK_REJECT({
                taskId: taskRes.taskId,
                formDetailId: taskRes.processNodeConfig.approveFormId,
                approveMessage: "",
                ccPersons: ccPersonsData.value.ccPersons
            }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("审批驳回成功");
                    closeModal();
                    emits("refresh");
                }
            });
        }
    }
};

// 驳回到节点
let fallbackModal = ref<any>({
    show: false,
    configData: {}
});

let openFallbackModal = () => {
    fallbackModal.value.show = true;
    fallbackModal.value.configData = props.configData;
};

// 获取流程详情
const processDetail = ref<any>({
    starterTrueName: "",
    startTime: "",
    processFormVO: {
        elements: [],
        wcfName: "",
        wcfOptions: {}
    },
    processHistoryVoList: []
});

const getProcessProgress = async () => {
    await GET_OA_INSTANCE_FORM({ processInstId: props.configData.processInstanceId }).then((res) => {
        if (res.data.code === 0) {
            processDetail.value = res.data.data;
        }
    });
};
</script>
