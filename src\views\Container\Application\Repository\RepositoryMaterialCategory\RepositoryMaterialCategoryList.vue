<template>
    <div>
        <n-card hoverable>
            <table-searchbar
                v-model:form="searchForm"
                :config="searchConfig"
                :options="searchOptions"
                @search="onSearch"
            />
        </n-card>
        <n-card class="mt" hoverable>
            <n-space class="mb">
                <n-button secondary type="primary" @click="openEditModal()">新增</n-button>
            </n-space>
            <n-data-table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                :pagination="tablePagination"
                :row-key="tableRowKey"
                :single-line="false"
                bordered
                remote
                striped
                children-key="childrenList"
                @update:checked-row-keys="changeTableSelection"
            />
        </n-card>
        <!--新增编辑-->
        <RepositoryMaterialCategoryEdit
            v-model:show="editModal.show"
            :configData="editModal.configData"
            @refresh="getTableData()"
        />
    </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from "vue";
import { NText } from "naive-ui";
import type { DataTableColumns } from "naive-ui";
import type { TableSearchbarConfig, TableSearchbarData, TableSearchbarOptions } from "@/components/TableSearchbar";
import { TableSearchbar } from "@/components/TableSearchbar";
import { TableActions } from "@/components/TableActions";
import { useCommonTable } from "@/hooks";
import {
    DELETE_REPOSITORY_CATEGORY,
    GET_REPOSITORY_CATEGORY_LIST,
    GET_REPOSITORY_CATEGORY_LIST_WITH_REFERENCE
} from "@/api/application/repository";
import RepositoryMaterialCategoryEdit from "./RepositoryMaterialCategoryEdit.vue";

interface RowProps {
    [key: string]: any;
}

onMounted(async () => {
    await getSearchOptions();
    getTableData();
});

// 搜索项
let searchConfig = ref<TableSearchbarConfig>([
    { label: "品类名称", prop: "categoryName", type: "input" },
    { label: "品类编号", prop: "categoryCode", type: "input" },
    { label: "是否隐藏", prop: "hideFlag", type: "radio" }
]);

let searchOptions = ref<TableSearchbarOptions>({
    hideFlag: [
        { label: "隐藏", value: 1 },
        { label: "显示", value: 0 }
    ]
});

let searchForm = ref<TableSearchbarData>({
    categoryName: null,
    categoryCode: null,
    hideFlag: null
});

let getSearchOptions = async () => {};

// 数据列表
let { tableRowKey, tableData, tableLoading, tableSelection, tablePaginationPreset, changeTableSelection } =
    useCommonTable<RowProps>("id");

let tableColumns = ref<DataTableColumns<RowProps>>([
    {
        type: "selection"
    },
    {
        title: "品类名称",
        key: "categoryName",
        align: "center"
    },
    {
        title: "品类编号",
        key: "categoryCode",
        align: "center"
    },
    {
        title: "操作",
        key: "actions",
        align: "center",
        width: "150",
        render(row) {
            return h(TableActions, {
                type: "button",
                buttonActions: [
                    {
                        label: "编辑",
                        tertiary: true,
                        onClick: () => {
                            openEditModal(row);
                        }
                    },
                    {
                        label: "删除",
                        type: "error",
                        tertiary: true,
                        onClick: () => {
                            onDelete(row);
                        }
                    }
                ]
            });
        }
    }
]);

let tablePagination = reactive({
    ...tablePaginationPreset,
    onChange: (page: number) => {
        tablePagination.page = page;
        getTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
        tablePagination.pageSize = pageSize;
        tablePagination.page = 1;
        getTableData();
    }
});

let getTableData = () => {
    tableLoading.value = true;
    if (!!searchForm.value.categoryName || !!searchForm.value.categoryCode || searchForm.value.hideFlag !== null) {
        GET_REPOSITORY_CATEGORY_LIST_WITH_REFERENCE({
            current: tablePagination.page,
            size: tablePagination.pageSize,
            ...searchForm.value
        }).then((res) => {
            if (res.data.code === 0) {
                tableData.value = res.data.data.records;
                tablePagination.itemCount = res.data.data.total;
                tableLoading.value = false;
            }
        });
    } else {
        GET_REPOSITORY_CATEGORY_LIST({
            current: tablePagination.page,
            size: tablePagination.pageSize
        }).then((res) => {
            if (res.data.code === 0) {
                tableData.value = res.data.data;
                tablePagination.itemCount = res.data.data.total;
                tableLoading.value = false;
            }
        });
    }
};

// 搜索
let onSearch = () => {
    tablePagination.page = 1;
    tablePagination.pageSize = 10;
    getTableData();
};

// 新增编辑
let editModal = ref<{ show: boolean; configData: UnKnownObject }>({
    show: false,
    configData: {}
});

let openEditModal = (row?: RowProps) => {
    editModal.value = {
        show: true,
        configData: row ?? {}
    };
};

// 删除
let onDelete = (row: RowProps) => {
    window.$dialog.warning({
        title: "警告",
        content: "确定删除该品类吗？",
        positiveText: "删除",
        negativeText: "取消",
        onPositiveClick: () => {
            DELETE_REPOSITORY_CATEGORY({ id: row.id }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("删除成功");
                    onSearch();
                }
            });
        }
    });
};
</script>
