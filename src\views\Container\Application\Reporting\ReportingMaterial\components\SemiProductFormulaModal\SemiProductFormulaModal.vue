<template>
    <div>
        <n-drawer
            v-model:show="show"
            :close-on-esc="false"
            :height="600"
            :mask-closable="false"
            placement="bottom"
            @update:show="changeModalShow(false)"
        >
            <n-drawer-content closable>
                <template #header>
                    <div class="flex-y-center">
                        <n-h3 class="m-0 p-0">
                            {{ `半成品规格型号：${props.configData.poleName} - ${props.configData.productModel}` }}
                        </n-h3>
                        <n-button class="ml" type="warning" @click="onImportFormula">导入配方</n-button>
                        <n-button class="ml-1" type="error" @click="onCancelBindFormula">取消配方绑定</n-button>
                    </div>
                </template>
                <n-tabs type="bar">
                    <n-tab-pane name="半成品配方清单">
                        <FormulaSpecifications
                            :configData="props.configData"
                            tabType="semiManufactureList"
                            ref="formulaSpecificationsRef"
                        />
                    </n-tab-pane>
                </n-tabs>
            </n-drawer-content>
        </n-drawer>
    </div>
</template>

<script lang="ts" setup>
import { computed, h, ref, watchEffect } from "vue";
import FormulaSpecifications from "./FormulaSpecifications.vue";
import { NSelect } from "naive-ui";
import {
    GET_FORMULA_PAGE_LIST,
    SEMI_MANUFACTURE_IMPORT_FORMULA,
    SEMI_PRODUCT_CANCEL_BIND_FORMULA
} from "@/api/application/reporting";

let props = withDefaults(defineProps<{ show: boolean; configData: UnKnownObject }>(), { show: () => false });

let emits = defineEmits(["update:show", "refresh"]);

// 弹窗展示
let show = computed({ get: () => props.show, set: (val) => changeModalShow(val) });

let changeModalShow = (show: boolean) => {
    emits("update:show", show);
    if (!show) {
        emits("refresh");
    }
};
let formulaSpecificationsRef = ref<any>(null);

watchEffect(() => {
    if (props.show) {
        getFormulaIdOptions();
    }
});

// 导入配方
let formulaId = ref<Nullable<string>>(null);

let formulaIdOptions = ref<any[]>([]);

let getFormulaIdOptions = () => {
    GET_FORMULA_PAGE_LIST({
        productGenre: "3",
        current: 1,
        size: 1000,
        companyId: props.configData.companyId
    }).then((res) => {
        console.log(1111111, res.data.data.records);
        formulaIdOptions.value = (res.data.data.records ?? []).map((item: any) => {
            return {
                ...item,
                label: `${item.formulaName}-${item.cementSuffix ?? ""}`,
                value: item.id
            };
        });
    });
};

let onImportFormula = () => {
    window.$dialog.warning({
        title: "选择配方",
        positiveText: "导入",
        negativeText: "取消",
        content: () => {
            return h("div", { class: "py-10px" }, [
                h(NSelect, {
                    options: formulaIdOptions.value,
                    clearable: true,
                    filterable: true,
                    placeholder: "请选择配方",
                    value: formulaId.value,
                    onUpdateValue: (v) => (formulaId.value = v)
                })
            ]);
        },
        onPositiveClick: () => {
            SEMI_MANUFACTURE_IMPORT_FORMULA({
                id: props.configData.id,
                formulaId: formulaId.value
            }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("导入成功");
                    formulaSpecificationsRef.value.getTableData();
                } else window.$message.error(res.data.message);
                formulaId.value = null;
            });
        },
        onNegativeClick: () => (formulaId.value = null)
    });
};

// 取消配方绑定
let onCancelBindFormula = () => {
    window.$dialog.warning({
        title: "提示",
        content: "确定要取消？",
        positiveText: "确认",
        negativeText: "取消",
        onPositiveClick: () => {
            SEMI_PRODUCT_CANCEL_BIND_FORMULA({ id: props.configData.id }).then((res) => {
                if (res.data.code === 0) {
                    window.$message.success("取消成功");
                    formulaSpecificationsRef.value.getTableData();
                } else {
                    window.$message.error(res.data.message);
                }
            });
        }
    });
};
</script>
